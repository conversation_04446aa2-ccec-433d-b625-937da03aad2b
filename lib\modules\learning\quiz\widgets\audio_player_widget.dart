import 'package:flutter/material.dart';
import 'package:flutter_study_flow_by_myself/app/services/audio_service.dart';
import 'package:get/get.dart';

/// 音频播放组件
///
/// 用于测验中的听力部分，提供音频播放控制
/// 使用 AudioService 统一处理音频播放逻辑
class AudioPlayerWidget extends StatefulWidget {
  /// 音频URL（可能是单词拼写或实际URL）
  final String audioUrl;

  const AudioPlayerWidget({super.key, required this.audioUrl});

  @override
  State<AudioPlayerWidget> createState() => _AudioPlayerWidgetState();
}

class _AudioPlayerWidgetState extends State<AudioPlayerWidget> {
  /// 音频服务实例
  late AudioService _audioService;

  @override
  void initState() {
    super.initState();
    _audioService = Get.find<AudioService>();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 播放按钮
          _buildPlayButton(),
          const SizedBox(width: 16),
          // 提示文本
          const Expanded(
            child: Text(
              '点击播放单词发音',
              style: TextStyle(fontSize: 16, color: Colors.blue),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建播放按钮
  Widget _buildPlayButton() {
    return InkWell(
      onTap: _playAudio,
      borderRadius: BorderRadius.circular(24),
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.blue.withValues(alpha: 0.1),
        ),
        child: const Icon(Icons.play_arrow, color: Colors.blue, size: 32),
      ),
    );
  }

  /// 播放音频
  void _playAudio() {
    // 从audioUrl中提取单词拼写
    String word = widget.audioUrl;

    // 如果是URL格式，从路径中提取文件名作为单词
    if (widget.audioUrl.contains('/')) {
      // 提取文件名（去掉路径和扩展名）
      final fileName = widget.audioUrl.split('/').last;
      if (fileName.contains('.')) {
        word = fileName.split('.').first; // 去掉扩展名，如 "loop.mp3" -> "loop"
      } else {
        word = fileName;
      }
    }

    // 使用AudioService播放，它会自动处理example.com URL并使用有道API
    _audioService.playWordAudio(word, url: widget.audioUrl);
  }
}
