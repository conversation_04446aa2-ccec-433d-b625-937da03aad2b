import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_study_flow_by_myself/app/services/spaced_repetition_service.dart';
import 'package:flutter_study_flow_by_myself/data/providers/review_item/review_item_local_provider.dart';
import 'package:flutter_study_flow_by_myself/data/providers/user_word_book/user_word_book_local_provider.dart';
import 'package:flutter_study_flow_by_myself/data/providers/word/word_local_provider.dart';
import 'package:flutter_study_flow_by_myself/data/providers/word/word_remote_provider.dart';
import 'package:flutter_study_flow_by_myself/data/providers/quiz/quiz_local_provider.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/review_item_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/word_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/quiz/quiz_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/learning_session_repository.dart';
import 'package:flutter_study_flow_by_myself/data/providers/learning_session/learning_session_local_provider.dart';
import 'package:flutter_study_flow_by_myself/app/managers/user_word_book_state_manager.dart';
import 'package:flutter_study_flow_by_myself/app/services/user_word_book_service.dart';
import 'package:flutter_study_flow_by_myself/app/services/review_plan_service.dart';
import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/app/config/app_config.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/app/services/isar_service.dart';
import 'package:flutter_study_flow_by_myself/app/services/network_service.dart';
import 'package:flutter_study_flow_by_myself/app/services/storage_service.dart';
import 'package:flutter_study_flow_by_myself/app/services/audio_service.dart';
import 'package:flutter_study_flow_by_myself/data/providers/word_book/word_book_local_provider.dart';
import 'package:flutter_study_flow_by_myself/data/providers/word_book/word_book_remote_provider.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/word_book_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/user_word_book_repository.dart';
import 'package:flutter_study_flow_by_myself/app/config/review_config.dart';

/// 应用初始化器
/// 负责管理所有服务的初始化顺序和依赖关系
class AppInitializer {
  /// 初始化核心服务（必须在应用启动前完成的服务）
  static Future<void> initCoreServices() async {
    _registerLogService();
    await _loadEnvironmentConfig();
    await _initializeDatabase();
  }

  /// 注册日志服务
  static void _registerLogService() {
    Get.put(LogService(), permanent: true);
  }

  /// 加载环境配置
  static Future<void> _loadEnvironmentConfig() async {
    final envFile = kDebugMode ? '.env.development' : '.env.production';
    await dotenv.load(fileName: envFile);
  }

  /// 初始化数据库服务
  static Future<void> _initializeDatabase() async {
    await Get.putAsync<IsarService>(() async {
      final isarService = IsarService(logService: Get.find<LogService>());
      await isarService.init();
      return isarService;
    }, permanent: true);
  }

  /// 初始化非核心服务（在InitialBinding中调用）
  static void initNonCoreServices() {
    _registerNetworkAndStorage();
    _registerAlgorithmServices(); // ⭐ 算法服务需要在仓库之前注册
    _registerDataProviders();
    _registerRepositories();
    _registerGlobalManagers();
    _registerGlobalServices(); // ⭐ 全局业务服务需要在仓库之后注册
    AppConfig.printConfig();
  }

  // ==================== 网络和存储服务 ====================
  static void _registerNetworkAndStorage() {
    Get.put(
      NetworkService(
        baseUrl: AppConfig.baseUrl,
        logService: Get.find<LogService>(),
      ),
      permanent: true,
    );

    Get.putAsync<StorageService>(() async {
      final storageService = StorageService();
      return await storageService.init();
    }, permanent: true);

    Get.put(AudioService(logService: Get.find<LogService>()), permanent: true);
  }

  // ==================== 数据提供者 ====================
  static void _registerDataProviders() {
    // 单词本相关提供者
    Get.put<WordBookLocalProvider>(
      WordBookLocalProvider(
        isarService: Get.find<IsarService>(),
        log: Get.find<LogService>(),
      ),
      permanent: true,
    );

    Get.put<WordBookRemoteProvider>(
      WordBookRemoteProvider(
        networkService: Get.find<NetworkService>(),
        log: Get.find<LogService>(),
      ),
      permanent: true,
    );

    Get.put<UserWordBookLocalProvider>(
      UserWordBookLocalProvider(
        isarService: Get.find<IsarService>(),
        log: Get.find<LogService>(),
      ),
      permanent: true,
    );

    // 单词相关提供者
    Get.put<WordLocalProvider>(
      WordLocalProvider(
        isarService: Get.find<IsarService>(),
        log: Get.find<LogService>(),
        wordBookProvider: Get.find<WordBookLocalProvider>(),
      ),
      permanent: true,
    );

    Get.put<WordRemoteProvider>(
      WordRemoteProvider(
        networkService: Get.find<NetworkService>(),
        log: Get.find<LogService>(),
      ),
      permanent: true,
    );

    // 复习相关提供者
    Get.put<ReviewItemLocalProvider>(
      ReviewItemLocalProvider(
        isarService: Get.find<IsarService>(),
        logService: Get.find<LogService>(),
      ),
      permanent: true,
    );

    // 测验相关提供者
    Get.put<QuizLocalProvider>(
      QuizLocalProvider(
        isarService: Get.find<IsarService>(),
        log: Get.find<LogService>(),
      ),
      permanent: true,
    );

    // 学习会话相关提供者
    Get.put<LearningSessionLocalProvider>(
      LearningSessionLocalProvider(
        isarService: Get.find<IsarService>(),
        log: Get.find<LogService>(),
      ),
      permanent: true,
    );
  }

  // ==================== 仓库层 ====================
  static void _registerRepositories() {
    Get.put<WordBookRepository>(
      WordBookRepository(
        localProvider: Get.find<WordBookLocalProvider>(),
        remoteProvider: Get.find<WordBookRemoteProvider>(),
        log: Get.find<LogService>(),
      ),
      permanent: true,
    );

    Get.put<UserWordBookRepository>(
      UserWordBookRepository(
        localProvider: Get.find<UserWordBookLocalProvider>(),
        remoteProvider: Get.find<WordBookRemoteProvider>(),
        isarService: Get.find<IsarService>(),
        log: Get.find<LogService>(),
      ),
      permanent: true,
    );

    Get.put<WordRepository>(
      WordRepository(
        localProvider: Get.find<WordLocalProvider>(),
        remoteProvider: Get.find<WordRemoteProvider>(),
        log: Get.find<LogService>(),
        reviewItemProvider: Get.find<ReviewItemLocalProvider>(),
      ),
      permanent: true,
    );

    Get.put<ReviewItemRepository>(
      ReviewItemRepository(
        reviewItemProvider: Get.find<ReviewItemLocalProvider>(),
        spacedRepetitionService: Get.find<SpacedRepetitionService>(),
        log: Get.find<LogService>(),
        wordProvider: Get.find<WordLocalProvider>(),
        userWordBookProvider: Get.find<UserWordBookLocalProvider>(),
      ),
      permanent: true,
    );

    Get.put<QuizRepository>(
      QuizRepository(
        localProvider: Get.find<QuizLocalProvider>(),
        wordRepository: Get.find<WordRepository>(),
        log: Get.find<LogService>(),
      ),
      permanent: true,
    );

    Get.put<LearningSessionRepository>(
      LearningSessionRepository(
        provider: Get.find<LearningSessionLocalProvider>(),
        log: Get.find<LogService>(),
      ),
      permanent: true,
    );
  }

  // ==================== 全局状态管理器 ====================
  static void _registerGlobalManagers() {
    Get.put<UserWordBookStateManager>(
      UserWordBookStateManager(
        userWordBookRepository: Get.find<UserWordBookRepository>(),
        log: Get.find<LogService>(),
      ),
      permanent: true,
    );
  }

  // ==================== 全局业务服务 ====================
  static void _registerGlobalServices() {
    // 注册用户单词本服务
    Get.put<UserWordBookService>(
      UserWordBookService(
        repository: Get.find<UserWordBookRepository>(),
        log: Get.find<LogService>(),
      ),
      permanent: true,
    );

    // 注册复习计划服务（依赖UserWordBookService）
    Get.put<ReviewPlanService>(
      ReviewPlanService(
        repository: Get.find<ReviewItemRepository>(),
        userWordBookService: Get.find<UserWordBookService>(),
        log: Get.find<LogService>(),
      ),
      permanent: true,
    );
  }

  // ==================== 算法服务 ====================
  static void _registerAlgorithmServices() {
    Get.put(SpacedRepetitionService(), permanent: true);
    Get.put(ReviewConfig(), permanent: true);
  }
}
