# Recall Views 目录说明文档

## 目录职责

`recall/views` 目录负责实现学习模块中单词回忆环节的用户界面部分，提供单词学习和复习的视觉呈现。该目录专注于UI渲染，遵循MVVM架构中视图层的职责，展示单词卡片、记忆评分界面和学习进度。

## 文件结构

```
recall/views/
└── recall_page.dart      # 单词回忆页面，展示单词卡片和记忆评分界面
```

## 核心功能

- **单词卡片展示**：展示待学习或复习的单词
- **卡片翻转动画**：提供单词正反面的翻转动画效果
- **记忆评分界面**：提供用户对记忆程度的自评选项
- **学习进度展示**：显示当前学习进度和剩余单词数量
- **发音播放控件**：提供单词发音播放按钮
- **导航控制**：提供查看详情和跳过等导航选项

## 主要文件说明

### recall_page.dart

- **职责**：单词回忆页面的主要UI组件
- **依赖关系**：
  - `RecallController`：获取单词数据和处理用户交互
  - `LearningController`：同步全局学习状态
  - 各种Flutter UI组件和动画库
- **主要方法和属性**：
  - `RecallPage`：回忆页面的主StatelessWidget
  - `_buildWordCard()`：构建单词卡片组件
  - `_buildRatingButtons()`：构建记忆评分按钮组
  - `_buildProgressIndicator()`：构建学习进度指示器
  - `_buildActionButtons()`：构建操作按钮（详情、跳过等）
- **实现的核心功能**：
  - 使用卡片组件展示单词和释义
  - 实现卡片翻转动画效果
  - 提供记忆评分界面
  - 显示学习进度和导航控件

## 运行流程

1. 用户进入学习流程，`LearningController` 初始化学习会话并加载单词
2. `RecallPage` 被加载，从 `RecallController` 获取当前单词数据
3. 页面渲染单词卡片，默认显示单词正面（仅单词本身）
4. 用户点击卡片，触发翻转动画，显示单词释义和例句
5. 用户评估自己的记忆程度，选择相应的评分按钮
6. 系统记录评分，加载下一个单词，更新进度指示器
7. 重复步骤3-6直到所有单词学习完成，然后进入下一阶段

## 状态管理

- 页面主要通过 `GetX` 的 `Obx` 和 `.obs` 变量监听 `RecallController` 中的状态变化
- 监听的关键状态包括：
  - `currentWord`：当前展示的单词对象
  - `isCardFlipped`：卡片是否翻转（显示释义）
  - `isPlaying`：发音播放状态
  - `currentIndex`：当前单词索引
  - `totalWords`：总单词数量

## 与其他模块的关系

- **recall/controllers**：获取单词数据和处理用户交互
- **learning模块**：通过 `LearningController` 同步全局学习状态
- **detail模块**：提供查看单词详情的入口
- **app/services**：使用音频服务播放单词发音

## 常见混淆点

1. **回忆页vs详情页**：
   - 回忆页(Recall)：专注于单词记忆和学习流程，提供批量学习
   - 详情页(Detail)：专注于展示单个单词的详细信息，提供深入了解

2. **视图与控制器的分离**：
   - 视图仅负责UI渲染，不包含业务逻辑
   - 所有数据处理和状态管理应在 `RecallController` 中实现

3. **新词学习vs复习**：
   - 同一个回忆页面组件用于处理新词学习和复习
   - 控制器根据当前学习阶段提供不同的单词集和学习逻辑

## 最佳实践

- **流畅动画**：实现平滑的卡片翻转和过渡动画
- **响应式设计**：确保在不同屏幕尺寸下正确显示
- **性能优化**：使用高效的动画实现和图片缓存
- **用户体验**：提供清晰的视觉反馈和操作指引
- **错误处理**：妥善处理数据加载失败和音频播放问题
- **无障碍支持**：确保界面元素满足基本的可访问性要求

## 代码示例

```dart
class RecallPage extends GetView<RecallController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(() => Text(controller.isNewWordsMode 
          ? '学习新词' 
          : '复习单词')),
        actions: [
          IconButton(
            icon: Icon(Icons.help_outline),
            onPressed: () => _showHelpDialog(context),
            tooltip: '学习帮助',
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return Center(child: CircularProgressIndicator());
        }
        
        if (controller.hasError.value) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 48, color: Colors.red[300]),
                SizedBox(height: 16),
                Text(controller.errorMessage.value),
                SizedBox(height: 24),
                ElevatedButton(
                  child: Text('重试'),
                  onPressed: controller.retryLoading,
                ),
              ],
            ),
          );
        }
        
        return Column(
          children: [
            // 进度指示器
            _buildProgressIndicator(),
            
            // 单词卡片
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: _buildWordCard(),
              ),
            ),
            
            // 记忆评分按钮
            Obx(() => controller.isCardFlipped.value
              ? _buildRatingButtons()
              : SizedBox(height: 80)), // 占位，保持布局稳定
            
            // 底部操作按钮
            _buildActionButtons(),
          ],
        );
      }),
    );
  }
  
  Widget _buildProgressIndicator() {
    return Padding(
      padding: EdgeInsets.fromLTRB(16, 8, 16, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Obx(() => Text(
                '${controller.currentIndex.value + 1}/${controller.totalWords}',
                style: TextStyle(fontWeight: FontWeight.w500),
              )),
              Obx(() => Text(
                '${(controller.progress * 100).toStringAsFixed(0)}%',
                style: TextStyle(fontWeight: FontWeight.w500),
              )),
            ],
          ),
          SizedBox(height: 8),
          LinearProgressIndicator(
            value: controller.progress,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
          ),
        ],
      ),
    );
  }
  
  Widget _buildWordCard() {
    final word = controller.currentWord.value;
    if (word == null) return SizedBox.shrink();
    
    return GestureDetector(
      onTap: controller.flipCard,
      child: AnimatedFlipCard(
        isFlipped: controller.isCardFlipped.value,
        frontWidget: _buildCardFront(word),
        backWidget: _buildCardBack(word),
      ),
    );
  }
  
  Widget _buildCardFront(Word word) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              word.word,
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16),
            Text(
              word.phonetic,
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[700],
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24),
            IconButton(
              icon: Obx(() => Icon(
                controller.isPlaying.value 
                  ? Icons.volume_up 
                  : Icons.volume_up_outlined,
                color: Colors.blue,
                size: 32,
              )),
              onPressed: controller.playPronunciation,
              tooltip: '播放发音',
            ),
            SizedBox(height: 32),
            Text(
              '点击卡片查看释义',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildCardBack(Word word) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(24),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Text(
                  word.word,
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              SizedBox(height: 8),
              Center(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      word.phonetic,
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[700],
                      ),
                    ),
                    SizedBox(width: 8),
                    IconButton(
                      icon: Icon(Icons.volume_up_outlined, color: Colors.blue),
                      onPressed: controller.playPronunciation,
                      tooltip: '播放发音',
                      iconSize: 20,
                      padding: EdgeInsets.zero,
                      constraints: BoxConstraints(),
                    ),
                  ],
                ),
              ),
              Divider(height: 32),
              ...word.definitions.map((def) => Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.grey[200],
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            def.partOfSpeech,
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 4),
                    Text(
                      def.meaning,
                      style: TextStyle(fontSize: 16),
                    ),
                    if (def.chineseMeaning != null) ...[
                      SizedBox(height: 2),
                      Text(
                        def.chineseMeaning!,
                        style: TextStyle(
                          color: Colors.grey[700],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ],
                ),
              )).toList(),
              if (word.examples.isNotEmpty) ...[
                Divider(height: 32),
                Text(
                  '例句:',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                ),
                SizedBox(height: 8),
                ...word.examples.take(2).map((example) => Padding(
                  padding: const EdgeInsets.only(bottom: 12.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        example.sentence,
                        style: TextStyle(fontSize: 14),
                      ),
                      SizedBox(height: 4),
                      Text(
                        example.translation,
                        style: TextStyle(
                          color: Colors.grey[700],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                )).toList(),
              ],
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildRatingButtons() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '记忆程度评分:',
            style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 14,
            ),
          ),
          SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildRatingButton(
                rating: 1,
                color: Colors.red[400]!,
                label: '不认识',
              ),
              _buildRatingButton(
                rating: 2,
                color: Colors.orange[400]!,
                label: '有点印象',
              ),
              _buildRatingButton(
                rating: 3,
                color: Colors.yellow[700]!,
                label: '模糊记得',
              ),
              _buildRatingButton(
                rating: 4,
                color: Colors.lightGreen[400]!,
                label: '记得清楚',
              ),
              _buildRatingButton(
                rating: 5,
                color: Colors.green[600]!,
                label: '非常熟悉',
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  Widget _buildRatingButton({
    required int rating,
    required Color color,
    required String label,
  }) {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 4),
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            primary: color,
            padding: EdgeInsets.symmetric(vertical: 12),
          ),
          child: Text(
            '$rating',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          onPressed: () => controller.rateWord(rating),
        ),
      ),
    );
  }
  
  Widget _buildActionButtons() {
    return Padding(
      padding: EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          OutlinedButton.icon(
            icon: Icon(Icons.info_outline),
            label: Text('详情'),
            onPressed: controller.viewWordDetails,
          ),
          OutlinedButton.icon(
            icon: Icon(Icons.skip_next),
            label: Text('跳过'),
            onPressed: controller.skipWord,
          ),
        ],
      ),
    );
  }
  
  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('学习帮助'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('1. 点击卡片查看单词释义和例句'),
            SizedBox(height: 8),
            Text('2. 点击发音图标听取单词发音'),
            SizedBox(height: 8),
            Text('3. 查看释义后，根据记忆程度为自己评分'),
            SizedBox(height: 8),
            Text('4. 点击"详情"查看单词的更多信息'),
            SizedBox(height: 8),
            Text('5. 点击"跳过"暂时跳过当前单词'),
          ],
        ),
        actions: [
          TextButton(
            child: Text('了解了'),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }
}

// 卡片翻转动画组件
class AnimatedFlipCard extends StatelessWidget {
  final bool isFlipped;
  final Widget frontWidget;
  final Widget backWidget;
  
  const AnimatedFlipCard({
    required this.isFlipped,
    required this.frontWidget,
    required this.backWidget,
  });
  
  @override
  Widget build(BuildContext context) {
    return TweenAnimationBuilder(
      tween: Tween<double>(
        begin: 0,
        end: isFlipped ? 180 : 0,
      ),
      duration: Duration(milliseconds: 300),
      builder: (context, double value, child) {
        final content = value >= 90 ? backWidget : frontWidget;
        return Transform(
          alignment: Alignment.center,
          transform: Matrix4.identity()
            ..setEntry(3, 2, 0.001)
            ..rotateY(value * pi / 180),
          child: Transform(
            alignment: Alignment.center,
            transform: Matrix4.identity()
              ..rotateY(value >= 90 ? pi : 0),
            child: content,
          ),
        );
      },
    );
  }
}
```

## 相关文件

- **lib/modules/learning/recall/controllers/recall_controller.dart**: 提供单词数据和业务逻辑
- **lib/modules/learning/controllers/learning_controller.dart**: 管理整体学习流程
- **lib/modules/learning/detail/views/detail_page.dart**: 单词详情页面
- **lib/data/models/word.dart**: 单词数据模型
- **lib/app/services/audio_service.dart**: 音频播放服务

## 常见问题与解决方案

1. **问题**：卡片翻转动画卡顿  
   **解决方案**：优化动画实现，使用更高效的变换方法，考虑使用预渲染

2. **问题**：单词发音播放失败  
   **解决方案**：实现多音源备选方案，提供在线和离线发音选项，妥善处理音频权限问题

3. **问题**：屏幕尺寸适配问题  
   **解决方案**：使用弹性布局和自适应组件，确保在不同屏幕尺寸下正确显示

4. **问题**：卡片内容过多导致显示不全  
   **解决方案**：使用滚动视图，实现内容截断和"查看更多"功能，优先显示最重要的信息 