# 复习流程重构计划

本文档概述了单词复习流程的重构计划，基于新的需求和现有的学习流程实现方式。

## 1. 当前实现分析

### 1.1 现有复习流程的问题
- 没有按组（每组5个单词）分组处理单词
- 测验生成是随机选取当前单词的测验，而不是为每个单词提前生成3道固定测验题
- 没有跟踪单个单词的3道测验题完成情况，只记录了单词级别的错误次数
- 下次复习时间的计算是在每个单词答对时立即进行，而不是在完成该单词的所有测验题后
- 当前UI没有提供足够的进度指示，如当前组/总组数、每组内进度等
- 复习流程与新学流程代码结构不一致，维护成本高

## 2. 重构目标

### 2.1 核心目标
1. 实现分组复习流程，每组5个单词，分组测验
2. 为每个待复习单词生成3道固定测验题，总共3n道题
3. 在单词完成所有3道题时计算下次复习时间
4. 提供清晰的分组和进度指示
5. 统一与新学流程的代码结构和数据流

### 2.2 技术目标
1. 提高代码复用性，减少重复逻辑
2. 确保测试覆盖率
3. 优化状态管理和事件传递
4. 改进错误处理和日志记录
5. 提高UI响应速度

## 3. 核心数据结构变更

### 3.1 ReviewController 状态变量
```dart
// 单词分组相关
final groupSize = 5; // 每组单词数量
final quizzesPerWord = 3; // 每个单词的测验题数量
final currentGroupIndex = 0.obs; // 当前组索引

// 单词和测验题管理
final words = <WordModel>[].obs; // 待复习单词列表
final quizzesByWord = <List<dynamic>>[].obs; // 每个单词对应的测验题列表
final currentPhaseQuizzes = <dynamic>[].obs; // 当前组的所有测验题，打乱顺序

// 进度跟踪
final currentWordIndex = 0.obs; // 当前单词索引
final currentQuizIndex = 0.obs; // 当前测验题索引
final completedQuizCount = 0.obs; // 已完成测验题数量
final totalQuizCount = 0.obs; // 总测验题数量

// 单词学习状态跟踪
final wordErrorCountMap = <int, int>{}.obs; // 单词ID -> 错误次数
final wordCompletedQuizzes = <int, int>{}.obs; // 单词ID -> 已完成测验题数量
```

## 4. 重构步骤

### 4.1 控制器层重构

#### 4.1.1 ReviewController 重构
1. 增加单词分组和测验题生成逻辑
```dart
// 加载待复习单词并分组
Future<void> loadDueReviewWords() async {
  // 1. 获取待复习单词
  // 2. 为每个单词生成3道测验题
  // 3. 设置分组信息和状态
}

// 准备当前组的测验题
void _prepareCurrentGroupQuizzes() {
  // 1. 获取当前组的单词
  // 2. 收集这些单词的所有测验题
  // 3. 打乱测验题顺序
  // 4. 更新当前组测验题列表
}

// 移动到下一组
void _moveToNextGroup() {
  // 1. 增加组索引
  // 2. 准备下一组的测验题
  // 3. 重置当前组内状态
}
```

2. 更新答题处理逻辑
```dart
// 处理用户回答
Future<void> handleAnswer(bool isCorrect, int wordId) async {
  if (isCorrect) {
    // 答对处理
    // 1. 增加该单词已完成测验题数量
    // 2. 检查是否完成该单词所有测验题
    // 3. 如果完成，计算质量评分并设置下次复习时间
    // 4. 检查是否完成当前组所有测验题
    // 5. 如果完成，进入下一组或结束复习
  } else {
    // 答错处理
    // 1. 增加错误计数
    // 2. 导航到详情页面
  }
}
```

#### 4.1.2 ReviewQuizController 重构
1. 简化为只处理单个测验题的显示和交互
2. 移除复杂的测验题加载和组织逻辑，改为使用ReviewController提供的数据

### 4.2 UI层重构

#### 4.2.1 ReviewQuizPage 重构
1. 增加组进度和总进度显示
2. 优化测验题显示和交互逻辑
3. 添加当前组/总组信息的显示

#### 4.2.2 ReviewDetailPage 重构
1. 确保从详情页返回后继续同一题测验
2. 优化显示内容和样式

#### 4.2.3 ReviewResultPage 重构
1. 增加更详细的复习统计信息
2. 优化结果展示和数据可视化

## 5. 测试计划

### 5.1 单元测试
- ReviewController 核心方法测试
- 测验题生成和组织逻辑测试
- 答题处理和进度计算测试

### 5.2 集成测试
- 完整复习流程测试
- 边界条件测试（如只有1个单词、单词数不足一组等）
- 错误场景测试

### 5.3 UI测试
- 各页面交互和状态更新测试
- 复习过程导航测试

## 6. 实施时间线

1. **第1阶段：基础架构重构**
   - ReviewController 核心数据结构改造
   - 测验题生成和组织逻辑实现
   - 单词分组机制实现

2. **第2阶段：复习流程逻辑实现**
   - 答题处理逻辑重构
   - 下次复习时间计算改造
   - 导航和流程控制更新

3. **第3阶段：UI重构和改进**
   - ReviewQuizPage 重构
   - ReviewDetailPage 适配
   - ReviewResultPage 优化

4. **第4阶段：测试和优化**
   - 执行测试计划
   - 性能优化
   - 边界条件处理
   - 用户体验改进 