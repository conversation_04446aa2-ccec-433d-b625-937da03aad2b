import 'package:flutter/material.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/detail/controllers/review_detail_controller.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/services/review_state_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/widgets/review_exit_dialog.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/widgets/word_card.dart';
import 'package:get/get.dart';

/// 复习详情页面
///
/// 显示单词详细信息，参考DetailPage的设计
/// 用户答错题目后查看单词详情，然后可以继续复习
class ReviewDetailPage extends GetView<ReviewDetailController> {
  const ReviewDetailPage({super.key});

  @override
  Widget build(BuildContext context) {
    final stateService = Get.find<ReviewStateService>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('单词详情'),
        elevation: 0,
        // 🔑 自定义返回按钮
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => ReviewExitDialog.show(context: context),
        ),
      ),
      body: Obx(() {
        // 加载状态
        if (controller.isLoading.value) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('正在加载单词详情...'),
              ],
            ),
          );
        }

        // 获取当前单词
        final currentWord = controller.currentWord.value;
        if (currentWord == null) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  '无法加载单词详情',
                  style: TextStyle(fontSize: 18, color: Colors.grey),
                ),
              ],
            ),
          );
        }

        return Column(
          children: [
            // 进度提示
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              color: Colors.blue[50],
              child: Obx(() {
                final completedCount = stateService.completedWords.length;
                final totalCount = stateService.allReviewWords.length;
                final currentGroup = stateService.currentGroupIndex + 1;
                final totalGroups = stateService.wordGroups.length;

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '复习进度：$completedCount/$totalCount 个单词',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.blue[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '当前第 $currentGroup 组，共 $totalGroups 组',
                      style: TextStyle(fontSize: 12, color: Colors.blue[600]),
                    ),
                  ],
                );
              }),
            ),

            // 单词详情卡片
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: WordCard(
                  word: currentWord,
                  onPlayAudio: () => controller.playWordAudio(),
                ),
              ),
            ),

            // 底部操作按钮
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.2),
                    spreadRadius: 1,
                    blurRadius: 5,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: ElevatedButton(
                onPressed: controller.startQuiz,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                ),
                child: const Text(
                  '继续复习',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                ),
              ),
            ),
          ],
        );
      }),
    );
  }
}
