# routes 目录说明文档

## 目录职责

`routes` 目录负责管理整个应用的路由系统，基于GetX的路由管理机制，实现页面间的导航。该目录包含全局路由配置和路由路径常量，是所有页面导航的集中管理点。

## 目录结构

```
routes/
├── app_pages.dart        # GetX 全局路由配置（所有 GetPage 列表）
└── app_routes.dart       # 全局路由路径常量定义
```

## 设计理念

- **统一管理**：集中管理所有模块的路由，避免路由分散和冲突
- **命名常量**：使用常量而非硬编码字符串定义路由路径，减少拼写错误
- **模块化组织**：将各个功能模块的路由收集整合，便于维护和扩展

## 主要文件说明

### app_pages.dart

- **职责**：汇集所有模块的 `GetPage` 列表，形成一个完整的应用路由表
- **内容**：定义一个包含所有路由配置的静态列表，被 `GetMaterialApp` 的 `getPages` 参数使用
- **设计原因**：`GetMaterialApp` 需要一个总的路由表，通过此文件统一管理
- **典型结构**：
  ```dart
  abstract class AppPages {
    static final routes = [
      ...AuthRoutes.routes,
      ...HomeRoutes.routes,
      ...LearningRoutes.routes,
      // 其他模块路由...
    ];
  }
  ```

### app_routes.dart

- **职责**：集中定义所有模块的路由路径字符串常量
- **内容**：包含各个模块路由路径的常量定义，通常按模块组织
- **设计原因**：避免硬编码字符串，统一管理所有路由路径，降低拼写错误，方便重构
- **典型结构**：
  ```dart
  abstract class AppRoutes {
    // 可能包含一些全局路由常量
    static const INITIAL = '/';
    
    // 也可以按模块组织（通常这部分会从各模块的routes文件导入）
    static const HOME = '/home';
    static const LOGIN = '/login';
    // 其他路由路径...
  }
  ```

## 易混淆点

1. **`app_routes.dart` 与 `app_pages.dart` 的区别**：
   - `app_routes.dart` 仅定义**路径常量**（如 `/home`, `/login`）
   - `app_pages.dart` 将这些路径与具体的**页面和绑定**关联，构成 `GetPage` 对象列表
   - 这两个文件职责不同，不可混淆或合并

2. **全局路由与模块路由的关系**：
   - 各模块（如 `auth`, `home`）在其 `routes` 文件夹中定义自己的路由配置
   - `app_pages.dart` 汇总这些配置，形成统一的路由表
   - 导航时使用的是模块定义的路由常量，如 `AuthRoutes.LOGIN`

## 最佳实践

- 路由路径常量命名应使用大写蛇形命名法（`SCREAMING_SNAKE_CASE`）
- 模块内路由路径应添加模块前缀，如 `/auth/login`, `/home/<USER>
- 添加新页面时，应先在对应模块的路由文件中定义，然后确保被 `app_pages.dart` 导入
- 使用路由时优先使用命名路由（`Get.toNamed()`）而非直接创建页面实例

## 代码示例

### 定义模块路由（在模块内的routes文件中）
```dart
// lib/modules/auth/routes/auth_routes.dart
abstract class AuthRoutes {
  static const AUTH = '/auth';
  static const LOGIN = '/auth/login';
  static const REGISTER = '/auth/register';
  
  static final routes = [
    GetPage(
      name: LOGIN,
      page: () => LoginPage(),
      binding: LoginBinding(),
    ),
    GetPage(
      name: REGISTER,
      page: () => RegisterPage(),
      binding: RegisterBinding(),
    ),
    // 其他路由...
  ];
}
```

### 在 app_pages.dart 中汇集所有路由
```dart
// lib/app/routes/app_pages.dart
import 'package:flutter_base_app_template/modules/auth/routes/auth_routes.dart';
import 'package:flutter_base_app_template/modules/home/<USER>/home_routes.dart';
// 其他模块路由...

abstract class AppPages {
  static final routes = [
    ...AuthRoutes.routes,
    ...HomeRoutes.routes,
    // 其他模块路由...
  ];
}
```

### 使用路由进行导航
```dart
// 在任何需要导航的地方
void goToLogin() {
  Get.toNamed(AuthRoutes.LOGIN);
}

void goToHomeAndRemoveUntil() {
  Get.offAllNamed(HomeRoutes.HOME);
}
```

## 相关联文件

- `lib/main.dart`：配置 `GetMaterialApp` 时使用 `AppPages.routes`
- `lib/modules/*/routes/*.dart`：各模块的路由定义文件
- 各个页面文件：作为路由目标被 `GetPage` 引用

## 常见问题与解决方案

- **路由不存在错误**：确保路由路径已在 `app_pages.dart` 中正确注册，并且拼写无误
- **路由页面不显示**：检查 `GetPage` 中的 `page` 参数是否返回了正确的页面实例
- **参数传递问题**：使用 `Get.toNamed(route, arguments: data)` 传递参数，并在目标页面通过 `Get.arguments` 获取 