# widgets 目录说明文档

## 目录职责

`widgets` 目录存放了 `learning` 模块内部使用的可复用UI组件。这些组件专门为学习流程设计，与业务逻辑紧密相关，但又具有一定程度的可复用性，可以在学习模块的不同页面中重复使用。

## 文件结构

```
widgets/
├── word_card.dart           # 单词卡片组件，用于展示单词及其相关信息
└── progress_bar.dart        # 学习进度条组件，用于显示当前学习进度
```

## 核心组件

### `word_card.dart`

`WordCard` 是一个核心UI组件，用于展示单词学习过程中的单词卡片。

- **职责**：
  - 展示单词文本、音标、释义、例句等信息
  - 支持卡片翻转效果（正面显示单词，背面显示释义）
  - 适应不同学习场景（新词学习、复习等）的显示需求

- **主要属性**：
  - `word`：要显示的单词数据模型
  - `showMeaning`：是否显示单词释义（控制卡片是否翻转）
  - `onTap`：点击卡片时的回调函数
  - `onLongPress`：长按卡片时的回调函数
  - `animationDuration`：翻转动画持续时间

- **使用示例**：
```dart
WordCard(
  word: currentWord,
  showMeaning: isShowingMeaning,
  onTap: () => controller.toggleMeaning(),
  animationDuration: Duration(milliseconds: 300),
)
```

### `progress_bar.dart`

`ProgressBar` 用于显示学习进度，让用户了解当前学习会话的完成情况。

- **职责**：
  - 以可视化方式展示学习进度百分比
  - 显示已完成/总计单词数
  - 提供不同状态的视觉反馈（如正常、接近完成等）

- **主要属性**：
  - `progress`：当前进度值（0.0到1.0之间）
  - `totalCount`：总单词数
  - `currentCount`：当前已学习的单词数
  - `showText`：是否显示文字进度
  - `color`：进度条颜色

- **使用示例**：
```dart
ProgressBar(
  progress: controller.progress,
  totalCount: controller.wordsList.length,
  currentCount: controller.currentIndex.value + 1,
  showText: true,
)
```

## 实现细节

### `WordCard` 实现

```dart
class WordCard extends StatelessWidget {
  final Word word;
  final bool showMeaning;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final Duration animationDuration;
  
  const WordCard({
    Key? key,
    required this.word,
    this.showMeaning = false,
    this.onTap,
    this.onLongPress,
    this.animationDuration = const Duration(milliseconds: 300),
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      onLongPress: onLongPress,
      child: AnimatedSwitcher(
        duration: animationDuration,
        transitionBuilder: (Widget child, Animation<double> animation) {
          // 实现翻转动画
          final rotateAnimation = Tween(begin: pi, end: 0.0).animate(animation);
          return AnimatedBuilder(
            animation: rotateAnimation,
            child: child,
            builder: (context, child) {
              final isBack = rotateAnimation.value < pi / 2;
              final tilt = Transform(
                transform: Matrix4.identity()
                  ..setEntry(3, 2, 0.001)
                  ..rotateX(isBack ? pi : rotateAnimation.value),
                alignment: Alignment.center,
                child: child,
              );
              return tilt;
            },
          );
        },
        child: showMeaning 
            ? _buildBackSide() // 卡片背面（显示释义）
            : _buildFrontSide(), // 卡片正面（显示单词）
      ),
    );
  }
  
  Widget _buildFrontSide() {
    return Card(
      key: ValueKey('front'),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: EdgeInsets.all(24),
        width: double.infinity,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              word.text,
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            if (word.phonetic.isNotEmpty) ...[
              SizedBox(height: 8),
              Text(
                '/${word.phonetic}/',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
            SizedBox(height: 24),
            Text(
              '点击查看释义',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildBackSide() {
    return Card(
      key: ValueKey('back'),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: EdgeInsets.all(24),
        width: double.infinity,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text(
                word.text,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            SizedBox(height: 16),
            Text(
              '释义:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            ...word.definitions.map((def) => Padding(
              padding: EdgeInsets.only(bottom: 8),
              child: Text('${def.partOfSpeech} ${def.meaning}'),
            )),
            if (word.examples.isNotEmpty) ...[
              SizedBox(height: 16),
              Text(
                '例句:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 8),
              Text(word.examples.first.text),
              SizedBox(height: 4),
              Text(
                word.examples.first.translation,
                style: TextStyle(color: Colors.grey[600]),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
```

### `ProgressBar` 实现

```dart
class ProgressBar extends StatelessWidget {
  final double progress;
  final int totalCount;
  final int currentCount;
  final bool showText;
  final Color? color;
  
  const ProgressBar({
    Key? key,
    required this.progress,
    required this.totalCount,
    required this.currentCount,
    this.showText = true,
    this.color,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final progressColor = color ?? theme.primaryColor;
    final isCompleted = progress >= 1.0;
    
    return Column(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child: LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(
              isCompleted ? Colors.green : progressColor,
            ),
            minHeight: 8,
          ),
        ),
        if (showText) ...[
          SizedBox(height: 8),
          Text(
            '$currentCount / $totalCount ${isCompleted ? "完成！" : ""}',
            style: TextStyle(
              fontSize: 12,
              color: isCompleted ? Colors.green : Colors.grey[600],
            ),
          ),
        ],
      ],
    );
  }
}
```

## 最佳实践

1. **保持组件的专注性**：
   - 每个组件应专注于单一功能
   - 避免在组件中包含业务逻辑，应通过回调函数传递事件
   - 将视图逻辑封装在组件内，减少外部页面的代码量

2. **参数设计**：
   - 必要参数使用 `required` 关键字标记
   - 为可选参数提供合理的默认值
   - 使用命名参数提高可读性

3. **组件复用**：
   - 设计时考虑不同使用场景的需求
   - 提供足够的自定义选项
   - 在组件内部处理边界情况和错误状态

4. **性能优化**：
   - 对于频繁重建的组件，考虑使用 `const` 构造函数
   - 合理使用 `AnimatedBuilder` 和 `AnimatedSwitcher` 以优化动画性能
   - 对于复杂组件，考虑使用 `RepaintBoundary` 限制重绘范围

## 与其他目录的关系

- **views目录**：views中的页面使用这些组件构建UI
- **controllers目录**：控制器提供组件所需的数据和回调函数
- **models目录**：组件依赖models中定义的数据结构

## 扩展计划

未来可能添加的组件：

- **WordCardList**：管理多个单词卡片的列表组件
- **MemoryLevelIndicator**：显示单词记忆等级的指示器
- **LearningTimer**：学习计时器组件
- **QuizOptionButton**：测验选项按钮组件
- **ScoreDisplay**：分数展示组件 