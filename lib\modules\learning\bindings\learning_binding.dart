import 'package:flutter_study_flow_by_myself/modules/learning/controllers/learning_controller.dart';
import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/app/services/audio_service.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/learning_session_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/quiz/quiz_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/review_item_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/user_word_book_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/word_repository.dart';

// 导入控制器
import '../recall/controllers/recall_controller.dart';
import '../detail/controllers/detail_controller.dart';
import '../quiz/controllers/quiz_controller.dart';
import '../result/controllers/result_controller.dart';
import '../completion/controllers/word_book_completion_controller.dart';

// 导入服务
import '../services/learning_state_service.dart';
import '../services/learning_persistence_service.dart';
import '../services/learning_navigation_service.dart';
import '../services/quiz_generation_service.dart';
import '../services/learning_quality_service.dart';

/// 学习模块绑定类
///
/// 负责注册和初始化学习模块所需的所有依赖项，包括服务和控制器
class LearningBinding extends Bindings {
  @override
  void dependencies() {
    // 获取全局服务
    final logService = Get.find<LogService>();
    final audioService = Get.find<AudioService>();

    // 获取仓库
    final wordRepository = Get.find<WordRepository>();
    final learningSessionRepository = Get.find<LearningSessionRepository>();
    final quizRepository = Get.find<QuizRepository>();
    final reviewItemRepository = Get.find<ReviewItemRepository>();
    final userWordBookRepository = Get.find<UserWordBookRepository>();

    // 注册服务层
    // 1. 状态服务
    Get.lazyPut<LearningStateService>(
      () => LearningStateService(log: logService),
    );

    // 2. 测验生成服务
    Get.lazyPut<QuizGenerationService>(
      () => QuizGenerationService(
        quizRepository: quizRepository,
        wordRepository: wordRepository,
        stateService: Get.find<LearningStateService>(),
        log: logService,
      ),
    );

    // 3. 导航服务
    Get.lazyPut<LearningNavigationService>(
      () => LearningNavigationService(
        stateService: Get.find<LearningStateService>(),
        log: logService,
      ),
    );

    // 4. 持久化服务
    Get.lazyPut<LearningPersistenceService>(
      () => LearningPersistenceService(
        repository: learningSessionRepository,
        stateService: Get.find<LearningStateService>(),
        wordRepository: wordRepository,
        log: logService,
      ),
    );

    // 5. 学习质量服务
    Get.lazyPut<LearningQualityService>(
      () => LearningQualityService(
        stateService: Get.find<LearningStateService>(),
        reviewItemRepository: reviewItemRepository,
        wordRepository: wordRepository,
        userWordBookRepository: userWordBookRepository,
        log: logService,
      ),
    );

    // 注册控制器层
    // 1. 主学习控制器
    Get.lazyPut<LearningController>(
      () => LearningController(
        stateService: Get.find<LearningStateService>(),
        persistenceService: Get.find<LearningPersistenceService>(),
        navigationService: Get.find<LearningNavigationService>(),
        qualityService: Get.find<LearningQualityService>(),
        sessionRepository: learningSessionRepository,
        userWordBookRepository: userWordBookRepository,
        log: logService,
      ),
    );

    // 2. 回忆阶段控制器
    Get.lazyPut<RecallController>(
      () => RecallController(
        learningController: Get.find<LearningController>(),
        stateService: Get.find<LearningStateService>(),
        navigationService: Get.find<LearningNavigationService>(),
        audioService: audioService,
        wordRepository: wordRepository,
        log: logService,
      ),
    );

    // 4. 测验阶段控制器
    Get.lazyPut<QuizController>(
      () => QuizController(
        learningController: Get.find<LearningController>(),
        stateService: Get.find<LearningStateService>(),
        navigationService: Get.find<LearningNavigationService>(),
        quizGenerationService: Get.find<QuizGenerationService>(),
        persistenceService: Get.find<LearningPersistenceService>(),
        log: logService,
      ),
    );

    // 3. 详情阶段控制器
    Get.lazyPut<DetailController>(
      () => DetailController(
        learningController: Get.find<LearningController>(),
        stateService: Get.find<LearningStateService>(),
        navigationService: Get.find<LearningNavigationService>(),
        audioService: audioService,
        wordRepository: wordRepository,
        quizGenerationService: Get.find<QuizGenerationService>(),
        log: logService,
      ),
    );

    // 5. 结果页面控制器
    Get.lazyPut<ResultController>(
      () => ResultController(
        learningController: Get.find<LearningController>(),
        stateService: Get.find<LearningStateService>(),
        navigationService: Get.find<LearningNavigationService>(),
        log: logService,
      ),
    );

    // 6. 单词本完成庆祝页面控制器
    Get.lazyPut<WordBookCompletionController>(
      () => WordBookCompletionController(
        log: logService,
        userWordBookRepository: userWordBookRepository,
      ),
    );
  }
}
