import 'package:flutter/material.dart';

/// 测验进度指示器组件
///
/// 显示当前测验进度，包括总题数、已完成题数和正确题数
class QuizProgressIndicator extends StatelessWidget {
  /// 总题数
  final int totalQuizzes;

  /// 已完成题数
  final int completedQuizzes;

  /// 正确题数
  final int correctQuizzes;

  /// 是否显示数字
  final bool showNumbers;

  const QuizProgressIndicator({
    super.key,
    required this.totalQuizzes,
    required this.completedQuizzes,
    required this.correctQuizzes,
    this.showNumbers = true,
  });

  @override
  Widget build(BuildContext context) {
    // 计算进度百分比
    final progressPercent =
        totalQuizzes == 0 ? 0.0 : completedQuizzes / totalQuizzes;

    // 计算正确率
    final correctPercent =
        completedQuizzes == 0 ? 0.0 : correctQuizzes / completedQuizzes;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 进度条
        Stack(
          children: [
            // 灰色背景
            Container(
              width: double.infinity,
              height: 10,
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(5),
              ),
            ),
            // 蓝色进度条（已完成部分）
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              width: MediaQuery.of(context).size.width * progressPercent,
              height: 10,
              decoration: BoxDecoration(
                color: _getProgressColor(correctPercent),
                borderRadius: BorderRadius.circular(5),
              ),
            ),
          ],
        ),

        // 进度数字
        if (showNumbers) ...[
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // 左侧显示进度
              Text(
                '进度: $completedQuizzes/$totalQuizzes',
                style: const TextStyle(fontSize: 14, color: Colors.black54),
              ),
              // 右侧显示正确率
              Text(
                '正确率: ${(correctPercent * 100).toStringAsFixed(0)}%',
                style: TextStyle(
                  fontSize: 14,
                  color: _getProgressColor(correctPercent),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// 根据正确率获取进度条颜色
  Color _getProgressColor(double correctPercent) {
    if (correctPercent >= 0.8) {
      return Colors.green; // 80%以上显示绿色
    } else if (correctPercent >= 0.6) {
      return Colors.blue; // 60-80%显示蓝色
    } else if (correctPercent >= 0.4) {
      return Colors.orange; // 40-60%显示橙色
    } else {
      return Colors.red; // 40%以下显示红色
    }
  }
}
