import 'dart:math';

/// SM-2间隔重复算法服务类
///
/// 该服务提供基于SM-2算法的间隔重复学习功能，
/// 纯算法服务，不直接操作数据模型
///
/// 不继承GetxService的原因：
/// 1. 纯算法服务，无内部状态管理需求
/// 2. 不需要生命周期钩子或资源管理
/// 3. 功能独立，易于测试和复用
/// 4. 与框架解耦，可以在任何环境中使用
class SpacedRepetitionService {
  SpacedRepetitionService();

  /// 根据认识状态和测验结果计算学习阶段评分
  /// [wasRecognized] - 用户是否认识
  /// [quizCorrect] - 测验是否答对
  /// 返回0-5的评分
  int calculateLearningScore(bool wasRecognized, bool quizCorrect) {
    if (wasRecognized && quizCorrect) return 5; // 认识且答对
    if (!wasRecognized && quizCorrect) return 4; // 不认识但答对
    if (wasRecognized && !quizCorrect) return 2; // 认识但答错
    return 0; // 不认识且答错
  }

  /// 计算新的难易度因子
  /// [currentEF] - 当前难易度因子
  /// [score] - 评分(0-5)
  /// 返回新的难易度因子
  double calculateNewEaseFactor(double currentEF, int score) {
    if (score < 3) return currentEF; // 评分低于3分时不调整难易度

    double newEF =
        currentEF + (0.1 - (5 - score) * (0.08 + (5 - score) * 0.02));
    return max(1.3, newEF); // 确保不小于1.3
  }

  /// 计算下次复习间隔天数
  /// [repetitionCount] - 当前成功复习次数
  /// [easeFactor] - 难易度因子
  /// [previousInterval] - 上次复习间隔
  /// [score] - 评分(0-5)
  /// 返回下次复习间隔天数
  int calculateNextInterval(
    int repetitionCount,
    double easeFactor,
    int previousInterval,
    int score,
  ) {
    // 评分低于3分，安排明天复习
    if (score < 3) return 1;

    // 计算下次复习间隔
    if (repetitionCount == 0) return 1; // 首次成功，1天后复习
    if (repetitionCount == 1) return 6; // 第二次成功，6天后复习

    // 其后每次间隔 = 上次间隔 * 难易度因子
    return (previousInterval * easeFactor).round();
  }

  /// 计算下次复习日期
  /// [intervalDays] - 间隔天数
  /// 返回下次复习日期
  DateTime calculateNextReviewDate(int intervalDays) {
    return DateTime.now().add(Duration(days: intervalDays));
  }

  /// 判断是否到期需要复习
  /// [nextReviewDate] - 下次复习日期
  /// [now] - 当前时间点
  /// 返回布尔值表示是否需要复习
  bool isDueForReview(DateTime nextReviewDate, DateTime now) {
    return nextReviewDate.isBefore(now) || nextReviewDate.isAtSameMomentAs(now);
  }
}
