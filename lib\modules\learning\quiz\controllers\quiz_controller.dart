import 'dart:async';
import 'package:flutter_study_flow_by_myself/data/models/quiz/choice_quiz_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/quiz/spelling_quiz_model.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/controllers/learning_controller.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/services/learning_navigation_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/services/learning_persistence_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/services/learning_state_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/services/quiz_generation_service.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/quiz/models/quiz_answer_state.dart';
import 'package:get/get.dart';

/// 测验控制器
///
/// 负责管理测验页面的状态和交互逻辑
/// 使用状态模式管理答题状态，确保状态一致性
class QuizController extends GetxController {
  // ==================== 依赖注入 ====================
  final LearningController learningController;
  final LearningStateService _stateService;
  final LearningNavigationService _navigationService;
  final QuizGenerationService _quizGenerationService;
  final LearningPersistenceService _persistenceService;
  final LogService _log;

  // ==================== 响应式状态变量 ====================
  /// 唯一的真实状态：当前答题状态
  final Rx<QuizAnswerState> answerState = Rx<QuizAnswerState>(
    const QuizUnansweredState(),
  );

  // ==================== 计算属性 ====================
  /// 计算属性：是否已回答当前题目
  bool get hasAnswered => answerState.value.hasAnswered;

  /// 计算属性：选择题的选中选项索引
  int? get selectedOptionIndex => answerState.value.selectedOptionIndex;

  /// 计算属性：上次答题是否正确
  bool? get isLastAnswerCorrect => answerState.value.isCorrect;

  /// 计算属性：动态按钮文本
  ///
  /// 根据答题状态和学习阶段动态生成按钮文本
  ///
  /// UI 使用位置：
  /// - quiz_page.dart:96 - 显示按钮文本
  /// - quiz_page.dart:83 - 根据文本设置按钮颜色
  ///
  /// 按钮文本逻辑：
  /// - 未答题：返回空字符串（不显示按钮）
  /// - 答错：显示"查看详情"
  /// - 答对：根据学习阶段显示不同文本
  ///   - 最终测验阶段：显示"下一题"
  ///   - 学习阶段最后一个单词：显示"开始随机测验"
  ///   - 学习阶段其他单词：显示"下一个单词"
  ///
  /// 返回值：String - 按钮显示文本
  String get actionButtonText {
    if (!hasAnswered) return '';

    final isCorrect = isLastAnswerCorrect ?? false;

    if (!isCorrect) {
      return '查看详情';
    }

    // 答对的情况
    if (_stateService.isInFinalQuiz) {
      return '下一题';
    }

    // 学习阶段答对
    // 替换已删除的isLastWord方法，直接检查索引
    bool isLastWord =
        _stateService.currentWordIndex >= _stateService.wordIds.length - 1;
    return isLastWord ? '开始随机测验' : '下一个单词';
  }

  // ==================== 构造函数 ====================
  QuizController({
    required this.learningController,
    required LearningStateService stateService,
    required LearningNavigationService navigationService,
    required QuizGenerationService quizGenerationService,
    required LearningPersistenceService persistenceService,
    required LogService log,
  }) : _stateService = stateService,
       _navigationService = navigationService,
       _quizGenerationService = quizGenerationService,
       _persistenceService = persistenceService,
       _log = log;

  // ==================== 生命周期方法 ====================
  @override
  void onInit() {
    super.onInit();
    _log.i('初始化测验控制器');

    // 监听quiz变化，自动重置为未答题状态
    ever(_quizGenerationService.currentQuiz, (newQuiz) {
      if (newQuiz != null) {
        _log.d('检测到新题目，重置为未答题状态');
        answerState.value = const QuizUnansweredState();
      }
    });
  }

  // ==================== 答题处理方法 ====================
  /// 选择题答题处理
  void onChoiceQuizAnswered(int selectedIndex) {
    if (hasAnswered) return;

    final quiz = _quizGenerationService.currentQuiz.value;
    if (quiz is ChoiceQuizModel) {
      final isCorrect = selectedIndex == quiz.correctOptionIndex;

      // 一次性更新所有相关状态
      answerState.value = QuizAnsweredState.choice(
        selectedIndex: selectedIndex,
        isCorrect: isCorrect,
      );

      _handleQuizResult(quiz.wordId, isCorrect);
    }
  }

  /// 拼写题答题处理
  void onSpellingQuizAnswered(String userInput, bool isCorrect) {
    if (hasAnswered) return;

    final quiz = _quizGenerationService.currentQuiz.value;
    if (quiz is SpellingQuizModel) {
      // 一次性更新所有相关状态
      answerState.value = QuizAnsweredState.spelling(
        userInput: userInput,
        isCorrect: isCorrect,
      );

      _handleQuizResult(quiz.wordId, isCorrect);
    }
  }

  /// 处理测验结果并更新状态
  void _handleQuizResult(int wordId, bool isCorrect) {
    _log.i('测验结果: 单词ID=$wordId, 答对=${isCorrect ? "是" : "否"}');

    if (isCorrect) {
      _stateService.updateWordState(wordId, completedQuizzesIncrement: 1);

      final updatedState = _stateService.getWordLearningState(wordId);
      if (updatedState.completedQuizzes >= 3) {
        _log.i('单词 $wordId 已达到3次正确答题，更新复习项');
        learningController.updateWordReviewItem(wordId);
      }

      // 🔑 关键：答对后立即保存学习状态，确保进度不丢失
      _saveCurrentStateAsync();
    } else {
      _stateService.updateWordState(wordId, errorCountIncrement: 1);
    }
  }

  /// 异步保存当前学习状态，不阻塞UI
  void _saveCurrentStateAsync() {
    _persistenceService.saveCurrentState().catchError((e) {
      _log.e('保存学习状态失败: $e');
      // 不抛出异常，避免影响答题流程
    });
  }

  // ==================== 用户交互处理 ====================
  /// 处理用户点击操作按钮
  Future<void> onActionButtonPressed() async {
    if (!hasAnswered) return;

    final buttonText = actionButtonText;
    _log.d('用户点击操作按钮: $buttonText');

    switch (buttonText) {
      case '开始随机测验':
        await _startRandomQuizPhase();
        break;
      case '查看详情':
        await _handleWrongAnswer();
        break;
      case '下一题':
      case '下一个单词':
        await _handleCorrectAnswer();
        break;
      default:
        _log.w('未知的按钮文本: $buttonText');
        break;
    }
  }

  // ==================== 导航处理方法 ====================
  /// 处理答对情况下的导航
  Future<void> _handleCorrectAnswer() async {
    if (_stateService.isInFinalQuiz) {
      await _handleCorrectAnswerInFinalQuiz();
    } else {
      await _handleCorrectAnswerInLearningPhase();
    }
  }

  /// 处理答错情况下的导航
  Future<void> _handleWrongAnswer() async {
    if (_stateService.isInFinalQuiz) {
      // 最终测验阶段：使用当前测验题的单词ID
      final wordId = _extractWordIdFromCurrentQuiz();

      if (wordId > 0) {
        _log.i('最终测验阶段答错，导航到单词 $wordId 的详情页');
        await _navigationService.navigateToDetailById(wordId);
        return;
      }

      // 如果无法获取当前测验题的单词ID，记录错误但仍尝试导航
      _log.w('最终测验阶段无法获取当前测验题的单词ID，使用currentWordIndex');
    }

    // 学习阶段或最终测验阶段的fallback：使用currentWordIndex
    _log.i('使用currentWordIndex导航到详情页: ${_stateService.currentWordIndex}');
    await _navigationService.navigateToDetail(_stateService.currentWordIndex);
  }

  /// 从当前测验题中提取单词ID
  ///
  /// 统一处理不同类型测验题的单词ID提取逻辑
  ///
  /// 调用位置：
  /// - _handleWrongAnswer() 中获取答错题目的单词ID
  ///
  /// 返回值：
  /// - 成功时返回单词ID (> 0)
  /// - 失败时返回 -1
  ///
  /// 支持的测验类型：
  /// - ChoiceQuizModel：选择题
  /// - SpellingQuizModel：拼写题
  int _extractWordIdFromCurrentQuiz() {
    final currentQuiz = _quizGenerationService.currentQuiz.value;
    if (currentQuiz == null) {
      _log.w('当前没有测验题，无法提取单词ID');
      return -1;
    }

    if (currentQuiz is ChoiceQuizModel) {
      return currentQuiz.wordId;
    } else if (currentQuiz is SpellingQuizModel) {
      return currentQuiz.wordId;
    }

    _log.w('未知的测验题类型: ${currentQuiz.runtimeType}，无法提取单词ID');
    return -1;
  }

  // ==================== 阶段转换方法 ====================

  /// 启动随机测验阶段
  ///
  /// 处理从学习阶段到随机测验阶段的完整转换，包括状态设置、题目生成和状态保存
  ///
  /// 调用位置：
  /// - onActionButtonPressed() 中当用户点击"开始随机测验"按钮时调用
  ///
  /// 执行流程：
  /// 1. 设置学习状态为最终测验模式
  /// 2. 生成第一道随机测验题目
  /// 3. 保存当前学习状态到数据库
  /// 4. 根据生成结果决定继续测验或进入结果页面
  ///
  /// 业务逻辑：
  /// - 如果成功生成题目，在当前页面显示随机测验题
  /// - 如果没有需要练习的单词，导航到结果页面
  /// - 通过quiz变化监听自动重置UI状态
  ///
  /// 设计说明：
  /// - 采用就地转换策略，避免不必要的页面跳转
  /// - 保持用户体验的流畅性和连续性
  /// - 集成完整的状态管理和持久化功能
  ///
  /// 异常处理：
  /// - 捕获所有异常并记录详细错误信息
  /// - 发生错误时导航到结果页面，确保用户流程不中断
  ///
  /// 返回值：无返回值（Future&lt;void&gt;）
  Future<void> _startRandomQuizPhase() async {
    _log.i('启动随机测验阶段');

    try {
      // 1. 设置为随机测验阶段
      _stateService.setInFinalQuiz(true);

      // 2. 生成第一道随机测验题
      await _quizGenerationService.generateRandomQuiz();

      // 3. 检查是否成功生成随机测验题
      if (_quizGenerationService.currentQuiz.value != null) {
        // 4. 保存当前状态到数据库，确保进度不丢失
        await _persistenceService.saveCurrentState(enteringFinalQuiz: true);

        // 成功生成题目，状态会自动重置（通过quiz变化监听）
        _log.i('随机测验阶段启动成功，显示第一道随机题');
      } else {
        // 没有需要练习的单词了，直接进入结果页面
        _log.i('所有单词都已完成练习，导航到结果页面');
        await _navigationService.navigateToResult();
      }
    } catch (e) {
      _log.e('启动随机测验阶段失败', error: e);
      // 发生错误时，直接进入结果页面
      await _navigationService.navigateToResult();
    }
  }

  /// 处理随机测验阶段答对情况下的导航
  Future<void> _handleCorrectAnswerInFinalQuiz() async {
    _log.i('处理随机测验阶段答对情况');

    // 生成下一道随机测验题（状态会自动重置）
    await _quizGenerationService.generateRandomQuiz();

    // 检查是否还有需要练习的单词
    if (_quizGenerationService.currentQuiz.value != null) {
      // 还有题目，继续测验
      _log.i('生成下一道随机测验题');
    } else {
      // 所有单词都完成了，导航到结果页面
      _log.i('所有单词都已完成练习，导航到结果页面');
      await _navigationService.navigateToResult();
    }
  }

  /// 处理学习阶段答对情况下的导航
  Future<void> _handleCorrectAnswerInLearningPhase() async {
    _log.i('学习阶段答对，准备导航到下一个单词');

    // 检查是否是最后一个单词
    // 替换已删除的isLastWord方法，直接检查索引
    bool isLastWord =
        _stateService.currentWordIndex >= _stateService.wordIds.length - 1;
    if (isLastWord) {
      // 如果是最后一个单词，准备进入随机测验阶段
      // 注意：这里只是准备，实际的随机测验启动在用户点击按钮时进行
      _log.i('当前是最后一个单词，准备进入随机测验阶段');

      // 按钮文本已经在 _updateActionButtonText 中设置为"开始随机测验"
      // 用户点击按钮时会调用 _startRandomQuizPhase() 方法
    } else {
      // 不是最后一个单词，移动到下一个单词
      _log.i('移动到下一个单词的回忆阶段');
      // 使用服务层方法更新索引，保持封装性
      _stateService.moveToNextWord();
      await _navigationService.navigateToRecall();
    }
  }
}
