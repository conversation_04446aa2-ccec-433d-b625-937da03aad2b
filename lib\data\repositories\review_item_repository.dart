import 'package:flutter_study_flow_by_myself/app/config/review_config.dart';
import 'package:flutter_study_flow_by_myself/app/exceptions/app_exception.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/app/services/spaced_repetition_service.dart';
import 'package:flutter_study_flow_by_myself/data/models/review_item_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/user_word_book_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_model.dart';
import 'package:flutter_study_flow_by_myself/data/providers/review_item/review_item_local_provider.dart';
import 'package:flutter_study_flow_by_myself/data/providers/word/word_local_provider.dart';
import 'package:flutter_study_flow_by_myself/data/providers/user_word_book/user_word_book_local_provider.dart';

/// ReviewItemRepository 负责处理复习项的业务逻辑
///
/// 核心职责：
/// - 管理单词的复习状态
/// - 提供基于单词本的复习项查询
/// - 处理复习结果和调整间隔
///
/// 架构特点：
/// - 所有复习项在单词本下载时已经创建，无需按需创建
/// - 所有操作都基于特定用户单词本，确保学习进度独立
/// - 提供完整的异常处理和日志记录
class ReviewItemRepository {
  final ReviewItemLocalProvider _reviewItemProvider;
  final WordLocalProvider _wordProvider;
  final UserWordBookLocalProvider _userWordBookProvider;
  final SpacedRepetitionService _spacedRepetitionService;
  final LogService _log;

  ReviewItemRepository({
    required ReviewItemLocalProvider reviewItemProvider,
    required WordLocalProvider wordProvider,
    required UserWordBookLocalProvider userWordBookProvider,
    required SpacedRepetitionService spacedRepetitionService,
    required LogService log,
  }) : _reviewItemProvider = reviewItemProvider,
       _wordProvider = wordProvider,
       _userWordBookProvider = userWordBookProvider,
       _spacedRepetitionService = spacedRepetitionService,
       _log = log;

  /// 获取特定单词本中待复习的单词
  ///
  /// 🔑 用途说明：
  /// - 获取该单词本中**真正需要复习**的单词（已学习且到期）
  /// - 主要用于：复习流程中获取待复习的单词列表
  /// - 自动过滤新词，只返回已学习且到期的单词
  ///
  /// [userWordBookId] 用户单词本ID
  /// [limit] 限制返回的数量，默认为50
  /// 返回待复习的单词列表
  Future<List<WordModel>> getDueReviewWordsByUserWordBook(
    int userWordBookId, {
    int limit = 50,
  }) async {
    try {
      // 1. 获取到期的复习项（已过滤新词）
      final now = DateTime.now();
      final dueItems = await _reviewItemProvider
          .getDueReviewItemsForUserWordBook(userWordBookId, now);

      if (dueItems.isEmpty) {
        return [];
      }

      // 2. 限制数量
      final limitedItems =
          dueItems.length > limit ? dueItems.sublist(0, limit) : dueItems;

      // 3. 获取对应的单词
      final wordIds = limitedItems.map((item) => item.wordId).toList();
      final words = await _wordProvider.getWordsByIds(wordIds);

      _log.d('获取待复习单词成功: userWordBookId=$userWordBookId, 数量=${words.length}');
      return words;
    } catch (e) {
      _log.e('获取待复习单词失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '获取待复习单词失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 处理学习结果
  ///
  /// 将新学习的单词转换为已学习状态，并设置首次复习时间
  /// [word] 学习的单词
  /// [userWordBook] 用户单词本
  /// [quality] 学习质量评分（0-5）
  /// 返回更新后的复习项
  Future<ReviewItemModel> processLearningResult(
    WordModel word,
    UserWordBookModel userWordBook,
    int quality,
  ) async {
    try {
      // 1. 获取复习项（应该已存在，因为在下载时就创建了）
      final reviewItem = await _reviewItemProvider
          .getReviewItemByWordIdAndUserWordBook(word.id, userWordBook.id);

      if (reviewItem == null) {
        throw AppException(AppExceptionType.unknown, '复习项不存在，请检查单词本是否正确下载');
      }

      // 2. 使用间隔重复算法计算下次复习间隔
      final interval = _spacedRepetitionService.calculateNextInterval(
        reviewItem.repetitionCount,
        reviewItem.easeFactor,
        reviewItem.previousInterval,
        quality,
      );

      // 3. 更新复习项状态
      reviewItem.isNewWord = false; // 标记为已学习
      reviewItem.previousInterval = interval;
      // 使用ReviewConfig计算下次复习时间（支持测试模式）
      reviewItem.nextReviewDate = ReviewConfig.instance.calculateNextReviewDate(
        interval,
      );

      // 4. 更新学习时间记录
      final now = DateTime.now();
      reviewItem.lastReviewDate = now; // 更新上次复习时间
      reviewItem.totalReviewCount += 1; // 增加总学习次数

      // 如果是首次学习，设置首次学习时间
      reviewItem.firstStudyDate ??= now;

      // 4. 保存更新
      await _reviewItemProvider.saveItem(reviewItem);

      _log.d(
        '处理学习结果成功: wordId=${word.id}, userWordBookId=${userWordBook.id}, nextReview=${reviewItem.nextReviewDate}',
      );
      return reviewItem;
    } catch (e) {
      _log.e('处理学习结果失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '处理学习结果失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 处理复习成功结果
  ///
  /// 根据用户的评分调整复习间隔
  /// [word] 复习的单词
  /// [userWordBook] 用户单词本
  /// [quality] 复习质量评分（0-5）
  /// 返回更新后的复习项
  ///
  /// 设计说明:
  /// 1. 该方法是复习流程中更新复习项状态的主要方法
  /// 2. quality参数（0-5）表示用户对该单词的记忆质量:
  ///    - 5分：完美记忆（直接答对）
  ///    - 3分：良好记忆（答错1次后答对）
  ///    - 2分：一般记忆（答错2次后答对）
  ///    - 1分：困难记忆（答错3次或更多后答对）
  ///    - 0分：完全不记得（在当前流程中不使用，因为用户必须最终答对）
  /// 3. 质量分数由Controller层基于累积的错误次数计算
  /// 4. 质量分数直接影响下次复习的间隔时间和难度系数
  /// 5. 此设计确保了系统可以区分"直接答对"和"多次尝试后答对"的情况
  Future<ReviewItemModel> processReviewSuccess(
    WordModel word,
    UserWordBookModel userWordBook,
    int quality,
  ) async {
    try {
      // 1. 获取复习项
      final reviewItem = await _reviewItemProvider
          .getReviewItemByWordIdAndUserWordBook(word.id, userWordBook.id);

      if (reviewItem == null) {
        throw AppException(AppExceptionType.unknown, '复习项不存在');
      }

      // 2. 使用间隔重复算法计算下次复习间隔和难易度
      final newEF = _spacedRepetitionService.calculateNewEaseFactor(
        reviewItem.easeFactor,
        quality,
      );

      final newInterval = _spacedRepetitionService.calculateNextInterval(
        reviewItem.repetitionCount + 1,
        newEF,
        reviewItem.previousInterval,
        quality,
      );

      // 3. 更新复习项状态
      reviewItem.easeFactor = newEF;
      reviewItem.previousInterval = newInterval;
      // 使用ReviewConfig计算下次复习时间（支持测试模式）
      reviewItem.nextReviewDate = ReviewConfig.instance.calculateNextReviewDate(
        newInterval,
      );
      reviewItem.repetitionCount = reviewItem.repetitionCount + 1;

      // 4. 更新学习时间记录
      final now = DateTime.now();
      reviewItem.lastReviewDate = now; // 更新上次复习时间
      reviewItem.totalReviewCount += 1; // 增加总学习次数 // 增加复习次数

      // 4. 保存更新
      await _reviewItemProvider.saveItem(reviewItem);

      _log.d(
        '处理复习成功: wordId=${word.id}, userWordBookId=${userWordBook.id}, nextReview=${reviewItem.nextReviewDate}',
      );
      return reviewItem;
    } catch (e) {
      _log.e('处理复习成功结果失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '处理复习成功结果失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 处理复习失败结果
  ///
  /// 重置间隔，安排近期复习
  /// [word] 复习的单词
  /// [userWordBook] 用户单词本
  /// 返回更新后的复习项
  ///
  /// 设计说明:
  /// 1. 该方法设计用于"最终失败"的场景，而非临时失败
  /// 2. 在当前复习流程中，我们要求用户必须答对才能进入下一单词，因此不会调用此方法
  /// 3. 复习过程中的错误会累积计数，但不会调用此方法更新复习项状态
  /// 4. 只有当用户最终答对时，才会使用累积的错误计数来计算质量分数，并通过processReviewSuccess更新状态
  /// 5. 保留此方法是为了支持可能的未来扩展，如:
  ///    - 允许用户跳过难记的单词
  ///    - 实现复习时间限制，超时未答对的单词标记为失败
  ///    - 为教师/家长提供强制重置复习间隔的功能
  Future<ReviewItemModel> processReviewFailure(
    WordModel word,
    UserWordBookModel userWordBook,
  ) async {
    try {
      // 1. 获取复习项
      final reviewItem = await _reviewItemProvider
          .getReviewItemByWordIdAndUserWordBook(word.id, userWordBook.id);

      if (reviewItem == null) {
        throw AppException(AppExceptionType.unknown, '复习项不存在');
      }

      // 2. 重置复习状态，安排明天复习
      reviewItem.previousInterval = 1; // 重置为1天
      // 使用ReviewConfig计算下次复习时间（支持测试模式）
      reviewItem.nextReviewDate = ReviewConfig.instance.calculateNextReviewDate(
        1,
      );
      reviewItem.repetitionCount = reviewItem.repetitionCount + 1; // 增加复习次数

      // 3. 更新学习时间记录
      final now = DateTime.now();
      reviewItem.lastReviewDate = now; // 更新上次复习时间
      reviewItem.totalReviewCount += 1; // 增加总学习次数

      // 3. 保存更新
      await _reviewItemProvider.saveItem(reviewItem);

      _log.d(
        '处理复习失败: wordId=${word.id}, userWordBookId=${userWordBook.id}, 安排明日复习',
      );
      return reviewItem;
    } catch (e) {
      _log.e('处理复习失败结果失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '处理复习失败结果失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 获取特定单词本中的学习统计信息
  ///
  /// [userWordBookId] 用户单词本ID
  /// 返回包含统计信息的Map
  Future<Map<String, dynamic>> getReviewStatsByUserWordBook(
    int userWordBookId,
  ) async {
    try {
      // 获取复习项总数
      final totalItems = await _reviewItemProvider.countItemsByUserWordBook(
        userWordBookId,
      );

      // 获取已学习单词数量（非新词）
      final learnedCount = await _reviewItemProvider
          .getLearnedWordsCountByUserWordBook(userWordBookId);

      // 获取今日到期复习项数量
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final tomorrow = today.add(const Duration(days: 1));
      final dueTodayCount = await _reviewItemProvider
          .countDueItemsInRangeByUserWordBook(userWordBookId, today, tomorrow);

      // 获取明天到期复习项数量
      final dayAfterTomorrow = tomorrow.add(const Duration(days: 1));
      final dueTomorrowCount = await _reviewItemProvider
          .countDueItemsInRangeByUserWordBook(
            userWordBookId,
            tomorrow,
            dayAfterTomorrow,
          );

      return {
        'totalItems': totalItems,
        'learnedCount': learnedCount,
        'dueTodayCount': dueTodayCount,
        'dueTomorrowCount': dueTomorrowCount,
      };
    } catch (e) {
      _log.e('获取复习统计信息失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '获取复习统计信息失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 获取当前激活单词本中待复习的单词数量
  ///
  /// 返回当前时间点待复习的单词数量（与复习流程查询逻辑一致）
  Future<int> getDueReviewCountForActiveWordBook() async {
    try {
      // 1. 获取当前激活的单词本
      final activeWordBook =
          await _userWordBookProvider.getActiveUserWordBook();
      if (activeWordBook == null) {
        return 0; // 没有激活的单词本
      }

      // 2. 使用当前时间点查询，与复习流程保持一致
      final now = DateTime.now();

      return await _reviewItemProvider.countDueItemsByCurrentTimeByUserWordBook(
        activeWordBook.id,
        now,
      );
    } catch (e) {
      _log.e('获取激活单词本待复习数量失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '获取激活单词本待复习数量失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 获取指定用户单词本的所有复习项
  ///
  /// 🔑 用途说明：
  /// - 获取该单词本的**所有**复习项（包括新词和已学习词）
  /// - 主要用于：复习计划页面显示、统计分析、过滤排序等
  /// - 不过滤新词，返回完整的复习项列表
  ///
  /// [userWordBookId] 用户单词本ID
  /// 返回该用户单词本的所有复习项
  Future<List<ReviewItemModel>> getReviewItemsByUserWordBook(
    int userWordBookId,
  ) async {
    try {
      final items = await _reviewItemProvider.getAllItemsByUserWordBook(
        userWordBookId,
      );
      _log.d(
        '获取用户单词本所有复习项成功: userWordBookId=$userWordBookId, 数量=${items.length}',
      );
      return items;
    } catch (e) {
      _log.e('获取用户单词本所有复习项失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '获取用户单词本所有复习项失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 获取指定用户单词本中待复习的复习项
  ///
  /// 🔑 用途说明：
  /// - 获取该单词本中**真正需要复习**的项目（已学习且到期）
  /// - 主要用于：复习流程、待复习数量统计、复习提醒等
  /// - 自动过滤新词，只返回已学习且到期的复习项
  ///
  /// [userWordBookId] 用户单词本ID
  /// [now] 当前时间点，用于判断是否到期
  /// 返回该用户单词本中待复习的复习项
  Future<List<ReviewItemModel>> getDueReviewItemsForUserWordBook(
    int userWordBookId,
    DateTime now,
  ) async {
    try {
      final items = await _reviewItemProvider.getDueReviewItemsForUserWordBook(
        userWordBookId,
        now,
      );
      _log.d(
        '获取用户单词本待复习项成功: userWordBookId=$userWordBookId, 数量=${items.length}',
      );
      return items;
    } catch (e) {
      _log.e('获取用户单词本待复习项失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '获取用户单词本待复习项失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 获取今日新词
  ///
  /// 从当前激活单词本中获取指定数量的未学习单词
  /// [limit] 需要获取的单词数量
  /// 返回今日待学习的单词列表
  Future<List<WordModel>> getTodayNewWords(int limit) async {
    try {
      // 1. 获取当前激活的单词本
      final activeWordBook =
          await _userWordBookProvider.getActiveUserWordBook();
      if (activeWordBook == null) {
        _log.w('没有激活的单词本');
        return [];
      }

      // 2. 获取该单词本中标记为新词的复习项对应的单词ID
      final newWordIds = await _reviewItemProvider.getNewWordIdsByUserWordBook(
        activeWordBook.id,
      );

      if (newWordIds.isEmpty) {
        _log.w('激活的单词本中没有标记为新词的单词');
        return [];
      }

      _log.i('激活的单词本中找到${newWordIds.length}个标记为新词的单词');

      // 3. 随机打乱顺序，以获得不同的学习体验
      newWordIds.shuffle();

      // 4. 限制数量
      final limitedIds =
          newWordIds.length > limit ? newWordIds.sublist(0, limit) : newWordIds;

      // 5. 获取对应的单词
      final words = await _wordProvider.getWordsByIds(limitedIds);
      _log.i(
        '获取今日新词成功: userWordBookId=${activeWordBook.id}, 数量=${words.length}',
      );

      return words;
    } catch (e) {
      _log.e('获取今日新词失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '获取今日新词失败',
        type: AppExceptionType.unknown,
      );
    }
  }
}
