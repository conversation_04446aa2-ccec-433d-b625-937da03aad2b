import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/data/models/user_word_book_model.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/user_word_book_repository.dart';
import 'package:get/get.dart';

/// 用户单词本状态管理器
/// 
/// 负责管理应用全局的用户单词本状态，确保所有页面的状态保持同步。
/// 当任何地方修改用户单词本状态时，其他监听的控制器会自动收到通知并更新。
/// 
/// 架构定位：
/// - 属于全局状态管理器（Manager），不是页面控制器（Controller）
/// - 管理用户与单词本的关联状态，而非单词本模板本身
/// - 提供跨页面的状态同步机制
/// 
/// 主要功能：
/// - 维护当前激活的用户单词本状态
/// - 提供状态同步接口
/// - 通过响应式编程自动通知状态变化
/// 
/// 使用场景：
/// - 用户激活/停用单词本后同步首页状态
/// - 用户删除单词本后更新相关页面
/// - 确保学习进度、复习数据等与当前激活单词本一致
/// 
/// 使用方式：
/// ```dart
/// // 在页面控制器中监听状态变化
/// final manager = Get.find<UserWordBookStateManager>();
/// ever(manager.activeUserWordBook, (userWordBook) {
///   // 处理状态变化
///   _handleUserWordBookChanged(userWordBook);
/// });
/// 
/// // 在业务操作后触发状态同步
/// await manager.syncActiveUserWordBook();
/// ```
/// 
/// 设计原则：
/// - 单一职责：只管理用户单词本状态
/// - 响应式：使用Rx实现自动通知
/// - 无副作用：不直接操作UI，只管理状态
class UserWordBookStateManager extends GetxController {
  final UserWordBookRepository _userWordBookRepository;
  final LogService _log;

  /// 构造函数注入依赖
  UserWordBookStateManager({
    required UserWordBookRepository userWordBookRepository,
    required LogService log,
  }) : _userWordBookRepository = userWordBookRepository,
       _log = log;

  /// 当前激活的用户单词本
  ///
  /// 使用 Rx 包装，支持响应式监听。
  /// 当值发生变化时，所有监听者会自动收到通知。
  ///
  /// 注意：这里存储的是 UserWordBookModel（用户与单词本的关联），
  /// 而不是 WordBookModel（单词本模板）
  final Rx<UserWordBookModel?> activeUserWordBook = Rx<UserWordBookModel?>(
    null,
  );

  @override
  void onInit() {
    super.onInit();
    // 初始化时加载当前激活的用户单词本
    _loadInitialState();
  }

  /// 同步激活的用户单词本状态
  ///
  /// 从数据库重新获取当前激活的用户单词本，并更新状态。
  /// 这会触发所有监听者的回调函数。
  ///
  /// 使用场景：
  /// - 用户激活/停用单词本后
  /// - 用户删除单词本后
  /// - 需要确保状态与数据库同步时
  ///
  /// 方法命名说明：
  /// - 使用 sync 而不是 reload，更准确地表达同步状态的含义
  /// - 强调这是用户单词本（UserWordBook）而不是单词本模板（WordBook）
  Future<void> syncActiveUserWordBook() async {
    try {
      final userWordBook =
          await _userWordBookRepository.getActiveUserWordBook();
      activeUserWordBook.value = userWordBook;
      _log.i('用户单词本状态已同步: ${userWordBook?.id ?? 'null'}');
    } catch (e) {
      _log.e('同步激活用户单词本状态失败', error: e);
      activeUserWordBook.value = null;
    }
  }

  /// 私有方法：加载初始状态
  Future<void> _loadInitialState() async {
    await syncActiveUserWordBook();
  }
}
