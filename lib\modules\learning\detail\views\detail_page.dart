import 'package:flutter/material.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_model.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/detail/controllers/detail_controller.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/services/learning_state_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/widgets/learning_progress_bar.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/widgets/learning_exit_dialog.dart';
import 'package:get/get.dart';

class DetailPage extends GetView<DetailController> {
  const DetailPage({super.key});

  @override
  Widget build(BuildContext context) {
    // 获取状态服务
    final stateService = Get.find<LearningStateService>();

    // 注意：自动播放逻辑已移至 DetailController.onInit() 中实现
    // 通过监听 currentWord 变化和页面渲染完成来触发，无需在此处手动调用
    return Scaffold(
      appBar: AppBar(
        title: const Text('单词详情'),
        elevation: 0,
        // 🔑 自定义返回按钮
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => LearningExitDialog.show(context: context),
        ),
      ),
      body: Column(
        children: [
          // 顶部进度条
          Obx(
            () => LearningProgressBar(
              learnedWords: stateService.learnedWordCount,
              totalWords: stateService.wordIds.length,
              completedQuizzes: stateService.totalCompletedQuizzes,
              totalQuizzes: stateService.wordIds.length * 3, // 每个单词需要完成3次
              isInFinalQuiz: stateService.isInFinalQuiz,
            ),
          ),

          // 内容区域
          Expanded(
            child: Obx(() {
              // 检查是否有当前单词
              final word = controller.currentWord.value;
              if (word == null) {
                return const Center(child: CircularProgressIndicator());
              }

              return SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 单词卡片 - 包含拼写和音标
                      _buildWordHeader(word),
                      const SizedBox(height: 24),

                      // 释义部分
                      _buildDefinitionSection(word),
                      const SizedBox(height: 24),

                      // 例句部分
                      _buildExamplesSection(word),
                      const SizedBox(height: 32),

                      // 操作按钮
                      _buildActionButtons(),
                    ],
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  // 单词标题部分（拼写+音标）
  Widget _buildWordHeader(WordModel word) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            Text(
              word.spelling,
              style: const TextStyle(fontSize: 32, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            if (word.phonetic.isNotEmpty)
              Text(
                '/${word.phonetic}/',
                style: TextStyle(
                  fontSize: 20,
                  fontStyle: FontStyle.italic,
                  color: Colors.grey[700],
                ),
              ),
            IconButton(
              icon: const Icon(Icons.volume_up),
              onPressed: controller.playWordAudio,
              color: Colors.blue,
            ),
          ],
        ),
      ),
    );
  }

  // 释义部分
  Widget _buildDefinitionSection(WordModel word) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '释义',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Card(
          elevation: 1,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(word.definition, style: const TextStyle(fontSize: 18)),
          ),
        ),
      ],
    );
  }

  // 例句部分
  Widget _buildExamplesSection(WordModel word) {
    final examples = word.examples;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '例句',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        ...examples.map(
          (example) => Card(
            elevation: 1,
            margin: const EdgeInsets.only(bottom: 8),
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Text(
                example,
                style: const TextStyle(fontSize: 16, height: 1.5),
              ),
            ),
          ),
        ),
        if (examples.isEmpty) const Text('暂无例句'),
      ],
    );
  }

  // 底部操作按钮
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: controller.startQuiz,
            icon: const Icon(Icons.quiz),
            label: const Text('开始测验', style: TextStyle(fontSize: 16)),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }
}
