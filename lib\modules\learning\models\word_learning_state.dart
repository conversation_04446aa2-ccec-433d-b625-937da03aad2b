import 'dart:convert';

/// WordLearningState模型
///
/// 用于跟踪单词学习状态，包括用户是否认识该单词、错误次数和已正确答题数量
/// 此模型仅在内存中使用，不直接存储到数据库，而是序列化为JSON存储在LearningSession中
class WordLearningState {
  /// 用户是否认识此单词
  bool isRecognized;

  /// 错误次数累计
  int errorCount;

  /// 已正确答题数量
  int completedQuizzes;

  /// 构造函数
  WordLearningState({
    this.isRecognized = false,
    this.errorCount = 0,
    this.completedQuizzes = 0,
  });

  /// 创建一个初始状态的WordLearningState实例
  factory WordLearningState.initial() => WordLearningState(
    isRecognized: false,
    errorCount: 0,
    completedQuizzes: 0,
  );

  /// 从JSON映射创建WordLearningState实例
  factory WordLearningState.fromJson(Map<String, dynamic> json) {
    return WordLearningState(
      isRecognized: json['isRecognized'] as bool? ?? false,
      errorCount: json['errorCount'] as int? ?? 0,
      completedQuizzes: json['completedQuizzes'] as int? ?? 0,
    );
  }

  /// 将WordLearningState转换为JSON映射
  Map<String, dynamic> toJson() => {
    'isRecognized': isRecognized,
    'errorCount': errorCount,
    'completedQuizzes': completedQuizzes,
  };

  /// 创建当前状态的副本
  WordLearningState copyWith({
    bool? isRecognized,
    int? errorCount,
    int? completedQuizzes,
  }) {
    return WordLearningState(
      isRecognized: isRecognized ?? this.isRecognized,
      errorCount: errorCount ?? this.errorCount,
      completedQuizzes: completedQuizzes ?? this.completedQuizzes,
    );
  }

  @override
  String toString() =>
      'WordLearningState(isRecognized: $isRecognized, errorCount: $errorCount, completedQuizzes: $completedQuizzes)';

  /// 序列化单词状态映射为JSON字符串
  ///
  /// 将`Map<int, WordLearningState>`转换为JSON字符串，用于持久化存储
  /// [states] 单词ID到学习状态的映射
  /// 返回JSON字符串
  static String serializeStates(Map<int, WordLearningState> states) {
    try {
      final jsonMap = <String, dynamic>{};

      for (final entry in states.entries) {
        jsonMap[entry.key.toString()] = entry.value.toJson();
      }

      return jsonEncode(jsonMap);
    } catch (e) {
      throw Exception('序列化单词状态失败: $e');
    }
  }

  /// 反序列化JSON字符串为单词状态映射
  ///
  /// 将JSON字符串转换为`Map<int, WordLearningState>`，用于状态恢复
  /// [json] JSON字符串
  /// 返回单词ID到学习状态的映射
  static Map<int, WordLearningState> deserializeStates(String json) {
    try {
      final Map<String, dynamic> jsonMap = jsonDecode(json);
      final states = <int, WordLearningState>{};

      for (final entry in jsonMap.entries) {
        final wordId = int.parse(entry.key);
        final state = WordLearningState.fromJson(entry.value);
        states[wordId] = state;
      }

      return states;
    } catch (e) {
      throw Exception('反序列化单词状态失败: $e');
    }
  }
}
