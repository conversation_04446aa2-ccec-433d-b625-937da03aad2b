import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/data/models/learning_session.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/learning_session_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/word_repository.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/app/exceptions/app_exception.dart';
import '../models/word_learning_state.dart';
import 'learning_state_service.dart';

/// 学习持久化服务
///
/// 职责：
/// - 管理学习会话的创建、保存和恢复
/// - 处理学习状态的序列化和反序列化
/// - 提供智能清理机制，优化数据存储
/// - 确保数据一致性和原子操作
///
/// 架构说明：
/// 本服务专注于学习数据的持久化管理，通过依赖注入获取仓库和状态服务，
/// 使用统一的异常处理模式，确保数据操作的可靠性和一致性。
/// 采用智能清理机制避免数据冗余，提供完善的会话恢复功能。
class LearningPersistenceService extends GetxService {
  // ==================== 依赖注入 ====================
  /// 学习会话仓库 - 处理学习会话的数据库操作
  ///
  /// 调用位置：
  /// - createNewSession() 中调用 createSession() 创建新会话
  /// - saveCurrentState() 中调用 getSessionById() 和 updateSession()
  /// - restoreSession() 中获取会话数据
  /// - markSessionAsCompleted() 中调用 completeSession() 完成会话
  /// - performSmartCleanup() 中调用 performSmartCleanup() 清理数据
  ///
  /// 用途：提供学习会话的CRUD操作和智能清理功能
  final LearningSessionRepository _repository;

  /// 学习状态服务 - 管理学习过程中的内存状态
  ///
  /// 调用位置：
  /// - saveCurrentState() 中调用 getAllWordStates() 获取当前状态
  /// - restoreSession() 中调用 restore() 恢复状态
  /// - saveCurrentState() 中调用 setInFinalQuiz() 设置测验阶段
  ///
  /// 用途：获取和设置学习状态，处理状态的序列化和反序列化
  final LearningStateService _stateService;

  /// 单词仓库 - 处理单词数据的获取
  ///
  /// 调用位置：
  /// - createNewSession() 中调用 getWordsByIds() 获取单词数据
  ///
  /// 用途：验证单词ID的有效性并获取单词对象
  final WordRepository _wordRepository;

  /// 日志服务 - 记录持久化操作的日志信息
  ///
  /// 调用位置：
  /// - 所有方法中用于记录操作日志、错误信息和调试信息
  ///
  /// 用途：记录持久化操作的成功/失败状态，便于问题排查和性能监控
  final LogService _log;

  // ==================== 私有状态变量 ====================
  /// 当前活动的学习会话ID
  ///
  /// 存储当前正在进行的学习会话的唯一标识符
  ///
  /// 使用位置：
  /// - createNewSession() 中设置新会话ID
  /// - saveCurrentState() 中用于验证和获取当前会话
  /// - restoreSession() 中设置恢复的会话ID
  /// - markSessionAsCompleted() 中用于完成会话并清空
  ///
  /// 生命周期：从会话创建到会话完成
  String? _currentSessionId;

  // ==================== 构造函数 ====================
  /// 构造函数 - 依赖注入
  ///
  /// 通过构造函数注入所有必要的仓库和服务实例，确保依赖关系清晰
  /// 并提高代码的可测试性和可维护性
  ///
  /// 参数：
  /// - [repository] 学习会话仓库实例
  /// - [stateService] 学习状态服务实例
  /// - [wordRepository] 单词仓库实例
  /// - [log] 日志服务实例
  ///
  /// 注册位置：lib/modules/learning/bindings/learning_binding.dart
  LearningPersistenceService({
    required LearningSessionRepository repository,
    required LearningStateService stateService,
    required WordRepository wordRepository,
    required LogService log,
  }) : _repository = repository,
       _stateService = stateService,
       _wordRepository = wordRepository,
       _log = log;

  // ==================== 私有通用方法 ====================
  /// 通用数据库操作异常处理方法
  ///
  /// 提供统一的异常处理逻辑，减少重复代码，确保所有数据库操作的一致性
  ///
  /// 参数：
  /// - [operationName] 操作名称，用于日志记录和错误信息
  /// - [operation] 要执行的异步操作函数
  ///
  /// 异常处理逻辑：
  /// - 如果是AppException，记录详细错误信息并重新抛出
  /// - 如果是其他异常，包装为AppException并抛出
  /// - 保留原始异常信息便于调试
  ///
  /// 返回值：T - 操作函数的返回值
  Future<T> _handleDatabaseOperation<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    try {
      return await operation();
    } catch (e) {
      if (e is AppException) {
        _log.e('$operationName失败: ${e.message}', error: e);
        rethrow;
      } else {
        _log.e('$operationName失败', error: e);
        throw AppException(
          AppExceptionType.database,
          '$operationName失败',
          originalException: e,
        );
      }
    }
  }

  /// 验证当前会话是否有效
  ///
  /// 检查当前是否有活动会话，并从数据库获取会话详情进行验证
  ///
  /// 调用位置：
  /// - saveCurrentState() 中验证会话有效性
  /// - markSessionAsCompleted() 中验证会话存在性
  ///
  /// 业务逻辑：
  /// 1. 检查当前会话ID是否存在
  /// 2. 从数据库获取会话详情
  /// 3. 验证会话是否存在于数据库中
  ///
  /// 异常处理：
  /// - 如果没有当前会话ID，抛出AppException
  /// - 如果数据库中找不到会话，抛出AppException
  ///
  /// 返回值：LearningSession - 验证通过的会话对象
  Future<LearningSession> _validateCurrentSession() async {
    if (_currentSessionId == null) {
      _log.w('会话验证失败: 当前没有活动会话');
      throw AppException(AppExceptionType.unknown, '当前没有活动会话');
    }

    final session = await _repository.getSessionById(_currentSessionId!);
    if (session == null) {
      _log.w('会话验证失败: 找不到会话 $_currentSessionId');
      throw AppException(AppExceptionType.database, '找不到会话');
    }

    return session;
  }

  // ==================== 会话管理方法 ====================
  /// 创建新的学习会话
  ///
  /// 根据单词本ID和单词列表创建新的学习会话
  ///
  /// 调用位置：
  /// - learning_controller.dart:285 - 初始化新学习会话时调用
  ///
  /// 业务逻辑：
  /// 1. 验证单词ID列表的有效性
  /// 2. 从数据库获取对应的单词对象
  /// 3. 创建新的学习会话记录
  /// 4. 更新当前会话ID和单词本ID
  ///
  /// 参数：
  /// - [wordBookId] 单词本ID，标识学习的单词本
  /// - [wordIdList] 要学习的单词ID列表
  ///
  /// 异常处理：
  /// - 验证单词列表不为空
  /// - 使用通用异常处理方法统一处理数据库异常
  ///
  /// 返回值：LearningSession - 创建的新学习会话对象
  Future<LearningSession> createNewSession(
    int wordBookId,
    List<int> wordIdList,
  ) async {
    return await _handleDatabaseOperation('创建学习会话', () async {
      _log.i('创建新学习会话: 单词本ID=$wordBookId, 单词数量=${wordIdList.length}');

      // 获取单词对象列表，验证单词ID的有效性
      final words = await _wordRepository.getWordsByIds(wordIdList);
      if (words.isEmpty) {
        _log.w('创建会话失败: 未找到任何单词');
        throw AppException(AppExceptionType.database, '创建会话失败: 未找到单词');
      }

      // 创建新的学习会话
      final wordIds = words.map((w) => w.id).toList();
      final session = await _repository.createSession(wordBookId, wordIds);

      // 更新当前会话ID
      _currentSessionId = session.sessionId;

      _log.i('成功创建学习会话: ${session.sessionId}');
      return session;
    });
  }

  /// 保存当前学习状态
  ///
  /// 将当前的学习进度和状态保存到数据库
  ///
  /// 调用位置：
  /// - learning_controller.dart:391 - 进入最终测验阶段时保存状态
  ///
  /// 业务逻辑：
  /// 1. 验证当前会话的有效性
  /// 2. 序列化当前的单词学习状态
  /// 3. 更新会话的学习进度和时间戳
  /// 4. 如果进入最终测验，设置相应标志
  /// 5. 将更新后的会话保存到数据库
  ///
  /// 参数：
  /// - [enteringFinalQuiz] 是否进入最终测验阶段，默认为false
  ///
  /// 状态更新内容：
  /// - wordLearningStateJson: 序列化的单词学习状态
  /// - currentWordIndex: 当前学习的单词索引
  /// - lastStudyTime: 最后学习时间
  /// - isInFinalQuiz: 是否在最终测验阶段（可选）
  ///
  /// 异常处理：
  /// - 使用会话验证方法确保会话有效性
  /// - 使用通用异常处理方法统一处理数据库异常
  ///
  /// 返回值：无返回值（void）
  Future<void> saveCurrentState({bool enteringFinalQuiz = false}) async {
    await _handleDatabaseOperation('保存学习状态', () async {
      _log.i('保存学习状态: 会话ID=$_currentSessionId, 进入最终测验=$enteringFinalQuiz');

      // 验证当前会话的有效性
      final session = await _validateCurrentSession();

      // 序列化单词状态，保存当前的学习进度
      final statesJson = WordLearningState.serializeStates(
        _stateService.getAllWordStates(),
      );

      // 更新会话状态信息
      session.wordLearningStateJson = statesJson;
      session.currentWordIndex = _stateService.currentWordIndex;
      session.lastStudyTime = DateTime.now();

      // 如果进入最终测验阶段，设置相应标志
      if (enteringFinalQuiz) {
        session.isInFinalQuiz = true;
        _stateService.setInFinalQuiz(true);
        _log.d('设置最终测验阶段标志');
      }

      // 保存更新后的会话到数据库
      await _repository.updateSession(session);
      _log.i('学习状态已成功保存');
    });
  }

  /// 恢复会话状态
  ///
  /// 从数据库中的会话数据恢复学习状态到内存
  ///
  /// 调用位置：
  /// - learning_controller.dart:330 - 从数据库恢复学习会话时调用
  ///
  /// 业务逻辑：
  /// 1. 更新当前会话ID和单词本ID
  /// 2. 反序列化会话中保存的单词学习状态
  /// 3. 恢复学习状态服务的内存状态
  /// 4. 确保所有状态数据的一致性
  ///
  /// 参数：
  /// - [session] 要恢复的学习会话对象，包含完整的状态数据
  ///
  /// 恢复内容：
  /// - 当前会话ID和单词本ID
  /// - 单词ID列表和当前学习索引
  /// - 是否在最终测验阶段的标志
  /// - 每个单词的详细学习状态
  ///
  /// 设计说明：
  /// - 使用反序列化方法将JSON状态转换为内存对象
  /// - 通过状态服务的restore方法一次性恢复所有状态
  /// - 确保恢复后的状态与保存时完全一致
  ///
  /// 异常处理：
  /// - 使用通用异常处理方法统一处理异常
  /// - 记录详细的会话ID信息便于调试
  ///
  /// 返回值：无返回值（void）
  Future<void> restoreSession(LearningSession session) async {
    await _handleDatabaseOperation('恢复会话状态', () async {
      _log.i('恢复会话状态: ${session.sessionId}');

      // 更新当前会话ID
      _currentSessionId = session.sessionId;

      // 反序列化单词状态，将JSON数据转换为内存对象
      final states = WordLearningState.deserializeStates(
        session.wordLearningStateJson,
      );

      // 恢复学习状态服务的完整状态
      _stateService.restore(
        session.wordIds,
        session.currentWordIndex,
        session.isInFinalQuiz,
        states,
      );

      _log.i(
        '会话状态已成功恢复: 单词数=${session.wordIds.length}, 当前索引=${session.currentWordIndex}',
      );
    });
  }

  /// 完成学习会话
  ///
  /// 将当前学习会话标记为已完成状态
  ///
  /// 调用位置：
  /// - learning_controller.dart 中的 _markSessionAsCompleted() 私有方法调用
  ///
  /// 业务逻辑：
  /// 1. 验证当前会话的有效性
  /// 2. 调用仓库层的原子操作完成会话
  /// 3. 清除当前会话ID，结束会话生命周期
  ///
  /// 设计说明：
  /// - 使用仓库层的 completeSession() 方法确保原子操作
  /// - 语义清晰，专门用于会话完成的操作
  /// - 完成后清除会话ID，避免后续误操作
  ///
  /// 异常处理：
  /// - 如果没有当前会话，记录警告但不抛出异常
  /// - 如果找不到会话，记录警告但不抛出异常
  /// - 其他异常使用通用异常处理方法
  ///
  /// 返回值：无返回值（void）
  Future<void> markSessionAsCompleted() async {
    try {
      if (_currentSessionId == null) {
        _log.w('标记会话完成失败: 当前没有活动会话');
        return;
      }

      _log.i('标记学习会话为已完成: $_currentSessionId');

      // 获取当前会话，如果不存在则直接返回
      final session = await _repository.getSessionById(_currentSessionId!);
      if (session == null) {
        _log.w('标记会话完成失败: 找不到会话 $_currentSessionId');
        return;
      }

      // 🔑 使用专门的 completeSession 方法，语义更清晰，原子操作
      await _repository.completeSession(session);

      _log.i('学习会话已标记为完成');

      // 清除当前会话ID，结束会话生命周期
      _currentSessionId = null;
    } catch (e) {
      _log.e('标记学习会话完成失败', error: e);
      throw AppException(
        AppExceptionType.database,
        '标记学习会话完成失败',
        originalException: e,
      );
    }
  }

  // ==================== 数据清理方法 ====================
  /// 执行智能清理（同单词本 + 过期会话）
  ///
  /// 清理过期的学习会话数据，释放存储空间并优化性能
  ///
  /// 调用位置：
  /// - learning_controller.dart:568 - 完成学习会话后执行清理
  ///
  /// 业务逻辑：
  /// 1. 验证当前会话的存在性
  /// 2. 获取当前会话的详细信息
  /// 3. 调用仓库层的智能清理方法
  /// 4. 清理同单词本的其他会话和过期会话
  ///
  /// 清理策略：
  /// - 清理同一单词本的其他未完成会话（避免重复数据）
  /// - 清理超过24小时的过期会话（释放存储空间）
  /// - 保留当前活动会话不被清理
  ///
  /// 设计说明：
  /// - 智能清理不影响主流程，异常时只记录日志
  /// - 使用仓库层的批量清理方法提高效率
  /// - 清理结果包含详细的统计信息
  ///
  /// 异常处理：
  /// - 如果没有当前会话，记录警告并跳过清理
  /// - 如果找不到会话，记录警告并跳过清理
  /// - 清理过程中的异常不会抛出，避免影响主流程
  ///
  /// 返回值：无返回值（void）
  Future<void> performSmartCleanup() async {
    try {
      if (_currentSessionId == null) {
        _log.w('没有当前会话，跳过智能清理');
        return;
      }

      // 获取当前会话信息用于清理参数
      final currentSession = await _repository.getSessionById(
        _currentSessionId!,
      );
      if (currentSession == null) {
        _log.w('找不到当前会话，跳过智能清理');
        return;
      }

      _log.i('开始执行智能清理: 当前会话=${currentSession.sessionId}');

      // 调用仓库层的智能清理方法
      final result = await _repository.performSmartCleanup(
        currentSessionId: currentSession.id,
        wordBookId: currentSession.userWordBookId,
        expiredHours: 24,
      );

      _log.i(
        '智能清理完成: 同单词本清理${result['sameBookCleaned']}个, 过期清理${result['expiredCleaned']}个',
      );
    } catch (e) {
      _log.e('智能清理失败', error: e);
      // 🔑 重要设计：不抛出异常，避免影响主流程
      // 智能清理是优化操作，失败不应该影响用户的正常学习流程
    }
  }
}
