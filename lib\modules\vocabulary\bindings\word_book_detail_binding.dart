import 'package:flutter_study_flow_by_myself/data/repositories/review_item_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/user_word_book_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/word_repository.dart';
import 'package:flutter_study_flow_by_myself/modules/vocabulary/controllers/word_book_detail_controller.dart';
import 'package:get/get.dart';

class WordBookDetailBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<WordBookDetailController>(
      () => WordBookDetailController(
        userWordBookRepository: Get.find<UserWordBookRepository>(),
        wordRepository: Get.find<WordRepository>(),
        reviewItemRepository: Get.find<ReviewItemRepository>(),
        log: Get.find(),
      ),
    );
  }
}
