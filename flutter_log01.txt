Launching lib\main.dart on sdk gphone64 x86 64 in debug mode...
Running Gradle task 'assembleDebug'...                           2,520ms
√ Built build\app\outputs\flutter-apk\app-debug.apk
Installing build\app\outputs\flutter-apk\app-debug.apk...        1,501ms
I/flutter ( 6631): [IMPORTANT:flutter/shell/platform/android/android_context_gl_impeller.cc(94)] Using the Impeller rendering backend (OpenGLES).
I/flutter ( 6631): ╔══════════════════════════════════════════════════════╗
I/flutter ( 6631): ║                 ISAR CONNECT STARTED                 ║
I/flutter ( 6631): ╟──────────────────────────────────────────────────────╢
I/flutter ( 6631): ║         Open the link to connect to the Isar         ║
I/flutter ( 6631): ║        Inspector while this build is running.        ║
I/flutter ( 6631): ╟──────────────────────────────────────────────────────╢
I/flutter ( 6631): ║ https://inspect.isar.dev/3.1.0+1/#/46523/wNNRUXm8OaE ║
I/flutter ( 6631): ╚══════════════════════════════════════════════════════╝
I/flutter ( 6631): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter ( 6631): │ #0   LogService.d (package:flutter_study_flow_by_myself/app/services/log_service.dart:47:13)
I/flutter ( 6631): │ #1   AppConfig.printConfig (package:flutter_study_flow_by_myself/app/config/app_config.dart:41:16)
I/flutter ( 6631): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter ( 6631): │ 07:37:51.293 (+0:00:01.054730)
I/flutter ( 6631): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter ( 6631): │ 🐛 --- App Configuration ---
I/flutter ( 6631): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter ( 6631): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter ( 6631): │ #0   LogService.d (package:flutter_study_flow_by_myself/app/services/log_service.dart:47:13)
I/flutter ( 6631): │ #1   AppConfig.printConfig (package:flutter_study_flow_by_myself/app/config/app_config.dart:42:16)
I/flutter ( 6631): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter ( 6631): │ 07:37:51.316 (+0:00:01.077561)
I/flutter ( 6631): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter ( 6631): │ 🐛 Base URL: https://dev.api.example.com/api/v1/
I/flutter ( 6631): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter ( 6631): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter ( 6631): │ #0   LogService.d (package:flutter_study_flow_by_myself/app/services/log_service.dart:47:13)
I/flutter ( 6631): │ #1   AppConfig.printConfig (package:flutter_study_flow_by_myself/app/config/app_config.dart:43:16)
I/flutter ( 6631): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter ( 6631): │ 07:37:51.322 (+0:00:01.083427)
I/flutter ( 6631): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter ( 6631): │ 🐛 App Environment: development
I/flutter ( 6631): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter ( 6631): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter ( 6631): │ #0   LogService.d (package:flutter_study_flow_by_myself/app/services/log_service.dart:47:13)
I/flutter ( 6631): │ #1   AppConfig.printConfig (package:flutter_study_flow_by_myself/app/config/app_config.dart:44:16)
I/flutter ( 6631): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter ( 6631): │ 07:37:51.337 (+0:00:01.099330)
I/flutter ( 6631): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter ( 6631): │ 🐛 Is Production: false
I/flutter ( 6631): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter ( 6631): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter ( 6631): │ #0   LogService.d (package:flutter_study_flow_by_myself/app/services/log_service.dart:47:13)
I/flutter ( 6631): │ #1   AppConfig.printConfig (package:flutter_study_flow_by_myself/app/config/app_config.dart:45:16)
I/flutter ( 6631): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter ( 6631): │ 07:37:51.345 (+0:00:01.106415)
I/flutter ( 6631): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter ( 6631): │ 🐛 Is Development: true
I/flutter ( 6631): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter ( 6631): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter ( 6631): │ #0   LogService.d (package:flutter_study_flow_by_myself/app/services/log_service.dart:47:13)
I/flutter ( 6631): │ #1   AppConfig.printConfig (package:flutter_study_flow_by_myself/app/config/app_config.dart:46:16)
I/flutter ( 6631): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter ( 6631): │ 07:37:51.358 (+0:00:01.119715)
I/flutter ( 6631): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter ( 6631): │ 🐛 Is Staging: false
I/flutter ( 6631): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter ( 6631): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter ( 6631): │ #0   LogService.d (package:flutter_study_flow_by_myself/app/services/log_service.dart:47:13)
I/flutter ( 6631): │ #1   AppConfig.printConfig (package:flutter_study_flow_by_myself/app/config/app_config.dart:47:16)
I/flutter ( 6631): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter ( 6631): │ 07:37:51.367 (+0:00:01.128880)
I/flutter ( 6631): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter ( 6631): │ 🐛 -------------------------
I/flutter ( 6631): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
Syncing files to device sdk gphone64 x86 64...                      51ms

Flutter run key commands.
r Hot reload. 
R Hot restart.
h List all available interactive commands.
d Detach (terminate "flutter run" but leave application running).
c Clear the screen
q Quit (terminate the application on the device).

A Dart VM Service on sdk gphone64 x86 64 is available at: http://127.0.0.1:52816/Rat-ELn4nC4=/
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   HomeController.onInit (package:flutter_study_flow_by_myself/modules/home/<USER>/home_controller.dart:114:10)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:51.734 (+0:00:01.495988)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡 HomeController 初始化开始 时间: 2025-07-30 07:37:51.733773[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
E/libEGL  ( 6631): called unimplemented OpenGL ES API
The Flutter DevTools debugger and profiler on sdk gphone64 x86 64 is available at: http://127.0.0.1:9103?uri=http://127.0.0.1:52816/Rat-ELn4nC4=/
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   UserWordBookStateManager.syncActiveUserWordBook (package:flutter_study_flow_by_myself/app/managers/user_word_book_state_manager.dart:90:12)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:52.581 (+0:00:02.343114)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡 用户单词本状态已同步: 2[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   WordBookRemoteProvider.getAvailableWordBooks (package:flutter_study_flow_by_myself/data/providers/word_book/word_book_remote_provider.dart:41:12)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:52.590 (+0:00:02.351943)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡 获取可用单词本列表[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   HomeController.loadActiveWordBook.<anonymous closure> (package:flutter_study_flow_by_myself/modules/home/<USER>/home_controller.dart:152:16)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:52.625 (+0:00:02.387236)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡 加载激活的单词本成功: 2, 名称: 前端开发词汇[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   HomeController.loadWordBookProgress (package:flutter_study_flow_by_myself/modules/home/<USER>/home_controller.dart:178:12)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:52.665 (+0:00:02.427307)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡 加载单词本学习进度成功: 10%[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   HomeController._cleanupExpiredSessionsOnStartup (package:flutter_study_flow_by_myself/modules/home/<USER>/home_controller.dart:222:12)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:52.675 (+0:00:02.437023)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡 启动时清理过期会话[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   LearningSessionRepository.getLatestUnfinishedSession (package:flutter_study_flow_by_myself/data/repositories/learning_session_repository.dart:99:12)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:52.682 (+0:00:02.444217)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡 获取最新未完成会话: 单词本ID=2[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   LearningSessionRepository.cleanupExpiredSessions (package:flutter_study_flow_by_myself/data/repositories/learning_session_repository.dart:317:12)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:52.694 (+0:00:02.455776)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡 开始清理超过 24 小时的过期会话[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   LearningSessionRepository.getExpiredUnfinishedSessions (package:flutter_study_flow_by_myself/data/repositories/learning_session_repository.dart:208:12)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:52.702 (+0:00:02.464265)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡 查询超过 2025-07-29 07:37:52.701418 的未完成会话[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   LearningSessionRepository.getLatestUnfinishedSession (package:flutter_study_flow_by_myself/data/repositories/learning_session_repository.dart:104:14)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:52.724 (+0:00:02.486178)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡 没有找到未完成的会话[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   HomeController.loadSessionStatus.<anonymous closure> (package:flutter_study_flow_by_myself/modules/home/<USER>/home_controller.dart:209:16)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:52.731 (+0:00:02.493135)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡 未检测到未完成的会话[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/Choreographer( 6631): Skipped 137 frames!  The application may be doing too much work on its main thread.
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   LearningSessionRepository.getExpiredUnfinishedSessions (package:flutter_study_flow_by_myself/data/repositories/learning_session_repository.dart:212:12)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:52.792 (+0:00:02.554120)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡 找到 0 个过期未完成会话[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/OpenGLRenderer( 6631): Davey! duration=2337ms; Flags=1, FrameTimelineVsyncId=1981590, IntendedVsync=4613448503712, Vsync=4615731836954, InputEventId=0, HandleInputStart=4615739843000, AnimationStart=4615739962400, PerformTraversalsStart=4615740341300, DrawStart=4615742758000, FrameDeadline=4613465170378, FrameInterval=4615739471800, FrameStartTime=16666666, SyncQueued=4615743409900, SyncStart=4615744196200, IssueDrawCommandsStart=4615744885400, SwapBuffers=4615749777800, FrameCompleted=4615786770600, DequeueBufferDuration=20366800, QueueBufferDuration=15395100, GpuCompleted=4615766333100, SwapBuffersCompleted=4615786770600, DisplayPresentTime=0, CommandSubmissionCompleted=4615749777800, 
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   LearningSessionRepository.cleanupExpiredSessions (package:flutter_study_flow_by_myself/data/repositories/learning_session_repository.dart:323:14)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:52.803 (+0:00:02.564657)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡 没有找到需要清理的过期会话[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   HomeController._cleanupExpiredSessionsOnStartup.<anonymous closure> (package:flutter_study_flow_by_myself/modules/home/<USER>/home_controller.dart:234:18)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:52.808 (+0:00:02.569644)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡 后台没有找到需要清理的过期会话[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   HomeController.loadDueReviewCount.<anonymous closure> (package:flutter_study_flow_by_myself/modules/home/<USER>/home_controller.dart:261:14)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:52.824 (+0:00:02.585656)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡 加载待复习单词数量: 0[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   HomeController._initializeHomePageDataAsync.<anonymous closure> (package:flutter_study_flow_by_myself/modules/home/<USER>/home_controller.dart:125:14)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:52.828 (+0:00:02.590008)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡 HomeController 数据初始化完成 时间: 2025-07-30 07:37:52.828048[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:56:12)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.407 (+0:00:03.168452)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡 成功加载词书列表：本地2个，远程5个[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:59:12)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.411 (+0:00:03.172780)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡 === 数据库中用户词书详细信息 ===[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:64:14)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.424 (+0:00:03.185848)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡 --- 词书 0 ---[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:65:14)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.429 (+0:00:03.190457)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   本地ID: 1[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:66:14)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.433 (+0:00:03.195022)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   远程ID: 1001[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:67:14)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.436 (+0:00:03.198004)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   本地WordBookId: 1[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:68:14)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.439 (+0:00:03.200735)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   isDownloaded: false[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:69:14)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.442 (+0:00:03.204367)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   isActive: false[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:70:14)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.461 (+0:00:03.223031)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   isCompleted: false[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:71:14)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.468 (+0:00:03.229594)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   totalWords: 20[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:72:14)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.470 (+0:00:03.231739)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   totalLearnedWords: 10[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:73:14)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.483 (+0:00:03.244992)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   dailyNewWordsCount: 2[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:74:14)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.485 (+0:00:03.247372)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   addedDate: 2025-07-18 18:18:50.411950[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:75:14)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.498 (+0:00:03.259830)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   lastLearningDate: 2025-07-26 11:31:04.783799[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:80:16)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.499 (+0:00:03.261064)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   关联词书名称: 基础编程词汇[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:81:16)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.501 (+0:00:03.262836)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   关联词书描述: 涵盖编程领域最核心、最基础的术语，适合编程初学者。[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:82:16)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.503 (+0:00:03.264479)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   关联词书ID: 1[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:83:16)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.517 (+0:00:03.278870)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   关联词书远程ID: 1001[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:84:16)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.519 (+0:00:03.280904)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   关联词书单词数: 20[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:85:16)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.526 (+0:00:03.287738)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   关联词书是否自定义: false[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
E/libEGL  ( 6631): called unimplemented OpenGL ES API
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:64:14)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.540 (+0:00:03.301733)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡 --- 词书 1 ---[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:65:14)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.556 (+0:00:03.317545)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   本地ID: 2[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:66:14)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.565 (+0:00:03.326822)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   远程ID: 1002[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:67:14)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.569 (+0:00:03.331146)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   本地WordBookId: 2[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:68:14)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.577 (+0:00:03.338514)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   isDownloaded: true[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:69:14)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.590 (+0:00:03.352053)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   isActive: true[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:70:14)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.594 (+0:00:03.356386)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   isCompleted: false[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:71:14)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.597 (+0:00:03.358963)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   totalWords: 20[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:72:14)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.612 (+0:00:03.374148)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   totalLearnedWords: 2[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:73:14)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.617 (+0:00:03.378877)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   dailyNewWordsCount: 2[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:74:14)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.619 (+0:00:03.380537)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   addedDate: 2025-07-30 06:29:00.538872[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:75:14)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.622 (+0:00:03.384210)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   lastLearningDate: 2025-07-30 06:30:41.799229[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:80:16)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.630 (+0:00:03.391534)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   关联词书名称: 前端开发词汇[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:81:16)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.635 (+0:00:03.396968)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   关联词书描述: 专注于Web前端开发领域，包含HTML、CSS、JavaScript及主流框架相关词汇。[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:82:16)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.639 (+0:00:03.401263)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   关联词书ID: 2[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:83:16)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.642 (+0:00:03.404117)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   关联词书远程ID: 1002[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:84:16)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.650 (+0:00:03.411850)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   关联词书单词数: 20[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:85:16)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.652 (+0:00:03.413483)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡   关联词书是否自定义: false[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.loadWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:90:12)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.652 (+0:00:03.414339)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡 === 调试信息结束 ===[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.filteredLocalWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:186:10)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.792 (+0:00:03.554406)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡 === filteredLocalWordBooks 调试信息 ===[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.filteredLocalWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:187:10)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.798 (+0:00:03.559461)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡 总本地词书数量: 2[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.filteredLocalWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:192:12)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.804 (+0:00:03.566145)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡 本地词书0: ID=1, remoteId=1001, isDownloaded=false, 词书名=基础编程词汇[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.filteredLocalWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:192:12)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.809 (+0:00:03.571407)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡 本地词书1: ID=2, remoteId=1002, isDownloaded=true, 词书名=前端开发词汇[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
I/flutter ( 6631): [38;5;12m│ #0   LogService.i (package:flutter_study_flow_by_myself/app/services/log_service.dart:52:13)[0m
I/flutter ( 6631): [38;5;12m│ #1   VocabularyController.filteredLocalWordBooks (package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart:200:10)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 07:37:53.818 (+0:00:03.579462)[0m
I/flutter ( 6631): [38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄[0m
I/flutter ( 6631): [38;5;12m│ 💡 过滤后词书数量: 2[0m
I/flutter ( 6631): [38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
D/EGL_emulation( 6631): app_time_stats: avg=40.97ms min=3.88ms max=370.51ms count=30
D/ProfileInstaller( 6631): Installing profile for com.example.flutter_study_flow_by_myself
