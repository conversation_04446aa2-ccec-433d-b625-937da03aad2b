# Detail 子模块说明文档

## 子模块职责

`detail` 子模块负责展示单词的详细信息，包括单词的释义、例句、音标、发音、词源等完整信息。当用户需要深入了解某个单词时，可以从学习流程中跳转到详情页面，查看该单词的全面信息。本模块提供丰富的交互功能，帮助用户全方位掌握单词。

## 目录结构

```
detail/
├── controllers/
│   └── detail_controller.dart      # 详情页控制器，管理单词详细信息和交互逻辑
└── views/
    └── detail_page.dart            # 详情页UI，展示单词的全部信息
```

## 核心功能

1. **单词详情展示**：显示单词的全部信息，包括：
   - 单词拼写和音标
   - 多个释义和词性
   - 例句和翻译
   - 同义词和反义词
   - 词根词缀信息

2. **音频播放**：提供单词发音的音频播放功能

3. **添加笔记**：允许用户为单词添加个人笔记或记忆技巧

4. **收藏功能**：支持将单词添加到收藏夹或自定义单词本

5. **记忆状态标记**：显示和更新单词的记忆状态

6. **相关单词推荐**：展示与当前单词相关的其他词汇

## 主要文件说明

### `detail_controller.dart`

`DetailController` 负责管理详情页的状态和逻辑，包括：

- **依赖**：
  - `WordRepository`：获取单词的详细信息
  - `AudioService`：管理单词发音的播放
  - `UserDataService`：管理用户笔记和收藏状态
  
- **主要功能**：
  - 加载单词详细信息
  - 控制音频播放
  - 处理用户笔记添加和更新
  - 管理单词收藏状态
  - 获取相关单词推荐

### `detail_page.dart`

`DetailPage` 构建单词详情的用户界面，包括：

- **依赖**：
  - `DetailController`：获取单词信息和管理用户交互
  
- **主要功能**：
  - 展示单词的所有详细信息
  - 提供音频播放按钮
  - 展示笔记编辑界面
  - 提供收藏和状态标记按钮
  - 展示相关单词列表

## 运行流程

1. 用户在学习流程中点击"查看详情"按钮或单词本中选择某个单词
2. 应用导航到 `DetailPage`，并传递单词ID
3. `DetailController` 初始化并加载单词的完整信息
4. 页面展示加载动画，然后呈现单词详情
5. 用户可以播放发音、添加笔记、标记收藏等
6. 用户操作（如添加笔记、更新记忆状态）会被保存
7. 用户可以通过返回按钮回到之前的页面，或查看推荐的相关单词

## 状态管理

`DetailController` 管理以下本地状态：

- **当前单词**：详情页正在展示的单词完整信息
- **加载状态**：单词信息和音频加载状态
- **播放状态**：音频播放的状态
- **笔记内容**：用户为单词添加的笔记
- **收藏状态**：单词是否被收藏
- **记忆状态**：单词的记忆等级和下次复习时间

## 与其他模块的关系

- **与 `LearningController` 的关系**：
  - 学习过程中可以跳转到详情页，再返回继续学习
  - 详情页的记忆状态更新会反馈给学习控制器
  
- **与 `RecallModule` 的关系**：
  - 记忆卡片中可以点击查看详情，跳转到详情页
  
- **与 `WordRepository` 的关系**：
  - 获取单词的完整信息，包括例句、同义词等
  
- **与全局服务的关系**：
  - 使用 `AudioService` 播放单词发音
  - 使用 `UserDataService` 保存用户笔记和收藏状态

## 常见混淆点

1. **`detail` 模块与 `recall` 模块的区别**：
   - `detail` 模块提供单词的完整详细信息和多种交互功能
   - `recall` 模块专注于单词记忆过程，信息相对简化
   
2. **`detail` 模块与单词本的区别**：
   - `detail` 模块是单个单词详情的展示页面
   - 单词本是多个单词的集合和管理界面

## 最佳实践

1. **信息展示**：
   - 采用清晰的视觉层次，突出重要信息
   - 使用可折叠区域处理长内容，避免页面过长
   - 考虑不同屏幕尺寸的适配
   
2. **交互设计**：
   - 保持简洁直观的操作方式
   - 为关键操作提供视觉和触觉反馈
   - 提供手势支持，如双击播放发音
   
3. **性能优化**：
   - 延迟加载非关键内容，如例句图片
   - 缓存常用单词的详细信息和音频
   - 预加载可能查看的相关单词

## 代码示例

### `DetailController` 实现示例

```dart
import 'package:get/get.dart';
import '../../../data/repositories/word_repository.dart';
import '../../../app/services/audio_service.dart';
import '../../../app/services/user_data_service.dart';
import '../../../data/models/word.dart';
import '../../../data/models/note.dart';

class DetailController extends GetxController {
  final WordRepository wordRepository = Get.find<WordRepository>();
  final AudioService audioService = Get.find<AudioService>();
  final UserDataService userDataService = Get.find<UserDataService>();
  
  final Rx<Word?> word = Rxn<Word>();
  final RxBool isLoading = true.obs;
  final RxBool isPlaying = false.obs;
  final RxString note = ''.obs;
  final RxBool isFavorite = false.obs;
  final RxInt memoryLevel = 0.obs;
  final RxList<Word> relatedWords = <Word>[].obs;
  
  @override
  void onInit() {
    super.onInit();
    
    // 获取路由参数中的单词ID
    final String wordId = Get.parameters['wordId'] ?? '';
    if (wordId.isNotEmpty) {
      loadWordDetails(wordId);
    } else {
      Get.back(); // 无效的ID，返回上一页
    }
  }
  
  Future<void> loadWordDetails(String wordId) async {
    isLoading.value = true;
    
    try {
      // 加载单词详情
      final wordDetail = await wordRepository.getWordDetails(wordId);
      word.value = wordDetail;
      
      // 加载用户数据
      final userNote = await userDataService.getWordNote(wordId);
      note.value = userNote?.content ?? '';
      
      isFavorite.value = await userDataService.isWordFavorite(wordId);
      memoryLevel.value = await userDataService.getWordMemoryLevel(wordId);
      
      // 获取相关单词
      relatedWords.value = await wordRepository.getRelatedWords(wordId);
      
      // 预加载音频
      audioService.preloadAudio(wordDetail.pronunciation);
    } catch (e) {
      Get.snackbar('错误', '加载单词详情失败: $e');
    } finally {
      isLoading.value = false;
    }
  }
  
  void playPronunciation() async {
    if (word.value == null || isPlaying.value) return;
    
    isPlaying.value = true;
    
    try {
      await audioService.playAudio(word.value!.pronunciation);
    } catch (e) {
      Get.snackbar('错误', '播放发音失败: $e');
    } finally {
      isPlaying.value = false;
    }
  }
  
  void saveNote(String content) async {
    if (word.value == null) return;
    
    try {
      note.value = content;
      await userDataService.saveWordNote(
        Note(
          wordId: word.value!.id,
          content: content,
          updatedAt: DateTime.now(),
        ),
      );
      Get.snackbar('成功', '笔记已保存');
    } catch (e) {
      Get.snackbar('错误', '保存笔记失败: $e');
    }
  }
  
  void toggleFavorite() async {
    if (word.value == null) return;
    
    try {
      isFavorite.value = !isFavorite.value;
      await userDataService.setWordFavorite(word.value!.id, isFavorite.value);
    } catch (e) {
      // 恢复原状态
      isFavorite.value = !isFavorite.value;
      Get.snackbar('错误', '更新收藏状态失败: $e');
    }
  }
  
  void updateMemoryLevel(int level) async {
    if (word.value == null) return;
    
    try {
      memoryLevel.value = level;
      await userDataService.updateWordMemoryLevel(word.value!.id, level);
    } catch (e) {
      Get.snackbar('错误', '更新记忆状态失败: $e');
    }
  }
}
```

### `DetailPage` 实现示例

```dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/detail_controller.dart';
import '../../../widgets/memory_level_indicator.dart';

class DetailPage extends GetView<DetailController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('单词详情'),
        actions: [
          Obx(() => IconButton(
            icon: Icon(
              controller.isFavorite.value 
                  ? Icons.favorite 
                  : Icons.favorite_border,
              color: controller.isFavorite.value 
                  ? Colors.red 
                  : null,
            ),
            onPressed: controller.toggleFavorite,
          )),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return Center(child: CircularProgressIndicator());
        }
        
        if (controller.word.value == null) {
          return Center(child: Text('无法加载单词详情'));
        }
        
        final word = controller.word.value!;
        
        return SingleChildScrollView(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 单词和音标
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          word.text,
                          style: TextStyle(
                            fontSize: 28, 
                            fontWeight: FontWeight.bold
                          ),
                        ),
                        if (word.phonetic.isNotEmpty) 
                          Text(
                            '/${word.phonetic}/',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                            ),
                          ),
                      ],
                    ),
                  ),
                  IconButton(
                    icon: Icon(Icons.volume_up),
                    iconSize: 32,
                    color: Theme.of(context).primaryColor,
                    onPressed: controller.playPronunciation,
                  ),
                ],
              ),
              
              Divider(height: 32),
              
              // 记忆状态
              Row(
                children: [
                  Text('记忆状态: ', style: TextStyle(fontWeight: FontWeight.bold)),
                  MemoryLevelIndicator(
                    level: controller.memoryLevel.value,
                    onChanged: controller.updateMemoryLevel,
                  ),
                ],
              ),
              
              SizedBox(height: 24),
              
              // 释义部分
              Text('释义', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              SizedBox(height: 8),
              ...word.definitions.map((def) => Padding(
                padding: EdgeInsets.only(bottom: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 40,
                      child: Text(
                        def.partOfSpeech,
                        style: TextStyle(
                          fontStyle: FontStyle.italic,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                    Expanded(
                      child: Text(def.meaning),
                    ),
                  ],
                ),
              )),
              
              SizedBox(height: 24),
              
              // 例句部分
              if (word.examples.isNotEmpty) ...[
                Text('例句', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                SizedBox(height: 8),
                ...word.examples.map((example) => Card(
                  margin: EdgeInsets.only(bottom: 12),
                  child: Padding(
                    padding: EdgeInsets.all(12),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(example.text),
                        SizedBox(height: 4),
                        Text(
                          example.translation,
                          style: TextStyle(color: Colors.grey[700]),
                        ),
                      ],
                    ),
                  ),
                )),
                SizedBox(height: 24),
              ],
              
              // 同反义词
              if (word.synonyms.isNotEmpty || word.antonyms.isNotEmpty) ...[
                Text('同义词与反义词', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                SizedBox(height: 8),
                if (word.synonyms.isNotEmpty) ...[
                  Text('同义词:', style: TextStyle(fontWeight: FontWeight.bold)),
                  Wrap(
                    spacing: 8,
                    children: word.synonyms.map((syn) => Chip(
                      label: Text(syn),
                    )).toList(),
                  ),
                  SizedBox(height: 12),
                ],
                if (word.antonyms.isNotEmpty) ...[
                  Text('反义词:', style: TextStyle(fontWeight: FontWeight.bold)),
                  Wrap(
                    spacing: 8,
                    children: word.antonyms.map((ant) => Chip(
                      label: Text(ant),
                    )).toList(),
                  ),
                ],
                SizedBox(height: 24),
              ],
              
              // 词源信息
              if (word.etymology.isNotEmpty) ...[
                Text('词源', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                SizedBox(height: 8),
                Text(word.etymology),
                SizedBox(height: 24),
              ],
              
              // 笔记部分
              Text('我的笔记', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              SizedBox(height: 8),
              TextField(
                controller: TextEditingController(text: controller.note.value),
                decoration: InputDecoration(
                  border: OutlineInputBorder(),
                  hintText: '添加你的笔记或记忆技巧...',
                ),
                maxLines: 4,
                onChanged: (value) => controller.note.value = value,
              ),
              SizedBox(height: 8),
              Align(
                alignment: Alignment.centerRight,
                child: ElevatedButton(
                  onPressed: () => controller.saveNote(controller.note.value),
                  child: Text('保存笔记'),
                ),
              ),
              
              SizedBox(height: 24),
              
              // 相关单词
              if (controller.relatedWords.isNotEmpty) ...[
                Text('相关单词', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                SizedBox(height: 8),
                SizedBox(
                  height: 40,
                  child: ListView.separated(
                    scrollDirection: Axis.horizontal,
                    itemCount: controller.relatedWords.length,
                    separatorBuilder: (_, __) => SizedBox(width: 8),
                    itemBuilder: (context, index) {
                      final relatedWord = controller.relatedWords[index];
                      return ActionChip(
                        label: Text(relatedWord.text),
                        onPressed: () => Get.toNamed(
                          '/learning/detail',
                          parameters: {'wordId': relatedWord.id},
                        ),
                      );
                    },
                  ),
                ),
              ],
            ],
          ),
        );
      }),
    );
  }
}
```

## 相关文件

- `lib/data/repositories/word_repository.dart`：单词数据仓库，提供详细信息
- `lib/app/services/audio_service.dart`：音频服务，管理发音播放
- `lib/app/services/user_data_service.dart`：用户数据服务，管理笔记和收藏
- `lib/modules/learning/learning_controller.dart`：学习模块主控制器
- `lib/modules/learning/widgets/memory_level_indicator.dart`：记忆等级指示器组件

## 常见问题与解决方案

1. **音频播放问题**：
   - **症状**：单词发音无法播放或延迟严重
   - **解决方案**：实现音频预加载和缓存机制，支持离线播放，优化音频格式

2. **内容加载延迟**：
   - **症状**：单词详情页面加载时间过长
   - **解决方案**：实现分步加载，先显示基本信息，后加载额外内容，添加骨架屏优化体验

3. **笔记同步问题**：
   - **症状**：用户笔记无法正确保存或在设备间同步
   - **解决方案**：改进笔记保存机制，添加自动保存功能，实现云端同步 