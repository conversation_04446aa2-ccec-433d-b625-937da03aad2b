# learning 模块说明文档

## 模块职责

`learning` 模块是应用程序的核心功能模块，负责实现单词学习的完整流程，包括新词学习、单词复习、测验巩固、学习统计等功能。该模块实现了一套完整的学习会话管理机制，协调多个子模块共同完成学习流程。

## 目录结构

```
learning/
├── bindings/
│   └── learning_binding.dart        # 注册学习模块的依赖
├── controllers/
│   └── learning_controller.dart     # 【主流程控制器】管理整体学习会话
├── models/
│   └── learning_session.dart        # 学习会话数据模型（临时状态）
├── views/
│   └── learning_page.dart           # 学习流程主页面
├── widgets/
│   ├── word_card.dart               # 单词卡片组件
│   └── progress_bar.dart            # 学习进度组件
├── routes/
│   └── learning_routes.dart         # 定义学习相关路由
├── recall/                          # 单词回忆子模块（新词学习和复习回忆）
│   ├── controllers/
│   │   └── recall_controller.dart   # 回忆页局部控制器，管理当前卡片、翻页、发音等
│   └── views/
│       └── recall_page.dart         # 回忆页面UI
├── detail/                          # 单词详情子模块
│   ├── controllers/
│   │   └── detail_controller.dart   # 详情页控制器，管理单词收藏状态、加载更多例句等
│   └── views/
│       └── detail_page.dart         # 详情页面UI
├── quiz/                            # 单词测验子模块
│   ├── controllers/
│   │   └── quiz_controller.dart     # 测验页控制器，管理题目、选项、计时、答案判断等
│   └── views/
│       └── quiz_page.dart           # 测验页面UI
├── result/                          # 学习结果展示子模块
│   ├── controllers/
│   │   └── result_controller.dart   # 结果页控制器，管理统计数据展示、榜单等
│   └── views/
│       └── result_page.dart         # 结果页面UI
└── complete/                        # 学习完成打卡子模块
    ├── controllers/
    │   └── complete_controller.dart # 完成页控制器，管理打卡信息、分享逻辑
    └── views/
        └── complete_page.dart       # 完成打卡页面UI
```

## 核心功能

- **学习会话管理**：创建和管理学习会话，协调多个学习阶段
- **新词学习**：展示新词，记忆读音、释义、例句等
- **单词复习**：根据记忆曲线复习已学单词
- **测验巩固**：通过多种题型测试学习效果
- **学习统计**：记录和展示学习进度、正确率等数据
- **学习完成**：展示学习成果，提供打卡和分享功能

## 主要文件说明

### learning_binding.dart

- **职责**：注册学习模块所需的依赖项
- **依赖项**：LearningController、RecallController、QuizController、WordRepository 等
- **使用方式**：在路由定义中通过 `binding: LearningBinding()` 使用

### learning_controller.dart

- **职责**：管理整体学习会话，是学习流程的协调者
- **依赖**：WordRepository、StorageService
- **主要功能**：
  - 创建和初始化学习会话
  - 加载新词和复习词列表
  - 跟踪学习进度和状态
  - 协调子页面之间的切换（如新词学完跳到复习）
  - 提交学习结果

### learning_session.dart

- **职责**：定义学习会话的数据结构
- **内容**：
  - 新词和复习词列表
  - 当前学习阶段标识
  - 学习进度和统计数据
  - 用户在会话中的操作记录

### recall/recall_controller.dart

- **职责**：管理单词回忆页面的局部逻辑
- **依赖**：LearningController
- **主要功能**：
  - 管理当前显示的单词卡片
  - 处理翻页和词义显示逻辑
  - 处理用户的记忆评分操作
  - 管理单词发音播放

## 学习流程

学习模块实现了一个完整的学习流程，通常包括以下阶段：

1. **会话初始化**：`LearningController` 创建学习会话，加载今日新词和待复习单词
2. **新词学习**：用户学习新词，使用 `RecallController` 管理学习过程
3. **复习**：学习完新词后，进入复习阶段，回顾之前学过的单词
4. **测验**：完成复习后，进入测验阶段，通过 `QuizController` 管理测验题目
5. **结果展示**：测验完成后，展示学习统计和结果
6. **学习完成**：提供打卡和分享功能，结束本次学习会话

## 状态管理

学习模块使用多层次的状态管理：

1. **全局学习状态**：由 `LearningController` 管理，包括：
   - 学习会话整体状态（`learningSession.obs`）
   - 当前学习阶段（`currentStage.obs`）
   - 总体进度（`overallProgress.obs`）

2. **子页面局部状态**：由各子模块控制器管理，如：
   - `RecallController` 管理当前展示的单词和翻页状态
   - `QuizController` 管理当前题目、选项和答题情况

## 与其他模块的关系

- **home模块**：提供学习入口，展示学习统计摘要
- **data层**：通过 `WordRepository` 获取单词数据，提交学习进度
- **app/services**：使用 `StorageService` 存储学习设置，使用 `AudioService` 播放单词读音

## 易混淆点

1. **学习控制器的层级关系**：
   - `LearningController`：主控制器，管理整体学习会话
   - 子模块控制器（如`RecallController`）：管理特定页面的局部状态和交互
   - 子控制器通常通过 `Get.find<LearningController>()` 获取主控制器以更新全局状态

2. **子模块vs独立模块**：
   - `recall`、`quiz` 等是 `learning` 的子模块，不是独立模块
   - 子模块没有自己的 `routes` 和 `bindings`，由主模块统一管理
   - 子模块的路由在 `learning_routes.dart` 中定义

## 最佳实践

- **学习算法**：实现科学的间隔重复算法，优化记忆效果
- **离线支持**：支持离线学习，定期同步学习进度
- **学习设置**：提供个性化学习设置，如每日新词数量、提醒时间等
- **数据分析**：记录详细的学习数据，提供学习分析和建议
- **学习体验**：加入游戏化元素，提高学习乐趣和坚持度

## 代码示例

### LearningController实现

```dart
class LearningController extends GetxController {
  final WordRepository _wordRepository = Get.find<WordRepository>();
  final StorageService _storageService = Get.find<StorageService>();
  
  // 学习会话状态
  final Rx<LearningSession> session = LearningSession.empty().obs;
  final RxInt currentStage = RxInt(LearningStage.NEW_WORDS);
  final RxBool isLoading = true.obs;
  final RxBool hasError = false.obs;
  final RxString errorMessage = ''.obs;
  
  // 计算属性
  int get totalWords => session.value.newWords.length + session.value.reviewWords.length;
  int get learnedWords => session.value.learnedNewWords.length + session.value.learnedReviewWords.length;
  double get overallProgress => totalWords > 0 ? learnedWords / totalWords : 0.0;
  
  @override
  void onInit() {
    super.onInit();
    initLearningSession();
  }
  
  Future<void> initLearningSession() async {
    isLoading.value = true;
    hasError.value = false;
    
    try {
      // 加载学习设置
      final settings = await _loadLearningSettings();
      
      // 并行加载新词和复习词
      final results = await Future.wait([
        _wordRepository.getNewWords(count: settings.dailyNewWords),
        _wordRepository.getWordsForReview(count: settings.dailyReviewWords),
      ]);
      
      // 创建新的学习会话
      session.value = LearningSession(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        newWords: results[0] as List<Word>,
        reviewWords: results[1] as List<Word>,
        learnedNewWords: [],
        learnedReviewWords: [],
        startTime: DateTime.now(),
        settings: settings,
      );
      
      // 默认从新词学习开始
      currentStage.value = LearningStage.NEW_WORDS;
    } catch (e) {
      hasError.value = true;
      errorMessage.value = '初始化学习会话失败: $e';
    } finally {
      isLoading.value = false;
    }
  }
  
  Future<LearningSettings> _loadLearningSettings() async {
    // 从本地存储加载学习设置，或使用默认设置
    return LearningSettings(
      dailyNewWords: await _storageService.getDailyNewWords() ?? 10,
      dailyReviewWords: await _storageService.getDailyReviewWords() ?? 20,
      // 其他设置...
    );
  }
  
  // 记录新词学习结果
  void recordNewWordLearned(Word word, int score) {
    final updatedSession = session.value.copyWith(
      learnedNewWords: [...session.value.learnedNewWords, word],
    );
    session.value = updatedSession;
    
    // 保存学习记录到本地，异步执行
    _wordRepository.updateWordLearningStatus(word.id, score);
    
    // 检查是否所有新词都已学完
    if (session.value.learnedNewWords.length >= session.value.newWords.length) {
      // 新词学习完毕，进入复习阶段
      if (session.value.reviewWords.isNotEmpty) {
        currentStage.value = LearningStage.REVIEW;
      } else {
        // 没有待复习单词，直接进入结果页
        currentStage.value = LearningStage.RESULT;
      }
    }
  }
  
  // 记录复习词学习结果
  void recordReviewWordLearned(Word word, int score) {
    final updatedSession = session.value.copyWith(
      learnedReviewWords: [...session.value.learnedReviewWords, word],
    );
    session.value = updatedSession;
    
    // 保存复习记录到本地，异步执行
    _wordRepository.updateWordReviewStatus(word.id, score);
    
    // 检查是否所有复习词都已完成
    if (session.value.learnedReviewWords.length >= session.value.reviewWords.length) {
      // 复习完毕，进入测验阶段
      currentStage.value = LearningStage.QUIZ;
    }
  }
  
  // 记录测验结果
  void recordQuizResult(Map<int, bool> answers) {
    final updatedSession = session.value.copyWith(
      quizResults: answers,
      endTime: DateTime.now(),
    );
    session.value = updatedSession;
    
    // 保存测验结果
    _wordRepository.saveQuizResults(session.value.id, answers);
    
    // 进入结果页
    currentStage.value = LearningStage.RESULT;
  }
  
  // 完成学习会话
  Future<void> completeSession() async {
    // 提交完整的学习统计数据
    await _wordRepository.submitLearningSession(session.value);
    
    // 重置状态，准备下一次学习
    session.value = LearningSession.empty();
    currentStage.value = LearningStage.COMPLETED;
  }
  
  // 导航到特定页面
  void navigateTo(int stage) {
    currentStage.value = stage;
    switch (stage) {
      case LearningStage.NEW_WORDS:
        Get.toNamed(LearningRoutes.RECALL);
        break;
      case LearningStage.REVIEW:
        Get.toNamed(LearningRoutes.RECALL);
        break;
      case LearningStage.QUIZ:
        Get.toNamed(LearningRoutes.QUIZ);
        break;
      case LearningStage.RESULT:
        Get.toNamed(LearningRoutes.RESULT);
        break;
      case LearningStage.COMPLETED:
        Get.toNamed(LearningRoutes.COMPLETE);
        break;
    }
  }
}

// 学习阶段常量
abstract class LearningStage {
  static const NEW_WORDS = 0;
  static const REVIEW = 1;
  static const QUIZ = 2;
  static const RESULT = 3;
  static const COMPLETED = 4;
}
```

### RecallController实现

```dart
class RecallController extends GetxController {
  final LearningController _learningController = Get.find<LearningController>();
  final AudioService _audioService = Get.find<AudioService>();
  
  // 局部状态
  final RxInt currentIndex = 0.obs;
  final RxBool isShowingMeaning = false.obs;
  final RxBool isLoading = false.obs;
  
  // 计算属性
  List<Word> get wordsList {
    // 根据当前学习阶段返回不同的单词列表
    final stage = _learningController.currentStage.value;
    if (stage == LearningStage.NEW_WORDS) {
      return _learningController.session.value.newWords;
    } else {
      return _learningController.session.value.reviewWords;
    }
  }
  
  Word? get currentWord {
    if (wordsList.isEmpty || currentIndex.value >= wordsList.length) {
      return null;
    }
    return wordsList[currentIndex.value];
  }
  
  bool get isNewWordsStage => 
      _learningController.currentStage.value == LearningStage.NEW_WORDS;
  
  // 方法
  @override
  void onInit() {
    super.onInit();
    // 自动播放第一个单词的读音
    if (currentWord != null) {
      playWordPronunciation();
    }
  }
  
  void toggleMeaning() {
    isShowingMeaning.value = !isShowingMeaning.value;
  }
  
  void playWordPronunciation() {
    if (currentWord != null) {
      _audioService.playAudio(currentWord!.audioUrl);
    }
  }
  
  void nextWord() {
    if (currentIndex.value < wordsList.length - 1) {
      // 还有下一个单词
      currentIndex.value++;
      isShowingMeaning.value = false;
      playWordPronunciation();
    } else {
      // 所有单词都已显示
      _finishRecallStage();
    }
  }
  
  void previousWord() {
    if (currentIndex.value > 0) {
      currentIndex.value--;
      isShowingMeaning.value = false;
      playWordPronunciation();
    }
  }
  
  void recordMemoryScore(int score) {
    if (currentWord == null) return;
    
    // 记录学习结果
    if (isNewWordsStage) {
      _learningController.recordNewWordLearned(currentWord!, score);
    } else {
      _learningController.recordReviewWordLearned(currentWord!, score);
    }
    
    // 进入下一个单词
    nextWord();
  }
  
  void viewWordDetails() {
    if (currentWord == null) return;
    Get.toNamed(LearningRoutes.DETAIL, arguments: currentWord);
  }
  
  void _finishRecallStage() {
    // 当前阶段完成，让主控制器决定下一步
    final stage = _learningController.currentStage.value;
    if (stage == LearningStage.NEW_WORDS) {
      if (_learningController.session.value.reviewWords.isNotEmpty) {
        // 有复习词，进入复习阶段
        _learningController.navigateTo(LearningStage.REVIEW);
      } else {
        // 没有复习词，直接进入测验
        _learningController.navigateTo(LearningStage.QUIZ);
      }
    } else {
      // 复习阶段完成，进入测验
      _learningController.navigateTo(LearningStage.QUIZ);
    }
  }
}
```

## 相关联文件

- `lib/app/services/audio_service.dart`：播放单词发音
- `lib/data/repositories/word_repository.dart`：获取单词数据，提交学习进度
- `lib/data/models/word_model.dart`：单词数据模型
- `lib/app/routes/app_routes.dart`：全局路由常量
- `lib/app/routes/app_pages.dart`：全局路由配置

## 常见问题与解决方案

- **学习算法优化**：
  - 实现更科学的间隔重复算法
  - 根据用户的记忆情况动态调整复习频率
  - 使用机器学习预测单词难度和遗忘曲线

- **性能优化**：
  - 预加载单词音频和图片
  - 分批加载单词数据，避免一次加载过多
  - 优化单词卡片切换动画，减少卡顿

- **多设备同步**：
  - 实现学习进度的云端同步
  - 处理多设备学习数据冲突
  - 支持离线学习，恢复网络后自动同步 