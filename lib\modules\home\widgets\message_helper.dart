import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 首页消息提示帮助类
class HomeMessageHelper {
  /// 显示错误消息给用户
  static void showErrorMessage(String message) {
    Get.snackbar(
      '错误',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red.withValues(alpha: 0.1),
      colorText: Colors.red,
      duration: const Duration(seconds: 3),
    );
  }

  /// 显示提示消息给用户
  static void showInfoMessage(String message) {
    Get.snackbar(
      '提示',
      message,
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }
}
