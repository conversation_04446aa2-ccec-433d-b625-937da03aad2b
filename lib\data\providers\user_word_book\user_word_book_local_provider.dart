import 'package:flutter_study_flow_by_myself/app/exceptions/app_exception.dart';
import 'package:flutter_study_flow_by_myself/app/services/isar_service.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/data/models/user_word_book_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_book_model.dart';
import 'package:isar/isar.dart';

/// UserWordBookLocalProvider 负责与本地数据库交互，管理 UserWordBookModel 的 CRUD 操作
///
/// 主要功能：
/// - 获取所有用户单词本
/// - 获取当前激活的单词本
/// - 更新单词本状态（激活/停用）
/// - 更新学习进度和设置
/// - 管理用户单词本的生命周期
class UserWordBookLocalProvider {
  final IsarService _isarService;
  final LogService _log;

  UserWordBookLocalProvider({
    required IsarService isarService,
    required LogService log,
  }) : _isarService = isarService,
       _log = log;

  /// 获取所有用户单词本
  Future<List<UserWordBookModel>> getAll() async {
    try {
      final userWordBooks = await _isarService.getAll<UserWordBookModel>();
      return userWordBooks;
    } catch (e) {
      _log.e('获取所有用户单词本失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：获取所有用户单词本失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 根据ID获取单个用户单词本
  /// [id] 用户单词本的 Isar ID
  /// 返回对应的 UserWordBookModel 对象，如果不存在则返回 null
  Future<UserWordBookModel?> getById(int id) async {
    try {
      return await _isarService.get<UserWordBookModel>(id);
    } catch (e) {
      _log.e('获取用户单词本失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：获取用户单词本失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 根据本地单词本ID获取用户单词本
  /// [wordBookId] 本地单词本的数据库ID
  /// 返回对应的 UserWordBookModel 对象，如果不存在则返回 null
  Future<UserWordBookModel?> getByWordBookId(int wordBookId) async {
    try {
      return await _isarService.isar.userWordBookModels
          .filter()
          .wordBookIdEqualTo(wordBookId)
          .findFirst();
    } catch (e) {
      _log.e('根据本地单词本ID获取用户单词本失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：根据本地单词本ID获取用户单词本失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 根据远程单词本ID获取用户单词本
  /// [remoteWordBookId] 远程单词本的ID
  /// 返回对应的 UserWordBookModel 对象，如果不存在则返回 null
  Future<UserWordBookModel?> getByRemoteWordBookId(int remoteWordBookId) async {
    try {
      // 使用Isar自动生成的查询方法，比内存过滤更高效
      return await _isarService.isar.userWordBookModels
          .filter()
          .remoteWordBookIdEqualTo(remoteWordBookId)
          .findFirst();
    } catch (e) {
      _log.e('根据远程单词本ID获取用户单词本失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：根据远程单词本ID获取用户单词本失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 获取当前激活状态的用户单词本
  /// 返回状态为 ACTIVE 的用户单词本，如果没有则返回 null
  Future<UserWordBookModel?> getActiveUserWordBook() async {
    try {
      return await _isarService.isar.userWordBookModels
          .filter()
          .isActiveEqualTo(true)
          .findFirst();
    } catch (e) {
      _log.e('获取激活单词本失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：获取激活单词本失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 将所有用户单词本设置为非激活状态
  /// 通常在激活新单词本之前调用，确保全局只有一个激活的单词本
  ///
  /// 注意：此方法假设调用者已经开启了一个写事务，不会自行开启事务
  /// 这样设计是为了避免嵌套事务问题，使其可以在外部事务中被调用
  Future<void> deactivateAllWordBooks() async {
    try {
      // 获取所有用户单词本
      var allBooks =
          await _isarService.isar.userWordBookModels.where().findAll();

      // 批量停用所有单词本
      for (var book in allBooks) {
        book.deactivate();
      }

      // 批量保存（一次性提交，提升性能）
      await _isarService.isar.userWordBookModels.putAll(allBooks);

      _log.i('成功将所有用户单词本设置为非激活状态');
    } catch (e) {
      _log.e('批量停用用户单词本失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：批量停用用户单词本失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 保存用户单词本
  /// [userWordBook] 要保存的 UserWordBookModel 对象
  /// 返回保存成功后的 Isar ID
  Future<int> save(UserWordBookModel userWordBook) async {
    try {
      return await _isarService.isar.writeTxn(() async {
        return await _isarService.isar.userWordBookModels.put(userWordBook);
      });
    } catch (e) {
      _log.e('保存用户单词本失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：保存用户单词本失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 批量保存用户单词本
  /// [userWordBooks] 要保存的 UserWordBookModel 对象列表
  /// 返回保存成功后的 Isar ID 列表
  Future<List<int>> saveAll(List<UserWordBookModel> userWordBooks) async {
    try {
      return await _isarService.putAll<UserWordBookModel>(userWordBooks);
    } catch (e) {
      _log.e('批量保存用户单词本失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：批量保存用户单词本失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 根据ID删除用户单词本
  /// [id] 要删除的用户单词本 Isar ID
  /// 返回 true 表示删除成功，false 表示删除失败或不存在
  Future<bool> deleteById(int id) async {
    try {
      return await _isarService.delete<UserWordBookModel>(id);
    } catch (e) {
      _log.e('删除用户单词本失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：删除用户单词本失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 批量删除用户单词本
  /// [ids] 要删除的用户单词本 Isar ID 列表
  /// 返回成功删除的数量
  Future<int> deleteByIds(List<int> ids) async {
    try {
      return await _isarService.deleteByIds<UserWordBookModel>(ids);
    } catch (e) {
      _log.e('批量删除用户单词本失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：批量删除用户单词本失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 清空所有用户单词本数据
  Future<void> clearAll() async {
    try {
      await _isarService.clearCollection<UserWordBookModel>();
    } catch (e) {
      _log.e('清空所有用户单词本失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：清空所有用户单词本失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 查询用户单词本数量
  Future<int> count() async {
    try {
      return await _isarService.count<UserWordBookModel>();
    } catch (e) {
      _log.e('查询用户单词本数量失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：查询用户单词本数量失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 更新用户单词本信息
  /// [userWordBook] 要更新的用户单词本
  Future<int> update(UserWordBookModel userWordBook) async {
    try {
      return await save(userWordBook);
    } catch (e) {
      _log.e('更新用户单词本信息失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：更新用户单词本信息失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 获取已完成的用户单词本列表
  Future<List<UserWordBookModel>> getCompletedWordBooks() async {
    try {
      return await _isarService.isar.userWordBookModels
          .filter()
          .isCompletedEqualTo(true)
          .findAll();
    } catch (e) {
      _log.e('获取已完成用户单词本失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：获取已完成用户单词本失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 获取所有用户单词本列表（UserWordBookModel 的存在就表示已下载）
  Future<List<UserWordBookModel>> getDownloadedUserWordBooks() async {
    try {
      return await _isarService.isar.userWordBookModels.where().findAll();
    } catch (e) {
      _log.e('获取用户单词本列表失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：获取用户单词本列表失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 建立用户单词本与单词本的关联关系
  /// [userWordBook] 用户单词本
  /// [wordBook] 单词本
  Future<void> linkToWordBook(
    UserWordBookModel userWordBook,
    WordBookModel wordBook,
  ) async {
    try {
      await _isarService.isar.writeTxn(() async {
        // 建立关联关系
        userWordBook.wordBook.value = wordBook;

        // 保存用户单词本
        await _isarService.isar.userWordBookModels.put(userWordBook);

        // 保存关联关系
        await userWordBook.wordBook.save();
      });
    } catch (e) {
      _log.e('建立用户单词本与单词本关联失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：建立用户单词本与单词本关联失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 加载用户单词本的关联数据
  /// [userWordBook] 要加载关联数据的用户单词本
  /// [loadWordBook] 是否加载关联的单词本信息
  /// [loadReviewItems] 是否加载关联的复习项
  /// [loadLearningSessions] 是否加载关联的学习会话
  Future<void> loadRelations(
    UserWordBookModel userWordBook, {
    bool loadWordBook = false,
    bool loadReviewItems = false,
    bool loadLearningSessions = false,
  }) async {
    try {
      if (loadWordBook) {
        await userWordBook.wordBook.load();
      }
      if (loadReviewItems) {
        await userWordBook.reviewItems.load();
      }
      if (loadLearningSessions) {
        await userWordBook.learningSessions.load();
      }
    } catch (e) {
      _log.e('加载用户单词本关联数据失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：加载用户单词本关联数据失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 检查用户是否已经添加了指定的单词本
  /// [wordBookId] 单词本ID
  /// 返回 true 表示已添加，false 表示未添加
  Future<bool> hasWordBook(int wordBookId) async {
    try {
      final userWordBook = await getByWordBookId(wordBookId);
      return userWordBook != null;
    } catch (e) {
      _log.e('检查用户单词本是否存在失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：检查用户单词本是否存在失败',
        type: AppExceptionType.database,
      );
    }
  }
}
