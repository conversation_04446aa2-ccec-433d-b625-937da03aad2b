import 'package:flutter/material.dart';
import 'package:flutter_study_flow_by_myself/data/models/quiz/spelling_quiz_model.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/quiz/controllers/review_quiz_controller.dart';

/// 复习专用拼写测验组件
///
/// 基于SpellingQuizWidget，适配ReviewQuizController
class ReviewSpellingQuizWidget extends StatefulWidget {
  /// 拼写测验模型
  final SpellingQuizModel quiz;

  /// 复习Quiz控制器
  final ReviewQuizController controller;

  const ReviewSpellingQuizWidget({
    super.key,
    required this.quiz,
    required this.controller,
  });

  @override
  State<ReviewSpellingQuizWidget> createState() =>
      _ReviewSpellingQuizWidgetState();
}

class _ReviewSpellingQuizWidgetState extends State<ReviewSpellingQuizWidget> {
  /// 用户拼写的字母列表
  final List<Map<String, dynamic>> userSpelling = [];

  /// 可用字母列表（打乱的）
  late List<Map<String, dynamic>> availableLetters;

  /// 是否已提交答案
  bool hasSubmitted = false;

  @override
  void initState() {
    super.initState();
    _initializeLetters();
  }

  /// 关键：当quiz变化时重新初始化
  @override
  void didUpdateWidget(ReviewSpellingQuizWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.quiz.correctAnswer != widget.quiz.correctAnswer) {
      _initializeLetters();
      setState(() {
        userSpelling.clear();
        hasSubmitted = false;
      });
    }
  }

  /// 初始化字母列表
  void _initializeLetters() {
    final correctAnswer = widget.quiz.correctAnswer.toLowerCase();
    final letters = correctAnswer.split('');

    // 简化字母映射，只需要字母和唯一标识
    availableLetters =
        letters.asMap().entries.map((entry) {
          return {
            'letter': entry.value,
            'id': '${entry.key}_${entry.value}', // 唯一标识
          };
        }).toList();

    // 打乱字母顺序
    availableLetters.shuffle();
  }

  /// 添加字母到拼写区域
  void _addLetter(Map<String, dynamic> letterData) {
    if (hasSubmitted) return;

    setState(() {
      userSpelling.add(letterData);
      availableLetters.remove(letterData);
    });

    // 检查是否拼写完成
    if (availableLetters.isEmpty) {
      _submitAnswer();
    }
  }

  /// 从拼写区域移除字母
  void _removeLetter(Map<String, dynamic> letterData) {
    if (hasSubmitted) return;

    setState(() {
      userSpelling.remove(letterData);
      availableLetters.add(letterData);
    });
  }

  /// 提交答案
  void _submitAnswer() {
    if (hasSubmitted) return;

    final userInput = userSpelling.map((e) => e['letter'] as String).join('');
    final isCorrect =
        userInput.toLowerCase() == widget.quiz.correctAnswer.toLowerCase();

    setState(() {
      hasSubmitted = true;
    });

    // 调用控制器的答题方法
    widget.controller.onSpellingQuizAnswered(userInput, isCorrect);
  }

  /// 重置拼写
  void _resetSpelling() {
    if (hasSubmitted) return;

    setState(() {
      availableLetters.addAll(userSpelling);
      userSpelling.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 题目提示
          const Text(
            '请根据中文含义拼写出正确的英文单词：',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 16),

          // 中文含义显示
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
            ),
            child: Text(
              widget.quiz.hint,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 24),

          // 拼写区域
          _buildSpellingArea(),
          const SizedBox(height: 24),

          // 可用字母区域
          _buildAvailableLettersArea(),
          const SizedBox(height: 16),

          // 操作按钮
          _buildActionButtons(),
        ],
      ),
    );
  }

  /// 构建拼写区域
  Widget _buildSpellingArea() {
    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(minHeight: 60),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
      ),
      child:
          userSpelling.isEmpty
              ? const Text(
                '点击下方字母进行拼写',
                style: TextStyle(fontSize: 16, color: Colors.grey),
                textAlign: TextAlign.center,
              )
              : Wrap(
                spacing: 8,
                runSpacing: 8,
                children:
                    userSpelling.asMap().entries.map((entry) {
                      final index = entry.key;
                      final letterData = entry.value;
                      return _buildSpellingLetter(letterData, index);
                    }).toList(),
              ),
    );
  }

  /// 构建拼写区域的字母
  /// [letterData] 字母数据
  /// [currentIndex] 当前字母在拼写中的位置索引
  Widget _buildSpellingLetter(
    Map<String, dynamic> letterData,
    int currentIndex,
  ) {
    final letter = letterData['letter'] as String;
    final correctLetters = widget.quiz.correctAnswer.toLowerCase().split('');

    // 采用新学流程的逻辑：直接比较当前位置的字母是否正确
    final isCorrect =
        hasSubmitted &&
        currentIndex < correctLetters.length &&
        letter.toLowerCase() == correctLetters[currentIndex];

    return GestureDetector(
      onTap: () => _removeLetter(letterData),
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color:
              hasSubmitted
                  ? (isCorrect
                      ? Colors.green.withValues(alpha: 0.2)
                      : Colors.red.withValues(alpha: 0.2))
                  : Colors.blue.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color:
                hasSubmitted
                    ? (isCorrect ? Colors.green : Colors.red)
                    : Colors.blue,
            width: 2,
          ),
        ),
        child: Center(
          child: Text(
            letterData['letter'].toString().toUpperCase(),
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color:
                  hasSubmitted
                      ? (isCorrect ? Colors.green[700] : Colors.red[700])
                      : Colors.blue[700],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建可用字母区域
  Widget _buildAvailableLettersArea() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '可用字母：',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children:
              availableLetters.map((letterData) {
                return _buildAvailableLetter(letterData);
              }).toList(),
        ),
      ],
    );
  }

  /// 构建可用字母
  Widget _buildAvailableLetter(Map<String, dynamic> letterData) {
    return GestureDetector(
      onTap: () => _addLetter(letterData),
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: Colors.grey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
        ),
        child: Center(
          child: Text(
            letterData['letter'].toString().toUpperCase(),
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Row(
      children: [
        // 重置按钮
        Expanded(
          child: OutlinedButton(
            onPressed: hasSubmitted ? null : _resetSpelling,
            child: const Text('重置'),
          ),
        ),
        const SizedBox(width: 16),

        // 提交按钮
        Expanded(
          child: ElevatedButton(
            onPressed:
                (userSpelling.isNotEmpty && !hasSubmitted)
                    ? _submitAnswer
                    : null,
            child: const Text('提交'),
          ),
        ),
      ],
    );
  }
}
