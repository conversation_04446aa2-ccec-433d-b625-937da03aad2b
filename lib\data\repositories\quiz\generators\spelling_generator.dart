import 'package:flutter_study_flow_by_myself/data/models/quiz/quiz_type.dart';
import 'package:flutter_study_flow_by_myself/data/models/quiz/spelling_quiz_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_model.dart';

/// 拼写题生成器
class SpellingGenerator {
  // 私有构造函数，防止实例化
  SpellingGenerator._();

  /// 静态生成方法
  ///
  /// [word] 目标单词
  ///
  /// 返回生成的拼写测验题
  static SpellingQuizModel generate(WordModel word) {
    // 创建测验模型
    return SpellingQuizModel(
      wordId: word.id,
      hint: word.definition,
      correctAnswer: word.spelling,
      type: QuizType.spelling,
    );
  }
}
