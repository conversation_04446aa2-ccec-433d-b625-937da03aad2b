import 'package:flutter_study_flow_by_myself/data/models/quiz/quiz_type.dart';
import 'package:isar/isar.dart';

part 'spelling_quiz_model.g.dart';

/// 拼写测验模型
///
/// 用于存储拼写类型测验题的数据
@Collection()
class SpellingQuizModel {
  /// 主键ID
  Id id = Isar.autoIncrement;

  /// 正确答案（需要拼写的单词）
  late String correctAnswer;

  /// 提示信息（通常是单词的中文释义）
  late String hint;

  /// 测验类型
  @enumerated
  late QuizType type;

  /// 解析内容（可选）
  String? explanation;

  /// 与该单词相关的单词ID
  late int wordId;

  SpellingQuizModel({
    required this.correctAnswer,
    required this.hint,
    required this.type,
    this.explanation,
    required this.wordId,
  });

  /// 从JSON创建对象
  factory SpellingQuizModel.fromJson(Map<String, dynamic> json) =>
      SpellingQuizModel(
        correctAnswer: json['correctAnswer'],
        hint: json['hint'],
        type: QuizType.values[json['type'] ?? 0],
        explanation: json['explanation'],
        wordId: json['wordId'],
      )..id = json['id'] ?? Isar.autoIncrement;

  /// 将对象转换为JSON
  Map<String, dynamic> toJson() => {
    'id': id,
    'correctAnswer': correctAnswer,
    'hint': hint,
    'type': type.index,
    'explanation': explanation,
    'wordId': wordId,
  };
}
