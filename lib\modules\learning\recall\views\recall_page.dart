import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/recall/controllers/recall_controller.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/services/learning_state_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/widgets/learning_progress_bar.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/widgets/learning_exit_dialog.dart';

/// 回忆页面
///
/// 显示单词拼写，隐藏释义，让用户判断是否认识该单词
class RecallPage extends GetView<RecallController> {
  const RecallPage({super.key});

  @override
  Widget build(BuildContext context) {
    // 获取状态服务
    final stateService = Get.find<LearningStateService>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('单词学习'),
        elevation: 0,
        // 🔑 自定义返回按钮
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => LearningExitDialog.show(context: context),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            // 顶部进度条
            Obx(
              () => LearningProgressBar(
                learnedWords: stateService.learnedWordCount,
                totalWords: stateService.wordIds.length,
                completedQuizzes: stateService.totalCompletedQuizzes,
                totalQuizzes: stateService.wordIds.length * 3, // 每个单词需要完成3次
                isInFinalQuiz: stateService.isInFinalQuiz,
              ),
            ),

            // 主要内容区域
            Expanded(
              child: Obx(() {
                final word = controller.currentWord.value;

                if (word == null) {
                  return const Center(child: CircularProgressIndicator());
                }

                return Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // 单词卡片
                      Card(
                        elevation: 4,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(24.0),
                          child: Column(
                            children: [
                              // 单词拼写
                              Text(
                                word.spelling,
                                style: const TextStyle(
                                  fontSize: 32,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),

                              // 音频播放按钮
                              ElevatedButton.icon(
                                onPressed: controller.playWordAudio,
                                icon: const Icon(Icons.volume_up),
                                label: const Text('播放发音'),
                                style: ElevatedButton.styleFrom(
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(30),
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 24,
                                    vertical: 12,
                                  ),
                                ),
                              ),

                              // 释义（默认隐藏）
                              AnimatedOpacity(
                                opacity:
                                    controller.isDefinitionVisible.value
                                        ? 1.0
                                        : 0.0,
                                duration: const Duration(milliseconds: 300),
                                child: Padding(
                                  padding: const EdgeInsets.only(top: 24.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        '释义：${word.definition}',
                                        style: const TextStyle(fontSize: 18),
                                      ),
                                      if (word.phonetic.isNotEmpty)
                                        Padding(
                                          padding: const EdgeInsets.only(
                                            top: 8.0,
                                          ),
                                          child: Text(
                                            '音标：${word.phonetic}',
                                            style: const TextStyle(
                                              fontSize: 16,
                                              color: Colors.grey,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 32),

                      // 查看释义按钮
                      TextButton(
                        onPressed: controller.toggleDefinition,
                        child: Text(
                          controller.isDefinitionVisible.value
                              ? '隐藏释义'
                              : '查看释义',
                          style: const TextStyle(fontSize: 16),
                        ),
                      ),

                      const SizedBox(height: 32),

                      // 认识和不认识按钮
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          // 不认识按钮
                          ElevatedButton(
                            onPressed: controller.onUnknownPressed,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.redAccent,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 32,
                                vertical: 16,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(30),
                              ),
                            ),
                            child: const Text(
                              '不认识',
                              style: TextStyle(fontSize: 18),
                            ),
                          ),

                          // 认识按钮
                          ElevatedButton(
                            onPressed: controller.onKnownPressed,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 32,
                                vertical: 16,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(30),
                              ),
                            ),
                            child: const Text(
                              '认识',
                              style: TextStyle(fontSize: 18),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              }),
            ),
          ],
        ),
      ),
    );
  }
}
