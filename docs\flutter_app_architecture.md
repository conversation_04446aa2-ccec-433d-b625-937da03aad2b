# Flutter应用架构详解

本文档详细说明项目采用的分层架构，包括各层的职责、特点、常见功能和示例代码。本文档专为新手Flutter开发者设计，力求通俗易懂。

## 架构总览

项目采用清晰的分层架构，自下而上分为：

```
┌─────────────────────────────────┐
│            View 层              │  UI界面，使用GetX构建
└─────────────────┬───────────────┘
                  │
┌─────────────────▼───────────────┐
│         Controller 层           │  UI业务逻辑和状态管理
└─────────────────┬───────────────┘
                  │
┌─────────────────▼───────────────┐
│         Repository 层           │  数据业务协调层
└─────────────────┬───────────────┘
                  │
┌────────┬────────▼────────┬──────┐
│  Local │    Remote       │ 其他 │  数据访问层
│Provider│   Provider      │Provider
└────────┴─────────────────┴──────┘
                  │
┌─────────────────▼───────────────┐
│           Service 层            │  基础服务层
└─────────────────────────────────┘
```

## 各层的简单类比

想象一家餐厅的运作方式：

- **View层** - 就是餐厅的前台和用餐区域，顾客(用户)在这里点餐和用餐
- **Controller层** - 相当于服务员，接收顾客点单，将菜单传到厨房，上菜给顾客
- **Repository层** - 相当于厨师长，决定如何准备菜品，协调厨房工作
- **Provider层** - 相当于厨房里的各个厨师，专门负责特定类型的菜品准备
- **Service层** - 相当于厨房设备和基础设施，为厨师提供烹饪工具和原料

## 1. Service 层

### 职责
- 提供基础设施服务，与具体业务逻辑无关
- 封装第三方库，为上层提供统一的接口
- 提供全局可访问的服务功能

### 特点
- 通常是**单例**模式（整个应用只有一个实例）
- **无状态**或状态很少（不保存业务数据）
- 较少依赖其他组件（独立运行）
- 可被多个模块复用（通用工具）

### 常见服务
1. **IsarService**: 数据库访问服务（处理数据库连接和基本操作）
2. **NetworkService**: 网络请求服务（封装HTTP请求）
3. **LogService**: 日志记录服务（统一的日志记录）
4. **StorageService**: 文件存储服务（处理文件读写）
5. **AudioService**: 音频播放服务（处理音频播放）
6. **SpacedRepetitionService**: 间隔重复算法服务（实现记忆算法）

### 示例：间隔重复算法服务

```dart
class SpacedRepetitionService {
  // 计算下一次复习时间
  DateTime calculateNextReviewDate({
    required int repetitionCount,
    required double easeFactor,
    required int previousInterval,
    required bool wasCorrect,
  }) {
    if (!wasCorrect) {
      return DateTime.now().add(Duration(minutes: 10)); // 错误答案很快重复
    }

    // SM-2 算法实现
    int nextInterval;
    if (repetitionCount == 0) {
      nextInterval = 1; // 第一次复习，间隔1天
    } else if (repetitionCount == 1) {
      nextInterval = 6; // 第二次复习，间隔6天
    } else {
      nextInterval = (previousInterval * easeFactor).round(); // 后续复习
    }

    // 计算下次复习日期
    return DateTime.now().add(Duration(days: nextInterval));
  }

  // 计算新的难度因子
  double calculateNewEaseFactor({
    required double currentEaseFactor,
    required int quality, // 0-5的评分
  }) {
    // SM-2算法调整难度因子
    double newEaseFactor = currentEaseFactor + (0.1 - (5 - quality) * (0.08 + (5 - quality) * 0.02));
    
    // 难度因子不低于1.3
    if (newEaseFactor < 1.3) {
      newEaseFactor = 1.3;
    }
    
    return newEaseFactor;
  }
}
```

## 2. Provider 层

### 职责
- 直接与数据源交互（本地数据库、网络API等）
- 执行原子化的CRUD操作（创建、读取、更新、删除）
- 处理数据访问异常，转换为应用异常

### 特点
- 不包含业务逻辑，只关注数据操作
- 分为Local Provider(本地数据)和Remote Provider(远程API)
- 方法通常是简单的数据操作
- 每个Provider专注于单一模型的数据访问

### 通俗理解
Provider就像仓库管理员或图书馆管理员：
- 负责存取特定类型的物品/数据
- 知道物品/数据在哪里以及如何获取
- 不关心为什么需要这些物品/数据或用来做什么

### 常见功能
- **查询操作**：`getById`、`getAll`、`query`、`find`等
- **写入操作**：`insert`、`update`、`save`、`delete`等
- **批量操作**：`batchInsert`、`batchUpdate`等
- **关联查询**：`getWithRelations`等

### 示例：本地数据Provider

```dart
class UserWordBookLocalProvider {
  final IsarService _isarService;
  final LogService _log;
  
  UserWordBookLocalProvider(this._isarService, this._log);

  // 根据ID获取单个实体
  Future<UserWordBookModel?> getById(int id) async {
    try {
      return await _isarService.isar.userWordBookModels.get(id);
    } catch (e) {
      _log.e('获取用户单词本失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：获取用户单词本失败',
        type: AppExceptionType.database,
      );
    }
  }
  
  // 获取激活状态的单词本
  Future<UserWordBookModel?> getActiveUserWordBook() async {
    try {
      return await _isarService.isar.userWordBookModels
          .filter()
          .statusEqualTo(WordBookStatus.ACTIVE)
          .findFirst();
    } catch (e) {
      _log.e('获取激活单词本失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：获取激活单词本失败',
        type: AppExceptionType.database,
      );
    }
  }
  
  // 将所有单词本设置为非激活状态
  Future<void> deactivateAllWordBooks() async {
    try {
      await _isarService.isar.writeTxn(() async {
        var allBooks = await _isarService.isar.userWordBookModels.where().findAll();
        for (var book in allBooks) {
          book.status = WordBookStatus.DOWNLOADED;
          await _isarService.isar.userWordBookModels.put(book);
        }
      });
    } catch (e) {
      _log.e('设置单词本状态失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：设置单词本状态失败',
        type: AppExceptionType.database,
      );
    }
  }
  
  // 保存用户单词本
  Future<int> save(UserWordBookModel userWordBook) async {
    try {
      return await _isarService.isar.writeTxn(() async {
        return await _isarService.isar.userWordBookModels.put(userWordBook);
      });
    } catch (e) {
      _log.e('保存用户单词本失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：保存用户单词本失败',
        type: AppExceptionType.database,
      );
    }
  }
}
```

### 示例：远程数据Provider

```dart
class WordBookRemoteProvider {
  final NetworkService _networkService;
  final LogService _log;
  
  WordBookRemoteProvider(this._networkService, this._log);
  
  // 从远程API获取完整的单词本数据
  Future<Map<String, dynamic>> fetchCompleteWordBook(int wordBookId) async {
    try {
      final response = await _networkService.get(
        '/wordbooks/$wordBookId/complete',
        queryParameters: {'includeWords': true},
      );
      
      return response.data;
    } catch (e) {
      _log.e('获取远程单词本失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '远程API：获取单词本数据失败',
        type: AppExceptionType.network,
      );
    }
  }
  
  // 获取可用单词本列表
  Future<List<Map<String, dynamic>>> fetchAvailableWordBooks() async {
    try {
      final response = await _networkService.get('/wordbooks');
      return List<Map<String, dynamic>>.from(response.data['items']);
    } catch (e) {
      _log.e('获取可用单词本列表失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '远程API：获取可用单词本列表失败',
        type: AppExceptionType.network,
      );
    }
  }
}
```

## 3. Repository 层

### 职责
- 协调本地和远程Provider
- 实现与数据相关的业务规则
- 为Controller层提供统一的数据接口
- 管理缓存策略和数据同步

### 特点
- **中间人**角色，屏蔽数据源细节
- 整合多个Provider的功能
- 实现比Provider更高级的数据操作
- 处理缓存逻辑和数据一致性

### 通俗理解
可以将Repository想象为**私人管家**：
- Controller层(你)只需要提出要求："我需要今天学习的单词或者帮我激活单词本"
- Repository层(管家)负责具体事务："从本地获取还是从网络下载？如何组织？需要应用什么规则？或者怎么激活单词本？从哪获取单词本？是不是同时只能有一个单词本激活？"
- Controller只需要提出要求、给任务即可，Repository就是具体实现这些逻辑，让Controller不用管细节

### 常见功能
- **数据聚合**：从多个Provider获取数据并组合
- **缓存策略**：决定何时使用缓存、何时联网获取
- **数据转换**：API返回的格式与应用内使用的格式转换
- **业务规则**：实施与数据相关的业务规则
- **错误处理**：统一处理各种数据错误

### 示例：用户单词本Repository

```dart
class UserWordBookRepository {
  final UserWordBookLocalProvider _localProvider;
  final WordBookRepository _wordBookRepository;
  final LogService _log;
  
  UserWordBookRepository(
    this._localProvider,
    this._wordBookRepository,
    this._log,
  );
  
  // 获取所有用户单词本
  Future<List<UserWordBookModel>> getAllUserWordBooks() async {
    return await _localProvider.getAll();
  }
  
  // 获取激活的用户单词本
  Future<UserWordBookModel?> getActiveUserWordBook() async {
    return await _localProvider.getActiveUserWordBook();
  }
  
  // 激活单词本 - 包含业务规则
  Future<void> activateWordBook(int userWordBookId) async {
    try {
      // 业务规则1: 检查单词本是否存在
      final userWordBook = await _localProvider.getById(userWordBookId);
      if (userWordBook == null) {
        throw AppException('单词本不存在', type: AppExceptionType.business);
      }
      
      // 业务规则2: 只有已下载的单词本才能激活
      if (userWordBook.status == WordBookStatus.NOT_DOWNLOADED) {
        throw AppException('未下载的单词本不能激活', type: AppExceptionType.business);
      }
      
      // 业务规则3: 同一时刻只能有一个激活的单词本
      await _localProvider.deactivateAllWordBooks();
      
      // 更新状态并保存
      userWordBook.status = WordBookStatus.ACTIVE;
      await _localProvider.save(userWordBook);
      
      // 记录日志
      _log.i('激活单词本: ${userWordBook.id}');
    } catch (e) {
      _log.e('激活单词本失败: $e', error: e);
      rethrow; // 重新抛出异常供Controller处理
    }
  }
  
  // 下载并创建用户单词本
  Future<UserWordBookModel> downloadAndCreateUserWordBook(int remoteWordBookId) async {
    try {
      // 1. 下载单词本数据(通过WordBookRepository)
      final wordBook = await _wordBookRepository.downloadWordBook(remoteWordBookId);
      
      // 2. 检查是否已存在该单词本的用户关系
      final existingUserWordBook = await _localProvider.getByWordBookId(wordBook.id);
      if (existingUserWordBook != null) {
        return existingUserWordBook; // 已存在则直接返回
      }
      
      // 3. 创建用户单词本关系
      final userWordBook = UserWordBookModel(
        wordBookId: wordBook.id,
        status: WordBookStatus.DOWNLOADED, // 默认已下载但未激活
        totalWords: wordBook.wordCount,
        dailyNewWordsCount: 10, // 默认每日学习10个新词
        addedDate: DateTime.now(),
      );
      
      // 4. 保存并返回
      final id = await _localProvider.save(userWordBook);
      userWordBook.id = id;
      
      // 5. 建立关联关系
      userWordBook.wordBook.value = wordBook;
      
      return userWordBook;
    } catch (e) {
      _log.e('下载创建用户单词本失败: $e', error: e);
      throw AppException.fromException(
        e, 
        defaultMessage: '下载单词本失败',
        type: AppExceptionType.business,
      );
    }
  }
  
  // 更新用户单词本学习设置
  Future<void> updateLearningSettings(
    int userWordBookId, {
    int? dailyNewWordsCount,
  }) async {
    try {
      final userWordBook = await _localProvider.getById(userWordBookId);
      if (userWordBook == null) {
        throw AppException('单词本不存在', type: AppExceptionType.business);
      }
      
      // 更新学习设置
      if (dailyNewWordsCount != null) {
        // 业务规则：每日学习单词数必须在合理范围内
        if (dailyNewWordsCount < 1 || dailyNewWordsCount > 50) {
          throw AppException('每日学习单词数必须在1-50之间', type: AppExceptionType.business);
        }
        userWordBook.dailyNewWordsCount = dailyNewWordsCount;
      }
      
      // 保存更新
      await _localProvider.save(userWordBook);
    } catch (e) {
      _log.e('更新学习设置失败: $e', error: e);
      rethrow;
    }
  }
}
```

### 与数据相关的业务规则示例

```dart
class ReviewItemRepository {
  final ReviewItemLocalProvider _localProvider;
  final SpacedRepetitionService _spacedRepetitionService;
  
  // 处理学习结果，更新复习项状态
  Future<void> processLearningResult(
    int reviewItemId, 
    int quality // 0-5的评分
  ) async {
    final reviewItem = await _localProvider.getById(reviewItemId);
    if (reviewItem == null) {
      throw AppException('复习项不存在', type: AppExceptionType.business);
    }
    
    // 业务规则1: 根据质量评分判断是否答对
    final bool wasCorrect = quality >= 3;
    
    // 业务规则2: 如果是首次学习的新词
    if (reviewItem.isNewWord) {
      reviewItem.isNewWord = false; // 标记为非新词
      
      if (!wasCorrect) {
        // 业务规则3: 首次学习未通过，10分钟后复习
        reviewItem.nextReviewDate = DateTime.now().add(Duration(minutes: 10));
        await _localProvider.save(reviewItem);
        return;
      }
    }
    
    // 业务规则4: 应用间隔重复算法更新复习参数
    final newEaseFactor = _spacedRepetitionService.calculateNewEaseFactor(
      currentEaseFactor: reviewItem.easeFactor,
      quality: quality,
    );
    
    // 更新复习计数和难度因子
    reviewItem.repetitionCount = wasCorrect ? reviewItem.repetitionCount + 1 : 0;
    reviewItem.easeFactor = newEaseFactor;
    
    // 计算下次复习时间
    reviewItem.nextReviewDate = _spacedRepetitionService.calculateNextReviewDate(
      repetitionCount: reviewItem.repetitionCount,
      easeFactor: reviewItem.easeFactor,
      previousInterval: reviewItem.previousInterval,
      wasCorrect: wasCorrect,
    );
    
    // 更新上次间隔
    if (wasCorrect) {
      reviewItem.previousInterval = DateTime.now().difference(reviewItem.nextReviewDate).inDays;
    }
    
    // 保存更新
    await _localProvider.save(reviewItem);
  }
}
```

## 4. Controller 层

### 职责
- 处理UI事件和用户交互
- 管理页面状态(GetX响应式变量)
- 协调多个Repository完成UI相关的业务流程
- 处理页面间的导航

### 特点
- 关注UI业务逻辑而非数据获取细节
- 通常与特定页面/功能模块对应
- 持有响应式状态变量(Rx变量)
- 生命周期与页面生命周期关联

### 通俗理解
Controller就像一个餐厅服务员：
- 接收顾客(用户)的点单请求
- 将请求转达给厨房(Repository)
- 将准备好的食物(数据)呈现给顾客
- 管理顾客的用餐体验(UI状态)

### 常见功能
- **页面初始化**：`onInit`、`loadInitialData`等
- **用户交互**：`handleTap`、`submit`、`process`等
- **状态更新**：`updateState`、`refreshData`等
- **导航控制**：`navigateToDetail`、`goBack`等
- **错误处理**：`handleError`、展示错误UI

### 复杂流程处理
Controller负责**协调**复杂流程，但**不实现**其中的数据业务规则：

```dart
// Controller中的复杂流程示例
Future<void> startLearningSession() async {
  try {
    isLoading.value = true; // UI状态更新
    
    // 委托Repository创建学习会话(具体实现由Repository负责)
    final session = await _learningSessionRepository.createNewSession();
    
    // Controller负责UI流程和状态管理
    currentSession.value = session;
    Get.toNamed(Routes.LEARNING_DETAIL);
  } catch (e) {
    handleError(e); // 错误处理和UI反馈
  } finally {
    isLoading.value = false; // UI状态更新
  }
}
```

### 示例：词汇Controller

```dart
class VocabularyController extends GetxController {
  final UserWordBookRepository _userWordBookRepo;
  final WordBookRepository _wordBookRepo;
  final LogService _log;
  
  // 状态变量
  final RxList<UserWordBookModel> userWordBooks = <UserWordBookModel>[].obs;
  final Rx<UserWordBookModel?> activeWordBook = Rx<UserWordBookModel?>(null);
  final RxBool isLoading = false.obs;
  final RxBool isDownloading = false.obs;
  
  VocabularyController(this._userWordBookRepo, this._wordBookRepo, this._log);
  
  @override
  void onInit() {
    super.onInit();
    loadWordBooks();
  }
  
  // 加载单词本列表
  Future<void> loadWordBooks() async {
    try {
      isLoading.value = true;
      userWordBooks.value = await _userWordBookRepo.getAllUserWordBooks();
      activeWordBook.value = await _userWordBookRepo.getActiveUserWordBook();
    } catch (e) {
      _log.e('加载单词本失败: $e', error: e);
      Get.snackbar('错误', '无法加载单词本列表');
    } finally {
      isLoading.value = false;
    }
  }
  
  // 处理用户切换单词本
  Future<void> switchActiveWordBook(int userWordBookId) async {
    try {
      isLoading.value = true; // UI状态更新
      
      // 核心业务逻辑委托给Repository
      await _userWordBookRepo.activateWordBook(userWordBookId);
      
      // UI相关逻辑由Controller处理
      await loadWordBooks(); // 重新加载以更新状态
      Get.snackbar('成功', '已切换单词本');
    } catch (e) {
      _log.e('切换单词本失败: $e', error: e);
      Get.snackbar('错误', '切换单词本失败: ${e.toString()}');
    } finally {
      isLoading.value = false;
    }
  }
  
  // 下载新单词本
  Future<void> downloadWordBook(int remoteWordBookId) async {
    if (isDownloading.value) return; // 防止重复下载
    
    try {
      isDownloading.value = true;
      final userWordBook = await _userWordBookRepo.downloadAndCreateUserWordBook(remoteWordBookId);
      await loadWordBooks(); // 重新加载列表
      Get.snackbar('成功', '单词本已下载');
    } catch (e) {
      _log.e('下载单词本失败: $e', error: e);
      Get.snackbar('错误', '下载单词本失败: ${e.toString()}');
    } finally {
      isDownloading.value = false;
    }
  }
  
  // 更新单词本设置
  Future<void> updateWordBookSettings(int userWordBookId, int dailyWords) async {
    try {
      await _userWordBookRepo.updateLearningSettings(
        userWordBookId,
        dailyNewWordsCount: dailyWords,
      );
      await loadWordBooks(); // 刷新数据
      Get.snackbar('成功', '学习设置已更新');
    } catch (e) {
      _log.e('更新设置失败: $e', error: e);
      Get.snackbar('错误', '更新设置失败: ${e.toString()}');
    }
  }
}
```

### 示例：学习Controller

```dart
class LearningController extends GetxController {
  final UserWordBookRepository _userWordBookRepo;
  final WordRepository _wordRepo;
  final ReviewItemRepository _reviewItemRepo;
  
  // 状态变量
  final RxList<WordModel> wordsToLearn = <WordModel>[].obs;
  final RxInt currentWordIndex = 0.obs;
  final RxBool isLoading = false.obs;
  final RxBool isCompleted = false.obs;
  
  // 获取当前单词
  WordModel? get currentWord => 
      currentWordIndex.value < wordsToLearn.length ? 
      wordsToLearn[currentWordIndex.value] : null;
  
  @override
  void onInit() {
    super.onInit();
    loadWordsToLearn();
  }
  
  // 加载今日学习单词
  Future<void> loadWordsToLearn() async {
    try {
      isLoading.value = true;
      
      // 获取激活的单词本
      final activeBook = await _userWordBookRepo.getActiveUserWordBook();
      if (activeBook == null) {
        Get.offNamed(Routes.WORDBOOK_SELECTION);
        throw AppException('未选择单词本', type: AppExceptionType.business);
      }
      
      // 获取今日待学习的单词
      wordsToLearn.value = await _wordRepo.getTodayNewWordsForUserWordBook(
        activeBook.id,
        activeBook.dailyNewWordsCount,
      );
      
      // 没有需要学习的单词
      if (wordsToLearn.isEmpty) {
        isCompleted.value = true;
      }
      
    } catch (e) {
      Get.snackbar('错误', '无法加载学习内容: ${e.toString()}');
    } finally {
      isLoading.value = false;
    }
  }
  
  // 处理单词学习结果
  Future<void> handleLearningResult(bool remembered) async {
    if (currentWord == null) return;
    
    try {
      final activeBook = await _userWordBookRepo.getActiveUserWordBook();
      if (activeBook == null) return;
      
      // 创建或获取ReviewItem
      final reviewItem = await _reviewItemRepo.getOrCreateReviewItem(
        currentWord!,
        activeBook,
      );
      
      // 处理学习结果
      await _reviewItemRepo.processLearningResult(
        reviewItem.id,
        remembered ? 3 : 1, // 简化的质量评分
      );
      
      // 移动到下一个单词
      if (currentWordIndex.value < wordsToLearn.length - 1) {
        currentWordIndex.value++;
      } else {
        // 学习完成
        isCompleted.value = true;
        Get.offNamed(Routes.LEARNING_COMPLETE);
      }
      
    } catch (e) {
      Get.snackbar('错误', '处理学习结果失败: ${e.toString()}');
    }
  }
}
```

## Controller与Repository的业务逻辑区分

虽然两层都处理"业务逻辑"，但它们关注的是**不同类型**的业务逻辑：

### Repository层的业务逻辑
- **数据相关的规则**：与数据模型、关系和约束相关
- **领域规则**：与业务领域本身相关的固定规则
- 例如：
  - "同一时刻只能有一个激活的单词本"
  - "复习项的间隔按SM2算法计算" 
  - "未下载的单词本不能被激活"

### Controller层的业务逻辑
- **用户交互流程**：与UI和用户操作相关的流程
- **会话状态管理**：当前屏幕和功能的状态
- 例如：
  - "学习完所有单词后显示完成页面"
  - "下载完成后显示成功提示"
  - "根据学习进度更新进度条"

### 区分示例
| 功能 | Controller做什么 | Repository做什么 |
|------|----------------|-----------------|
| 激活单词本 | 显示加载状态、处理错误提示、更新UI | 检查是否已下载、停用其他单词本、更改状态 |
| 学习单词 | 显示单词卡片、更新进度条、导航到下一步 | 获取待学习单词、记录学习状态、计算复习时间 |
| 下载单词本 | 显示下载进度、成功/失败提示 | 获取远程数据、保存到本地、建立关联关系 |

## 各层之间的关系

### 数据流向
- **向下**：用户操作通过Controller层传递请求
- **向上**：数据通过各层处理后返回到UI

### 依赖规则
1. Controller依赖Repository
2. Repository依赖Provider和Service
3. Provider依赖Service
4. Service层是最底层，不应依赖上层组件

### 实际调用示例

用户激活单词本的完整流程：
```
用户点击"激活单词本"按钮
    ↓
VocabularyController.switchActiveWordBook(id)
    ↓ 
    ↓ [Controller设置isLoading = true，更新UI状态]
    ↓
UserWordBookRepository.activateWordBook(id)
    ↓
    ↓ [Repository应用业务规则：检查单词本是否存在、已下载等]
    ↓
UserWordBookLocalProvider.getById(id)
    ↓
    ↓ [Provider进行数据库查询]
    ↓
UserWordBookLocalProvider.deactivateAllWordBooks()
    ↓
    ↓ [Provider批量更新数据]
    ↓
UserWordBookLocalProvider.save(userWordBook)
    ↓
    ↓ [Provider保存数据]
    ↓
Repository返回结果给Controller
    ↓
    ↓ [Controller刷新数据、设置isLoading = false]
    ↓
Controller显示成功提示、更新UI
```

## 总结

这种分层架构带来的好处：

1. **关注点分离**：每层只负责特定任务，降低复杂度
2. **可测试性**：各层可单独测试，无需模拟整个应用
3. **可维护性**：修改一层逻辑不影响其他层
4. **可扩展性**：新功能只需在相应层添加，不破坏现有结构
5. **代码复用**：避免重复逻辑，特别是数据处理逻辑

遵循这种架构设计，可以使应用在不断迭代和功能扩展的过程中保持良好的结构和可维护性。

## 新手常见问题解答

**Q: 我应该把所有业务逻辑都放在Repository中吗？**  
A: 不是所有的。与数据相关的业务规则应放在Repository中，与UI流程相关的应放在Controller中。

**Q: Provider和Repository有什么区别？**  
A: Provider只负责原始数据操作，Repository则整合多个Provider并应用业务规则。

**Q: 为什么不直接在Controller中调用Provider？**  
A: 这会导致业务规则分散在各处，难以维护，且无法复用。Repository层能集中管理业务规则。

**Q: 一个模型需要创建多少个类？**  
A: 通常一个核心模型会对应：1个Model类、1-2个Provider类（本地/远程）、1个Repository类。

**Q: 什么时候应该创建新的Service？**  
A: 当某个功能是通用的、与特定业务无关、可被多个模块使用时，应考虑创建Service。 