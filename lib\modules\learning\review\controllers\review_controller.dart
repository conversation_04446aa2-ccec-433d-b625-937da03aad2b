import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/review_item_repository.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_model.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/services/review_state_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/services/review_quiz_generation_service.dart';

/// 复习主控制器
///
/// 负责复习流程的初始化和启动，参考LearningController的设计模式
///
/// 实际职责：
/// - 处理复习流程的初始化和路由参数
/// - 管理用户单词本ID状态
/// - 协调状态服务和题目生成服务的初始化
/// - 提供复习流程的基础数据访问
///
/// 注意：不负责UI状态管理、导航控制、质量评分等具体业务逻辑
class ReviewController extends GetxController {
  // 依赖注入（只依赖实际使用的服务）
  final ReviewStateService _stateService;
  final ReviewItemRepository _reviewItemRepository;
  final ReviewQuizGenerationService _quizGenerationService;
  final LogService _log;

  ReviewController({
    required ReviewStateService stateService,
    required ReviewItemRepository reviewItemRepository,
    required ReviewQuizGenerationService quizGenerationService,
    required LogService log,
  }) : _stateService = stateService,
       _reviewItemRepository = reviewItemRepository,
       _quizGenerationService = quizGenerationService,
       _log = log;

  // 响应式状态（只保留实际使用的状态）
  final currentUserWordBookId = 0.obs;

  @override
  void onInit() {
    super.onInit();
    _log.i('初始化复习主控制器');
    _handleRouteArguments();
  }

  /// 处理路由参数
  void _handleRouteArguments() {
    final args = Get.arguments as Map<String, dynamic>?;
    final userWordBookId = args?['userWordBookId'] as int?;

    if (userWordBookId != null) {
      _startReviewFlow(userWordBookId);
    } else {
      _log.w('未收到有效的用户单词本ID参数');
      Get.snackbar('参数错误', '缺少必要参数，请重试');
      Get.back();
    }
  }

  /// 启动复习流程
  Future<void> _startReviewFlow(int userWordBookId) async {
    try {
      _log.i('启动复习流程，用户单词本ID: $userWordBookId');

      // 0. 设置当前用户单词本ID（关键修复：确保后续更新ReviewItem时使用正确的ID）
      currentUserWordBookId.value = userWordBookId;

      // 1. 获取待复习单词
      final words = await _getReviewWords(userWordBookId);

      if (words.isEmpty) {
        _log.i('当前没有待复习的单词');
        Get.snackbar('提示', '当前没有待复习的单词');
        Get.back();
        return;
      }

      // 2. 初始化状态服务
      _stateService.initialize(words);

      // 3. 生成第一题
      await _quizGenerationService.generateFirstQuiz();

      _log.i(
        '复习流程启动完成，共${words.length}个单词，用户单词本ID: ${currentUserWordBookId.value}',
      );
    } catch (e) {
      _log.e('启动复习流程失败: $e');

      // 根据异常类型提供更具体的错误信息
      String errorMessage;
      if (e.toString().contains('network') ||
          e.toString().contains('connection')) {
        errorMessage = '网络连接失败，请检查网络设置';
      } else if (e.toString().contains('database') ||
          e.toString().contains('sql')) {
        errorMessage = '数据加载失败，请重试';
      } else {
        errorMessage = '启动复习失败，请重试';
      }

      Get.snackbar('错误', errorMessage);
      Get.back();
    }
  }

  /// 获取待复习单词
  Future<List<WordModel>> _getReviewWords(int userWordBookId) async {
    final reviewItems = await _reviewItemRepository
        .getDueReviewWordsByUserWordBook(userWordBookId);
    return reviewItems;
  }

  @override
  void onClose() {
    _stateService.clear();
    _log.i('ReviewController销毁');
    super.onClose();
  }
}
