import 'package:flutter/widgets.dart';
import 'package:flutter_study_flow_by_myself/app/services/audio_service.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_model.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/word_repository.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/controllers/learning_controller.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/services/learning_navigation_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/services/learning_state_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/services/quiz_generation_service.dart';
import 'package:get/get.dart';

/// 单词详情页面控制器
///
/// 职责：
/// - 管理单词详情页面的数据加载和显示
/// - 控制单词音频的智能播放机制
/// - 处理用户与详情页面的交互操作
/// - 协调详情页面与学习流程的衔接
///
/// 架构说明：
/// 本控制器专注于单词详情展示的业务逻辑，通过依赖注入获取所需服务，
/// 实现了智能的音频播放防重复机制和灵活的单词加载策略。
class DetailController extends GetxController {
  // ==================== 依赖注入 ====================
  /// 学习主控制器 - 提供学习会话的上下文信息
  ///
  /// 调用位置：
  /// - 当前文件中未直接使用，但作为学习流程的上下文依赖保留
  ///
  /// 用途：提供学习会话状态和协调学习流程
  final LearningController learningController;

  /// 学习状态服务 - 管理学习过程中的状态数据
  ///
  /// 调用位置：
  /// - onInit() 中监听 currentDetailWordIdObs 变化
  /// - _loadWordDetails() 中获取 currentDetailWordId 和 getCurrentWordId()
  /// - progressIndicatorText getter 中获取 currentWordIndex 和 wordIds
  /// - startQuiz() 中检查 isInFinalQuiz 状态
  final LearningStateService _stateService;

  /// 学习导航服务 - 处理学习流程中的页面导航
  ///
  /// 调用位置：
  /// - startQuiz() 中调用 navigateToQuiz() 导航到测验页面
  final LearningNavigationService _navigationService;

  /// 音频服务 - 处理单词发音播放
  ///
  /// 调用位置：
  /// - playWordAudio() 中调用 playWordAudio() 播放单词音频
  final AudioService _audioService;

  /// 单词仓库 - 处理单词数据的获取
  ///
  /// 调用位置：
  /// - _loadWordDetails() 中调用 getWordById() 获取单词详情
  final WordRepository _wordRepository;

  /// 测验生成服务 - 处理测验题目的生成
  ///
  /// 调用位置：
  /// - startQuiz() 中调用 generateQuizForWord() 和 generateRandomQuiz()
  final QuizGenerationService _quizGenerationService;

  /// 日志服务 - 记录控制器运行日志
  ///
  /// 调用位置：
  /// - 所有方法中用于记录操作日志、错误信息和调试信息
  final LogService _log;

  // ==================== 响应式状态变量 ====================
  /// 当前显示的单词数据
  ///
  /// 存储当前详情页面展示的单词完整信息，包括拼写、释义、例句等
  ///
  /// UI 使用位置：
  /// - detail_page.dart:51 - 通过 Obx 监听，控制页面内容显示
  /// - detail_page.dart:63 - 在 _buildWordHeader() 中显示单词基本信息
  ///
  /// 内部监听：
  /// - onInit() 中通过 ever() 监听变化，触发自动音频播放逻辑
  ///
  /// 响应式监听：当单词数据变化时，自动更新UI显示并触发音频播放
  final Rx<WordModel?> currentWord = Rx<WordModel?>(null);

  // ==================== 私有状态变量 ====================
  // 注意：由于需求是每次进入详情页都播放音频（包括重复进入同一单词），
  // 因此不需要防重复播放的相关变量和逻辑

  // ==================== 构造函数 ====================
  /// 构造函数 - 依赖注入
  ///
  /// 通过构造函数注入所有必要的服务和控制器实例，确保依赖关系清晰
  /// 并提高代码的可测试性和可维护性
  ///
  /// 参数：
  /// - [learningController] 学习主控制器实例
  /// - [stateService] 学习状态服务实例
  /// - [navigationService] 学习导航服务实例
  /// - [audioService] 音频服务实例
  /// - [wordRepository] 单词仓库实例
  /// - [quizGenerationService] 测验生成服务实例
  /// - [log] 日志服务实例
  ///
  /// 注册位置：lib/modules/learning/bindings/learning_binding.dart
  DetailController({
    required this.learningController,
    required LearningStateService stateService,
    required LearningNavigationService navigationService,
    required AudioService audioService,
    required WordRepository wordRepository,
    required QuizGenerationService quizGenerationService,
    required LogService log,
  }) : _stateService = stateService,
       _navigationService = navigationService,
       _audioService = audioService,
       _wordRepository = wordRepository,
       _quizGenerationService = quizGenerationService,
       _log = log;

  // ==================== 生命周期方法 ====================
  /// 控制器初始化方法
  ///
  /// 在控制器创建时自动调用，负责设置响应式监听和初始化数据加载
  ///
  /// 调用时机：
  /// - 当用户导航到单词详情页面时，GetX 自动调用此方法
  /// - 通过学习流程中的导航服务触发
  ///
  /// 执行流程：
  /// 1. 调用父类的 onInit() 方法
  /// 2. 设置详情页单词ID变化的监听
  /// 3. 设置单词数据加载完成的监听（用于自动播放音频）
  /// 4. 如果初始化时已有单词ID，立即加载单词详情
  ///
  /// 监听机制：
  /// - 监听详情页单词ID变化：当用户切换查看的单词时触发
  /// - 监听单词数据变化：当单词数据加载完成时触发音频播放
  @override
  void onInit() {
    super.onInit();
    _log.i('初始化详情控制器');

    // 监听详情页单词ID变化
    // 当用户通过不同方式进入详情页或切换单词时触发
    ever(_stateService.currentDetailWordIdObs, (int wordId) {
      if (wordId > 0) {
        _log.i('检测到详情页单词ID变化: $wordId');
        _loadWordDetails();
      }
    });

    // 初始化时如果已经有数据，立即加载
    // 处理从其他页面导航过来时已经设置了单词ID的情况
    if (_stateService.currentDetailWordId > 0) {
      _loadWordDetails();
    }
  }

  // ==================== 私有方法 ====================
  /// 加载单词详情数据
  ///
  /// 实现灵活的单词加载策略：优先加载详情页指定的单词，
  /// 如果没有指定则加载当前学习单词
  ///
  /// 调用位置：
  /// - onInit() 中在单词ID变化时调用
  /// - onInit() 中在初始化时如果已有单词ID则调用
  ///
  /// 加载策略：
  /// 1. 优先使用详情页指定的单词ID (currentDetailWordId)
  /// 2. 如果没有指定，使用当前学习单词ID (getCurrentWordId)
  /// 3. 如果都没有，记录警告并返回
  ///
  /// 业务逻辑：
  /// 1. 确定要加载的单词ID
  /// 2. 从仓库获取单词详细数据
  /// 3. 更新响应式变量触发UI更新
  /// 4. 重置音频播放状态
  /// 5. 验证数据加载结果
  ///
  /// 异常处理：
  /// - 捕获所有异常并记录错误日志
  /// - 显示用户友好的错误提示
  /// - 不重新抛出异常，确保应用稳定性
  ///
  /// 返回值：无返回值（Future&lt;void&gt;）
  void _loadWordDetails() async {
    try {
      // 决定加载哪个单词：详情页指定的单词 > 当前学习单词
      int wordId = _stateService.currentDetailWordId;
      if (wordId <= 0) {
        wordId = _stateService.getCurrentWordId();
      }

      if (wordId <= 0) {
        _log.w('没有可加载的单词ID');
        return;
      }

      _log.i('加载单词详情: $wordId');
      final word = await _wordRepository.getWordById(wordId);
      currentWord.value = word; // 更新响应式变量，触发UI更新

      if (word == null) {
        throw Exception('无法加载单词ID: $wordId');
      }

      // 每次加载完单词后都自动播放音频
      // 使用延迟确保页面渲染完成
      _scheduleAutoPlayAudio();
    } catch (e) {
      _log.e('加载单词详情失败', error: e);
      Get.snackbar('警告', '加载单词详情失败');
    }
  }

  // ==================== 公共方法 ====================
  /// 播放单词音频（手动触发）
  ///
  /// 用户主动点击播放按钮时调用，直接播放当前单词的音频
  /// 手动播放不受防重复机制限制，用户可以随时重复播放
  ///
  /// 调用位置：
  /// - detail_page.dart 中的音频播放按钮点击事件
  ///
  /// 播放逻辑：
  /// 1. 检查当前是否有单词数据
  /// 2. 优先使用单词的自定义音频URL
  /// 3. 如果没有自定义URL，使用默认的TTS音频
  ///
  /// 音频来源优先级：
  /// - 单词自带的音频URL（如果存在且不为空）
  /// - 音频服务的默认TTS生成音频
  ///
  /// 返回值：无返回值（void）
  void playWordAudio() {
    if (currentWord.value != null) {
      final word = currentWord.value!;
      final String spelling = word.spelling;

      if (word.audioUrl != null && word.audioUrl!.isNotEmpty) {
        _audioService.playWordAudio(spelling, url: word.audioUrl);
      } else {
        _audioService.playWordAudio(spelling);
      }

      _log.i('手动播放单词音频: $spelling');
    }
  }

  /// 安排自动播放音频
  ///
  /// 每次加载单词后自动播放音频，确保页面渲染完成后再播放
  ///
  /// 调用位置：
  /// - _loadWordDetails() 中在单词加载完成后调用
  ///
  /// 播放策略：
  /// - 每次进入详情页都播放音频，包括重复进入同一单词
  /// - 使用PostFrameCallback确保页面渲染完成
  /// - 延迟300ms确保页面动画完成
  ///
  /// 设计原理：
  /// - 满足用户每次进入详情页都听到音频的需求
  /// - 避免音频播放与页面渲染冲突
  ///
  /// 返回值：无返回值（void）
  void _scheduleAutoPlayAudio() {
    if (currentWord.value != null) {
      final word = currentWord.value!;
      _log.i('安排自动播放音频: ${word.spelling}');

      // 使用 PostFrameCallback 确保页面渲染完成
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // 延迟300ms确保页面动画完成
        Future.delayed(const Duration(milliseconds: 300), () {
          // 检查单词是否仍然是当前单词（避免快速切换时的问题）
          if (currentWord.value?.id == word.id) {
            _log.i('页面渲染完成，开始自动播放音频: ${word.spelling}');
            playWordAudio(); // 直接调用播放方法，无需防重复机制
          }
        });
      });
    }
  }

  // ==================== 数据访问接口 ====================
  /// 获取当前单词的学习进度文本
  ///
  /// 返回格式化的进度文本，显示当前单词在学习序列中的位置
  ///
  /// UI 使用位置：
  /// - detail_page.dart 中显示学习进度信息
  /// - 可能在进度条或状态栏中使用
  ///
  /// 数据来源：
  /// - currentWordIndex: 当前单词在学习序列中的索引（从0开始）
  /// - wordIds.length: 总单词数量
  ///
  /// 格式说明：
  /// - 索引+1是因为用户界面通常从1开始计数
  /// - 返回格式："当前位置 / 总数"，如 "3 / 10"
  ///
  /// 返回值：String - 格式化的进度文本
  String get progressIndicatorText {
    final currentIndex = _stateService.currentWordIndex + 1;
    final totalWords = _stateService.wordIds.length;
    return '$currentIndex / $totalWords';
  }

  // ==================== 测验相关方法 ====================
  /// 开始测验
  ///
  /// 从详情页面返回到测验页面，根据当前学习阶段生成相应的测验题目
  ///
  /// 调用位置：
  /// - detail_page.dart 中的"开始测验"按钮点击事件
  ///
  /// 业务逻辑：
  /// 1. 根据当前学习阶段（学习阶段 vs 最终测验阶段）选择不同策略
  /// 2. 为指定单词生成测验题目
  /// 3. 导航到测验页面继续学习流程
  ///
  /// 测验生成策略：
  /// - **最终测验阶段**：
  ///   - 优先为当前详情页的单词生成题目
  ///   - 如果无法获取详情页单词ID，生成随机题目
  /// - **学习阶段**：
  ///   - 为当前学习单词生成题目
  ///   - 确保测验与学习进度同步
  ///
  /// 导航处理：
  /// - 无论题目生成是否成功，都会导航到测验页面
  /// - 测验页面会处理没有题目的情况
  ///
  /// 异常处理：
  /// - 捕获题目生成过程中的异常
  /// - 记录错误日志但不阻止导航
  /// - 确保用户能够继续学习流程
  ///
  /// 返回值：无返回值（Future&lt;void&gt;）
  void startQuiz() async {
    try {
      _log.i('从详情页返回测验页面');

      // 根据当前阶段生成新的测验题
      if (_stateService.isInFinalQuiz) {
        // 最终测验阶段：为当前详情页的单词生成题目
        final detailWordId = _stateService.currentDetailWordId;
        if (detailWordId > 0) {
          _log.i('最终测验阶段：为详情页单词 $detailWordId 生成题目');
          await _quizGenerationService.generateQuizForWord(detailWordId);
        } else {
          _log.w('最终测验阶段：无法获取详情页单词ID，生成随机题目');
          await _quizGenerationService.generateRandomQuiz();
        }
      } else {
        // 学习阶段：为当前单词生成题目
        final currentWordId = _stateService.getCurrentWordId();
        if (currentWordId > 0) {
          _log.i('学习阶段：为当前单词 $currentWordId 生成题目');
          await _quizGenerationService.generateQuizForWord(currentWordId);
        }
      }

      // 导航到测验页面（状态会自动重置）
      await _navigationService.navigateToQuiz();
    } catch (e) {
      _log.e('返回测验页面失败', error: e);
      // 如果生成题目失败，仍然导航到测验页面
      // 测验页面会处理没有题目的情况
      await _navigationService.navigateToQuiz();
    }
  }
}
