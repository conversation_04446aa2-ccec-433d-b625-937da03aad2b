# widgets 目录说明文档

## 目录职责

`widgets` 目录负责存放应用程序中可重用的UI组件，这些组件设计为通用、可配置且能够在应用的不同部分重复使用。全局widgets不包含业务逻辑，而是专注于UI表现和交互，通过属性配置实现不同场景下的适应性。

## 文件结构

```
widgets/
├── buttons/                 # 按钮相关组件
│   ├── primary_button.dart      # 主要按钮
│   ├── secondary_button.dart    # 次要按钮
│   └── icon_button.dart         # 图标按钮
├── cards/                   # 卡片相关组件
│   ├── info_card.dart           # 信息卡片
│   └── action_card.dart         # 可操作卡片
├── dialogs/                 # 对话框相关组件
│   ├── confirm_dialog.dart      # 确认对话框
│   ├── loading_dialog.dart      # 加载对话框
│   └── input_dialog.dart        # 输入对话框
├── inputs/                  # 输入相关组件
│   ├── text_input.dart          # 文本输入框
│   ├── search_bar.dart          # 搜索栏
│   └── dropdown_select.dart     # 下拉选择框
└── indicators/              # 指示器相关组件
    ├── loading_indicator.dart   # 加载指示器
    ├── progress_bar.dart        # 进度条
    └── badge.dart               # 徽章
```

## 设计原则

1. **组件化**：将UI拆分为小型、独立且可重用的组件

2. **可配置性**：通过属性参数使组件能适应不同场景

3. **一致性**：保持设计风格和交互方式的一致

4. **无状态优先**：优先使用无状态组件，必要时再使用有状态组件

5. **关注点分离**：UI组件专注于表现层，不包含业务逻辑

## 组件设计模式

### 基础组件

基础组件是最底层的UI元素，直接封装Flutter原生组件，添加项目特定的样式和行为：

```dart
// buttons/primary_button.dart
class PrimaryButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final bool isLoading;
  final bool isFullWidth;
  
  const PrimaryButton({
    Key? key,
    required this.text,
    required this.onPressed,
    this.isLoading = false,
    this.isFullWidth = false,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        primary: Theme.of(context).primaryColor,
        padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        minimumSize: isFullWidth ? Size(double.infinity, 48) : null,
      ),
      onPressed: isLoading ? null : onPressed,
      child: isLoading
          ? SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : Text(
              text,
              style: TextStyle(fontSize: 16),
            ),
    );
  }
}
```

### 复合组件

复合组件由多个基础组件组合而成，实现更复杂的UI结构：

```dart
// cards/info_card.dart
class InfoCard extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData icon;
  final VoidCallback? onTap;
  
  const InfoCard({
    Key? key,
    required this.title,
    this.subtitle,
    required this.icon,
    this.onTap,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: EdgeInsets.symmetric(vertical: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(icon, size: 32, color: Theme.of(context).primaryColor),
              SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (subtitle != null) ...[
                      SizedBox(height: 4),
                      Text(
                        subtitle!,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              if (onTap != null)
                Icon(Icons.chevron_right, color: Colors.grey),
            ],
          ),
        ),
      ),
    );
  }
}
```

### 行为组件

行为组件封装特定的交互行为，提供一致的用户体验：

```dart
// dialogs/confirm_dialog.dart
class ConfirmDialog extends StatelessWidget {
  final String title;
  final String message;
  final String confirmText;
  final String cancelText;
  final VoidCallback onConfirm;
  final VoidCallback? onCancel;
  
  const ConfirmDialog({
    Key? key,
    required this.title,
    required this.message,
    this.confirmText = '确认',
    this.cancelText = '取消',
    required this.onConfirm,
    this.onCancel,
  }) : super(key: key);
  
  // 显示对话框的静态方法
  static Future<bool> show({
    required BuildContext context,
    required String title,
    required String message,
    String confirmText = '确认',
    String cancelText = '取消',
  }) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return ConfirmDialog(
          title: title,
          message: message,
          confirmText: confirmText,
          cancelText: cancelText,
          onConfirm: () => Navigator.of(context).pop(true),
          onCancel: () => Navigator.of(context).pop(false),
        );
      },
    ) ?? false;
  }
  
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(title),
      content: Text(message),
      actions: [
        TextButton(
          onPressed: onCancel ?? () => Navigator.of(context).pop(false),
          child: Text(cancelText),
        ),
        ElevatedButton(
          onPressed: onConfirm,
          child: Text(confirmText),
        ),
      ],
    );
  }
}
```

## 使用示例

### 基础组件

```dart
// 使用主按钮
PrimaryButton(
  text: '登录',
  onPressed: () => _handleLogin(),
  isLoading: _isLoggingIn,
  isFullWidth: true,
)
```

### 复合组件

```dart
// 使用信息卡片
InfoCard(
  title: '个人资料',
  subtitle: '编辑您的个人信息',
  icon: Icons.person,
  onTap: () => Navigator.pushNamed(context, '/profile'),
)
```

### 行为组件

```dart
// 使用确认对话框
void _confirmLogout() async {
  final confirmed = await ConfirmDialog.show(
    context: context,
    title: '退出登录',
    message: '确定要退出当前账号吗？',
  );
  
  if (confirmed) {
    await _authService.logout();
    Navigator.pushReplacementNamed(context, '/login');
  }
}
```

## 最佳实践

1. **命名规范**：
   - 使用明确的名称描述组件的功能和用途
   - 使用一致的命名模式（如 `XxxCard`, `XxxButton`）

2. **文档和注释**：
   - 为每个组件添加清晰的文档注释，说明其用途、参数和使用方法
   - 使用示例代码展示典型用法

3. **参数设计**：
   - 必要参数使用 `required` 关键字标记
   - 为可选参数提供合理的默认值
   - 使用命名参数提高可读性

4. **封装与扩展**：
   - 尽量封装原生组件的复杂性
   - 提供足够的扩展点，允许在特殊情况下自定义行为

5. **主题适配**：
   - 使用 `Theme.of(context)` 获取当前主题
   - 避免硬编码颜色和尺寸，优先使用主题中定义的值

6. **响应式设计**：
   - 考虑不同屏幕尺寸下的适应性
   - 使用灵活的布局，如 `Expanded`, `Flexible` 等

## 与其他目录的关系

- **themes目录**：全局组件应使用主题定义的样式
- **constants目录**：组件可能使用常量中定义的值
- **utils目录**：组件可能使用工具函数处理数据
- **business modules**：业务模块使用全局组件构建UI

## 测试策略

1. **单元测试**：
   - 测试组件的属性是否正确传递
   - 测试组件的回调函数是否被正确调用

2. **Widget测试**：
   - 测试组件在不同配置下的渲染结果
   - 测试组件的交互行为

3. **Golden测试**：
   - 对比组件的视觉外观是否符合预期
   - 检测UI变更

## 扩展计划

- 创建更多专用组件，如表单组件、图表组件等
- 实现动画组件库，提供常用的动画效果
- 添加更多的无障碍支持
- 集成更丰富的主题定制能力 