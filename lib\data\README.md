# data 目录说明文档

## 目录职责

`data` 目录负责所有数据相关的操作，是整个应用的数据层。它处理数据的定义、获取、存储、转换和业务逻辑处理，与UI和控制器层完全解耦。数据层为上层提供一致的数据访问接口，屏蔽了数据来源的细节（如网络API、本地数据库或缓存）。

## 目录结构

```
data/
├── models/                   # 数据模型定义
│   ├── base_model.dart       # 基础数据模型类
│   ├── user_model.dart       # 用户数据模型
│   └── word_model.dart       # 单词数据模型
├── repositories/             # 仓储层：抽象数据访问
│   ├── base_repository.dart  # 仓储层的抽象基类/接口
│   ├── user_repository.dart  # 用户数据仓储
│   └── word_repository.dart  # 单词数据仓储
└── providers/                # 数据源提供者：底层数据源的具体实现
    ├── dio_client.dart       # Dio HTTP 客户端的封装配置
    ├── base_api_provider.dart # 基础 API 请求封装类
    ├── word_api_provider.dart # 单词相关的远程 API 调用
    ├── user_api_provider.dart # 用户相关的远程 API 调用
    └── local_storage_provider.dart # 本地存储的底层读写
```

## 设计理念

- **分层设计**：将数据层分为模型、仓储和数据源三个层次，实现关注点分离
- **单一职责**：每个类都有明确的单一职责，便于测试和维护
- **抽象与实现分离**：通过仓储层抽象数据访问接口，使业务逻辑不依赖于具体数据源
- **数据转换**：在适当的层次处理数据格式转换，保证上层获得类型安全的数据

## 主要文件夹说明

### models/

- **职责**：定义应用程序中所有数据实体的结构
- **内容**：包含所有业务实体的数据模型类，定义属性、序列化方法等
- **设计原因**：提供类型安全的数据结构，避免使用动态类型的Map，便于IDE提示和编译时检查
- **典型文件**：
  - `base_model.dart`：基础模型类，提供通用的序列化/反序列化方法
  - `user_model.dart`：用户相关数据模型
  - `word_model.dart`：单词相关数据模型

### repositories/

- **职责**：提供统一的数据访问接口给上层（Controller/Service）
- **内容**：抽象出数据操作的业务逻辑，决定数据从哪里来（网络/本地/缓存）
- **设计原因**：
  - 将数据获取逻辑与业务逻辑分离
  - 实现数据源的无缝切换（如从测试API切换到生产API）
  - 处理数据缓存策略、数据组合和转换
- **依赖**：各种 `Provider` 实现具体的数据获取
- **典型文件**：
  - `base_repository.dart`：定义通用数据操作的接口
  - `user_repository.dart`：处理用户数据相关的业务逻辑
  - `word_repository.dart`：处理单词数据相关的业务逻辑

### providers/

- **职责**：封装与具体数据源（远程API、本地数据库、文件系统）交互的底层实现
- **内容**：处理网络请求、数据库操作、文件读写等底层数据访问
- **设计原因**：
  - 隔离第三方库依赖（如Dio、sqflite）
  - 处理特定于数据源的细节（如HTTP请求头、响应解析）
- **典型文件**：
  - `dio_client.dart`：封装Dio HTTP客户端配置
  - `word_api_provider.dart`：封装与单词相关的API请求
  - `local_storage_provider.dart`：封装本地存储操作

## 数据流向

典型的数据流向如下：

1. **Controller/Service 层**：通过 Repository 请求数据
2. **Repository 层**：
   - 决定从哪个数据源获取数据（网络、缓存、本地数据库）
   - 可能合并多个数据源的数据
   - 实现业务相关的数据处理逻辑
3. **Provider 层**：
   - 执行实际的数据获取操作（HTTP请求、数据库查询）
   - 处理原始数据格式转换为应用数据模型
4. **Models**：在各层之间传递的强类型数据结构

## 易混淆点

1. **`repositories` 与 `providers` 的区别**：
   - `repositories` 是**业务接口**，实现业务逻辑，决定数据来源
   - `providers` 是**技术实现**，直接与特定数据源交互
   - 例如，`WordRepository` 可能会根据网络状况决定从 `WordApiProvider` 或本地缓存获取数据

2. **`StorageService` 与 `LocalStorageProvider` 的区别**：
   - `StorageService`（在 `app/services/`）提供高层业务接口
   - `LocalStorageProvider` 提供底层技术实现
   - 前者关注"存什么"，后者关注"怎么存"

3. **模型转换的责任**：
   - 原始数据到模型的转换通常在 `Provider` 层完成
   - 复杂的业务模型组装在 `Repository` 层完成
   - 确保上层（Controller/Service）总是获得类型安全的模型对象

## 最佳实践

- 模型类应实现 `fromJson` 和 `toJson` 方法，支持序列化
- Repository层应定义接口（抽象类），便于测试时使用Mock实现
- Provider层应处理所有与特定技术栈相关的细节，不泄露实现细节
- 使用异步编程处理I/O操作，返回 `Future` 或 `Stream`

## 代码示例

### 定义数据模型
```dart
// lib/data/models/user_model.dart
class User {
  final int id;
  final String username;
  final String email;
  final String? avatar;
  
  User({
    required this.id,
    required this.username,
    required this.email,
    this.avatar,
  });
  
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      username: json['username'],
      email: json['email'],
      avatar: json['avatar'],
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'avatar': avatar,
    };
  }
}
```

### 创建API Provider
```dart
// lib/data/providers/user_api_provider.dart
import 'package:dio/dio.dart';
import '../models/user_model.dart';

class UserApiProvider {
  final Dio _dio;
  
  UserApiProvider(this._dio);
  
  Future<User> getUserById(int id) async {
    try {
      final response = await _dio.get('/users/$id');
      return User.fromJson(response.data);
    } on DioError catch (e) {
      // 处理特定于Dio的错误
      throw Exception('Failed to load user: ${e.message}');
    } catch (e) {
      // 处理其他错误
      throw Exception('Unexpected error: $e');
    }
  }
  
  // 其他API方法...
}
```

### 实现Repository
```dart
// lib/data/repositories/user_repository.dart
import '../models/user_model.dart';
import '../providers/user_api_provider.dart';
import '../providers/local_storage_provider.dart';

class UserRepository {
  final UserApiProvider _apiProvider;
  final LocalStorageProvider _storageProvider;
  
  UserRepository(this._apiProvider, this._storageProvider);
  
  Future<User> getUserById(int id) async {
    try {
      // 先尝试从本地缓存获取
      final cachedData = await _storageProvider.read('user_$id');
      if (cachedData != null) {
        return User.fromJson(cachedData);
      }
      
      // 如果缓存不存在，从API获取
      final user = await _apiProvider.getUserById(id);
      
      // 缓存结果
      await _storageProvider.write('user_$id', user.toJson());
      
      return user;
    } catch (e) {
      // 处理业务级别的错误
      throw Exception('无法获取用户信息');
    }
  }
  
  // 其他业务方法...
}
```

## 相关联文件

- `lib/app/services/`：使用repository层获取和处理数据
- `lib/modules/*/controllers/`：通过repository或service访问数据
- `pubspec.yaml`：定义依赖的第三方数据处理库（如dio, sqflite）

## 常见问题与解决方案

- **数据解析错误**：确保模型的 `fromJson` 方法正确处理所有可能的数据格式
- **数据源切换**：在Repository层实现切换逻辑，上层代码无需修改
- **缓存策略**：在Repository层实现适当的缓存逻辑，如时间过期或强制刷新
- **错误处理**：Provider层处理技术错误，Repository层转换为业务错误，向上抛出用户友好的错误信息 