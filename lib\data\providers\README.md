# providers 目录说明文档

## 目录职责

`providers` 目录包含数据源提供者，负责封装与具体数据源（远程API、本地数据库、文件系统等）交互的底层实现。它处理网络请求、数据库操作、文件读写等底层数据访问细节，将原始数据转换为应用可用的数据模型，并处理与特定数据源相关的技术异常。

## 目录结构

```
providers/
├── dio_client.dart           # Dio HTTP 客户端的封装配置
├── base_api_provider.dart    # 基础 API 请求封装类
├── word_api_provider.dart    # 单词相关的远程 API 调用
├── user_api_provider.dart    # 用户相关的远程 API 调用
└── local_storage_provider.dart # 本地存储的底层读写
```

## 设计理念

- **技术细节封装**：隔离特定技术实现细节，使上层代码不依赖于具体的第三方库
- **单一职责**：每个Provider只负责与特定数据源的交互
- **异常处理**：捕获并处理特定于数据源的技术异常（如HTTP错误、数据库异常）
- **数据转换**：将原始数据（如JSON、数据库记录）转换为应用定义的模型对象

## 主要文件说明

### dio_client.dart

- **职责**：封装和配置Dio HTTP客户端，统一管理HTTP请求的配置
- **内容**：Dio实例创建、baseUrl设置、超时配置、拦截器添加
- **设计原因**：集中管理HTTP客户端配置，便于全局修改和维护
- **典型功能**：
  - 配置基本URL和超时时间
  - 添加认证Token拦截器
  - 添加日志记录拦截器
  - 处理常见HTTP错误

### base_api_provider.dart

- **职责**：为API请求提供通用的基础功能
- **内容**：通用的请求方法、响应处理、错误转换等
- **设计原因**：减少各API Provider的重复代码，统一处理响应格式
- **典型功能**：
  - 封装通用的GET、POST、PUT、DELETE方法
  - 统一处理API响应格式
  - 提供通用的错误处理逻辑

### word_api_provider.dart / user_api_provider.dart

- **职责**：封装特定业务领域的API请求
- **内容**：与特定API端点交互的方法，如获取单词列表、用户认证等
- **依赖**：`DioClient`进行实际的HTTP请求
- **设计原因**：按业务领域划分API请求，提高代码的内聚性和可维护性
- **典型方法**：
  - `fetchNewWords(int count)`
  - `getUserById(int id)`
  - `login(String username, String password)`

### local_storage_provider.dart

- **职责**：封装本地数据存储操作
- **内容**：提供本地数据的读、写、删除等基本操作
- **设计原因**：统一本地存储接口，隐藏具体存储技术（如SharedPreferences、Hive）的细节
- **典型方法**：
  - `read(String key)`
  - `write(String key, dynamic value)`
  - `delete(String key)`
  - `clear()`

## Provider的设计与实现

### 基本结构

Provider通常遵循以下结构：

1. **接口定义**：定义数据访问方法的抽象接口
2. **具体实现**：实现接口的具体类，处理实际的数据访问
3. **依赖注入**：通过构造函数注入所需依赖（如HttpClient）

```dart
// 接口定义
abstract class UserApiProvider {
  Future<User> getUserById(int id);
  Future<User> login(String username, String password);
}

// 具体实现
class UserApiProviderImpl implements UserApiProvider {
  final Dio _dio;
  
  UserApiProviderImpl(this._dio);
  
  @override
  Future<User> getUserById(int id) async {
    // 实现...
  }
  
  @override
  Future<User> login(String username, String password) async {
    // 实现...
  }
}
```

### 数据转换

Provider负责将原始数据转换为应用定义的模型：

```dart
Future<User> getUserById(int id) async {
  try {
    final response = await _dio.get('/users/$id');
    return User.fromJson(response.data);
  } catch (e) {
    // 处理错误...
    throw e;
  }
}
```

### 错误处理

Provider应捕获特定于数据源的异常，并进行适当处理：

```dart
Future<User> getUserById(int id) async {
  try {
    final response = await _dio.get('/users/$id');
    return User.fromJson(response.data);
  } on DioError catch (e) {
    // 处理Dio特定的错误
    if (e.type == DioErrorType.connectTimeout) {
      throw ApiTimeoutException('连接超时');
    } else if (e.response?.statusCode == 404) {
      throw ResourceNotFoundException('用户不存在');
    } else {
      throw ApiException('API错误: ${e.message}');
    }
  } catch (e) {
    // 处理其他错误
    throw DataSourceException('获取用户数据失败: $e');
  }
}
```

## 易混淆点

1. **Provider vs Repository**：
   - **Provider**：处理**如何**从特定数据源获取数据的技术细节
   - **Repository**：处理**应该**从哪个数据源获取数据的业务决策
   - Provider通常由Repository调用，而不是直接被Controller使用

2. **`ApiProvider` vs `LocalStorageProvider`**：
   - 虽然都位于`providers`目录，但它们处理不同类型的数据源
   - `ApiProvider`处理远程API交互
   - `LocalStorageProvider`处理本地数据存储
   - 但它们都遵循相同的设计原则：封装特定数据源的访问细节

3. **`DioClient` vs 各种`ApiProvider`**：
   - `DioClient`是HTTP客户端的通用配置，不包含业务逻辑
   - 各`ApiProvider`使用`DioClient`进行HTTP请求，但添加了特定业务领域的逻辑

## 最佳实践

1. **HTTP客户端配置**：

```dart
// lib/data/providers/dio_client.dart
import 'package:dio/dio.dart';
import 'package:flutter_base_app_template/app/services/log_service.dart';

class DioClient {
  final LogService _logService;
  late Dio _dio;

  DioClient(this._logService) {
    _dio = Dio(
      BaseOptions(
        baseUrl: 'https://api.example.com/v1',
        connectTimeout: const Duration(seconds: 5),
        receiveTimeout: const Duration(seconds: 10),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ),
    );

    // 添加拦截器
    _dio.interceptors.add(LogInterceptor(
      logPrint: (log) => _logService.debug('Dio: $log'),
      requestBody: true,
      responseBody: true,
    ));

    // 添加认证拦截器
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // 从本地存储获取Token并添加到请求头
        final token = await _getAuthToken();
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        return handler.next(options);
      },
    ));
  }

  Dio get dio => _dio;

  // 获取认证Token的辅助方法
  Future<String?> _getAuthToken() async {
    // 实现从本地存储获取Token的逻辑
    return null; // 示例
  }
}
```

2. **API Provider实现**：

```dart
// lib/data/providers/word_api_provider.dart
import 'package:dio/dio.dart';
import 'package:flutter_base_app_template/data/models/word_model.dart';

class WordApiProvider {
  final Dio _dio;
  
  WordApiProvider(this._dio);
  
  Future<List<Word>> fetchNewWords(int count) async {
    try {
      final response = await _dio.get('/words/new', queryParameters: {'count': count});
      
      final List<dynamic> wordsData = response.data['data'];
      return wordsData.map((wordData) => Word.fromJson(wordData)).toList();
    } on DioError catch (e) {
      // 处理Dio特定的错误
      _handleDioError(e);
    } catch (e) {
      throw Exception('获取新词失败: $e');
    }
  }
  
  Future<List<Word>> fetchWordsForReview(int count) async {
    // 类似实现...
  }
  
  Future<Word> fetchWordDetails(int wordId) async {
    // 类似实现...
  }
  
  Future<void> updateWordStatus(int wordId, bool isLearned) async {
    try {
      await _dio.put(
        '/words/$wordId/status',
        data: {'is_learned': isLearned},
      );
    } on DioError catch (e) {
      _handleDioError(e);
    } catch (e) {
      throw Exception('更新单词状态失败: $e');
    }
  }
  
  // 处理Dio错误的辅助方法
  Never _handleDioError(DioError e) {
    if (e.type == DioErrorType.connectionTimeout) {
      throw Exception('网络连接超时，请检查网络设置');
    } else if (e.type == DioErrorType.receiveTimeout) {
      throw Exception('服务器响应超时，请稍后重试');
    } else if (e.response != null) {
      final statusCode = e.response!.statusCode;
      final message = e.response!.data['message'] ?? '未知错误';
      
      if (statusCode == 401) {
        throw Exception('身份验证失败，请重新登录');
      } else if (statusCode == 403) {
        throw Exception('没有访问权限');
      } else if (statusCode == 404) {
        throw Exception('请求的资源不存在');
      } else {
        throw Exception('服务器错误 ($statusCode): $message');
      }
    } else {
      throw Exception('网络请求失败: ${e.message}');
    }
  }
}
```

3. **本地存储Provider实现**：

```dart
// lib/data/providers/local_storage_provider.dart
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

abstract class LocalStorageProvider {
  Future<T?> read<T>(String key);
  Future<void> write<T>(String key, T value);
  Future<void> delete(String key);
  Future<void> clear();
  Future<Map<String, dynamic>> readAll(String prefix);
}

class SharedPrefsProvider implements LocalStorageProvider {
  @override
  Future<T?> read<T>(String key) async {
    final prefs = await SharedPreferences.getInstance();
    
    if (T == String) {
      return prefs.getString(key) as T?;
    } else if (T == int) {
      return prefs.getInt(key) as T?;
    } else if (T == bool) {
      return prefs.getBool(key) as T?;
    } else if (T == double) {
      return prefs.getDouble(key) as T?;
    } else {
      // 对于复杂对象，存储为JSON字符串
      final value = prefs.getString(key);
      if (value == null) return null;
      return jsonDecode(value) as T?;
    }
  }
  
  @override
  Future<void> write<T>(String key, T value) async {
    final prefs = await SharedPreferences.getInstance();
    
    if (value is String) {
      await prefs.setString(key, value);
    } else if (value is int) {
      await prefs.setInt(key, value);
    } else if (value is bool) {
      await prefs.setBool(key, value);
    } else if (value is double) {
      await prefs.setDouble(key, value);
    } else {
      // 对于复杂对象，转换为JSON字符串
      await prefs.setString(key, jsonEncode(value));
    }
  }
  
  @override
  Future<void> delete(String key) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(key);
  }
  
  @override
  Future<void> clear() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }
  
  @override
  Future<Map<String, dynamic>> readAll(String prefix) async {
    final prefs = await SharedPreferences.getInstance();
    final allKeys = prefs.getKeys();
    final filteredKeys = allKeys.where((key) => key.startsWith(prefix));
    
    final result = <String, dynamic>{};
    for (final key in filteredKeys) {
      result[key] = await read(key);
    }
    
    return result;
  }
}
```

## 相关联文件

- `lib/data/models/*.dart`：Provider使用的数据模型
- `lib/data/repositories/*.dart`：调用Provider获取数据
- `pubspec.yaml`：定义Provider依赖的第三方库

## 调试与错误处理

### 网络请求调试

1. **日志拦截器**：
   - 使用Dio的LogInterceptor记录请求和响应
   - 考虑在开发环境启用详细日志，生产环境禁用

2. **Mock服务器**：
   - 使用工具如Mock Service Worker (MSW)模拟API响应
   - 创建测试专用的Mock Provider实现

### 本地存储调试

1. **查看存储内容**：
   - 在开发时添加查看所有存储内容的方法
   - 考虑添加导出/导入功能便于调试

### 错误处理策略

1. **异常分类**：
   - 将异常分为网络错误、服务器错误、数据格式错误等
   - 针对不同类型错误提供不同处理方式

2. **降级策略**：
   - 当网络不可用时，尝试从缓存获取数据
   - 当服务器返回错误时，提供有意义的错误信息

## 常见问题与解决方案

- **API版本变更**：
  - 使用适配器模式处理不同版本的API
  - 在Provider层转换响应格式，保持上层代码稳定

- **认证Token过期**：
  - 使用拦截器自动刷新过期的Token
  - 处理刷新失败的情况，如重定向到登录页面

- **缓存与同步**：
  - 考虑实现简单的缓存机制，如LRU缓存
  - 使用ETags或Last-Modified进行HTTP缓存 