import 'package:flutter/material.dart';
import 'package:flutter_study_flow_by_myself/data/models/quiz/choice_quiz_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/quiz/spelling_quiz_model.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/quiz/controllers/quiz_controller.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/quiz/widgets/choice_quiz_widget_stateless.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/quiz/widgets/spelling_quiz_widget.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/services/learning_state_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/services/quiz_generation_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/widgets/learning_progress_bar.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/widgets/learning_exit_dialog.dart';
import 'package:get/get.dart';

/// 测验页面
///
/// 根据测验类型动态展示不同的测验组件
class QuizPage extends GetView<QuizController> {
  const QuizPage({super.key});

  @override
  Widget build(BuildContext context) {
    // 获取状态服务
    final stateService = Get.find<LearningStateService>();

    return Scaffold(
      appBar: AppBar(
        title: Text(stateService.isInFinalQuiz ? '最终测验' : '单词测验'),
        elevation: 0,
        // 🔑 自定义返回按钮
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => LearningExitDialog.show(context: context),
        ),
      ),
      body: Column(
        children: [
          // 顶部进度条
          Obx(() {
            return LearningProgressBar(
              // 学习阶段进度：已学习的单词数（答对过一题的单词）/ 总单词数
              learnedWords: stateService.learnedWordCount,
              totalWords: stateService.wordIds.length,
              // 测验阶段进度：在随机测验阶段显示总完成进度
              completedQuizzes:
                  stateService.isInFinalQuiz
                      ? stateService.totalCompletedQuizzes
                      : 0,
              totalQuizzes:
                  stateService.isInFinalQuiz
                      ? stateService.wordIds.length *
                          3 // 每个单词需要完成3次
                      : 1,
              // 是否在最终测验阶段
              isInFinalQuiz: stateService.isInFinalQuiz,
            );
          }),

          // 测验内容
          Expanded(
            child: Obx(() {
              final quizService = Get.find<QuizGenerationService>();
              final quiz = quizService.currentQuiz.value;

              if (quiz == null) {
                return const Center(child: CircularProgressIndicator());
              }

              return Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    // 测验题目
                    Expanded(child: _buildQuizContent(quiz)),

                    // 操作按钮（只在回答后显示）
                    Obx(() {
                      if (controller.hasAnswered) {
                        return Padding(
                          padding: const EdgeInsets.only(top: 16.0),
                          child: ElevatedButton(
                            onPressed: controller.onActionButtonPressed,
                            style: ElevatedButton.styleFrom(
                              backgroundColor:
                                  controller.actionButtonText == '查看详情'
                                      ? Colors.orange
                                      : Colors.blue,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 32,
                                vertical: 16,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(30),
                              ),
                            ),
                            child: Text(
                              controller.actionButtonText,
                              style: const TextStyle(fontSize: 18),
                            ),
                          ),
                        );
                      } else {
                        return const SizedBox.shrink();
                      }
                    }),
                  ],
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildQuizContent(dynamic quiz) {
    if (quiz is ChoiceQuizModel) {
      return ChoiceQuizWidgetStateless(quiz: quiz, controller: controller);
    } else if (quiz is SpellingQuizModel) {
      return SpellingQuizWidget(
        quiz: quiz,
        onAnswered:
            (userInput, isCorrect) =>
                controller.onSpellingQuizAnswered(userInput, isCorrect),
      );
    } else {
      return const Center(child: Text('不支持的测验类型'));
    }
  }
}
