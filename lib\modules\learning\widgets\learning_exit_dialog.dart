import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/modules/home/<USER>/home_controller.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/services/learning_persistence_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/services/learning_state_service.dart';

/// 学习退出确认弹窗
///
/// 提供统一的学习流程退出确认界面，包含进度保存和状态刷新逻辑
class LearningExitDialog {
  static final LogService _log = Get.find<LogService>();

  /// 显示退出确认对话框
  ///
  /// [context] 上下文
  /// [customTitle] 自定义标题，默认为"退出学习"
  /// [customContent] 自定义内容，默认根据学习进度生成
  static void show({
    required BuildContext context,
    String? customTitle,
    String? customContent,
  }) {
    final stateService = Get.find<LearningStateService>();

    // 根据学习进度生成不同的提示内容
    final content = customContent ?? _generateContentByProgress(stateService);

    Get.dialog(
      AlertDialog(
        title: Text(customTitle ?? '退出学习'),
        content: Text(content),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        actions: [
          // 继续学习按钮
          TextButton(
            onPressed: () => Get.back(),
            child: const Text(
              '继续学习',
              style: TextStyle(color: Colors.grey, fontSize: 16),
            ),
          ),

          // 退出学习按钮
          ElevatedButton(
            onPressed: () async {
              Get.back(); // 关闭对话框
              await _exitLearningSession();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('退出学习', style: TextStyle(fontSize: 16)),
          ),
        ],
      ),
      barrierDismissible: false, // 防止点击外部关闭
    );
  }

  /// 根据学习进度生成提示内容
  static String _generateContentByProgress(LearningStateService stateService) {
    final learnedCount = stateService.learnedWordCount;
    final totalWords = stateService.wordIds.length;
    final isInFinalQuiz = stateService.isInFinalQuiz;

    if (learnedCount == 0) {
      return '确定要退出学习吗？\n学习进度将会保存，下次可以继续。';
    } else if (learnedCount < 3) {
      return '您已经学习了 $learnedCount 个单词。\n确定要退出吗？学习进度将会保存。';
    } else if (isInFinalQuiz) {
      return '您正在进行最终测验！\n确定要退出吗？当前进度将会保存。';
    } else {
      final progress = ((learnedCount / totalWords) * 100).toInt();
      return '您已完成 $progress% 的学习进度（$learnedCount/$totalWords）。\n确定要退出吗？学习进度将会保存。';
    }
  }

  /// 执行退出学习会话的逻辑
  static Future<void> _exitLearningSession() async {
    try {
      _log.i('用户确认退出学习，开始保存状态');

      // 显示加载提示
      Get.dialog(
        const AlertDialog(
          content: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在保存学习进度...'),
            ],
          ),
        ),
        barrierDismissible: false,
      );

      // 1. 保存当前学习状态
      if (Get.isRegistered<LearningPersistenceService>()) {
        final persistenceService = Get.find<LearningPersistenceService>();
        await persistenceService.saveCurrentState();
        _log.i('学习状态保存成功');
      } else {
        _log.w('LearningPersistenceService未注册，跳过状态保存');
      }

      // 2. 等待一小段时间确保数据库操作完成
      await Future.delayed(const Duration(milliseconds: 100));

      // 3. 刷新首页状态
      if (Get.isRegistered<HomeController>()) {
        final homeController = Get.find<HomeController>();
        await homeController.refreshData();
        _log.i('首页状态刷新成功');
      } else {
        _log.w('HomeController未注册，跳过状态刷新');
      }

      // 关闭加载对话框
      Get.back();

      // 3. 返回首页
      Get.offAllNamed('/main');

      // 显示成功提示
      Get.snackbar(
        '学习进度已保存',
        '您可以随时继续学习',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green[700],
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      _log.e('退出学习时保存状态失败: $e');

      // 关闭加载对话框
      if (Get.isDialogOpen == true) {
        Get.back();
      }

      // 显示错误提示但仍然退出
      Get.snackbar(
        '保存失败',
        '学习进度保存失败，但已退出学习',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange[700],
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );

      // 仍然返回首页
      Get.offAllNamed('/main');
    }
  }

  /// 快速退出（无确认，用于特殊情况）
  ///
  /// [showLoading] 是否显示加载提示
  static Future<void> quickExit({bool showLoading = true}) async {
    if (showLoading) {
      Get.dialog(
        const AlertDialog(
          content: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在保存...'),
            ],
          ),
        ),
        barrierDismissible: false,
      );
    }

    await _exitLearningSession();
  }
}
