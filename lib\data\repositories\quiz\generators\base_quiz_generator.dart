import 'package:flutter_study_flow_by_myself/data/models/word_model.dart';

/// 测验题生成工具类
///
/// 提供测验题生成器共用的工具方法
class QuizGeneratorUtils {
  /// 私有构造函数，防止实例化
  QuizGeneratorUtils._();

  /// 清理和格式化单词
  ///
  /// 移除多余空格，转换为小写，去除标点符号等
  static String cleanWord(String word) {
    return word.trim().toLowerCase();
  }

  /// 从单词列表中随机选择不包含目标单词的干扰项
  ///
  /// [targetWord] 目标单词，不应该包含在返回结果中
  /// [allWords] 所有可用的单词
  /// [count] 需要的干扰项数量
  /// [transform] 可选的转换函数，用于处理从单词对象到特定字符串值的转换
  static List<String> getRandomDistractors(
    WordModel targetWord,
    List<WordModel> allWords,
    int count,
    String Function(WordModel) transform,
  ) {
    // 创建单词副本并移除目标单词
    final availableWords = List<WordModel>.from(allWords);
    availableWords.removeWhere((word) => word.id == targetWord.id);

    // 如果可用单词不足，返回所有可用单词
    if (availableWords.length <= count) {
      return availableWords.map(transform).toList();
    }

    // 打乱顺序并取前count个
    availableWords.shuffle();
    return availableWords.take(count).map(transform).toList();
  }
}
