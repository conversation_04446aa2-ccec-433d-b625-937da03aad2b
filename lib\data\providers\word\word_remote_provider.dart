///
/// 职责：与远程 API 交互，处理与 WordModel 相关的远程数据操作。
/// 它将使用 NetworkService 发送 HTTP 请求，并处理 API 响应。
/// 这里的操作是针对具体业务实体的（如 Word），而不是 NetworkService 中的通用 HTTP 方法。
/// 例如：
/// - 从远程获取单词列表
/// - 提交用户学习进度到远程服务器
/// - 下载新的单词包
// ignore_for_file: unintended_html_in_doc_comment

library;

import 'package:flutter_study_flow_by_myself/app/services/network_service.dart'; // 导入 NetworkService
import 'package:flutter_study_flow_by_myself/data/models/word_model.dart'; // 导入 WordModel
import 'package:flutter_study_flow_by_myself/data/models/api_response.dart'; // 导入 ApiResponse
import 'package:flutter_study_flow_by_myself/app/exceptions/app_exception.dart'; // 导入 AppException
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart'; // 导入 LogService
import 'package:flutter_study_flow_by_myself/data/mock/mock_words.dart'; // 导入mock数据

class WordRemoteProvider {
  final NetworkService _networkService;
  final LogService _log;
  WordRemoteProvider({
    required NetworkService networkService,
    required LogService log,
  }) : _networkService = networkService,
       _log = log;

  /// 从远程 API 获取所有单词（MVP阶段直接返回mock数据）
  Future<List<WordModel>> fetchAllWords() async {
    try {
      _log.i('模拟网络请求：获取所有单词...');
      // TODO: MVP阶段用mock数据，后续接入真实接口时可恢复下方代码
      await Future.delayed(const Duration(milliseconds: 500));
      return mockWords;
      /*
      // 真实网络请求代码：
      _log.i('正在从远程 API 获取所有单词...');
      final ApiResponse<List<WordModel>> resp = await _networkService.get<List<WordModel>>(
        '/words',
        fromJsonT: (json) {
          if (json is List) {
            return json
                .map((item) => WordModel.fromJson(item as Map<String, dynamic>))
                .toList();
          }
          throw const FormatException('Expected a list for WordModel data');
        },
      );
      if (resp.code == 0 && resp.data != null) {
        return resp.data!;
      } else {
        throw AppException(AppExceptionType.network, resp.message, code: resp.code);
      }
      */
    } on AppException catch (e) {
      _log.e('网络错误或服务器错误: ${e.message}', error: e);
      rethrow;
    } catch (e) {
      _log.e('fetchAllWords 发生未知错误: $e', error: e);
      throw AppException(AppExceptionType.network, '未知错误: ${e.toString()}');
    }
  }

  /// 根据 ID 从远程 API 获取单个单词（MVP阶段直接返回mock数据）
  Future<WordModel> fetchWordById(String wordId) async {
    try {
      _log.i('模拟网络请求：获取单词 (ID: $wordId)...');
      // TODO: MVP阶段用mock数据，后续接入真实接口时可恢复下方代码
      await Future.delayed(const Duration(milliseconds: 300));
      final id = int.tryParse(wordId);
      final word = mockWords.firstWhere(
        (w) => w.id == id,
        orElse: () => throw AppException(AppExceptionType.network, '单词不存在'),
      );
      return word;
      /*
      // 真实网络请求代码：
      _log.i('正在从远程 API 获取单词 (ID: $wordId)...');
      final ApiResponse<WordModel> resp = await _networkService.get<WordModel>(
        '/words/$wordId',
        fromJsonT: (json) => WordModel.fromJson(json as Map<String, dynamic>),
      );
      if (resp.code == 0 && resp.data != null) {
        return resp.data!;
      } else {
        throw AppException(AppExceptionType.network, resp.message, code: resp.code);
      }
      */
    } on AppException catch (e) {
      _log.e('网络错误或服务器错误 (ID: $wordId): ${e.message}', error: e);
      rethrow;
    } catch (e) {
      _log.e('fetchWordById 发生未知错误 (ID: $wordId): $e', error: e);
      throw AppException(AppExceptionType.network, '未知错误: ${e.toString()}');
    }
  }

  /// 提交用户学习进度到远程服务器
  /// [learningData] 包含用户学习进度信息的 Map 或 DTO 对象
  /// 返回 true 表示提交成功，遇到 code!=0 抛异常
  Future<bool> submitLearningProgress(Map<String, dynamic> learningData) async {
    try {
      _log.i('正在提交学习进度到远程 API...');
      final ApiResponse<bool> resp = await _networkService.post<bool>(
        '/learning/progress',
        data: learningData,
        fromJsonT: (json) => true, // 假设成功响应即代表提交成功，根据实际后端返回调整
      );
      if (resp.code == 0) {
        return true;
      } else {
        throw AppException(
          AppExceptionType.network,
          resp.message,
          code: resp.code,
        );
      }
    } on AppException catch (e) {
      _log.e('网络错误或服务器错误 (提交学习进度): ${e.message}', error: e);
      rethrow;
    } catch (e) {
      _log.e('submitLearningProgress 发生未知错误: $e', error: e);
      throw AppException(AppExceptionType.network, '未知错误: ${e.toString()}');
    }
  }

  /// 下载新的单词包 (示例)
  /// [packageName] 单词包的名称或 ID
  /// 只返回 List<WordModel>，遇到 code!=0 抛异常
  Future<List<WordModel>> downloadWordPackage(String packageName) async {
    try {
      _log.i('正在下载单词包 $packageName...');
      final ApiResponse<List<WordModel>>
      resp = await _networkService.get<List<WordModel>>(
        '/word-packages/$packageName/download',
        fromJsonT: (json) {
          if (json is List) {
            return json
                .map((item) => WordModel.fromJson(item as Map<String, dynamic>))
                .toList();
          }
          throw const FormatException('Expected a list for WordModel data');
        },
      );
      if (resp.code == 0 && resp.data != null) {
        return resp.data!;
      } else {
        throw AppException(
          AppExceptionType.network,
          resp.message,
          code: resp.code,
        );
      }
    } on AppException catch (e) {
      _log.e('网络错误或服务器错误 (下载单词包): ${e.message}', error: e);
      rethrow;
    } catch (e) {
      _log.e('downloadWordPackage 发生未知错误: $e', error: e);
      throw AppException(AppExceptionType.network, '未知错误: ${e.toString()}');
    }
  }
}
