import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/app/exceptions/app_exception.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/review_item_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/word_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/user_word_book_repository.dart';
import 'learning_state_service.dart';

/// 学习质量服务
///
/// 职责：
/// - 计算单词的学习质量评分（0-100分）
/// - 将质量评分转换为SM-2算法的质量分数（1-5分）
/// - 更新单词的复习项，调整复习间隔和难度
/// - 处理间隔重复学习算法相关的逻辑
///
/// 架构说明：
/// 本服务专注于学习质量的计算和复习项的更新，通过依赖注入获取所需的仓库和服务，
/// 使用统一的异常处理模式，确保质量计算的准确性和复习项更新的可靠性。
/// 采用基于认知状态和错误次数的质量计算算法，符合间隔重复学习的理论基础。
class LearningQualityService extends GetxService {
  // ==================== 依赖注入 ====================
  /// 学习状态服务 - 管理学习过程中的状态数据
  ///
  /// 调用位置：
  /// - calculateLearningQuality() 中调用 getWordLearningState() 获取单词学习状态
  ///
  /// 用途：获取单词的认知状态和错误次数，用于质量计算
  final LearningStateService _stateService;

  /// 复习项仓库 - 处理复习项的数据库操作
  ///
  /// 调用位置：
  /// - updateWordReviewItem() 中调用 processLearningResult() 更新复习计划
  ///
  /// 用途：根据学习质量更新单词的复习间隔和难度系数
  final ReviewItemRepository _reviewItemRepository;

  /// 单词仓库 - 处理单词数据的获取
  ///
  /// 调用位置：
  /// - updateWordReviewItem() 中调用 getWordById() 获取单词对象
  ///
  /// 用途：验证单词ID的有效性并获取单词详细信息
  final WordRepository _wordRepository;

  /// 用户单词本仓库 - 处理用户单词本数据
  ///
  /// 调用位置：
  /// - updateWordReviewItem() 中调用 getUserWordBookById() 获取单词本对象
  ///
  /// 用途：验证单词本ID的有效性并获取单词本详细信息
  final UserWordBookRepository _userWordBookRepository;

  /// 日志服务 - 记录质量计算和更新操作的日志
  ///
  /// 调用位置：
  /// - 所有方法中用于记录操作日志、错误信息和调试信息
  ///
  /// 用途：记录质量计算过程和复习项更新的成功/失败状态
  final LogService _log;

  // ==================== 构造函数 ====================
  /// 构造函数 - 依赖注入
  ///
  /// 通过构造函数注入所有必要的仓库和服务实例，确保依赖关系清晰
  /// 并提高代码的可测试性和可维护性
  ///
  /// 参数：
  /// - [stateService] 学习状态服务实例
  /// - [reviewItemRepository] 复习项仓库实例
  /// - [wordRepository] 单词仓库实例
  /// - [userWordBookRepository] 用户单词本仓库实例
  /// - [log] 日志服务实例
  ///
  /// 注册位置：lib/modules/learning/bindings/learning_binding.dart
  LearningQualityService({
    required LearningStateService stateService,
    required ReviewItemRepository reviewItemRepository,
    required WordRepository wordRepository,
    required UserWordBookRepository userWordBookRepository,
    required LogService log,
  }) : _stateService = stateService,
       _reviewItemRepository = reviewItemRepository,
       _wordRepository = wordRepository,
       _userWordBookRepository = userWordBookRepository,
       _log = log;

  // ==================== 私有通用方法 ====================
  /// 通用异常处理方法
  ///
  /// 提供统一的异常处理逻辑，减少重复代码，确保所有操作的一致性
  ///
  /// 参数：
  /// - [operationName] 操作名称，用于日志记录和错误信息
  /// - [operation] 要执行的操作函数
  /// - [defaultValue] 操作失败时的默认返回值
  ///
  /// 异常处理逻辑：
  /// - 如果是AppException，记录详细错误信息并重新抛出
  /// - 如果是其他异常，记录错误信息并返回默认值
  /// - 保留原始异常信息便于调试
  ///
  /// 返回值：T - 操作函数的返回值或默认值
  T _handleOperation<T>(
    String operationName,
    T Function() operation,
    T defaultValue,
  ) {
    try {
      return operation();
    } catch (e) {
      if (e is AppException) {
        _log.e('$operationName失败: ${e.message}', error: e);
        rethrow;
      } else {
        _log.e('$operationName失败', error: e);
        return defaultValue;
      }
    }
  }

  /// 通用异步操作异常处理方法
  ///
  /// 提供统一的异步操作异常处理逻辑
  ///
  /// 参数：
  /// - [operationName] 操作名称，用于日志记录和错误信息
  /// - [operation] 要执行的异步操作函数
  ///
  /// 异常处理逻辑：
  /// - 记录操作失败的详细信息
  /// - 不重新抛出异常，避免影响主流程
  ///
  /// 返回值：无返回值（void）
  Future<void> _handleAsyncOperation(
    String operationName,
    Future<void> Function() operation,
  ) async {
    try {
      await operation();
    } catch (e) {
      _log.e('$operationName失败', error: e);
      // 不重新抛出异常，避免影响主流程
    }
  }

  // ==================== 质量计算方法 ====================
  /// 计算单词的学习质量
  ///
  /// 根据单词的认知状态和错误次数计算学习质量评分
  ///
  /// 调用位置：
  /// - updateWordReviewItem() 中调用，用于计算复习质量评分
  ///
  /// 业务逻辑：
  /// 1. 从状态服务获取单词的学习状态
  /// 2. 根据认知状态设置基础分数（认识80分，不认识40分）
  /// 3. 根据错误次数扣分（每错一次扣10分，最多扣40分）
  /// 4. 计算最终分数并确保不低于0分
  ///
  /// 质量计算算法：
  /// - 基础分数：isRecognized ? 80 : 40
  /// - 错误惩罚：errorCount * 10（最多40分）
  /// - 最终分数：baseScore - errorPenalty（最低0分）
  ///
  /// 参数：
  /// - [wordId] 单词ID，用于获取学习状态
  ///
  /// 异常处理：
  /// - 使用通用异常处理方法，出错时返回0分
  /// - 记录详细的计算过程和结果
  ///
  /// 返回值：int - 学习质量分数（0-100）
  int calculateLearningQuality(int wordId) {
    return _handleOperation('计算学习质量', () {
      final state = _stateService.getWordLearningState(wordId);

      // 基础分数：认识为80分，不认识为40分
      int baseScore = state.isRecognized ? 80 : 40;

      // 根据错误次数扣分，每错一次扣10分，最多扣40分
      int errorPenalty = state.errorCount * 10;
      errorPenalty = errorPenalty > 40 ? 40 : errorPenalty;

      // 最终分数
      int finalScore = baseScore - errorPenalty;
      finalScore = finalScore < 0 ? 0 : finalScore;

      _log.d(
        '单词 $wordId 学习质量: $finalScore (认识: ${state.isRecognized}, 错误: ${state.errorCount})',
      );
      return finalScore;
    }, 0); // 出错时返回最低分
  }

  // ==================== 复习项更新方法 ====================
  /// 更新单个单词的复习项
  ///
  /// 根据单词的学习质量更新其复习计划，调整复习间隔和难度系数
  ///
  /// 调用位置：
  /// - learning_controller.dart:420 - 学习控制器中调用，更新单词复习项
  ///
  /// 业务逻辑：
  /// 1. 验证单词本ID的有效性（修复空指针风险）
  /// 2. 获取单词和单词本的详细信息
  /// 3. 计算单词的学习质量评分
  /// 4. 将质量评分转换为SM-2算法的质量分数（1-5分）
  /// 5. 调用仓库层更新复习计划
  ///
  /// 质量分数转换规则：
  /// - 90-100分 → 5分（优秀：完全掌握）
  /// - 75-89分 → 4分（良好：记得清楚）
  /// - 60-74分 → 3分（一般：有些困难）
  /// - 40-59分 → 2分（较差：勉强记住）
  /// - 0-39分 → 1分（很差：完全不记得）
  ///
  /// 参数：
  /// - [wordId] 单词ID，用于获取单词信息和计算质量
  /// - [userWordBookId] 用户单词本ID，可能为null需要验证
  ///
  /// 🔧 重要修复：
  /// - 添加了userWordBookId的空值检查，避免空指针异常
  /// - 使用通用异常处理方法，确保异常处理的一致性
  ///
  /// 异常处理：
  /// - 验证参数有效性，无效时记录警告并返回
  /// - 使用通用异步异常处理方法，不影响主流程
  ///
  /// 返回值：无返回值（void）
  Future<void> updateWordReviewItem(int wordId, int? userWordBookId) async {
    await _handleAsyncOperation('更新单词复习项', () async {
      _log.i('更新单词复习项: $wordId');

      // 🔑 重要修复：添加空值检查，避免空指针异常
      if (userWordBookId == null) {
        _log.w('单词本ID为空，跳过更新复习项: $wordId');
        return;
      }

      // 获取单词信息
      final word = await _wordRepository.getWordById(wordId);
      if (word == null) {
        _log.w('找不到单词: $wordId，跳过更新复习项');
        return;
      }

      // 获取单词本信息
      final userWordBook = await _userWordBookRepository.getUserWordBookById(
        userWordBookId, // 现在可以安全使用，已经验证非空
      );
      if (userWordBook == null) {
        _log.w('找不到单词本: $userWordBookId，跳过更新复习项');
        return;
      }

      // 根据学习质量计算复习质量评分（1-5分）
      final quality = calculateLearningQuality(wordId);

      // 更精细的质量分数转换，符合SM-2算法要求
      int qualityScore;
      if (quality >= 90) {
        qualityScore = 5; // 优秀：完全掌握
      } else if (quality >= 75) {
        qualityScore = 4; // 良好：记得清楚
      } else if (quality >= 60) {
        qualityScore = 3; // 一般：有些困难
      } else if (quality >= 40) {
        qualityScore = 2; // 较差：勉强记住
      } else {
        qualityScore = 1; // 很差：完全不记得
      }

      // 更新复习项，根据学习质量调整复习计划
      await _reviewItemRepository.processLearningResult(
        word,
        userWordBook,
        qualityScore,
      );

      _log.i('已更新单词 $wordId 的复习计划，质量评分: $qualityScore');
    });
  }
}
