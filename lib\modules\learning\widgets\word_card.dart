import 'package:flutter/material.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_model.dart';

/// 单词卡片组件
///
/// 显示单词、音标和发音按钮
class WordCard extends StatelessWidget {
  final WordModel word;
  final VoidCallback? onPlayAudio;

  const WordCard({super.key, required this.word, this.onPlayAudio});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 单词和音标
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        word.spelling,
                        style: const TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '/${word.phonetic}/',
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.grey[700],
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ),
                ),

                // 发音按钮
                if (word.audioUrl != null)
                  IconButton(
                    onPressed: onPlayAudio,
                    icon: const Icon(Icons.volume_up),
                    iconSize: 32,
                    color: Theme.of(context).primaryColor,
                    tooltip: '播放发音',
                  ),
              ],
            ),

            // 词性和释义
            const SizedBox(height: 16),
            Text(word.definition, style: const TextStyle(fontSize: 18)),

            // 标签
            if (word.tags != null && word.tags!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 16.0),
                child: Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children:
                      word.tags!.map((tag) {
                        return Chip(
                          label: Text(tag),
                          backgroundColor: Colors.blue[50],
                          labelStyle: TextStyle(color: Colors.blue[800]),
                        );
                      }).toList(),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
