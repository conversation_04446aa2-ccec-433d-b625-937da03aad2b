# repositories 目录说明文档

## 目录职责

`repositories` 目录实现了应用的仓储层，是数据层中的核心组件。仓储层提供统一的数据访问接口给上层（Controller/Service），抽象数据操作的业务逻辑，决定数据从哪里来（网络/本地/缓存）。它是业务逻辑和底层数据源之间的桥梁，屏蔽了数据获取细节。

## 目录结构

```
repositories/
├── base_repository.dart  # 仓储层的抽象基类/接口
├── user_repository.dart  # 用户数据仓储（接口及实现）
└── word_repository.dart  # 单词数据仓储（接口及实现）
```

## 设计理念

- **抽象与实现分离**：定义接口（抽象类）与实现分离，便于测试和替换
- **单一职责**：每个仓储负责特定业务领域的数据操作
- **数据协调**：协调多个数据源，如网络API、本地数据库、内存缓存
- **业务逻辑处理**：处理数据缓存策略、数据组合和转换等业务逻辑

## 主要文件说明

### base_repository.dart

- **职责**：定义通用数据操作的接口或基类
- **内容**：可能包含通用的CRUD操作、错误处理机制等
- **设计原因**：统一接口，减少重复代码，提供基础功能
- **典型实现**：
  ```dart
  abstract class BaseRepository<T> {
    Future<T> getById(int id);
    Future<List<T>> getAll();
    Future<T> create(T item);
    Future<T> update(T item);
    Future<void> delete(int id);
    // 其他通用方法...
  }
  ```

### user_repository.dart

- **职责**：处理用户相关的数据操作和业务逻辑
- **内容**：用户认证、用户信息获取、个人资料更新等
- **依赖**：`UserApiProvider`（网络请求）、`LocalStorageProvider`（本地存储）等
- **典型方法**：
  - `login(String username, String password)`
  - `getUserProfile(int userId)`
  - `updateUserProfile(UserUpdateDto dto)`

### word_repository.dart

- **职责**：处理单词相关的数据操作和业务逻辑
- **内容**：获取单词列表、更新学习状态、获取详细释义等
- **依赖**：`WordApiProvider`（网络请求）、`WordLocalDataSource`（本地缓存）等
- **典型方法**：
  - `getNewWords(int count)`
  - `getWordsForReview(int count)`
  - `updateWordStatus(int wordId, LearningStatus status)`

## 仓储层的职责划分

仓储层位于数据流的中间层，具有以下关键职责：

1. **数据源选择逻辑**：
   - 决定从网络、缓存还是本地数据库获取数据
   - 实现"缓存优先"、"网络优先"等策略
   - 处理离线模式下的数据访问

2. **数据组合与转换**：
   - 将多个数据源的数据组合成所需的业务对象
   - 转换API响应格式为应用内部使用的模型

3. **缓存管理**：
   - 决定何时更新缓存
   - 处理缓存失效策略
   - 管理内存中的临时数据

4. **业务规则实现**：
   - 实现特定领域的业务规则（如学习算法）
   - 验证数据完整性和一致性

5. **错误处理与转换**：
   - 将技术性错误（HTTP错误、数据库错误）转换为业务友好的错误
   - 实现错误恢复策略

## 与其他层的关系

- **上层关系**：为Controller和Service提供数据访问接口
- **下层关系**：调用多个Provider获取原始数据
- **模型关系**：操作和返回Model对象，而非原始数据

## 易混淆点

1. **Repository vs Provider**：
   - **Repository**：实现业务逻辑和数据协调，关注"要获取什么数据"和"数据怎么处理"
   - **Provider**：实现技术细节，关注"如何从特定数据源获取数据"
   - 例如，`WordRepository` 决定是否需要从网络刷新数据，而 `WordApiProvider` 只负责如何发送API请求

2. **Repository vs Service**：
   - **Repository**：专注于数据获取和处理的业务逻辑
   - **Service**：专注于跨数据实体的复杂业务流程
   - 例如，`WordRepository` 处理单词数据的获取，而 `LearningService` 可能会协调用户数据和单词数据来实现学习算法

## 最佳实践

1. **接口与实现分离**：
   ```dart
   // 定义接口
   abstract class UserRepository {
     Future<User> getUserById(int id);
     // 其他方法...
   }
   
   // 实现接口
   class UserRepositoryImpl implements UserRepository {
     final UserApiProvider _apiProvider;
     final LocalStorageProvider _storageProvider;
     
     UserRepositoryImpl(this._apiProvider, this._storageProvider);
     
     @override
     Future<User> getUserById(int id) async {
       // 实现...
     }
     // 其他方法实现...
   }
   ```

2. **依赖注入**：
   ```dart
   // 在GetX绑定中注册
   class DataBinding extends Bindings {
     @override
     void dependencies() {
       // Provider注册
       Get.lazyPut<UserApiProvider>(() => UserApiProvider(Get.find<DioClient>()));
       Get.lazyPut<LocalStorageProvider>(() => LocalStorageProviderImpl());
       
       // Repository注册
       Get.lazyPut<UserRepository>(() => UserRepositoryImpl(
         Get.find<UserApiProvider>(),
         Get.find<LocalStorageProvider>(),
       ));
     }
   }
   ```

3. **错误处理转换**：
   ```dart
   Future<User> getUserById(int id) async {
     try {
       // 先尝试从缓存获取
       final cachedUser = await _storageProvider.read('user_$id');
       if (cachedUser != null) {
         return User.fromJson(cachedUser);
       }
       
       // 从API获取
       final user = await _apiProvider.getUserById(id);
       
       // 更新缓存
       await _storageProvider.write('user_$id', user.toJson());
       
       return user;
     } on DioError catch (e) {
       // 将技术错误转换为业务错误
       if (e.type == DioErrorType.connectTimeout) {
         throw NetworkException('网络连接超时，请检查网络设置');
       } else if (e.response?.statusCode == 404) {
         throw UserNotFoundException('用户不存在');
       } else {
         throw DataFetchException('获取用户数据失败: ${e.message}');
       }
     } catch (e) {
       // 其他未预期的错误
       throw UnexpectedException('发生未知错误: $e');
     }
   }
   ```

## 代码示例

### 完整的Repository实现

```dart
// lib/data/repositories/word_repository.dart
import 'package:flutter_base_app_template/data/models/word_model.dart';
import 'package:flutter_base_app_template/data/providers/word_api_provider.dart';
import 'package:flutter_base_app_template/data/providers/local_storage_provider.dart';

// 定义接口
abstract class WordRepository {
  Future<List<Word>> getNewWords(int count);
  Future<List<Word>> getWordsForReview(int count);
  Future<Word> getWordDetails(int wordId);
  Future<void> updateLearningStatus(int wordId, bool isLearned);
}

// 实现接口
class WordRepositoryImpl implements WordRepository {
  final WordApiProvider _apiProvider;
  final LocalStorageProvider _storageProvider;
  
  // 可选：内存缓存
  final Map<int, Word> _wordCache = {};
  
  WordRepositoryImpl(this._apiProvider, this._storageProvider);
  
  @override
  Future<List<Word>> getNewWords(int count) async {
    try {
      // 检查是否有网络连接
      final hasNetwork = await _checkNetworkConnection();
      
      if (hasNetwork) {
        // 从API获取新词
        final words = await _apiProvider.fetchNewWords(count);
        
        // 更新缓存
        for (final word in words) {
          _wordCache[word.id] = word;
          await _storageProvider.write('word_${word.id}', word.toJson());
        }
        
        return words;
      } else {
        // 离线模式：从本地存储获取
        final localWords = await _getLocalWords();
        return localWords.where((word) => !word.isLearned).take(count).toList();
      }
    } catch (e) {
      // 错误处理
      throw DataFetchException('获取新词失败: $e');
    }
  }
  
  @override
  Future<List<Word>> getWordsForReview(int count) async {
    // 类似实现...
  }
  
  @override
  Future<Word> getWordDetails(int wordId) async {
    // 实现...
  }
  
  @override
  Future<void> updateLearningStatus(int wordId, bool isLearned) async {
    try {
      // 更新本地缓存
      if (_wordCache.containsKey(wordId)) {
        final updatedWord = _wordCache[wordId]!.copyWith(isLearned: isLearned);
        _wordCache[wordId] = updatedWord;
        await _storageProvider.write('word_$wordId', updatedWord.toJson());
      }
      
      // 异步更新服务器
      await _apiProvider.updateWordStatus(wordId, isLearned);
    } catch (e) {
      // 错误处理
      throw DataUpdateException('更新单词状态失败: $e');
    }
  }
  
  // 辅助方法
  Future<bool> _checkNetworkConnection() async {
    // 实现网络检查逻辑
    return true; // 示例
  }
  
  Future<List<Word>> _getLocalWords() async {
    // 从本地存储获取单词列表
    final wordsData = await _storageProvider.readAll('word_');
    return wordsData.map((data) => Word.fromJson(data)).toList();
  }
}
```

## 相关联文件

- `lib/data/models/*.dart`：Repository操作的数据模型
- `lib/data/providers/*.dart`：Repository调用的底层数据源
- `lib/app/services/*.dart`：使用Repository提供高级服务
- `lib/modules/*/controllers/*.dart`：控制器通过Repository或Service获取数据

## 调试指南

1. **缓存问题**：
   - 检查缓存逻辑是否正确实现
   - 添加缓存失效机制，如TTL（存活时间）
   - 确保缓存在适当时机被更新或清除

2. **数据不一致**：
   - 检查多数据源同步逻辑
   - 实现乐观更新，先更新UI，再异步更新远程数据
   - 添加版本号或时间戳以解决冲突

3. **错误处理**：
   - 确保每个异常都被适当捕获和转换
   - 提供有意义的错误信息
   - 考虑重试机制和降级策略

## 常见问题与解决方案

- **性能问题**：
  - 实现高效的缓存策略
  - 考虑分页加载数据
  - 对频繁访问的数据使用内存缓存

- **离线支持**：
  - 实现离线优先策略
  - 在恢复网络连接时同步本地更改
  - 使用队列管理离线操作

- **测试难点**：
  - 使用依赖注入便于mock
  - 定义清晰的接口
  - 为复杂的业务逻辑编写单元测试 