# views 目录说明文档

## 目录职责

`views` 目录包含 `home` 模块的所有UI界面组件，负责展示首页的视觉内容和用户交互界面。这些视图组件遵循MVVM架构模式，主要负责UI的呈现，而将业务逻辑委托给对应的控制器处理。

## 文件结构

```
views/
├── home_page.dart           # 首页主界面
├── today_page.dart          # 今日学习页面
├── statistics_page.dart     # 学习统计页面
└── notifications_page.dart  # 通知列表页面
```

## 核心文件说明

### `home_page.dart`

`home_page.dart` 是首页模块的主UI界面，负责：

- **底部导航栏**：提供应用的主要导航功能
- **学习卡片**：展示今日学习计划和进度
- **推荐单词**：展示推荐学习的单词列表
- **学习统计**：显示用户的学习统计数据
- **通知中心**：展示系统和学习提醒通知

### `today_page.dart`

`today_page.dart` 是展示今日学习计划和进度的详细页面，包括：

- **学习目标**：今日需要学习的单词数量
- **完成进度**：已学习单词的进度条
- **学习按钮**：快速开始今日学习的入口
- **学习记录**：今日已完成的学习记录

### `statistics_page.dart`

`statistics_page.dart` 展示用户的学习统计数据，包括：

- **学习趋势图**：展示一段时间内的学习情况
- **单词掌握统计**：已掌握单词的数量和比例
- **学习时长统计**：每日或每周的学习时长
- **记忆效果分析**：记忆效果的统计和分析

### `notifications_page.dart`

`notifications_page.dart` 展示系统和学习相关的通知信息，包括：

- **通知列表**：所有通知的列表视图
- **未读标记**：标识未读通知的视觉指示
- **通知分类**：系统通知和学习提醒的分类
- **通知操作**：标记已读、删除等操作

## 实现示例

### `home_page.dart` 实现示例

```dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/home_controller.dart';
import '../../../widgets/learning_card.dart';
import '../../../widgets/word_item.dart';
import '../../../widgets/stats_summary.dart';
import '../../../widgets/notification_badge.dart';

class HomePage extends GetView<HomeController> {
  const HomePage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('首页'),
        actions: [
          // 通知图标，带未读数量标记
          Obx(() => NotificationBadge(
            count: controller.unreadNotifications.value,
            onTap: controller.viewAllNotifications,
          )),
          IconButton(
            icon: Icon(Icons.settings),
            onPressed: controller.viewSettings,
          ),
        ],
      ),
      body: Obx(() {
        // 显示加载状态
        if (controller.isLoading.value) {
          return Center(child: CircularProgressIndicator());
        }
        
        return RefreshIndicator(
          onRefresh: controller.refreshData,
          child: ListView(
            padding: EdgeInsets.all(16),
            children: [
              // 用户欢迎信息
              _buildWelcomeSection(),
              
              SizedBox(height: 24),
              
              // 今日学习卡片
              _buildLearningCard(),
              
              SizedBox(height: 24),
              
              // 推荐单词列表
              _buildRecommendedWords(),
              
              SizedBox(height: 24),
              
              // 学习统计摘要
              _buildStatsSummary(),
            ],
          ),
        );
      }),
      bottomNavigationBar: Obx(() => BottomNavigationBar(
        currentIndex: controller.currentIndex.value,
        onTap: controller.changeTab,
        items: [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: '首页',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.bar_chart),
            label: '统计',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: '我的',
          ),
        ],
      )),
    );
  }
  
  // 用户欢迎信息部分
  Widget _buildWelcomeSection() {
    return Obx(() => Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '你好，${controller.currentUser.value.name}',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8),
        Text(
          controller.isTodayPlanCompleted.value
            ? '今日学习任务已完成！'
            : '今天还有单词等待学习哦！',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey[600],
          ),
        ),
      ],
    ));
  }
  
  // 学习卡片部分
  Widget _buildLearningCard() {
    return Obx(() => LearningCard(
      completedCount: controller.todayStats.value.completedWords,
      totalCount: controller.todayStats.value.totalWords,
      onStartLearning: controller.startLearning,
      isCompleted: controller.isTodayPlanCompleted.value,
    ));
  }
  
  // 推荐单词列表部分
  Widget _buildRecommendedWords() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '推荐单词',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () => Get.toNamed('/word-list/recommended'),
              child: Text('查看更多'),
            ),
          ],
        ),
        SizedBox(height: 12),
        Obx(() => controller.recommendedWords.isEmpty
          ? Center(child: Text('暂无推荐单词'))
          : Column(
              children: controller.recommendedWords
                .map((word) => WordItem(
                  word: word,
                  onTap: () => controller.viewWordDetail(word),
                ))
                .toList(),
            )
        ),
      ],
    );
  }
  
  // 学习统计摘要部分
  Widget _buildStatsSummary() {
    return Obx(() => StatsSummary(
      stats: controller.todayStats.value,
      onViewDetails: () => controller.navigateToStatistics(),
    ));
  }
}
```

### `today_page.dart` 实现示例

```dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/home_controller.dart';
import '../../../widgets/progress_bar.dart';
import '../../../widgets/learning_record_item.dart';

class TodayPage extends GetView<HomeController> {
  const TodayPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('今日学习'),
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return Center(child: CircularProgressIndicator());
        }
        
        return ListView(
          padding: EdgeInsets.all(16),
          children: [
            // 学习目标卡片
            _buildGoalCard(),
            
            SizedBox(height: 24),
            
            // 今日进度
            _buildProgressSection(),
            
            SizedBox(height: 24),
            
            // 学习记录
            _buildLearningRecords(),
          ],
        );
      }),
      bottomNavigationBar: Padding(
        padding: EdgeInsets.all(16),
        child: ElevatedButton(
          onPressed: controller.isTodayPlanCompleted.value
            ? null
            : controller.startLearning,
          child: Text(
            controller.isTodayPlanCompleted.value
              ? '今日学习已完成'
              : '开始学习',
            style: TextStyle(fontSize: 18),
          ),
          style: ElevatedButton.styleFrom(
            padding: EdgeInsets.symmetric(vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ),
    );
  }
  
  // 学习目标卡片
  Widget _buildGoalCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '今日学习目标',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12),
            Obx(() => Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildGoalItem(
                  '新词数量',
                  '${controller.todayStats.value.newWords}',
                  Icons.add_circle_outline,
                ),
                _buildGoalItem(
                  '复习数量',
                  '${controller.todayStats.value.reviewWords}',
                  Icons.refresh,
                ),
                _buildGoalItem(
                  '总计单词',
                  '${controller.todayStats.value.totalWords}',
                  Icons.folder_open,
                ),
              ],
            )),
          ],
        ),
      ),
    );
  }
  
  // 目标项
  Widget _buildGoalItem(String title, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, size: 32, color: Get.theme.primaryColor),
        SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 4),
        Text(
          title,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }
  
  // 进度部分
  Widget _buildProgressSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '今日进度',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16),
        Obx(() => ProgressBar(
          progress: controller.todayStats.value.completedWords / 
                  (controller.todayStats.value.totalWords == 0 ? 1 : controller.todayStats.value.totalWords),
          totalCount: controller.todayStats.value.totalWords,
          currentCount: controller.todayStats.value.completedWords,
          showText: true,
        )),
      ],
    );
  }
  
  // 学习记录部分
  Widget _buildLearningRecords() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '学习记录',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 12),
        // 这里应该从控制器获取学习记录列表，这里使用假数据演示
        LearningRecordItem(
          time: '09:30',
          duration: '15分钟',
          count: 10,
          score: 85,
        ),
        Divider(),
        LearningRecordItem(
          time: '14:45',
          duration: '20分钟',
          count: 15,
          score: 90,
        ),
      ],
    );
  }
}
```

## 设计考量

1. **UI组件结构**：
   - 主页面使用Scaffold作为基本布局
   - 使用可滚动容器（如ListView）确保内容适应不同屏幕尺寸
   - 将复杂UI拆分为多个函数或组件，提高可读性和可维护性

2. **响应式UI**：
   - 使用Obx包装依赖于控制器状态的组件
   - 确保UI能够自动响应状态变化
   - 显示合适的加载状态和空状态

3. **用户体验**：
   - 提供视觉反馈（如加载指示器、进度条）
   - 使用RefreshIndicator支持下拉刷新
   - 采用适当的间距和排版，提高可读性

4. **组件复用**：
   - 将常用UI组件抽取为可复用组件
   - 使用参数化设计，使组件可配置
   - 避免重复代码，提高维护效率

## 与其他目录的关系

- **controllers目录**：视图通过GetX的依赖注入获取控制器
- **binding目录**：确保视图可以访问所需的控制器
- **routes目录**：定义如何导航到这些视图
- **widgets目录**：提供可复用的UI组件

## 最佳实践

1. **关注点分离**：
   - 视图只负责UI展示，不包含业务逻辑
   - 用户交互和事件处理委托给控制器
   - 将复杂UI拆分为多个小部件

2. **性能优化**：
   - 懒加载非立即可见的内容
   - 使用const构造函数标记不变的组件
   - 适当使用缓存避免重复计算

3. **错误处理**：
   - 优雅地处理加载错误和空数据
   - 提供友好的错误提示
   - 实现重试机制

4. **辅助功能**：
   - 使用语义标签提高可访问性
   - 确保适当的对比度
   - 支持屏幕阅读器

## 常见问题与解决方案

1. **布局溢出**：
   - 问题：文本或内容超出容器边界
   - 解决：使用弹性布局、文本溢出处理、可滚动容器

2. **状态不同步**：
   - 问题：UI未反映最新状态
   - 解决：确保所有依赖状态的组件都被Obx包装

3. **性能问题**：
   - 问题：UI更新缓慢或卡顿
   - 解决：减少重建范围，使用缓存和延迟加载

4. **设备适配**：
   - 问题：不同设备上显示异常
   - 解决：使用相对尺寸和MediaQuery，避免硬编码尺寸 