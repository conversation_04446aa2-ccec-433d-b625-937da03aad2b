import 'package:flutter_study_flow_by_myself/data/models/word_book_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_model.dart';

/// 存储所有程序员相关单词本的实际单词数据
/// key 为单词本的 wordBookId，value 为该单词本包含的单词列表（Map形式）
final Map<int, List<Map<String, dynamic>>> _programmerWordsData = {
  // 单词本ID 1001: 基础编程词汇
  1001: [
    {
      'spelling': 'variable',
      'phonetic': '/ˈvɛəriəbl/',
      'definition': '用于存储数据值的命名存储位置。',
      'examples': [
        'Declare a variable to store the user\'s name.',
        'Variables can hold different types of data.',
      ],
      'audioUrl': 'https://example.com/audio/variable.mp3',
      'imageUrl': 'https://placehold.co/150x100/ADD8E6/000000?text=Variable',
      'tags': ['基础', '编程'],
    },
    {
      'spelling': 'function',
      'phonetic': '/ˈfʌŋkʃən/',
      'definition': '一段设计用于执行特定任务的代码块。',
      'examples': [
        'Define a function to calculate the sum.',
        'Functions help organize code.',
      ],
      'audioUrl': 'https://example.com/audio/function.mp3',
      'imageUrl': 'https://placehold.co/150x100/ADD8E6/000000?text=Function',
      'tags': ['基础', '编程'],
    },
    {
      'spelling': 'loop',
      'phonetic': '/luːp/',
      'definition': '一系列指令，在满足特定条件之前重复执行。',
      'examples': [
        'Use a for loop to iterate over the array.',
        'An infinite loop can crash the program.',
      ],
      'audioUrl': 'https://example.com/audio/loop.mp3',
      'imageUrl': 'https://placehold.co/150x100/ADD8E6/000000?text=Loop',
      'tags': ['基础', '编程'],
    },
    {
      'spelling': 'conditional',
      'phonetic': '/kənˈdɪʃənl/',
      'definition': '根据条件判断执行不同操作的语句。',
      'examples': [
        'An if-else statement is a conditional block.',
        'Conditional logic is crucial for decision-making.',
      ],
      'audioUrl': 'https://example.com/audio/conditional.mp3',
      'imageUrl': 'https://placehold.co/150x100/ADD8E6/000000?text=Conditional',
      'tags': ['基础', '编程'],
    },
    {
      'spelling': 'array',
      'phonetic': '/əˈreɪ/',
      'definition': '存储在连续内存位置的项的集合。',
      'examples': [
        'Create an array of numbers.',
        'Access elements by their index in an array.',
      ],
      'audioUrl': 'https://example.com/audio/array.mp3',
      'imageUrl': 'https://placehold.co/150x100/ADD8E6/000000?text=Array',
      'tags': ['基础', '数据结构'],
    },
    {
      'spelling': 'string',
      'phonetic': '/strɪŋ/',
      'definition': '字符序列。',
      'examples': [
        'A string can contain letters, numbers, and symbols.',
        'Concatenate two strings together.',
      ],
      'audioUrl': 'https://example.com/audio/string.mp3',
      'imageUrl': 'https://placehold.co/150x100/ADD8E6/000000?text=String',
      'tags': ['基础', '数据类型'],
    },
    {
      'spelling': 'integer',
      'phonetic': '/ˈɪntɪdʒər/',
      'definition': '不带小数部分的整数。',
      'examples': [
        'The age variable should be an integer.',
        'Integer overflow can cause issues.',
      ],
      'audioUrl': 'https://example.com/audio/integer.mp3',
      'imageUrl': 'https://placehold.co/150x100/ADD8E6/000000?text=Integer',
      'tags': ['基础', '数据类型'],
    },
    {
      'spelling': 'boolean',
      'phonetic': '/ˈbuːliən/',
      'definition': '只有真或假两个值的布尔数据类型。',
      'examples': [
        'The result of a comparison is a boolean value.',
        'Use boolean flags to control program flow.',
      ],
      'audioUrl': 'https://example.com/audio/boolean.mp3',
      'imageUrl': 'https://placehold.co/150x100/ADD8E6/000000?text=Boolean',
      'tags': ['基础', '数据类型'],
    },
    {
      'spelling': 'syntax',
      'phonetic': '/ˈsɪntæks/',
      'definition': '定义编程语言中正确语句结构的规则集。',
      'examples': [
        'A syntax error prevents the code from running.',
        'Learn the correct syntax for each language.',
      ],
      'audioUrl': 'https://example.com/audio/syntax.mp3',
      'imageUrl': 'https://placehold.co/150x100/ADD8E6/000000?text=Syntax',
      'tags': ['基础', '概念'],
    },
    {
      'spelling': 'compiler',
      'phonetic': '/kəmˈpaɪlər/',
      'definition': '将高级语言代码翻译成低级语言的程序。',
      'examples': [
        'The compiler converts source code into executable code.',
        'A strong compiler can optimize code.',
      ],
      'audioUrl': 'https://example.com/audio/compiler.mp3',
      'imageUrl': 'https://placehold.co/150x100/ADD8E6/000000?text=Compiler',
      'tags': ['工具', '概念'],
    },
    {
      'spelling': 'interpreter',
      'phonetic': '/ɪnˈtɜːrprɪtər/',
      'definition': '直接执行编程或脚本语言指令的程序。',
      'examples': [
        'Python is an interpreted language.',
        'Interpreters execute code line by line.',
      ],
      'audioUrl': 'https://example.com/audio/interpreter.mp3',
      'imageUrl': 'https://placehold.co/150x100/ADD8E6/000000?text=Interpreter',
      'tags': ['工具', '概念'],
    },
    {
      'spelling': 'algorithm',
      'phonetic': '/ˈælɡərɪðəm/',
      'definition': '解决特定问题的一系列明确指令。',
      'examples': [
        'Design an efficient algorithm for sorting.',
        'Algorithms are the core of computer science.',
      ],
      'audioUrl': 'https://example.com/audio/algorithm.mp3',
      'imageUrl': 'https://placehold.co/150x100/ADD8E6/000000?text=Algorithm',
      'tags': ['基础', '算法'],
    },
    {
      'spelling': 'bug',
      'phonetic': '/bʌɡ/',
      'definition': '计算机程序或系统中的错误或缺陷。',
      'examples': [
        'We found a bug in the new feature.',
        'Debugging is part of the development process.',
      ],
      'audioUrl': 'https://example.com/audio/bug.mp3',
      'imageUrl': 'https://placehold.co/150x100/ADD8E6/000000?text=Bug',
      'tags': ['开发', '错误'],
    },
    {
      'spelling': 'debug',
      'phonetic': '/ˌdiːˈbʌɡ/',
      'definition': '识别并消除计算机硬件或软件中错误的过程。',
      'examples': [
        'Use a debugger to step through the code.',
        'Debugging can be time-consuming.',
      ],
      'audioUrl': 'https://example.com/audio/debug.mp3',
      'imageUrl': 'https://placehold.co/150x100/ADD8E6/000000?text=Debug',
      'tags': ['开发', '工具'],
    },
    {
      'spelling': 'IDE',
      'phonetic': '/ˌaɪ diː ˈiː/',
      'definition': '为程序员提供软件开发综合设施的应用程序。',
      'examples': [
        'Visual Studio Code is a popular IDE for web development.',
        'An IDE helps with code completion and debugging.',
      ],
      'audioUrl': 'https://example.com/audio/ide.mp3',
      'imageUrl': 'https://placehold.co/150x100/ADD8E6/000000?text=IDE',
      'tags': ['工具', '开发'],
    },
    {
      'spelling': 'framework',
      'phonetic': '/ˈfreɪmwɜːrk/',
      'definition': '用于构建软件平台的标准化概念、实践和标准集。',
      'examples': [
        'Flutter is a UI framework for mobile, web, and desktop.',
        'Choose the right framework for your project.',
      ],
      'audioUrl': 'https://example.com/audio/framework.mp3',
      'imageUrl': 'https://placehold.co/150x100/ADD8E6/000000?text=Framework',
      'tags': ['概念', '架构'],
    },
    {
      'spelling': 'library',
      'phonetic': '/ˈlaɪbrəri/',
      'definition': '计算机程序使用的非易失性资源集合。',
      'examples': [
        'Import a library for mathematical operations.',
        'Libraries provide reusable code.',
      ],
      'audioUrl': 'https://example.com/audio/library.mp3',
      'imageUrl': 'https://placehold.co/150x100/ADD8E6/000000?text=Library',
      'tags': ['概念', '编程'],
    },
    {
      'spelling': 'API',
      'phonetic': '/ˌeɪ piː ˈaɪ/',
      'definition': '一套定义好的规则，使应用程序能够相互通信。',
      'examples': [
        'Use the REST API to fetch data.',
        'An API defines how software components should interact.',
      ],
      'audioUrl': 'https://example.com/audio/api.mp3',
      'imageUrl': 'https://placehold.co/150x100/ADD8E6/000000?text=API',
      'tags': ['网络', '概念'],
    },
    {
      'spelling': 'database',
      'phonetic': '/ˈdeɪtəbeɪs/',
      'definition': '有组织的数据集合。',
      'examples': [
        'Store user information in a database.',
        'SQL databases are widely used.',
      ],
      'audioUrl': 'https://example.com/audio/database.mp3',
      'imageUrl': 'https://placehold.co/150x100/ADD8E6/000000?text=Database',
      'tags': ['数据', '存储'],
    },
    {
      'spelling': 'version control',
      'phonetic': '/ˈvɜːrʒn kənˈtroʊl/',
      'definition': '记录文件或文件集随时间变化，以便以后可以恢复特定版本的系统。',
      'examples': [
        'Git is a popular version control system.',
        'Version control helps teams collaborate.',
      ],
      'audioUrl': 'https://example.com/audio/version_control.mp3',
      'imageUrl':
          'https://placehold.co/150x100/ADD8E6/000000?text=Version+Control',
      'tags': ['工具', '协作'],
    },
  ],
  // 单词本ID 1002: 前端开发词汇
  1002: [
    {
      'spelling': 'HTML',
      'phonetic': '/ˌeɪtʃ tiː em ˈel/',
      'definition': '用于在Web浏览器中显示文档的标准标记语言。',
      'examples': [
        'HTML structures the content of a web page.',
        'Learn HTML to build websites.',
      ],
      'audioUrl': 'https://example.com/audio/html.mp3',
      'imageUrl': 'https://placehold.co/150x100/FFD700/000000?text=HTML',
      'tags': ['前端', 'Web'],
    },
    {
      'spelling': 'CSS',
      'phonetic': '/ˌsiː es ˈes/',
      'definition': '用于描述文档呈现的样式表语言。',
      'examples': [
        'CSS styles the appearance of HTML elements.',
        'Use CSS to make your website beautiful.',
      ],
      'audioUrl': 'https://example.com/audio/css.mp3',
      'imageUrl': 'https://placehold.co/150x100/4682B4/FFFFFF?text=CSS',
      'tags': ['前端', 'Web'],
    },
    {
      'spelling': 'JavaScript',
      'phonetic': '/ˈdʒævəskrɪpt/',
      'definition': 'Web的核心技术之一的编程语言。',
      'examples': [
        'JavaScript adds interactivity to web pages.',
        'Modern web applications heavily rely on JavaScript.',
      ],
      'audioUrl': 'https://example.com/audio/javascript.mp3',
      'imageUrl': 'https://placehold.co/150x100/F0DB4F/000000?text=JS',
      'tags': ['前端', 'Web'],
    },
    {
      'spelling': 'DOM',
      'phonetic': '/dɒm/',
      'definition': 'Web文档的编程接口。',
      'examples': [
        'Manipulate the DOM to change page content.',
        'The DOM represents the page structure.',
      ],
      'audioUrl': 'https://example.com/audio/dom.mp3',
      'imageUrl': 'https://placehold.co/150x100/FF4500/FFFFFF?text=DOM',
      'tags': ['前端', 'Web'],
    },
    {
      'spelling': 'responsive design',
      'phonetic': '/rɪˈspɒnsɪv dɪˈzaɪn/',
      'definition': '使网页在各种设备和屏幕尺寸上都能良好渲染的设计方法。',
      'examples': [
        'Responsive design ensures a good user experience on mobile.',
        'Use media queries for responsive design.',
      ],
      'audioUrl': 'https://example.com/audio/responsive_design.mp3',
      'imageUrl': 'https://placehold.co/150x100/87CEEB/000000?text=Responsive',
      'tags': ['前端', '设计'],
    },
    {
      'spelling': 'frontend framework',
      'phonetic': '/ˈfrʌntˌɛnd ˈfreɪmwɜːrk/',
      'definition': '提供构建Web应用程序基本结构的HTML、CSS和JavaScript文件集合。',
      'examples': [
        'React is a popular frontend framework.',
        'Choosing the right frontend framework is important.',
      ],
      'audioUrl': 'https://example.com/audio/frontend_framework.mp3',
      'imageUrl': 'https://placehold.co/150x100/98FB98/000000?text=Framework',
      'tags': ['前端', '框架'],
    },
    {
      'spelling': 'React',
      'phonetic': '/riːˈækt/',
      'definition': '用于构建用户界面的JavaScript库。',
      'examples': [
        'React uses a component-based architecture.',
        'Learn React to build dynamic web apps.',
      ],
      'audioUrl': 'https://example.com/audio/react.mp3',
      'imageUrl': 'https://placehold.co/150x100/61DAFB/000000?text=React',
      'tags': ['前端', '框架'],
    },
    {
      'spelling': 'Vue.js',
      'phonetic': '/vjuː dʒeɪ es/',
      'definition': '用于构建用户界面和单页应用程序的开源JavaScript框架。',
      'examples': [
        'Vue.js is known for its simplicity.',
        'Many developers choose Vue.js for its ease of use.',
      ],
      'audioUrl': 'https://example.com/audio/vuejs.mp3',
      'imageUrl': 'https://placehold.co/150x100/4FC08D/000000?text=Vue',
      'tags': ['前端', '框架'],
    },
    {
      'spelling': 'Angular',
      'phonetic': '/ˈæŋɡjələr/',
      'definition': '由Google的Angular团队领导的基于TypeScript的开源Web应用程序框架。',
      'examples': [
        'Angular is a comprehensive framework for large applications.',
        'It uses TypeScript for robust development.',
      ],
      'audioUrl': 'https://example.com/audio/angular.mp3',
      'imageUrl': 'https://placehold.co/150x100/DD0031/FFFFFF?text=Angular',
      'tags': ['前端', '框架'],
    },
    {
      'spelling': 'Webpack',
      'phonetic': '/ˈwɛbpæk/',
      'definition': '用于现代JavaScript应用程序的静态模块打包器。',
      'examples': [
        'Webpack bundles all assets into a single file.',
        'It optimizes code for production.',
      ],
      'audioUrl': 'https://example.com/audio/webpack.mp3',
      'imageUrl': 'https://placehold.co/150x100/8ED6FB/000000?text=Webpack',
      'tags': ['工具', '前端'],
    },
    {
      'spelling': 'NPM',
      'phonetic': '/ˌɛn piː ˈem/',
      'definition': 'JavaScript运行时环境Node.js的包管理器。',
      'examples': [
        'Use NPM to install project dependencies.',
        'NPM is essential for Node.js development.',
      ],
      'audioUrl': 'https://example.com/audio/npm.mp3',
      'imageUrl': 'https://placehold.co/150x100/CB3837/FFFFFF?text=NPM',
      'tags': ['工具', '前端'],
    },
    {
      'spelling': 'AJAX',
      'phonetic': '/ˈeɪdʒæks/',
      'definition': '一套Web开发技术，用于创建异步Web应用程序。',
      'examples': [
        'AJAX allows updating parts of a web page without reloading.',
        'It uses XMLHttpRequest for asynchronous requests.',
      ],
      'audioUrl': 'https://example.com/audio/ajax.mp3',
      'imageUrl': 'https://placehold.co/150x100/F0F8FF/000000?text=AJAX',
      'tags': ['网络', '前端'],
    },
    {
      'spelling': 'REST API',
      'phonetic': '/rɛst ˌeɪ piː ˈaɪ/',
      'definition': '一种使用HTTP请求访问和使用数据的应用程序编程接口的架构风格。',
      'examples': [
        'Interact with the backend using REST API calls.',
        'REST APIs are stateless.',
      ],
      'audioUrl': 'https://example.com/audio/rest_api.mp3',
      'imageUrl': 'https://placehold.co/150x100/FF6347/FFFFFF?text=REST',
      'tags': ['网络', 'API'],
    },
    {
      'spelling': 'SPA',
      'phonetic': '/ˌɛs piː ˈeɪ/',
      'definition': '通过动态重写当前网页与用户交互的Web应用程序或网站。',
      'examples': [
        'Gmail is an example of a Single Page Application.',
        'SPAs offer a fluid user experience.',
      ],
      'audioUrl': 'https://example.com/audio/spa.mp3',
      'imageUrl': 'https://placehold.co/150x100/DAA520/000000?text=SPA',
      'tags': ['前端', '架构'],
    },
    {
      'spelling': 'SSR',
      'phonetic': '/ˌɛs ɛs ˈɑːr/',
      'definition': '在服务器上渲染客户端或通用JavaScript应用程序，然后将完全渲染的页面发送到客户端的过程。',
      'examples': [
        'SSR improves initial page load time and SEO.',
        'Next.js supports Server-Side Rendering.',
      ],
      'audioUrl': 'https://example.com/audio/ssr.mp3',
      'imageUrl': 'https://placehold.co/150x100/DDA0DD/000000?text=SSR',
      'tags': ['前端', '架构'],
    },
    {
      'spelling': 'component',
      'phonetic': '/kəmˈpoʊnənt/',
      'definition': '用户界面的独立、可重用部分。',
      'examples': [
        'Build UI with reusable components.',
        'Each component manages its own state.',
      ],
      'audioUrl': 'https://example.com/audio/component.mp3',
      'imageUrl': 'https://placehold.co/150x100/FF69B4/000000?text=Component',
      'tags': ['前端', '概念'],
    },
    {
      'spelling': 'state management',
      'phonetic': '/steɪt ˈmænɪdʒmənt/',
      'definition': '在复杂应用程序中管理UI控件状态的过程。',
      'examples': [
        'Redux is a popular state management library.',
        'Effective state management is crucial for large apps.',
      ],
      'audioUrl': 'https://example.com/audio/state_management.mp3',
      'imageUrl': 'https://placehold.co/150x100/B0C4DE/000000?text=State',
      'tags': ['前端', '概念'],
    },
    {
      'spelling': 'TypeScript',
      'phonetic': '/ˈtaɪpskrɪpt/',
      'definition': 'JavaScript的超集，为语言添加了可选的静态类型。',
      'examples': [
        'TypeScript helps catch errors during development.',
        'Many modern frontend projects use TypeScript.',
      ],
      'audioUrl': 'https://example.com/audio/typescript.mp3',
      'imageUrl': 'https://placehold.co/150x100/007ACC/FFFFFF?text=TS',
      'tags': ['前端', '语言'],
    },
    {
      'spelling': 'bundler',
      'phonetic': '/ˈbʌndlər/',
      'definition': '将所有代码及其依赖项打包成一个JavaScript文件的工具。',
      'examples': [
        'Webpack is a widely used JavaScript bundler.',
        'Bundlers optimize code for deployment.',
      ],
      'audioUrl': 'https://example.com/audio/bundler.mp3',
      'imageUrl': 'https://placehold.co/150x100/F0E68C/000000?text=Bundler',
      'tags': ['工具', '前端'],
    },
    {
      'spelling': 'SEO',
      'phonetic': '/ˌes iː ˈoʊ/',
      'definition': '提高网站或网页从搜索引擎获取流量质量和数量的过程。',
      'examples': [
        'Good SEO practices improve website visibility.',
        'Content optimization is key for SEO.',
      ],
      'audioUrl': 'https://example.com/audio/seo.mp3',
      'imageUrl': 'https://placehold.co/150x100/F4A460/000000?text=SEO',
      'tags': ['前端', '概念'],
    },
  ],
  // 单词本ID 1003: 后端开发词汇
  1003: [
    {
      'spelling': 'server',
      'phonetic': '/ˈsɜːrvər/',
      'definition': '为其他程序或设备提供功能的计算机程序或设备。',
      'examples': [
        'The server hosts the website.',
        'Backend developers work on server-side logic.',
      ],
      'audioUrl': 'https://example.com/audio/server.mp3',
      'imageUrl': 'https://placehold.co/150x100/808080/FFFFFF?text=Server',
      'tags': ['后端', '网络'],
    },
    {
      'spelling': 'database management system',
      'phonetic': '/ˈdeɪtəbeɪs ˈmænɪdʒmənt ˈsɪstəm/',
      'definition': '允许用户定义、创建、维护和控制数据库访问的软件。',
      'examples': [
        'MySQL is a popular database management system.',
        'DBMS ensures data integrity.',
      ],
      'audioUrl': 'https://example.com/audio/dbms.mp3',
      'imageUrl': 'https://placehold.co/150x100/A9A9A9/000000?text=DBMS',
      'tags': ['后端', '数据库'],
    },
    {
      'spelling': 'SQL',
      'phonetic': '/ˌɛs kjuː ˈel/',
      'definition': '用于管理关系型数据库管理系统中数据的领域特定语言。',
      'examples': [
        'Write SQL queries to retrieve data.',
        'SQL is fundamental for relational databases.',
      ],
      'audioUrl': 'https://example.com/audio/sql.mp3',
      'imageUrl': 'https://placehold.co/150x100/4169E1/FFFFFF?text=SQL',
      'tags': ['后端', '数据库'],
    },
    {
      'spelling': 'NoSQL',
      'phonetic': '/ˌnoʊ es kjuː ˈel/',
      'definition': '不遵循传统关系型数据库模型的非关系型数据库管理系统。',
      'examples': [
        'MongoDB is a NoSQL database.',
        'NoSQL databases are good for unstructured data.',
      ],
      'audioUrl': 'https://example.com/audio/nosql.mp3',
      'imageUrl': 'https://placehold.co/150x100/6A5ACD/FFFFFF?text=NoSQL',
      'tags': ['后端', '数据库'],
    },
    {
      'spelling': 'API endpoint',
      'phonetic': '/ˌeɪ piː ˈaɪ ˈɛndpɔɪnt/',
      'definition': '客户端可以访问API的特定URL。',
      'examples': [
        'The /users endpoint returns a list of users.',
        'Each API endpoint performs a specific action.',
      ],
      'audioUrl': 'https://example.com/audio/api_endpoint.mp3',
      'imageUrl': 'https://placehold.co/150x100/FFDAB9/000000?text=Endpoint',
      'tags': ['后端', 'API'],
    },
    {
      'spelling': 'authentication',
      'phonetic': '/ɔːˌθɛntɪˈkeɪʃən/',
      'definition': '验证用户或进程身份的过程。',
      'examples': [
        'Implement authentication to secure your API.',
        'OAuth is a common authentication protocol.',
      ],
      'audioUrl': 'https://example.com/audio/authentication.mp3',
      'imageUrl': 'https://placehold.co/150x100/B0E0E6/000000?text=AuthN',
      'tags': ['后端', '安全'],
    },
    {
      'spelling': 'authorization',
      'phonetic': '/ˌɔːθəraɪˈzeɪʃən/',
      'definition': '确定用户或进程被允许做什么的过程。',
      'examples': [
        'Authorization controls access to resources.',
        'Role-based authorization is widely used.',
      ],
      'audioUrl': 'https://example.com/audio/authorization.mp3',
      'imageUrl': 'https://placehold.co/150x100/ADD8E6/000000?text=AuthZ',
      'tags': ['后端', '安全'],
    },
    {
      'spelling': 'middleware',
      'phonetic': '/ˈmɪdlwɛər/',
      'definition': '为软件应用程序提供操作系统之外服务的软件。',
      'examples': [
        'Express.js uses middleware for request processing.',
        'Middleware can handle logging and error handling.',
      ],
      'audioUrl': 'https://example.com/audio/middleware.mp3',
      'imageUrl': 'https://placehold.co/150x100/DDA0DD/000000?text=Middleware',
      'tags': ['后端', '架构'],
    },
    {
      'spelling': 'ORM',
      'phonetic': '/ˌoʊ ɑːr ˈem/',
      'definition': '一种编程技术，用于使用面向对象编程语言在不兼容的类型系统之间转换数据。',
      'examples': [
        'Use an ORM to interact with the database using objects.',
        'ORM simplifies database operations.',
      ],
      'audioUrl': 'https://example.com/audio/orm.mp3',
      'imageUrl': 'https://placehold.co/150x100/F0E68C/000000?text=ORM',
      'tags': ['后端', '数据库'],
    },
    {
      'spelling': 'load balancing',
      'phonetic': '/loʊd ˈbælənsɪŋ/',
      'definition': '将网络流量均匀分配到多个服务器，以确保没有单个服务器过载。',
      'examples': [
        'Load balancing improves application availability.',
        'Use a load balancer for high-traffic applications.',
      ],
      'audioUrl': 'https://example.com/audio/load_balancing.mp3',
      'imageUrl':
          'https://placehold.co/150x100/98FB98/000000?text=Load+Balancing',
      'tags': ['后端', '架构'],
    },
    {
      'spelling': 'microservices',
      'phonetic': '/ˈmaɪkroʊˌsɜːrvɪsɪz/',
      'definition': '一种将应用程序构建为松散耦合服务集合的架构风格。',
      'examples': [
        'Microservices allow independent deployment of services.',
        'Netflix uses a microservices architecture.',
      ],
      'audioUrl': 'https://example.com/audio/microservices.mp3',
      'imageUrl':
          'https://placehold.co/150x100/B0E0E6/000000?text=Microservices',
      'tags': ['后端', '架构'],
    },
    {
      'spelling': 'containerization',
      'phonetic': '/kənˌteɪnəraɪˈzeɪʃən/',
      'definition': '将软件代码及其所有依赖项打包，使其能够在任何基础设施上统一且一致地运行。',
      'examples': [
        'Containerization simplifies deployment and scaling.',
        'Docker is a popular containerization platform.',
      ],
      'audioUrl': 'https://example.com/audio/containerization.mp3',
      'imageUrl':
          'https://placehold.co/150x100/D8BFD8/000000?text=Containerization',
      'tags': ['DevOps', '架构'],
    },
    {
      'spelling': 'Docker',
      'phonetic': '/ˈdɒkər/',
      'definition': '一套使用操作系统级虚拟化以容器形式交付软件的平台即服务产品。',
      'examples': [
        'Run your application in a Docker container.',
        'Docker simplifies environment setup.',
      ],
      'audioUrl': 'https://example.com/audio/docker.mp3',
      'imageUrl': 'https://placehold.co/150x100/0DB7ED/FFFFFF?text=Docker',
      'tags': ['DevOps', '工具'],
    },
    {
      'spelling': 'Kubernetes',
      'phonetic': '/ˌkuːbərˈnɛtɪs/',
      'definition': '用于自动化计算机应用程序部署、扩展和管理的开源容器编排系统。',
      'examples': [
        'Deploy and manage containers with Kubernetes.',
        'Kubernetes provides powerful orchestration capabilities.',
      ],
      'audioUrl': 'https://example.com/audio/kubernetes.mp3',
      'imageUrl': 'https://placehold.co/150x100/326CE5/FFFFFF?text=K8s',
      'tags': ['DevOps', '工具'],
    },
    {
      'spelling': 'CI/CD',
      'phonetic': '/ˌsiː aɪ siː ˈdiː/',
      'definition': '通过在应用程序开发阶段引入自动化来频繁地向客户交付应用程序的方法。',
      'examples': [
        'Implement a CI/CD pipeline for automated deployments.',
        'CI/CD improves release frequency and reliability.',
      ],
      'audioUrl': 'https://example.com/audio/cicd.mp3',
      'imageUrl': 'https://placehold.co/150x100/FF4500/FFFFFF?text=CI/CD',
      'tags': ['DevOps', '流程'],
    },
    {
      'spelling': 'webhook',
      'phonetic': '/ˈwɛbhʊk/',
      'definition': '通过自定义回调增强或改变网页或Web应用程序行为的方法。',
      'examples': [
        'Use webhooks to receive real-time notifications.',
        'Webhooks are commonly used for integrations.',
      ],
      'audioUrl': 'https://example.com/audio/webhook.mp3',
      'imageUrl': 'https://placehold.co/150x100/FFB6C1/000000?text=Webhook',
      'tags': ['网络', 'API'],
    },
    {
      'spelling': 'cache',
      'phonetic': '/kæʃ/',
      'definition': '存储数据的硬件或软件组件，以便将来对该数据的请求可以更快地得到服务。',
      'examples': [
        'Implement caching to improve application performance.',
        'Redis can be used as a caching layer.',
      ],
      'audioUrl': 'https://example.com/audio/cache.mp3',
      'imageUrl': 'https://placehold.co/150x100/20B2AA/FFFFFF?text=Cache',
      'tags': ['性能', '存储'],
    },
    {
      'spelling': 'queue',
      'phonetic': '/kjuː/',
      'definition': '以先进先出（FIFO）方式存储项的数据结构。',
      'examples': [
        'Use a message queue for asynchronous tasks.',
        'Queues help manage background processes.',
      ],
      'audioUrl': 'https://example.com/audio/queue.mp3',
      'imageUrl': 'https://placehold.co/150x100/8A2BE2/FFFFFF?text=Queue',
      'tags': ['数据结构', '异步'],
    },
    {
      'spelling': 'logging',
      'phonetic': '/ˈlɒɡɪŋ/',
      'definition': '记录软件系统中发生的事件的过程。',
      'examples': [
        'Implement robust logging for debugging and monitoring.',
        'Logs provide insights into application behavior.',
      ],
      'audioUrl': 'https://example.com/audio/logging.mp3',
      'imageUrl': 'https://placehold.co/150x100/BDB76B/000000?text=Logging',
      'tags': ['开发', '运维'],
    },
    {
      'spelling': 'scalability',
      'phonetic': '/ˌskeɪləˈbɪləti/',
      'definition': '系统通过增加资源来处理不断增长的工作量的能力。',
      'examples': [
        'Design your system for high scalability.',
        'Scalability is crucial for growing applications.',
      ],
      'audioUrl': 'https://example.com/audio/scalability.mp3',
      'imageUrl': 'https://placehold.co/150x100/5F9EA0/FFFFFF?text=Scalability',
      'tags': ['架构', '性能'],
    },
  ],
  // 单词本ID 1004: 数据结构与算法词汇
  1004: [
    {
      'spelling': 'data structure',
      'phonetic': '/ˈdeɪtə ˈstrʌktʃər/',
      'definition': '在计算机中组织数据的特定方式，以便可以高效地使用。',
      'examples': [
        'Arrays and linked lists are common data structures.',
        'Choosing the right data structure is important for performance.',
      ],
      'audioUrl': 'https://example.com/audio/data_structure.mp3',
      'imageUrl':
          'https://placehold.co/150x100/FF6347/FFFFFF?text=Data+Structure',
      'tags': ['算法', '基础'],
    },
    {
      'spelling': 'algorithm',
      'phonetic': '/ˈælɡərɪðəm/',
      'definition': '解决特定问题的一系列明确指令。',
      'examples': [
        'Design an efficient algorithm for sorting.',
        'Algorithms are the core of computer science.',
      ],
      'audioUrl': 'https://example.com/audio/algorithm.mp3',
      'imageUrl': 'https://placehold.co/150x100/FFD700/000000?text=Algorithm',
      'tags': ['算法', '基础'],
    },
    {
      'spelling': 'array',
      'phonetic': '/əˈreɪ/',
      'definition': '存储在连续内存位置的项的集合。',
      'examples': [
        'An array is a basic data structure.',
        'Access elements by their index in an array.',
      ],
      'audioUrl': 'https://example.com/audio/array.mp3',
      'imageUrl': 'https://placehold.co/150x100/ADFF2F/000000?text=Array',
      'tags': ['数据结构', '基础'],
    },
    {
      'spelling': 'linked list',
      'phonetic': '/lɪŋkt lɪst/',
      'definition': '数据元素的线性集合，其顺序不由它们在内存中的物理位置决定。',
      'examples': [
        'A linked list consists of nodes, each containing data and a pointer to the next node.',
        'Linked lists are flexible for insertions and deletions.',
      ],
      'audioUrl': 'https://example.com/audio/linked_list.mp3',
      'imageUrl': 'https://placehold.co/150x100/87CEEB/000000?text=Linked+List',
      'tags': ['数据结构', '基础'],
    },
    {
      'spelling': 'stack',
      'phonetic': '/stæk/',
      'definition': '遵循特定操作顺序的线性数据结构（后进先出LIFO）。',
      'examples': [
        'A stack is like a pile of plates.',
        'Push and pop operations are used with a stack.',
      ],
      'audioUrl': 'https://example.com/audio/stack.mp3',
      'imageUrl': 'https://placehold.co/150x100/FFB6C1/000000?text=Stack',
      'tags': ['数据结构', '基础'],
    },
    {
      'spelling': 'queue',
      'phonetic': '/kjuː/',
      'definition': '遵循特定操作顺序的线性数据结构（先进先出FIFO）。',
      'examples': [
        'A queue is like a line at a store.',
        'Enqueue and dequeue operations are used with a queue.',
      ],
      'audioUrl': 'https://example.com/audio/queue.mp3',
      'imageUrl': 'https://placehold.co/150x100/DA70D6/000000?text=Queue',
      'tags': ['数据结构', '基础'],
    },
    {
      'spelling': 'tree',
      'phonetic': '/triː/',
      'definition': '模拟分层树结构的非线性数据结构。',
      'examples': [
        'A binary tree is a common type of tree data structure.',
        'Trees are used to represent hierarchical data.',
      ],
      'audioUrl': 'https://example.com/audio/tree.mp3',
      'imageUrl': 'https://placehold.co/150x100/9ACD32/000000?text=Tree',
      'tags': ['数据结构', '高级'],
    },
    {
      'spelling': 'graph',
      'phonetic': '/ɡræf/',
      'definition': '由节点（顶点）和边组成的非线性数据结构。',
      'examples': [
        'Graphs can represent networks and relationships.',
        'Social networks can be modeled as graphs.',
      ],
      'audioUrl': 'https://example.com/audio/graph.mp3',
      'imageUrl': 'https://placehold.co/150x100/6A5ACD/FFFFFF?text=Graph',
      'tags': ['数据结构', '高级'],
    },
    {
      'spelling': 'hash table',
      'phonetic': '/hæʃ ˈteɪbl/',
      'definition': '实现关联数组抽象数据类型的数据结构。',
      'examples': [
        'Hash tables provide fast lookups.',
        'They use a hash function to map keys to values.',
      ],
      'audioUrl': 'https://example.com/audio/hash_table.mp3',
      'imageUrl': 'https://placehold.co/150x100/4682B4/FFFFFF?text=Hash+Table',
      'tags': ['数据结构', '高级'],
    },
    {
      'spelling': 'sorting',
      'phonetic': '/ˈsɔːrtɪŋ/',
      'definition': '以特定顺序排列数据的过程。',
      'examples': [
        'Bubble sort and quicksort are sorting algorithms.',
        'Sorting data makes it easier to search.',
      ],
      'audioUrl': 'https://example.com/audio/sorting.mp3',
      'imageUrl': 'https://placehold.co/150x100/FF8C00/000000?text=Sorting',
      'tags': ['算法', '操作'],
    },
    {
      'spelling': 'searching',
      'phonetic': '/ˈsɜːrtʃɪŋ/',
      'definition': '在项集合中查找特定项的过程。',
      'examples': [
        'Binary search is an efficient searching algorithm for sorted data.',
        'Linear search checks every element.',
      ],
      'audioUrl': 'https://example.com/audio/searching.mp3',
      'imageUrl': 'https://placehold.co/150x100/DAA520/000000?text=Searching',
      'tags': ['算法', '操作'],
    },
    {
      'spelling': 'time complexity',
      'phonetic': '/taɪm kəmˈplɛksɪti/',
      'definition': '衡量算法运行时间随输入长度变化的量度。',
      'examples': [
        'Analyze the time complexity of an algorithm.',
        'O(n) represents linear time complexity.',
      ],
      'audioUrl': 'https://example.com/audio/time_complexity.mp3',
      'imageUrl':
          'https://placehold.co/150x100/B0C4DE/000000?text=Time+Complexity',
      'tags': ['算法', '分析'],
    },
    {
      'spelling': 'space complexity',
      'phonetic': '/speɪs kəmˈplɛksɪti/',
      'definition': '衡量算法所需工作存储（或内存）量的量度。',
      'examples': [
        'Space complexity is important for memory-constrained environments.',
        'O(1) means constant space complexity.',
      ],
      'audioUrl': 'https://example.com/audio/space_complexity.mp3',
      'imageUrl':
          'https://placehold.co/150x100/DDA0DD/000000?text=Space+Complexity',
      'tags': ['算法', '分析'],
    },
    {
      'spelling': 'Big O notation',
      'phonetic': '/bɪɡ oʊ noʊˈteɪʃən/',
      'definition': '描述函数在参数趋向特定值或无穷大时行为的数学表示法。',
      'examples': [
        'Big O notation describes the worst-case scenario.',
        'Understand Big O to compare algorithm efficiency.',
      ],
      'audioUrl': 'https://example.com/audio/big_o.mp3',
      'imageUrl': 'https://placehold.co/150x100/F0E68C/000000?text=Big+O',
      'tags': ['算法', '分析'],
    },
    {
      'spelling': 'recursion',
      'phonetic': '/rɪˈkɜːrʒən/',
      'definition': '一种解决问题的方法，其中解决方案取决于相同问题的较小实例的解决方案。',
      'examples': [
        'Factorial calculation can be done using recursion.',
        'Be careful with infinite recursion.',
      ],
      'audioUrl': 'https://example.com/audio/recursion.mp3',
      'imageUrl': 'https://placehold.co/150x100/9370DB/FFFFFF?text=Recursion',
      'tags': ['算法', '编程范式'],
    },
    {
      'spelling': 'dynamic programming',
      'phonetic': '/daɪˈnæmɪk ˈproʊɡræmɪŋ/',
      'definition': '通过将复杂问题分解为更简单的子问题来解决复杂问题的方法。',
      'examples': [
        'Dynamic programming is used for optimization problems.',
        'It often involves memoization.',
      ],
      'audioUrl': 'https://example.com/audio/dynamic_programming.mp3',
      'imageUrl':
          'https://placehold.co/150x100/8FBC8F/000000?text=Dynamic+Programming',
      'tags': ['算法', '编程范式'],
    },
    {
      'spelling': 'greedy algorithm',
      'phonetic': '/ˈɡriːdi ˈælɡərɪðəm/',
      'definition': '在每个阶段都做出局部最优选择，希望找到全局最优解的算法。',
      'examples': [
        'A greedy algorithm might not always find the optimal solution.',
        'The change-making problem can be solved with a greedy algorithm.',
      ],
      'audioUrl': 'https://example.com/audio/greedy_algorithm.mp3',
      'imageUrl':
          'https://placehold.co/150x100/CD5C5C/FFFFFF?text=Greedy+Algorithm',
      'tags': ['算法', '编程范式'],
    },
    {
      'spelling': 'divide and conquer',
      'phonetic': '/dɪˈvaɪd ænd ˈkɒŋkər/',
      'definition': '一种算法设计范式，基于递归地将问题分解为两个或更多子问题。',
      'examples': [
        'Merge sort uses a divide and conquer approach.',
        'It breaks down problems into smaller, manageable parts.',
      ],
      'audioUrl': 'https://example.com/audio/divide_and_conquer.mp3',
      'imageUrl':
          'https://placehold.co/150x100/FF7F50/000000?text=Divide+and+Conquer',
      'tags': ['算法', '编程范式'],
    },
    {
      'spelling': 'binary search',
      'phonetic': '/ˈbaɪnəri sɜːrtʃ/',
      'definition': '一种在已排序数组中查找目标值位置的搜索算法。',
      'examples': [
        'Binary search is very efficient for large sorted lists.',
        'It repeatedly divides the search interval in half.',
      ],
      'audioUrl': 'https://example.com/audio/binary_search.mp3',
      'imageUrl':
          'https://placehold.co/150x100/6495ED/FFFFFF?text=Binary+Search',
      'tags': ['算法', '搜索'],
    },
    {
      'spelling': 'graph traversal',
      'phonetic': '/ɡræf trəˈvɜːrsl/',
      'definition': '访问图中每个顶点（检查和/或更新）的过程。',
      'examples': [
        'Depth-First Search (DFS) is a graph traversal algorithm.',
        'Breadth-First Search (BFS) is another common method.',
      ],
      'audioUrl': 'https://example.com/audio/graph_traversal.mp3',
      'imageUrl':
          'https://placehold.co/150x100/40E0D0/000000?text=Graph+Traversal',
      'tags': ['算法', '图'],
    },
  ],
  // 单词本ID 1005: 软件工程与敏捷开发词汇
  1005: [
    {
      'spelling': 'software engineering',
      'phonetic': '/ˈsɒftwɛər ˌɛndʒɪˈnɪərɪŋ/',
      'definition': '对软件的开发、操作和维护采用系统化、规范化、可量化方法。',
      'examples': [
        'Software engineering principles guide robust development.',
        'It focuses on quality, efficiency, and maintainability.',
      ],
      'audioUrl': 'https://example.com/audio/software_engineering.mp3',
      'imageUrl':
          'https://placehold.co/150x100/90EE90/000000?text=Software+Engineering',
      'tags': ['软件工程', '概念'],
    },
    {
      'spelling': 'agile',
      'phonetic': '/ˈædʒaɪl/',
      'definition': '一种软件开发原则集，通过自组织和跨职能团队的协作努力，使需求和解决方案不断演进。',
      'examples': [
        'Agile methodologies emphasize flexibility and collaboration.',
        'Scrum is a popular agile framework.',
      ],
      'audioUrl': 'https://example.com/audio/agile.mp3',
      'imageUrl': 'https://placehold.co/150x100/FFD700/000000?text=Agile',
      'tags': ['敏捷', '方法论'],
    },
    {
      'spelling': 'Scrum',
      'phonetic': '/skrʌm/',
      'definition': '一种用于管理知识工作的敏捷框架，强调软件开发。',
      'examples': [
        'Scrum teams work in short sprints.',
        'Daily stand-ups are part of Scrum.',
      ],
      'audioUrl': 'https://example.com/audio/scrum.mp3',
      'imageUrl': 'https://placehold.co/150x100/FF6347/FFFFFF?text=Scrum',
      'tags': ['敏捷', '框架'],
    },
    {
      'spelling': 'Kanban',
      'phonetic': '/ˈkænbæn/',
      'definition': '一种管理和改进跨人际系统工作的方法。',
      'examples': [
        'Kanban boards visualize work in progress.',
        'It focuses on continuous flow.',
      ],
      'audioUrl': 'https://example.com/audio/kanban.mp3',
      'imageUrl': 'https://placehold.co/150x100/4682B4/FFFFFF?text=Kanban',
      'tags': ['敏捷', '方法论'],
    },
    {
      'spelling': 'sprint',
      'phonetic': '/sprɪnt/',
      'definition': '在Scrum团队中，完成一组工作的时间限制期。',
      'examples': [
        'Each sprint typically lasts 2-4 weeks.',
        'Teams commit to a set of user stories for each sprint.',
      ],
      'audioUrl': 'https://example.com/audio/sprint.mp3',
      'imageUrl': 'https://placehold.co/150x100/98FB98/000000?text=Sprint',
      'tags': ['敏捷', 'Scrum'],
    },
    {
      'spelling': 'backlog',
      'phonetic': '/ˈbæklɒɡ/',
      'definition': '需要为产品完成的功能或任务的优先列表。',
      'examples': [
        'The product backlog contains all desired features.',
        'Teams pull items from the backlog into a sprint.',
      ],
      'audioUrl': 'https://example.com/audio/backlog.mp3',
      'imageUrl': 'https://placehold.co/150x100/DDA0DD/000000?text=Backlog',
      'tags': ['敏捷', 'Scrum'],
    },
    {
      'spelling': 'user story',
      'phonetic': '/ˈjuːzər ˈstɔːri/',
      'definition': '从最终用户角度对功能的简单、非正式描述。',
      'examples': [
        '"As a user, I want to log in so I can access my profile." is a user story.',
        'User stories define value from the user\'s perspective.',
      ],
      'audioUrl': 'https://example.com/audio/user_story.mp3',
      'imageUrl': 'https://placehold.co/150x100/F0E68C/000000?text=User+Story',
      'tags': ['敏捷', '需求'],
    },
    {
      'spelling': 'acceptance criteria',
      'phonetic': '/əkˈsɛptəns kraɪˈtɪəriə/',
      'definition': '用户故事被视为完成必须满足的一组条件。',
      'examples': [
        'Acceptance criteria define the "done" for a user story.',
        'They help ensure quality and completeness.',
      ],
      'audioUrl': 'https://example.com/audio/acceptance_criteria.mp3',
      'imageUrl':
          'https://placehold.co/150x100/B0C4DE/000000?text=Acceptance+Criteria',
      'tags': ['敏捷', '需求'],
    },
    {
      'spelling': 'daily stand-up',
      'phonetic': '/ˈdeɪli ˈstændʌp/',
      'definition': '团队成员同步活动并计划未来24小时的简短日常会议。',
      'examples': [
        'The daily stand-up is a quick sync meeting.',
        'Each team member shares what they did, what they will do, and any impediments.',
      ],
      'audioUrl': 'https://example.com/audio/daily_stand_up.mp3',
      'imageUrl': 'https://placehold.co/150x100/ADD8E6/000000?text=Stand-up',
      'tags': ['敏捷', 'Scrum'],
    },
    {
      'spelling': 'retrospective',
      'phonetic': '/ˌrɛtrəˈspɛktɪv/',
      'definition': '在迭代（冲刺）结束时举行的一次会议，团队讨论哪些做得好，哪些可以改进，以及下一次迭代的承诺。',
      'examples': [
        'The team holds a retrospective to improve their process.',
        'Retrospectives are key for continuous improvement.',
      ],
      'audioUrl': 'https://example.com/audio/retrospective.mp3',
      'imageUrl':
          'https://placehold.co/150x100/FFB6C1/000000?text=Retrospective',
      'tags': ['敏捷', 'Scrum'],
    },
    {
      'spelling': 'refactoring',
      'phonetic': '/ˌriːˈfæktərɪŋ/',
      'definition': '在不改变其外部行为的情况下重构现有计算机代码的过程。',
      'examples': [
        'Refactoring improves code readability and maintainability.',
        'It helps reduce technical debt.',
      ],
      'audioUrl': 'https://example.com/audio/refactoring.mp3',
      'imageUrl': 'https://placehold.co/150x100/90EE90/000000?text=Refactoring',
      'tags': ['软件工程', '代码质量'],
    },
    {
      'spelling': 'technical debt',
      'phonetic': '/ˈtɛknɪkl dɛt/',
      'definition': '由于现在选择简单解决方案而非更好方法而导致的额外返工的隐含成本。',
      'examples': [
        'Ignoring technical debt can slow down future development.',
        'Address technical debt regularly.',
      ],
      'audioUrl': 'https://example.com/audio/technical_debt.mp3',
      'imageUrl':
          'https://placehold.co/150x100/CD5C5C/FFFFFF?text=Technical+Debt',
      'tags': ['软件工程', '概念'],
    },
    {
      'spelling': 'code review',
      'phonetic': '/koʊd rɪˈvjuː/',
      'definition': '对计算机源代码进行系统检查。',
      'examples': [
        'Code reviews improve code quality and knowledge sharing.',
        'Peer code review is a common practice.',
      ],
      'audioUrl': 'https://example.com/audio/code_review.mp3',
      'imageUrl': 'https://placehold.co/150x100/87CEEB/000000?text=Code+Review',
      'tags': ['软件工程', '协作'],
    },
    {
      'spelling': 'unit testing',
      'phonetic': '/ˈjuːnɪt ˈtɛstɪŋ/',
      'definition': '一种软件测试方法，通过测试源代码的单个单元来确定它们是否适合使用。',
      'examples': [
        'Write unit tests for individual functions.',
        'Unit testing helps catch bugs early.',
      ],
      'audioUrl': 'https://example.com/audio/unit_testing.mp3',
      'imageUrl':
          'https://placehold.co/150x100/ADFF2F/000000?text=Unit+Testing',
      'tags': ['测试', '软件工程'],
    },
    {
      'spelling': 'integration testing',
      'phonetic': '/ˌɪntɪˈɡreɪʃən ˈtɛstɪŋ/',
      'definition': '软件测试中的一个阶段，其中将单个软件模块组合并作为一组进行测试。',
      'examples': [
        'Integration testing verifies interactions between modules.',
        'It ensures different parts of the system work together.',
      ],
      'audioUrl': 'https://example.com/audio/integration_testing.mp3',
      'imageUrl':
          'https://placehold.co/150x100/DAA520/000000?text=Integration+Testing',
      'tags': ['测试', '软件工程'],
    },
    {
      'spelling': 'UAT',
      'phonetic': '/ˌjuː eɪ ˈtiː/',
      'definition': '任何软件开发过程的最后阶段，实际用户测试软件以确保其能够处理实际场景中的所需任务。',
      'examples': [
        'UAT ensures the software meets user requirements.',
        'User Acceptance Testing is crucial before release.',
      ],
      'audioUrl': 'https://example.com/audio/uat.mp3',
      'imageUrl': 'https://placehold.co/150x100/FF6347/FFFFFF?text=UAT',
      'tags': ['测试', '软件工程'],
    },
    {
      'spelling': 'MVP',
      'phonetic': '/ˌɛm viː ˈpiː/',
      'definition': '新产品的版本，功能足以满足早期客户并为未来产品开发提供反馈。',
      'examples': [
        'Start with an MVP to validate your idea.',
        'An MVP focuses on core functionality.',
      ],
      'audioUrl': 'https://example.com/audio/mvp.mp3',
      'imageUrl': 'https://placehold.co/150x100/40E0D0/000000?text=MVP',
      'tags': ['产品', '敏捷'],
    },
    {
      'spelling': 'CI/CD pipeline',
      'phonetic': '/ˌsiː aɪ siː ˈdiː ˈpaɪplaɪn/',
      'definition': '自动化构建、测试和部署代码更改的过程。',
      'examples': [
        'A robust CI/CD pipeline automates the release process.',
        'It ensures continuous delivery.',
      ],
      'audioUrl': 'https://example.com/audio/cicd_pipeline.mp3',
      'imageUrl':
          'https://placehold.co/150x100/6495ED/FFFFFF?text=CI/CD+Pipeline',
      'tags': ['DevOps', '自动化'],
    },
    {
      'spelling': 'DevOps',
      'phonetic': '/ˈdɛvɒps/',
      'definition': '结合软件开发（Dev）和信息技术运维（Ops）的一组实践。',
      'examples': [
        'DevOps promotes collaboration between development and operations teams.',
        'It aims to shorten the systems development life cycle.',
      ],
      'audioUrl': 'https://example.com/audio/devops.mp3',
      'imageUrl': 'https://placehold.co/150x100/8A2BE2/FFFFFF?text=DevOps',
      'tags': ['DevOps', '文化'],
    },
    {
      'spelling': 'stakeholder',
      'phonetic': '/ˈsteɪkhoʊldər/',
      'definition': '对组织有兴趣或关注的人、团体或组织。',
      'examples': [
        'Identify all stakeholders at the beginning of the project.',
        'Stakeholders provide requirements and feedback.',
      ],
      'audioUrl': 'https://example.com/audio/stakeholder.mp3',
      'imageUrl': 'https://placehold.co/150x100/FFC0CB/000000?text=Stakeholder',
      'tags': ['项目管理', '概念'],
    },
  ],
  // 单词本ID 2001: 测试单词本A（只有2个单词）
  2001: [
    {
      'spelling': 'test',
      'phonetic': '/tɛst/',
      'definition': '检验软件功能是否正常工作的过程。',
      'examples': [
        'We need to test the new feature before release.',
        'Unit tests help ensure code quality.',
      ],
      'audioUrl': 'https://example.com/audio/test.mp3',
      'imageUrl': 'https://placehold.co/150x100/FF69B4/FFFFFF?text=Test',
      'tags': ['测试', '基础'],
    },
    {
      'spelling': 'code',
      'phonetic': '/koʊd/',
      'definition': '用编程语言编写的指令集合。',
      'examples': [
        'Write clean and readable code.',
        'Code review helps improve code quality.',
      ],
      'audioUrl': 'https://example.com/audio/code.mp3',
      'imageUrl': 'https://placehold.co/150x100/32CD32/000000?text=Code',
      'tags': ['编程', '基础'],
    },
  ],
  // 单词本ID 2002: 测试单词本B（只有2个单词）
  2002: [
    {
      'spelling': 'app',
      'phonetic': '/æp/',
      'definition': '为特定目的设计的计算机程序或软件应用程序。',
      'examples': [
        'Download the mobile app from the store.',
        'This app helps users learn programming.',
      ],
      'audioUrl': 'https://example.com/audio/app.mp3',
      'imageUrl': 'https://placehold.co/150x100/4169E1/FFFFFF?text=App',
      'tags': ['应用', '移动'],
    },
    {
      'spelling': 'user',
      'phonetic': '/ˈjuːzər/',
      'definition': '使用计算机系统或软件应用程序的人。',
      'examples': [
        'The user interface should be intuitive.',
        'Consider user experience when designing features.',
      ],
      'audioUrl': 'https://example.com/audio/user.mp3',
      'imageUrl': 'https://placehold.co/150x100/FF8C00/000000?text=User',
      'tags': ['用户', '设计'],
    },
  ],
};

/// 模拟的7个程序员相关单词本数据（包含2个测试单词本）
final List<WordBookModel> mockWordBooks = [
  WordBookModel(
    name: '基础编程词汇',
    description: '涵盖编程领域最核心、最基础的术语，适合编程初学者。',
    wordCount: _programmerWordsData[1001]!.length,
    isCustom: false,
    wordBookId: 1001, // 使用 wordBookId 作为 mock 数据的唯一标识
  ),
  WordBookModel(
    name: '前端开发词汇',
    description: '专注于Web前端开发领域，包含HTML、CSS、JavaScript及主流框架相关词汇。',
    wordCount: _programmerWordsData[1002]!.length,
    isCustom: false,
    wordBookId: 1002,
  ),
  WordBookModel(
    name: '后端开发词汇',
    description: '涉及服务器、数据库、API设计等后端开发核心概念和技术词汇。',
    wordCount: _programmerWordsData[1003]!.length,
    isCustom: false,
    wordBookId: 1003,
  ),
  WordBookModel(
    name: '数据结构与算法词汇',
    description: '深入理解数据组织方式和问题解决步骤，是提升编程能力的的关键。',
    wordCount: _programmerWordsData[1004]!.length,
    isCustom: false,
    wordBookId: 1004,
  ),
  WordBookModel(
    name: '软件工程与敏捷开发词汇',
    description: '学习软件开发流程、项目管理方法及团队协作中的专业术语。',
    wordCount: _programmerWordsData[1005]!.length,
    isCustom: false,
    wordBookId: 1005,
  ),
  // 测试用的短单词本
  WordBookModel(
    name: '🧪 测试单词本A',
    description: '仅包含2个单词的测试单词本，用于快速测试学习流程的完整性。',
    wordCount: _programmerWordsData[2001]!.length,
    isCustom: false,
    wordBookId: 2001,
  ),
  WordBookModel(
    name: '🧪 测试单词本B',
    description: '另一个包含2个单词的测试单词本，用于验证completion页面跳转逻辑。',
    wordCount: _programmerWordsData[2002]!.length,
    isCustom: false,
    wordBookId: 2002,
  ),
];

/// 为指定单词本ID生成 WordModel 列表
List<WordModel> generateWordsForBook(int wordBookId) {
  final List<Map<String, dynamic>>? wordsData =
      _programmerWordsData[wordBookId];

  if (wordsData == null) {
    throw Exception('未找到ID为$wordBookId的单词本的词汇数据');
  }

  return wordsData
      .map(
        (data) => WordModel(
          spelling: data['spelling']!,
          phonetic: data['phonetic']!,
          definition: data['definition']!,
          examples: List<String>.from(
            data['examples']!,
          ), // 确保 examples 是 List<String>
          audioUrl: data['audioUrl'] as String?,
          imageUrl: data['imageUrl'] as String?,
          tags: data['tags'] != null ? List<String>.from(data['tags']!) : null,
        ),
      )
      .toList();
}

/// 模拟的单词本下载结果
class MockWordBookDownload {
  final WordBookModel wordBook;
  final List<WordModel> words;

  MockWordBookDownload(this.wordBook, this.words);
}

/// 获取模拟的单词本下载数据
MockWordBookDownload getMockWordBookDownload(int wordBookId) {
  // 找到对应的单词本
  final wordBook = mockWordBooks.firstWhere(
    (book) => book.wordBookId == wordBookId,
    orElse: () => throw Exception('未找到ID为$wordBookId的单词本'),
  );

  // 生成该单词本的单词
  final words = generateWordsForBook(wordBookId);

  return MockWordBookDownload(wordBook, words);
}
