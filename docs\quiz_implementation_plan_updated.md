# 测验功能实现计划

## 一、项目背景

当前项目是一个Flutter单词学习应用，目前已实现基本的单词学习和复习功能，并完成了单词与单词本的关联改造。现有的测验功能使用的是预先定义的固定测验题，与单词本内容不匹配，无法有效测试用户对当前学习单词的掌握程度。因此，需要实现一个动态生成测验题的系统，为学习和复习流程提供与单词本相关联的测验题。

## 二、需求概述

1. **主要目标**：
   - 改造现有学习流程和复习流程中的测验环节，使用全新自动生成的测验题
   - 确保测验题与用户当前学习的单词本内容匹配
   - 提供多样化的题型以测试不同层次的单词记忆

2. **MVP范围**：
   - 优先实现能够自动生成的基础题型（如看英选中、看中选英、拼写题、听音选义、例句填空）
   - 暂不实现需要人工或LLM辅助生成的复杂题型
   - 为将来实现独立测验流程和测验偏好设置预留架构空间

3. **后续规划**（非MVP范围）：
   - 独立测验流程（针对错题本或特定单词集）
   - 测验偏好设置（用户选择偏好题型）
   - 复杂题型的添加（如概念理解题、代码片段分析题等）

## 三、测验题型设计

根据用户认知层次模型（从记忆→理解→应用→分析），设计了以下测验题型：

### MVP阶段优先实现的题型：

| 题目名称 | 难度 | 自动生成方式 | 理由 |
|---------|------|------------|------|
| **看英选中** | ⭐ | 显示单词，从4个中文释义中选择正确的一项 | 直观简单，适合记忆初期，便于快速检测 |
| **看中选英** | ⭐⭐ | 显示中文释义，从4个英文单词中选择匹配项 | 倒置认知路径，锻炼中→英回忆能力 |
| **拼写题** | ⭐ | 显示中文释义，用户输入完整单词拼写 | 简单直接的记忆强化，测试拼写掌握度 |
| **听音选义** | ⭐⭐ | 自动生成 | 需要音频支持，测试听力理解能力 |
| **例句填空** | ⭐⭐ | 自动生成 | 测试语境理解能力 |

### 后续可添加的高级题型：

| 题目名称 | 难度 | 生成方式 | 备注 |
|---------|------|---------|------|
| **概念理解题** | ⭐⭐⭐⭐ | LLM生成+人工审核 | 需要预先准备并存储 |
| **代码片段分析题** | ⭐⭐⭐⭐⭐ | 手动设计 | 需要专业设计并存储 |

## 四、数据模型设计

基于多轮讨论，确定了以下数据模型设计方案：

### 1. 选择题模型

选择题类型使用一个统一的模型，通过枚举区分不同类型：

```dart
// 更新现有的ChoiceQuizModel
@Collection()
class ChoiceQuizModel {
  Id id = Isar.autoIncrement;
  late int wordId;
  late String question;
  late List<String> options;
  late int correctOptionIndex;
  String? explanation;
  
  // 新增字段，用于区分不同类型的选择题
  late QuizType type;
}

// 选择题类型枚举
enum QuizType {
  englishToChinese,   // 看英选中
  chineseToEnglish,   // 看中选英
  audioToMeaning,     // 听音选义
  sentenceCompletion, // 例句填空
}
```

### 2. 拼写题模型

拼写题需要单独的模型，因为其结构与选择题完全不同：

```dart
@Collection()
class SpellingQuizModel {
  Id id = Isar.autoIncrement;
  late int wordId;
  late String prompt; // 提示（如中文释义）
  late String correctAnswer; // 正确拼写
  String? explanation;
}
```

### 3. 模型使用策略

- **MVP阶段**：动态生成测验题，不存储到数据库，模型仅作为内存中的数据结构使用
- **后续阶段**：对于需要人工或LLM预先生成的复杂题型，将使用这些模型存储到数据库

## 五、系统设计原则

1. **动态生成vs持久化存储**：
   - 自动生成的基础题型：每次学习/复习时动态生成，不存储到数据库
   - 复杂题型：预先生成并存储在数据库中，需要Isar适配
   - QuizProvider主要服务于未来不能自动生成的复杂题型，MVP阶段可能不会大量使用

2. **架构考量**：
   - 将测验题生成逻辑放在Repository层，符合现有项目结构
   - 采用模块化设计，将不同题型的生成逻辑分离到独立文件中
   - 使用静态工具类处理各种题型生成，避免Repository文件过大
   - QuizRepository负责协调调用各生成器并管理复杂题型

3. **扩展性设计**：
   - 设计支持未来添加更多题型的架构
   - 为测验偏好设置预留接口
   - 确保生成逻辑与UI展示分离，便于各自独立演化

## 六、测验流程设计

1. **测验题准备**：
   - 在学习/复习流程开始时，为每个单词准备多个测验题
   - 每个单词随机生成3种不同类型的测验题
   - 这些测验题存储在内存中，无需持久化

2. **测验题展示**：
   - 当用户进入测验环节时，根据题型动态选择对应的UI组件
   - 使用工厂方法模式简化UI组件的选择逻辑

3. **结果处理**：
   - 测验结果影响单词的学习状态和复习安排
   - 继续使用现有的复习算法处理测验结果

## 七、实施步骤

### 步骤一：创建模块化的QuizRepository结构

**文件**：`lib/data/repositories/quiz/quiz_repository.dart`（修改）
**内容**：
- 添加WordRepository依赖
- 实现协调方法，调用各生成器生成测验题
- 实现为单个单词生成多种测验题的方法

**文件**：`lib/data/repositories/quiz/generators/`（新建目录）
**内容**：
- `base_quiz_generator.dart` - 共享的基础方法
- `distractor_utils.dart` - 干扰项生成工具类
- `english_chinese_generator.dart` - 看英选中生成器
- `chinese_english_generator.dart` - 看中选英生成器
- `spelling_generator.dart` - 拼写题生成器
- `audio_generator.dart` - 听音选义生成器
- `sentence_generator.dart` - 例句填空生成器

### 步骤二：定义测验题型枚举和更新模型

**文件**：`lib/data/models/quiz_type.dart`（新建）
**内容**：定义QuizType枚举类型

**文件**：`lib/data/models/spelling_quiz_model.dart`（新建）
**内容**：定义拼写题模型

**文件**：`lib/data/models/choice_quiz_model.dart`（修改）
**内容**：更新选择题模型，添加type字段

### 步骤三：修改依赖注入配置

**文件**：`lib/app/config/app_initializer.dart`（修改）
**内容**：更新QuizRepository的注入，添加WordRepository依赖

### 步骤四：修改LearningController支持动态生成测验题

**文件**：`lib/modules/learning/controllers/learning_controller.dart`（修改）
**内容**：
- 使用QuizRepository的新方法动态生成与当前学习单词相关的测验题
- 为每个单词生成多种测验题，存储在内存中
- 实现测验索引和状态管理

### 步骤五：修改ReviewQuizController支持动态生成测验题

**文件**：`lib/modules/learning/review/quiz/controllers/review_quiz_controller.dart`（修改）
**内容**：为复习流程集成动态生成测验题的功能

### 步骤六：实现UI组件

**文件**：`lib/modules/learning/quiz/widgets/choice_quiz_widget.dart`（修改或新建）
**内容**：实现选择题UI组件，支持各类选择题题型

**文件**：`lib/modules/learning/quiz/widgets/spelling_quiz_widget.dart`（新建）
**内容**：实现拼写题UI组件

**文件**：`lib/modules/learning/quiz/widgets/quiz_widget_factory.dart`（新建）
**内容**：实现工厂方法，根据题型选择正确的UI组件

## 八、UI组件设计

基于讨论，UI组件设计应简化为：

```
lib/modules/learning/quiz/widgets/
  ├── choice_quiz_widget.dart   # 所有选择题类型共用的组件
  ├── spelling_quiz_widget.dart # 拼写题组件（因为交互完全不同）
  ├── audio_player_widget.dart  # 音频播放组件（用于听音选义）
  ├── sentence_widget.dart      # 例句展示组件（用于例句填空）
  └── quiz_widget_factory.dart  # 简单工厂，根据模型类型选择组件
```

`choice_quiz_widget.dart` 内部根据题型枚举渲染不同的内容，但共享基本的选项列表展示逻辑：

```dart
class ChoiceQuizWidget extends StatelessWidget {
  final ChoiceQuizModel quiz;
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 根据题型渲染不同的题干部分
        _buildQuestionWidget(quiz),
        
        // 选项列表部分（所有选择题共用）
        _buildOptionsWidget(quiz),
      ],
    );
  }
  
  Widget _buildQuestionWidget(ChoiceQuizModel quiz) {
    switch (quiz.type) {
      case QuizType.englishToChinese:
        return Text(quiz.question); // 显示英文单词
      case QuizType.chineseToEnglish:
        return Text(quiz.question); // 显示中文释义
      case QuizType.audioToMeaning:
        return AudioPlayerWidget(word: quiz.question); // 音频播放组件
      case QuizType.sentenceCompletion:
        return SentenceWidget(sentence: quiz.question); // 例句展示组件
      default:
        return Text(quiz.question);
    }
  }
}
```

工厂模式简化为：
```dart
Widget buildQuizWidget(dynamic quizModel) {
  if (quizModel is ChoiceQuizModel) {
    return ChoiceQuizWidget(quiz: quizModel);
  } else if (quizModel is SpellingQuizModel) {
    return SpellingQuizWidget(quiz: quizModel);
  }
  
  throw UnimplementedError('未支持的测验类型');
}
```

## 九、关键算法设计

1. **QuizRepository测验题生成逻辑**：
   ```dart
   // 为单词生成多种测验题
   Future<List<dynamic>> generateQuizzesForWord(WordModel word, List<WordModel> allWords) async {
     try {
       final quizzes = <dynamic>[];
       final availableTypes = QuizType.values.toList()..shuffle(); // 打乱题型顺序
       
       // 为每个单词生成5种不同类型的测验题（或可用题型数量）
       for (int i = 0; i < min(5, availableTypes.length); i++) {
         final quizType = availableTypes[i];
         dynamic quiz;
         
         switch (quizType) {
           case QuizType.englishToChinese:
             quiz = EnglishChineseGenerator.generate(word, allWords);
             break;
           case QuizType.chineseToEnglish:
             quiz = ChineseEnglishGenerator.generate(word, allWords);
             break;
           case QuizType.audioToMeaning:
             quiz = AudioGenerator.generate(word, allWords);
             break;
           case QuizType.sentenceCompletion:
             quiz = SentenceGenerator.generate(word, allWords);
             break;
           case QuizType.spelling:
             quiz = SpellingGenerator.generate(word);
             break;
         }
         
         if (quiz != null) quizzes.add(quiz);
       }
       
       return quizzes;
     } catch (e) {
       _log.e('生成测验题失败: $e', error: e);
       throw AppException.fromException(
         e,
         defaultMessage: '生成测验题失败',
         type: AppExceptionType.unknown,
       );
     }
   }
   ```

2. **题型生成器实现示例**：
   ```dart
   class EnglishChineseGenerator {
     // 私有构造函数，防止实例化
     EnglishChineseGenerator._();
     
     // 静态生成方法
     static ChoiceQuizModel generate(
       WordModel word, 
       List<WordModel> allWords,
     ) {
       // 使用工具类生成干扰项
       final distractors = DistractorUtils.generateDistractors(
         word, allWords, 3, DistractorType.chineseTranslation,
       );
       
       // 构建选项，包含正确答案和干扰项
       final options = [word.translation, ...distractors];
       options.shuffle(); // 随机排序
       
       // 创建测验模型
       return ChoiceQuizModel(
         wordId: word.id,
         question: word.word,
         options: options,
         correctOptionIndex: options.indexOf(word.translation),
         type: QuizType.englishToChinese,
       );
     }
   }
   ```

3. **干扰项生成策略**：
   ```dart
   class DistractorUtils {
     // 生成干扰项
     static List<String> generateDistractors(
       WordModel targetWord, 
       List<WordModel> allWords, 
       int count,
       DistractorType type,
     ) {
       // 实现干扰项选择逻辑
       // - 从词库中选择与目标单词相似但不同的单词作为干扰项
       // - 考虑词性、词形、难度等因素提高干扰项质量
       // - 确保干扰项既有区分度又有一定迷惑性
     }
   }
   ```

## 十、非功能需求考虑

1. **性能优化**：
   - 测验题生成过程应避免影响UI响应
   - 合理使用异步操作和缓存机制

2. **用户体验**：
   - 确保测验界面清晰直观
   - 提供即时反馈，帮助用户理解错误

3. **可维护性**：
   - 测验题生成逻辑与UI展示分离
   - 为后续添加新题型预留扩展点

## 十一、实现模式选择

1. **静态工具类模式 vs 依赖注入模式**：
   - 为了避免Repository文件过大问题，我们采用静态工具类模式拆分生成逻辑
   - 主要层级(Controller/Repository/Provider/Service)仍使用依赖注入模式
   - 生成器类作为内部实现细节使用静态工具类模式

2. **静态工具类特点**：
   - 无状态，纯功能性，不需要实例化
   - 代码简洁直观，减少样板代码
   - 适合封装特定功能的算法逻辑
   - 作为Repository层的内部实现细节

3. **目录结构设计**：
   - 使用子目录组织相关功能，保持项目结构清晰
   - 保持现有的架构边界不变，只是在内部实现上进行优化
   - 便于后续扩展更多题型

## 十二、后续发展规划

1. **独立测验模块**：
   - 单独的测验流程，独立于学习和复习流程
   - 支持针对错题本或特定单词集的测验

2. **用户偏好设置**：
   - 允许用户选择偏好的测验题型
   - 提供测验难度调整选项

3. **测验数据分析**：
   - 收集用户在不同题型上的表现数据
   - 提供学习建议和针对性训练