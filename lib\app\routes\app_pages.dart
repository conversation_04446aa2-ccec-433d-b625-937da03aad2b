// ignore_for_file: constant_identifier_names

import 'package:flutter_study_flow_by_myself/modules/home/<USER>/home_binding.dart';
import 'package:flutter_study_flow_by_myself/modules/home/<USER>/home_page.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/routes/learning_pages.dart';
import 'package:flutter_study_flow_by_myself/modules/main/bindings/main_binding.dart';
import 'package:flutter_study_flow_by_myself/modules/main/views/main_page.dart';
import 'package:flutter_study_flow_by_myself/modules/profile/views/profile_page.dart';
import 'package:flutter_study_flow_by_myself/modules/statistics/views/statistics_page.dart';
import 'package:flutter_study_flow_by_myself/modules/vocabulary/bindings/word_book_detail_binding.dart';
import 'package:flutter_study_flow_by_myself/modules/vocabulary/views/vocabulary_page.dart';
import 'package:flutter_study_flow_by_myself/modules/vocabulary/views/word_book_detail_page.dart';
import 'package:get/get.dart';

part 'app_routes.dart';

class AppPages {
  AppPages._();

  static const INITIAL = AppRoutes.MAIN;

  static final routes = [
    GetPage(
      name: AppRoutes.MAIN,
      page: () => const MainPage(),
      binding: MainBinding(),
    ),
    GetPage(
      name: AppRoutes.HOME,
      page: () => const HomePage(),
      binding: HomeBinding(),
    ),
    // 各功能页面（保留独立访问能力）虽然可能不需要
    GetPage(
      name: AppRoutes.VOCABULARY,
      page: () => const VocabularyPage(),
      // 不需要单独的VocabularyBinding，因为Controller已在MainBinding中注册
      binding: MainBinding(),
    ),
    GetPage(
      name: AppRoutes.WORD_BOOK_DETAIL,
      page: () => const WordBookDetailPage(),
      binding: WordBookDetailBinding(),
    ),
    GetPage(name: AppRoutes.STATISTICS, page: () => const StatisticsPage()),
    GetPage(name: AppRoutes.PROFILE, page: () => const ProfilePage()),
    // 保持现有的学习和复习模块路由
    ...learningPages,
  ];
}
