import 'package:flutter_study_flow_by_myself/app/services/isar_service.dart';
import '../../models/learning_session.dart';
import 'package:isar/isar.dart'; // 引入 Id 类型
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/app/exceptions/app_exception.dart';

/// 职责：与本地数据库（通过 IsarService）交互，处理与 LearningSession 相关的本地数据操作。
/// 这里的操作是针对具体业务实体的（如 LearningSession），而不是 IsarService 中的通用泛型操作。
/// 它应该包含所有与学习会话相关的本地数据业务逻辑，例如：
/// - 获取所有学习会话
/// - 根据ID获取学习会话
/// - 保存/批量保存学习会话
/// - 删除/批量删除学习会话
/// - 清空所有学习会话
/// - 查询学习会话数量
/// - 根据用户单词本ID获取学习会话
class LearningSessionLocalProvider {
  final IsarService _isarService;
  final LogService _log;
  LearningSessionLocalProvider({
    required IsarService isarService,
    required LogService log,
  }) : _isarService = isarService,
       _log = log;

  /// 获取所有学习会话
  Future<List<LearningSession>> getAllSessions() async {
    try {
      return await _isarService.getAll<LearningSession>();
    } catch (e) {
      _log.e('获取所有学习会话失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：获取所有学习会话失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 根据ID获取单个学习会话
  Future<LearningSession?> getSessionById(Id sessionId) async {
    try {
      return await _isarService.get<LearningSession>(sessionId);
    } catch (e) {
      _log.e('根据ID获取学习会话失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：根据ID获取学习会话失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 保存单个学习会话
  Future<Id> saveSession(LearningSession session) async {
    try {
      return await _isarService.put<LearningSession>(session);
    } catch (e) {
      _log.e('保存学习会话失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：保存学习会话失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 批量保存学习会话
  Future<List<Id>> saveAllSessions(List<LearningSession> sessions) async {
    try {
      return await _isarService.putAll<LearningSession>(sessions);
    } catch (e) {
      _log.e('批量保存学习会话失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：批量保存学习会话失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 根据ID删除单个学习会话
  Future<bool> deleteSessionById(Id sessionId) async {
    try {
      return await _isarService.delete<LearningSession>(sessionId);
    } catch (e) {
      _log.e('删除学习会话失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：删除学习会话失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 批量删除学习会话
  Future<int> deleteSessionsByIds(List<Id> sessionIds) async {
    try {
      return await _isarService.deleteByIds<LearningSession>(sessionIds);
    } catch (e) {
      _log.e('批量删除学习会话失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：批量删除学习会话失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 清空所有学习会话
  Future<void> clearAllSessions() async {
    try {
      await _isarService.clearCollection<LearningSession>();
    } catch (e) {
      _log.e('清空所有学习会话失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：清空所有学习会话失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 查询学习会话数量
  Future<int> countSessions() async {
    try {
      return await _isarService.count<LearningSession>();
    } catch (e) {
      _log.e('查询学习会话数量失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：查询学习会话数量失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 获取特定用户单词本的所有学习会话
  ///
  /// [userWordBookId] 用户单词本ID
  /// 返回该用户单词本下的所有学习会话，按开始时间倒序排列
  Future<List<LearningSession>> getSessionsByUserWordBookId(
    int userWordBookId,
  ) async {
    try {
      return await _isarService.isar.learningSessions
          .filter()
          .userWordBookIdEqualTo(userWordBookId)
          .sortByStartTimeDesc()
          .findAll();
    } catch (e) {
      _log.e('获取用户单词本学习会话失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：获取用户单词本学习会话失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 获取特定用户单词本的最新学习会话
  ///
  /// [userWordBookId] 用户单词本ID
  /// 返回该用户单词本下的最新一次学习会话，如果不存在则返回null
  Future<LearningSession?> getLatestSessionByUserWordBookId(
    int userWordBookId,
  ) async {
    try {
      return await _isarService.isar.learningSessions
          .filter()
          .userWordBookIdEqualTo(userWordBookId)
          .sortByStartTimeDesc()
          .findFirst();
    } catch (e) {
      _log.e('获取用户单词本最新学习会话失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：获取用户单词本最新学习会话失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 获取特定用户单词本的最新未完成学习会话
  ///
  /// [userWordBookId] 用户单词本ID
  /// 返回该用户单词本下的最新一次未完成的学习会话，如果不存在则返回null
  Future<LearningSession?> getLatestUnfinishedSession(
    int userWordBookId,
  ) async {
    try {
      return await _isarService.isar.learningSessions
          .filter()
          .userWordBookIdEqualTo(userWordBookId)
          .endTimeIsNull() // 未完成的会话没有结束时间
          .sortByStartTimeDesc()
          .findFirst();
    } catch (e) {
      _log.e('获取用户单词本最新未完成学习会话失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：获取用户单词本最新未完成学习会话失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 获取特定用户单词本的学习会话数量
  ///
  /// [userWordBookId] 用户单词本ID
  /// 返回该用户单词本下的学习会话总数
  Future<int> countSessionsByUserWordBookId(int userWordBookId) async {
    try {
      return await _isarService.isar.learningSessions
          .filter()
          .userWordBookIdEqualTo(userWordBookId)
          .count();
    } catch (e) {
      _log.e('获取用户单词本学习会话数量失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：获取用户单词本学习会话数量失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 获取特定用户单词本的学习统计数据
  ///
  /// [userWordBookId] 用户单词本ID
  /// 返回包含总学习时间、总学习单词数等统计信息的Map
  Future<Map<String, dynamic>> getStatisticsByUserWordBookId(
    int userWordBookId,
  ) async {
    try {
      final sessions = await getSessionsByUserWordBookId(userWordBookId);

      int totalLearningMinutes = 0;
      int totalLearningWords = 0;

      // 计算总学习时间（分钟）和总学习单词数
      for (final session in sessions) {
        if (session.endTime != null) {
          // 计算学习时间（分钟）
          final duration = session.endTime!.difference(session.startTime);
          totalLearningMinutes += duration.inMinutes;
        }

        // 计算学习单词数
        // 如果在最终测验阶段，所有单词都已经学习过
        if (session.isInFinalQuiz) {
          totalLearningWords += session.wordIds.length;
        } else {
          // 如果在学习阶段，已学习的单词数为当前索引
          totalLearningWords += session.currentWordIndex;
        }
      }

      // 计算总学习天数（按日期去重）
      final uniqueDates = <String>{};
      for (final session in sessions) {
        final dateString =
            "${session.startTime.year}-${session.startTime.month}-${session.startTime.day}";
        uniqueDates.add(dateString);
      }

      return {
        'totalSessions': sessions.length,
        'totalLearningMinutes': totalLearningMinutes,
        'totalLearningWords': totalLearningWords,
        'totalLearningDays': uniqueDates.length,
        'averageWordsPerSession':
            sessions.isEmpty
                ? 0
                : (totalLearningWords / sessions.length).round(),
      };
    } catch (e) {
      _log.e('获取用户单词本学习统计数据失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：获取用户单词本学习统计数据失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 获取指定单词本的未完成会话（排除指定会话）
  Future<List<LearningSession>> getUnfinishedSessionsByWordBook(
    int wordBookId, {
    int? excludeSessionId,
  }) async {
    try {
      final isar = _isarService.isar;

      var query =
          isar.learningSessions
              .filter()
              .userWordBookIdEqualTo(wordBookId)
              .and()
              .endTimeIsNull();

      if (excludeSessionId != null) {
        query = query.and().not().idEqualTo(excludeSessionId);
      }

      return await query.findAll();
    } catch (e) {
      _log.e('获取指定单词本的未完成会话失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：获取指定单词本的未完成会话失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 获取超过指定时间的未完成会话
  Future<List<LearningSession>> getExpiredUnfinishedSessions(
    DateTime cutoffTime,
  ) async {
    try {
      final isar = _isarService.isar;

      return await isar.learningSessions
          .filter()
          .endTimeIsNull()
          .and()
          .lastStudyTimeLessThan(cutoffTime)
          .findAll();
    } catch (e) {
      _log.e('获取过期未完成会话失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：获取过期未完成会话失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 批量标记会话为已完成
  Future<int> batchMarkSessionsAsCompleted(List<int> sessionIds) async {
    try {
      if (sessionIds.isEmpty) return 0;

      final isar = _isarService.isar;
      final now = DateTime.now();

      return await isar.writeTxn(() async {
        int updatedCount = 0;

        for (final sessionId in sessionIds) {
          final session = await isar.learningSessions.get(sessionId);
          if (session != null && session.endTime == null) {
            session.endTime = now;
            await isar.learningSessions.put(session);
            updatedCount++;
          }
        }

        return updatedCount;
      });
    } catch (e) {
      _log.e('批量标记会话为已完成失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：批量标记会话为已完成失败',
        type: AppExceptionType.database,
      );
    }
  }
}
