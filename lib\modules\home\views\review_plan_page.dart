import 'package:flutter/material.dart';
import 'package:flutter_study_flow_by_myself/data/models/review_item_model.dart';
import 'package:flutter_study_flow_by_myself/modules/home/<USER>/review_plan_controller.dart';
import 'package:get/get.dart';

/// 复习计划详情页面
///
/// 显示当前激活单词本的所有待复习单词信息
class ReviewPlanPage extends GetView<ReviewPlanController> {
  const ReviewPlanPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('复习计划'),
        centerTitle: true,
        actions: [
          // 筛选按钮
          PopupMenuButton<String>(
            icon: const Icon(Icons.filter_list),
            onSelected: controller.changeFilterBy,
            itemBuilder:
                (context) => [
                  const PopupMenuItem(value: 'all', child: Text('全部单词')),
                  const PopupMenuItem(value: 'due', child: Text('已到期')),
                  const PopupMenuItem(value: 'upcoming', child: Text('即将到期')),
                  const PopupMenuItem(value: 'new', child: Text('新单词')),
                ],
          ),
          // 排序按钮
          PopupMenuButton<String>(
            icon: const Icon(Icons.sort),
            onSelected: controller.changeSortBy,
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'nextReviewDate',
                    child: Text('按复习时间'),
                  ),
                  const PopupMenuItem(
                    value: 'lastReviewDate',
                    child: Text('按上次复习'),
                  ),
                  const PopupMenuItem(
                    value: 'totalReviewCount',
                    child: Text('按复习次数'),
                  ),
                ],
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        if (controller.errorMessage.value.isNotEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  controller.errorMessage.value,
                  style: TextStyle(fontSize: 16, color: Colors.grey[600]),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: controller.refresh,
                  child: const Text('重试'),
                ),
              ],
            ),
          );
        }

        if (controller.filteredReviewItems.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.book_outlined, size: 64, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  '暂无复习计划',
                  style: TextStyle(fontSize: 16, color: Colors.grey[600]),
                ),
                const SizedBox(height: 8),
                Text(
                  '开始学习单词后会自动生成复习计划',
                  style: TextStyle(fontSize: 14, color: Colors.grey[500]),
                ),
              ],
            ),
          );
        }

        return Column(
          children: [
            // 统计信息卡片
            _buildStatsCard(),
            // 单词列表
            Expanded(
              child: RefreshIndicator(
                onRefresh: controller.refresh,
                child: ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: controller.filteredReviewItems.length,
                  itemBuilder: (context, index) {
                    final item = controller.filteredReviewItems[index];
                    return _buildWordCard(item);
                  },
                ),
              ),
            ),
          ],
        );
      }),
    );
  }

  /// 构建统计信息卡片
  Widget _buildStatsCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Obx(() {
        final wordBookName =
            controller.activeWordBook?.wordBook.value?.name ?? '未知单词本';
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              wordBookName,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem(
                  '总计',
                  controller.totalCount.toString(),
                  Colors.blue,
                ),
                _buildStatItem(
                  '已学习',
                  controller.learnedCount.toString(),
                  Colors.green,
                ),
                _buildStatItem(
                  '未学习',
                  controller.unlearnedCount.toString(),
                  Colors.orange,
                ),
                _buildStatItem(
                  '已到期',
                  controller.dueCount.toString(),
                  Colors.red,
                ),
              ],
            ),
          ],
        );
      }),
    );
  }

  /// 构建统计项
  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(label, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
      ],
    );
  }

  /// 构建单词卡片
  Widget _buildWordCard(ReviewItemModel item) {
    final word = item.word.value;
    if (word == null) {
      // 如果单词数据未加载，显示一个占位卡片
      return Card(
        margin: const EdgeInsets.only(bottom: 8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '单词ID: ${item.wordId}',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                '单词数据加载失败',
                style: TextStyle(fontSize: 14, color: Colors.red),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 单词和状态
            Row(
              children: [
                Expanded(
                  child: Text(
                    word.spelling,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: controller.getWordStatusColor(item),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getStatusText(item),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            // 释义
            Text(
              word.definition,
              style: TextStyle(fontSize: 14, color: Colors.grey[700]),
            ),
            const SizedBox(height: 12),
            // 复习信息
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    '下次复习',
                    controller.formatReviewDate(
                      item,
                      item.nextReviewDate,
                      isNext: true,
                    ),
                    Icons.schedule,
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    '上次复习',
                    controller.formatReviewDate(
                      item,
                      item.lastReviewDate,
                      isNext: false,
                    ),
                    Icons.history,
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    '学习次数',
                    '${item.totalReviewCount}次',
                    Icons.repeat,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            // 首次学习时间
            _buildInfoItem(
              '首次学习',
              item.firstStudyDate != null
                  ? controller.formatDate(item.firstStudyDate)
                  : (item.isNewWord ? '还未学习' : '未记录'),
              Icons.school,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建信息项
  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(fontSize: 10, color: Colors.grey[500]),
            ),
            Text(
              value,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            ),
          ],
        ),
      ],
    );
  }

  /// 获取状态文本
  String _getStatusText(ReviewItemModel item) {
    if (item.isNewWord) {
      return '新词';
    }

    final now = DateTime.now();
    if (item.nextReviewDate.isBefore(now)) {
      return '到期';
    } else if (item.nextReviewDate.difference(now).inDays <= 1) {
      return '即将到期';
    } else {
      return '正常';
    }
  }
}
