import 'package:get/get.dart';
import '../models/word_learning_state.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/app/exceptions/app_exception.dart';

/// 学习状态服务
///
/// 职责：
/// - 维护单词学习状态映射，跟踪每个单词的认知状态和学习进度
/// - 管理当前学习进度，包括单词索引和学习阶段状态
/// - 提供响应式状态访问接口，支持UI实时更新
/// - 处理学习状态的初始化、恢复和批量更新操作
///
/// 架构说明：
/// 本服务作为学习模块的核心状态管理中心，使用GetX响应式系统管理学习状态，
/// 通过统一的接口确保状态的一致性和可追踪性。所有控制器都应通过此服务
/// 更新状态，而不是直接修改状态数据，确保数据流的单向性和可预测性。
class LearningStateService extends GetxService {
  // ==================== 依赖注入 ====================
  /// 日志服务 - 记录状态操作和错误信息
  ///
  /// 调用位置：
  /// - 所有方法中用于记录操作日志、错误信息和调试信息
  ///
  /// 用途：记录状态变更过程，便于问题排查和性能监控
  final LogService _log;

  // ==================== 构造函数 ====================
  /// 构造函数 - 依赖注入
  ///
  /// 通过构造函数注入日志服务，确保依赖关系清晰
  ///
  /// 参数：
  /// - [log] 日志服务实例
  ///
  /// 注册位置：lib/modules/learning/bindings/learning_binding.dart
  LearningStateService({required LogService log}) : _log = log;

  // ==================== 私有响应式状态变量 ====================
  /// 单词ID到学习状态的映射
  ///
  /// 存储每个单词的详细学习状态，包括认知状态、错误次数、完成测验数量
  ///
  /// 使用位置：
  /// - initialize() 中初始化所有单词状态
  /// - restore() 中恢复状态数据
  /// - updateWordRecognition() 中更新认知状态
  /// - updateWordState() 中批量更新状态
  /// - getWordLearningState() 中获取单词状态
  /// - getAllWordStates() 中获取所有状态
  /// - learnedWordCount 和 totalCompletedQuizzes 计算属性中统计数据
  ///
  /// 响应式特性：支持UI通过计算属性实时监听状态变化
  final _wordStates = RxMap<int, WordLearningState>({});

  /// 学习单词ID列表
  ///
  /// 存储当前学习会话中所有单词的ID列表，按学习顺序排列
  ///
  /// 使用位置：
  /// - initialize() 中设置单词列表
  /// - restore() 中恢复单词列表
  /// - getCurrentWordId() 中根据索引获取当前单词ID
  /// - wordIds getter 中提供只读访问
  /// - quiz_page.dart:41,49 中获取总单词数用于进度显示
  ///
  /// 响应式特性：支持UI监听单词列表变化
  final _wordIds = RxList<int>([]);

  /// 当前学习单词索引
  ///
  /// 指示当前正在学习的单词在列表中的位置
  ///
  /// 使用位置：
  /// - initialize() 和 restore() 中设置索引
  /// - getCurrentWordId() 中用于定位当前单词
  /// - currentWordIndex getter 中提供访问
  /// - detail_controller.dart:238 中获取当前索引用于进度显示
  ///
  /// 响应式特性：支持UI监听索引变化，实现进度条更新
  final _currentWordIndex = RxInt(0);

  /// 是否在最终测验阶段
  ///
  /// 标识当前学习流程是否进入最终测验阶段
  ///
  /// 使用位置：
  /// - initialize() 中重置为false
  /// - restore() 和 setInFinalQuiz() 中设置状态
  /// - isInFinalQuiz getter 中提供访问
  /// - quiz_page.dart:44,48 中判断是否显示最终测验进度
  /// - detail_controller.dart:40 中检查测验阶段状态
  ///
  /// 响应式特性：支持UI根据阶段状态切换显示模式
  final _isInFinalQuiz = RxBool(false);

  /// 详情页要显示的单词ID
  ///
  /// 指定详情页面当前显示的单词ID，支持独立于学习进度的单词查看
  ///
  /// 使用位置：
  /// - setCurrentDetailWordId() 中设置要显示的单词
  /// - currentDetailWordId getter 中提供访问
  /// - currentDetailWordIdObs getter 中提供响应式访问
  /// - detail_controller.dart:147,156,192 中监听和获取详情页单词ID
  ///
  /// 响应式特性：支持详情页控制器监听单词切换，实现动态加载
  final _currentDetailWordId = RxInt(-1);

  // ==================== 公共访问接口 ====================
  /// 获取单词ID列表的只读视图
  ///
  /// 提供当前学习会话中所有单词ID的不可变访问
  ///
  /// 调用位置：
  /// - quiz_page.dart:41,49 - 获取总单词数用于进度条显示
  ///
  /// 用途：UI组件获取单词总数，计算学习进度百分比
  ///
  /// 返回值：List&lt;int&gt; - 单词ID列表的只读副本
  List<int> get wordIds => List.unmodifiable(_wordIds);

  /// 获取当前学习单词索引
  ///
  /// 返回当前正在学习的单词在列表中的位置
  ///
  /// 调用位置：
  /// - detail_controller.dart:238 - 获取当前索引用于进度指示器显示
  ///
  /// 用途：UI组件显示当前学习进度和位置信息
  ///
  /// 返回值：int - 当前单词索引（从0开始）
  int get currentWordIndex => _currentWordIndex.value;

  /// 获取当前学习单词索引的可观察对象
  ///
  /// 提供响应式访问，支持UI监听索引变化
  ///
  /// 调用位置：
  /// - recall_controller.dart - 监听索引变化，实现进度更新
  ///
  /// 用途：控制器监听学习进度变化，触发UI更新
  ///
  /// 返回值：RxInt - 响应式整数对象
  RxInt get currentWordIndexObs => _currentWordIndex;

  /// 获取是否在最终测验阶段
  ///
  /// 返回当前学习流程是否处于最终测验阶段
  ///
  /// 调用位置：
  /// - quiz_page.dart:44,48 - 判断是否显示最终测验进度
  /// - detail_controller.dart:40 - 检查测验阶段状态
  ///
  /// 用途：UI根据学习阶段切换显示模式和进度计算方式
  ///
  /// 返回值：bool - true表示在最终测验阶段，false表示在学习阶段
  bool get isInFinalQuiz => _isInFinalQuiz.value;

  /// 获取详情页要显示的单词ID
  ///
  /// 返回详情页面当前应该显示的单词ID
  ///
  /// 调用位置：
  /// - detail_controller.dart:156,192 - 获取要显示的单词ID
  ///
  /// 用途：详情页控制器确定要加载和显示的单词
  ///
  /// 返回值：int - 单词ID，-1表示未设置
  int get currentDetailWordId => _currentDetailWordId.value;

  /// 获取详情页单词ID的响应式对象
  ///
  /// 提供响应式访问，支持详情页控制器监听单词切换
  ///
  /// 调用位置：
  /// - detail_controller.dart:147 - 监听详情页单词ID变化
  /// - review_detail_controller.dart:57 - 复习模块中监听单词变化
  ///
  /// 用途：控制器监听单词切换，触发详情页内容更新
  ///
  /// 返回值：RxInt - 响应式整数对象
  RxInt get currentDetailWordIdObs => _currentDetailWordId;

  // ==================== 计算属性 ====================
  /// 获取已学习单词数（答对过一题的单词）
  ///
  /// 统计已经答对至少一道测验题的单词数量，用于学习进度计算
  ///
  /// 调用位置：
  /// - quiz_page.dart:40 - 显示学习阶段进度条中的已学习单词数
  /// - learning_controller.dart:35 - 更新学习进度统计
  ///
  /// 计算逻辑：
  /// - 遍历所有单词状态，筛选 completedQuizzes > 0 的单词
  /// - 返回符合条件的单词数量
  ///
  /// 用途：UI进度显示和学习统计分析
  ///
  /// 异常处理：出错时返回0，确保UI显示正常
  ///
  /// 返回值：int - 已学习单词数量
  int get learnedWordCount {
    try {
      return _wordStates.values
          .where((state) => state.completedQuizzes > 0)
          .length;
    } catch (e) {
      _log.e('获取已学习单词数失败', error: e);
      return 0;
    }
  }

  /// 获取已完成的测验题总数
  ///
  /// 统计所有单词已完成的测验题总数，用于最终测验阶段的进度计算
  ///
  /// 调用位置：
  /// - quiz_page.dart:45 - 显示最终测验阶段的总完成进度
  ///
  /// 计算逻辑：
  /// - 遍历所有单词状态，累加每个单词的 completedQuizzes 数量
  /// - 使用 fold 方法进行累加计算
  ///
  /// 用途：最终测验阶段的进度条显示
  ///
  /// 异常处理：出错时返回0，确保UI显示正常
  ///
  /// 返回值：int - 已完成测验题总数
  int get totalCompletedQuizzes {
    try {
      return _wordStates.values
          .map((state) => state.completedQuizzes)
          .fold(0, (a, b) => a + b);
    } catch (e) {
      _log.e('获取已完成测验题总数失败', error: e);
      return 0;
    }
  }

  // ==================== 核心状态管理方法 ====================
  /// 初始化服务
  ///
  /// 使用提供的单词ID列表初始化学习状态服务，清空现有数据并设置新的学习会话
  ///
  /// 调用位置：
  /// - learning_controller.dart:281 - 开始新学习会话时初始化状态
  ///
  /// 业务逻辑：
  /// 1. 清空现有的单词ID列表和状态映射
  /// 2. 添加新的单词ID列表
  /// 3. 为每个单词创建初始学习状态
  /// 4. 重置学习进度和阶段标志
  ///
  /// 参数：
  /// - [wordIds] 学习单词ID列表，不能为空
  ///
  /// 状态重置：
  /// - 当前单词索引重置为0
  /// - 最终测验标志重置为false
  /// - 所有单词状态重置为初始状态
  ///
  /// 异常处理：
  /// - 捕获所有异常并包装为AppException
  /// - 记录详细的错误信息便于调试
  ///
  /// 返回值：无返回值（void）
  void initialize(List<int> wordIds) {
    try {
      _log.i('初始化学习状态服务，单词数量: ${wordIds.length}');

      // 清空现有数据
      _wordIds.clear();
      _wordStates.clear();

      // 更新数据
      _wordIds.addAll(wordIds);

      // 初始化单词状态
      for (final wordId in wordIds) {
        _wordStates[wordId] = WordLearningState.initial();
      }

      // 重置其他状态
      _currentWordIndex.value = 0;
      _isInFinalQuiz.value = false;

      _log.i('学习状态服务初始化完成');
    } catch (e) {
      _log.e('初始化学习状态服务失败', error: e);
      throw AppException(
        AppExceptionType.unknown,
        '初始化学习状态失败',
        originalException: e,
      );
    }
  }

  /// 从现有状态恢复
  /// [wordIds] 学习单词ID列表
  /// [currentWordIndex] 当前学习单词索引
  /// [isInFinalQuiz] 是否在最终测验阶段
  /// [states] 单词学习状态映射
  void restore(
    List<int> wordIds,
    int currentWordIndex,
    bool isInFinalQuiz,
    Map<int, WordLearningState> states,
  ) {
    try {
      _log.i(
        '恢复学习状态，单词数量: ${wordIds.length}, 当前索引: $currentWordIndex, 最终测验: $isInFinalQuiz',
      );

      _wordIds.clear();
      _wordIds.addAll(wordIds);

      _wordStates.clear();
      _wordStates.addAll(states);

      _currentWordIndex.value = currentWordIndex;
      _isInFinalQuiz.value = isInFinalQuiz;

      _log.i('学习状态恢复完成');
    } catch (e) {
      _log.e('恢复学习状态失败', error: e);
      throw AppException(
        AppExceptionType.unknown,
        '恢复学习状态失败',
        originalException: e,
      );
    }
  }

  /// 更新单词认知状态
  /// [wordId] 单词ID
  /// [isRecognized] 是否认识
  void updateWordRecognition(int wordId, bool isRecognized) {
    try {
      if (!_wordStates.containsKey(wordId)) {
        _log.w('更新不存在的单词状态，创建新状态: $wordId');
        _wordStates[wordId] = WordLearningState.initial();
      }

      _wordStates[wordId] = _wordStates[wordId]!.copyWith(
        isRecognized: isRecognized,
      );

      _log.d('单词 $wordId 认知状态更新为: $isRecognized');
    } catch (e) {
      _log.e('更新单词认知状态失败: $wordId', error: e);
      throw AppException(
        AppExceptionType.unknown,
        '更新单词认知状态失败',
        originalException: e,
      );
    }
  }

  /// 批量更新单词状态
  ///
  /// 允许同时更新多个状态属性，确保状态更新的原子性，避免多次状态变更
  ///
  /// 调用位置：
  /// - quiz_controller.dart:146,157 - 处理测验结果时批量更新状态
  ///
  /// 业务逻辑：
  /// 1. 检查单词状态是否存在，不存在则创建初始状态
  /// 2. 根据提供的参数计算新的状态值
  /// 3. 使用 copyWith 方法原子性更新状态
  /// 4. 支持绝对值设置和增量更新两种模式
  ///
  /// 参数：
  /// - [wordId] 单词ID，必需参数
  /// - [isRecognized] 可选，是否认识该单词
  /// - [errorCount] 可选，设置错误次数的绝对值
  /// - [errorCountIncrement] 可选，错误次数增量
  /// - [completedQuizzes] 可选，设置已完成测验数量的绝对值
  /// - [completedQuizzesIncrement] 可选，已完成测验数量增量
  ///
  /// 更新策略：
  /// - 绝对值参数优先于增量参数
  /// - 支持部分更新，未提供的参数保持原值不变
  ///
  /// 异常处理：
  /// - 捕获所有异常并包装为AppException
  /// - 记录详细的操作信息便于调试
  ///
  /// 返回值：无返回值（void）
  void updateWordState(
    int wordId, {
    bool? isRecognized,
    int? errorCount,
    int? errorCountIncrement,
    int? completedQuizzes,
    int? completedQuizzesIncrement,
  }) {
    try {
      if (!_wordStates.containsKey(wordId)) {
        _log.w('更新不存在的单词状态，创建新状态: $wordId');
        _wordStates[wordId] = WordLearningState.initial();
      }

      final currentState = _wordStates[wordId]!;

      // 计算新的错误次数
      int? newErrorCount;
      if (errorCount != null) {
        newErrorCount = errorCount;
      } else if (errorCountIncrement != null) {
        newErrorCount = currentState.errorCount + errorCountIncrement;
      }

      // 计算新的已完成测验数量
      int? newCompletedQuizzes;
      if (completedQuizzes != null) {
        newCompletedQuizzes = completedQuizzes;
      } else if (completedQuizzesIncrement != null) {
        newCompletedQuizzes =
            currentState.completedQuizzes + completedQuizzesIncrement;
      }

      // 更新状态
      _wordStates[wordId] = currentState.copyWith(
        isRecognized: isRecognized,
        errorCount: newErrorCount,
        completedQuizzes: newCompletedQuizzes,
      );

      _log.d('单词 $wordId 状态已批量更新');
    } catch (e) {
      _log.e('批量更新单词状态失败: $wordId', error: e);
      throw AppException(
        AppExceptionType.unknown,
        '批量更新单词状态失败',
        originalException: e,
      );
    }
  }

  /// 获取单词学习状态
  /// [wordId] 单词ID
  /// 返回单词学习状态，如果不存在则返回初始状态
  WordLearningState getWordLearningState(int wordId) {
    try {
      final state = _wordStates[wordId] ?? WordLearningState.initial();
      return state;
    } catch (e) {
      _log.e('获取单词学习状态失败: $wordId', error: e);
      throw AppException(
        AppExceptionType.unknown,
        '获取单词学习状态失败',
        originalException: e,
      );
    }
  }

  // ==================== 单词导航方法 ====================
  /// 获取当前单词ID
  ///
  /// 返回当前学习单词的ID，用于确定当前正在学习的单词
  ///
  /// 调用位置：
  /// - detail_controller.dart:194 - 获取当前单词ID用于详情页显示
  ///
  /// 业务逻辑：
  /// 1. 检查单词列表是否为空
  /// 2. 验证当前索引是否在有效范围内
  /// 3. 返回对应索引位置的单词ID
  ///
  /// 边界处理：
  /// - 单词列表为空时返回-1
  /// - 索引超出范围时返回-1并记录警告
  ///
  /// 异常处理：
  /// - 捕获所有异常并返回-1
  /// - 记录详细的错误信息
  ///
  /// 返回值：int - 当前单词ID，失败时返回-1
  int getCurrentWordId() {
    try {
      if (_wordIds.isEmpty ||
          currentWordIndex < 0 ||
          currentWordIndex >= _wordIds.length) {
        _log.w('获取当前单词ID失败: 索引无效 $currentWordIndex，单词列表长度 ${_wordIds.length}');
        return -1; // 无效ID
      }
      return _wordIds[currentWordIndex];
    } catch (e) {
      _log.e('获取当前单词ID失败', error: e);
      return -1; // 无效ID
    }
  }

  /// 移动到下一个单词
  ///
  /// 将当前单词索引向前移动一位，用于学习流程中的单词切换
  ///
  /// 调用位置：
  /// - quiz_controller.dart:326 - 学习阶段答对后移动到下一个单词
  ///
  /// 业务逻辑：
  /// 1. 检查是否已经是最后一个单词
  /// 2. 如果不是最后一个，则索引加1
  /// 3. 记录索引变化的日志
  ///
  /// 边界处理：
  /// - 如果已经是最后一个单词，返回false且不更新索引
  /// - 确保索引不会超出有效范围
  ///
  /// 异常处理：
  /// - 捕获所有异常并返回false
  /// - 记录详细的错误信息
  ///
  /// 返回值：bool - true表示成功移动，false表示已经是最后一个单词或出错
  bool moveToNextWord() {
    try {
      // 检查是否已经是最后一个单词
      if (currentWordIndex >= _wordIds.length - 1) {
        _log.d('已经是最后一个单词，无法移动到下一个');
        return false;
      }

      _currentWordIndex.value++;
      _log.d('移动到下一个单词，当前索引: ${_currentWordIndex.value}');
      return true;
    } catch (e) {
      _log.e('移动到下一个单词失败', error: e);
      return false;
    }
  }

  /// 设置是否在最终测验阶段
  void setInFinalQuiz(bool value) {
    try {
      _isInFinalQuiz.value = value;
      _log.i('设置最终测验阶段状态: $value');
    } catch (e) {
      _log.e('设置最终测验阶段状态失败', error: e);
      throw AppException(
        AppExceptionType.unknown,
        '设置最终测验阶段状态失败',
        originalException: e,
      );
    }
  }

  /// 设置详情页要显示的单词ID
  void setCurrentDetailWordId(int wordId) {
    try {
      _currentDetailWordId.value = wordId;
      _log.d('设置详情页单词ID: $wordId');
    } catch (e) {
      _log.e('设置详情页单词ID失败', error: e);
      throw AppException(
        AppExceptionType.unknown,
        '设置详情页单词ID失败',
        originalException: e,
      );
    }
  }

  // ==================== 状态查询方法 ====================
  /// 获取所有单词状态
  ///
  /// 返回所有单词学习状态的副本，用于状态持久化和批量操作
  ///
  /// 调用位置：
  /// - learning_persistence_service.dart:268 - 保存学习状态到持久化存储
  /// - result_controller.dart:273 - 获取学习结果统计数据
  ///
  /// 业务逻辑：
  /// - 创建状态映射的副本，避免外部直接修改内部状态
  /// - 确保数据的不可变性和封装性
  ///
  /// 用途：
  /// - 状态持久化：将内存中的状态保存到数据库
  /// - 结果统计：计算学习完成情况和进度
  ///
  /// 异常处理：
  /// - 捕获所有异常并包装为AppException
  /// - 记录详细的错误信息便于调试
  ///
  /// 返回值：Map&lt;int, WordLearningState&gt; - 单词状态映射的副本
  Map<int, WordLearningState> getAllWordStates() {
    try {
      return Map.from(_wordStates);
    } catch (e) {
      _log.e('获取所有单词状态失败', error: e);
      throw AppException(
        AppExceptionType.unknown,
        '获取所有单词状态失败',
        originalException: e,
      );
    }
  }
}
