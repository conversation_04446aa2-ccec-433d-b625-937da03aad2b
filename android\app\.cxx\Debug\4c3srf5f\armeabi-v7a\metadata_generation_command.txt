                        -HD:\flutter_sdk\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=D:\AppData\Local\Android\Sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=D:\AppData\Local\Android\Sdk\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=D:\AppData\Local\Android\Sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\coding\flutter_demo\flutter_study_flow_by_myself\build\app\intermediates\cxx\Debug\4c3srf5f\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\coding\flutter_demo\flutter_study_flow_by_myself\build\app\intermediates\cxx\Debug\4c3srf5f\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=Debug
-BD:\coding\flutter_demo\flutter_study_flow_by_myself\android\app\.cxx\Debug\4c3srf5f\armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2