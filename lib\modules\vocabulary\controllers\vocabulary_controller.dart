import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/app/services/user_word_book_service.dart';
import 'package:flutter_study_flow_by_myself/data/models/user_word_book_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_book_model.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/word_book_repository.dart';
import 'package:get/get.dart';

class VocabularyController extends GetxController {
  // Service依赖注入
  final UserWordBookService _userWordBookService;
  final WordBookRepository _wordBookRepository;
  final LogService _log;

  // 页面特有的UI状态
  final remoteWordBooks = <WordBookModel>[].obs; // 远程可下载词书
  final isLoading = false.obs; // 加载状态
  final isDownloading = false.obs; // 下载状态
  final downloadProgress = 0.0.obs; // 下载进度
  final downloadingBookId = 0.obs; // 当前下载的词书ID
  final creatingReviewItemsMessage = ''.obs; // 创建复习项进度消息
  final creatingReviewItemsProgress = 0.0.obs; // 创建复习项进度

  // 搜索相关
  final searchQuery = ''.obs;
  final isFilteringLocal = true.obs; // 是否只过滤本地词书

  // ==================== 通过Service获取数据的Getter ====================
  /// 获取本地用户词书（来自Service）
  List<UserWordBookModel> get localUserWordBooks =>
      _userWordBookService.allUserWordBooks;

  VocabularyController({
    required UserWordBookService userWordBookService,
    required WordBookRepository wordBookRepository,
    required LogService log,
  }) : _userWordBookService = userWordBookService,
       _wordBookRepository = wordBookRepository,
       _log = log;

  @override
  void onInit() {
    super.onInit();
    loadWordBooks();
  }

  // 加载词书列表（委托给Service和Repository）
  Future<void> loadWordBooks() async {
    try {
      isLoading.value = true;

      // 1. 刷新Service中的用户词书数据
      await _userWordBookService.refreshWordBookData();

      // 2. 加载远程可用词书（页面特有的功能）
      final wordBooks = await _wordBookRepository.getAvailableWordBooks();
      remoteWordBooks.assignAll(wordBooks);

      _log.i('词书加载完成: 本地${localUserWordBooks.length}个, 远程${wordBooks.length}个');
    } catch (e) {
      _log.e('加载词书列表失败', error: e);
      Get.snackbar('错误', '加载词书列表失败', snackPosition: SnackPosition.BOTTOM);
    } finally {
      isLoading.value = false;
    }
  }

  // 下载词书
  Future<void> downloadWordBook(int wordBookId) async {
    if (isDownloading.value) {
      Get.snackbar('提示', '已有下载任务正在进行中', snackPosition: SnackPosition.BOTTOM);
      return;
    }

    try {
      isDownloading.value = true;
      downloadingBookId.value = wordBookId;
      downloadProgress.value = 0.0;
      creatingReviewItemsMessage.value = '';
      creatingReviewItemsProgress.value = 0.0;

      // 委托给Service下载并创建用户词书
      // TODO: 后续可以在Service中添加进度回调支持
      await _userWordBookService.downloadAndCreateWordBook(wordBookId);

      // 重新加载本地词书列表
      await loadWordBooks();

      Get.snackbar('成功', '词书下载完成', snackPosition: SnackPosition.BOTTOM);
    } catch (e) {
      _log.e('下载词书失败: $wordBookId', error: e);
      Get.snackbar('错误', '下载词书失败', snackPosition: SnackPosition.BOTTOM);
    } finally {
      isDownloading.value = false;
      downloadingBookId.value = 0;
    }
  }

  // 激活词书（委托给Service）
  Future<void> activateWordBook(int userWordBookId) async {
    try {
      // 委托给Service激活词书，Service会自动更新全局状态
      await _userWordBookService.activateWordBook(userWordBookId);

      // 更新页面状态
      await loadWordBooks();

      Get.snackbar('成功', '词书已激活', snackPosition: SnackPosition.BOTTOM);
    } catch (e) {
      _log.e('激活词书失败: $userWordBookId', error: e);
      Get.snackbar('错误', '激活词书失败', snackPosition: SnackPosition.BOTTOM);
    }
  }

  // 停用词书（委托给Service）
  Future<void> deactivateWordBook(int userWordBookId) async {
    try {
      // 委托给Service停用词书，Service会自动更新全局状态
      await _userWordBookService.deactivateWordBook(userWordBookId);

      // 更新页面状态
      await loadWordBooks();

      Get.snackbar('成功', '词书已停用', snackPosition: SnackPosition.BOTTOM);
    } catch (e) {
      _log.e('停用词书失败: $userWordBookId', error: e);
      Get.snackbar('错误', '停用词书失败', snackPosition: SnackPosition.BOTTOM);
    }
  }

  // 搜索词书
  void setSearchQuery(String query) {
    searchQuery.value = query;
  }

  // 切换过滤状态
  void toggleFilter() {
    isFilteringLocal.value = !isFilteringLocal.value;
  }

  // 获取过滤后的本地词书列表
  List<UserWordBookModel> get filteredLocalWordBooks {
    // 直接使用所有本地用户词书（UserWordBookModel的存在就表示已下载）
    var allLocalBooks = localUserWordBooks;

    // 如果没有搜索查询，直接返回所有本地词书
    if (searchQuery.isEmpty) return allLocalBooks;

    // 应用搜索过滤
    return allLocalBooks.where((book) {
      final wordBookName = book.wordBook.value?.name.toLowerCase() ?? '';
      return wordBookName.contains(searchQuery.value.toLowerCase());
    }).toList();
  }

  // 获取过滤后的远程词书列表
  List<WordBookModel> get filteredRemoteWordBooks {
    // 首先应用搜索过滤
    var filteredBooks =
        searchQuery.isEmpty
            ? remoteWordBooks
            : remoteWordBooks.where((book) {
              return book.name.toLowerCase().contains(
                searchQuery.value.toLowerCase(),
              );
            }).toList();

    // 然后过滤掉已下载的词书
    return filteredBooks
        .where((book) => !isWordBookDownloaded(book.wordBookId ?? 0))
        .toList();
  }

  // 检查词书是否已下载
  /// 检查远程词书是否已被下载到本地
  ///
  /// 重要说明：
  /// 1. 此方法检查远程词书ID是否与任何本地词书的远程ID匹配
  /// 2. 改进后使用remoteWordBookId字段直接比较，更加清晰明确
  /// 3. 不再依赖嵌套关系(wordBook.value?.wordBookId)，提高代码可靠性
  ///
  /// [remoteWordBookId] - 要检查的远程词书ID
  /// 返回值：如果已下载返回true，否则返回false
  bool isWordBookDownloaded(int remoteWordBookId) {
    return localUserWordBooks.any(
      (book) => book.remoteWordBookId == remoteWordBookId,
    );
  }

  // 获取指定远程ID对应的本地词书ID
  int? getLocalBookIdByRemoteId(int remoteWordBookId) {
    final book = localUserWordBooks.firstWhereOrNull(
      (book) => book.remoteWordBookId == remoteWordBookId,
    );
    return book?.id;
  }

  // 刷新单词本下载状态
  void refreshDownloadStatus() {
    // 强制刷新UI
    remoteWordBooks.refresh();
    // localUserWordBooks是getter，不需要refresh，通过Service自动更新
    update(); // 触发UI更新
  }
}
