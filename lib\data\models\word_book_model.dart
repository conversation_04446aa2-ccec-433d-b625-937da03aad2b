import 'package:isar/isar.dart';
import 'word_model.dart';

part 'word_book_model.g.dart';

@Collection()
class WordBookModel {
  /// Isar数据库主键，自动递增
  Id id = Isar.autoIncrement;

  /// 后端词书ID，用于与服务器同步
  /// 对于本地创建的词书，此字段可能为null
  int? wordBookId;

  /// 词书名称
  late String name;

  /// 词书描述
  late String description;

  /// 词书包含的单词数量
  late int wordCount;

  /// 是否为用户自定义词书
  late bool isCustom;

  /// 添加与WordModel的多对多关系
  final words = IsarLinks<WordModel>();

  WordBookModel({
    this.wordBookId,
    required this.name,
    required this.description,
    required this.wordCount,
    required this.isCustom,
  });

  /// 从JSON创建WordBookModel实例，包含类型安全检查
  factory WordBookModel.fromJson(Map<String, dynamic> json) {
    try {
      return WordBookModel(
        wordBookId: json['wordBookId'] as int?,
        name: json['name'] as String? ?? '未知词书',
        description: json['description'] as String? ?? '',
        wordCount: json['wordCount'] as int? ?? 0,
        isCustom: json['isCustom'] as bool? ?? false,
      )..id = json['id'] ?? Isar.autoIncrement;
    } catch (e) {
      throw FormatException('Invalid JSON format for WordBookModel: $e');
    }
  }

  /// 转换为完整的 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'wordBookId': wordBookId,
      'name': name,
      'description': description,
      'wordCount': wordCount,
      'isCustom': isCustom,
    };
  }

  /// 创建本地自定义词书实例
  factory WordBookModel.createLocal({
    required String name,
    required String description,
    int wordCount = 0,
  }) {
    return WordBookModel(
      name: name,
      description: description,
      wordCount: wordCount,
      isCustom: true,
    );
  }

  /// 更新单词数量
  void updateWordCount(int newCount) {
    if (newCount >= 0) {
      wordCount = newCount;
    }
  }

  /// 复制当前实例并允许修改部分字段
  WordBookModel copyWith({
    int? id,
    int? wordBookId,
    String? name,
    String? description,
    int? wordCount,
    bool? isCustom,
  }) {
    return WordBookModel(
      wordBookId: wordBookId ?? this.wordBookId,
      name: name ?? this.name,
      description: description ?? this.description,
      wordCount: wordCount ?? this.wordCount,
      isCustom: isCustom ?? this.isCustom,
    )..id = id ?? this.id;
  }

  /// 验证词书数据是否有效
  bool get isValid {
    return name.trim().isNotEmpty && wordCount >= 0;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WordBookModel &&
        other.id == id &&
        other.wordBookId == wordBookId &&
        other.name == name &&
        other.description == description &&
        other.wordCount == wordCount &&
        other.isCustom == isCustom;
  }

  @override
  int get hashCode {
    return Object.hash(id, wordBookId, name, description, wordCount, isCustom);
  }

  @override
  String toString() {
    return 'WordBookModel(id: $id, wordBookId: $wordBookId, name: $name, description: $description, wordCount: $wordCount, isCustom: $isCustom)';
  }

  // ==================== 静态方法 ====================

  /// 从JSON列表创建WordBookModel列表
  static List<WordBookModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList
        .map((json) => WordBookModel.fromJson(json as Map<String, dynamic>))
        .toList();
  }
}
