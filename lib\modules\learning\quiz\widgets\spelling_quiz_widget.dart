import 'package:flutter/material.dart';
import 'package:flutter_study_flow_by_myself/data/models/quiz/spelling_quiz_model.dart';

/// 拼写测验组件
///
/// 实现点击字母进行单词拼写的功能
class SpellingQuizWidget extends StatefulWidget {
  /// 拼写测验模型
  final SpellingQuizModel quiz;

  /// 答题回调
  final Function(String userInput, bool isCorrect) onAnswered;

  const SpellingQuizWidget({
    super.key,
    required this.quiz,
    required this.onAnswered,
  });

  @override
  State<SpellingQuizWidget> createState() => _SpellingQuizWidgetState();
}

class _SpellingQuizWidgetState extends State<SpellingQuizWidget> {
  /// 用户拼写的字母列表
  final List<Map<String, dynamic>> userSpelling = [];

  /// 可用字母列表（打乱的）
  late List<Map<String, dynamic>> availableLetters;

  /// 是否已提交答案
  bool hasAnswered = false;

  /// 答案是否正确
  bool? isCorrect;

  @override
  void initState() {
    super.initState();
    _initializeLetters();
  }

  /// 关键：当quiz变化时重新初始化
  @override
  void didUpdateWidget(SpellingQuizWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.quiz.correctAnswer != widget.quiz.correctAnswer) {
      _initializeLetters();
      setState(() {
        userSpelling.clear();
        hasAnswered = false;
        isCorrect = null;
      });
    }
  }

  /// 初始化可用字母
  void _initializeLetters() {
    // 将正确单词的字母打乱
    final letters = widget.quiz.correctAnswer.split('');
    letters.shuffle();

    // 初始化可用字母列表，包含字母和选中状态
    availableLetters =
        letters
            .asMap()
            .entries
            .map(
              (entry) => {
                'letter': entry.value,
                'index': entry.key,
                'isUsed': false,
              },
            )
            .toList();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 题目提示
          _buildQuestionPrompt(),
          const SizedBox(height: 32),
          // 拼写区域
          _buildSpellingArea(),
          const SizedBox(height: 24),
          // 可用字母区域
          _buildAvailableLetters(),
          const SizedBox(height: 24),
          // 答题结果反馈
          if (hasAnswered) _buildResult(),
          // 操作按钮
          if (!hasAnswered) _buildActionButtons(),
          const Spacer(),
        ],
      ),
    );
  }

  /// 构建题目提示
  Widget _buildQuestionPrompt() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '根据中文提示拼写单词：',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          widget.quiz.hint,
          style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  /// 构建拼写区域
  Widget _buildSpellingArea() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: _getBorderColor(), width: 2),
      ),
      child:
          userSpelling.isEmpty
              ? const Center(
                child: Text(
                  '点击下方字母进行拼写',
                  style: TextStyle(color: Colors.grey, fontSize: 16),
                ),
              )
              : Wrap(
                spacing: 8,
                runSpacing: 12,
                alignment: WrapAlignment.center,
                children:
                    userSpelling.asMap().entries.map((entry) {
                      final index = entry.key;
                      final item = entry.value;
                      final letter = item['letter'] as String;

                      return GestureDetector(
                        onTap:
                            hasAnswered
                                ? null
                                : () => _removeLetterFromSpelling(index),
                        child: Container(
                          width: 40,
                          height: 40,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: _getLetterBackgroundColor(
                              letter,
                              true,
                              index,
                            ),
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: 2,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                          child: Text(
                            letter.toUpperCase(),
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color:
                                  hasAnswered
                                      ? _getLetterTextColor(letter, index)
                                      : Colors.black87,
                            ),
                          ),
                        ),
                      );
                    }).toList(),
              ),
    );
  }

  /// 构建可用字母区域
  Widget _buildAvailableLetters() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Wrap(
        spacing: 8,
        runSpacing: 12,
        alignment: WrapAlignment.center,
        children:
            availableLetters.map((item) {
              final letter = item['letter'] as String;
              final index = item['index'] as int;
              final isUsed = item['isUsed'] as bool;

              // 如果字母已被使用或已经回答，则显示禁用状态
              if (isUsed || hasAnswered) {
                return Container(
                  width: 40,
                  height: 40,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    letter.toUpperCase(),
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[600],
                    ),
                  ),
                );
              }

              // 可点击的字母
              return GestureDetector(
                onTap: () => _addLetterToSpelling(letter, index),
                child: Container(
                  width: 40,
                  height: 40,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 2,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: Text(
                    letter.toUpperCase(),
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ),
              );
            }).toList(),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Row(
      children: [
        // 检查答案按钮
        if (userSpelling.isNotEmpty)
          Expanded(
            child: ElevatedButton(
              onPressed: _checkAnswer,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
              child: const Text('检查答案', style: TextStyle(fontSize: 16)),
            ),
          ),
        // 间隔
        if (userSpelling.isNotEmpty) const SizedBox(width: 12),
        // 清除按钮
        if (userSpelling.isNotEmpty)
          Expanded(
            child: ElevatedButton(
              onPressed: _clearSpelling,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                backgroundColor: Colors.grey[300],
                foregroundColor: Colors.black87,
              ),
              child: const Text('清除', style: TextStyle(fontSize: 16)),
            ),
          ),
      ],
    );
  }

  /// 构建答题结果反馈
  Widget _buildResult() {
    if (!hasAnswered || isCorrect == null) return const SizedBox();

    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color:
            isCorrect!
                ? Colors.green.withValues(alpha: 0.1)
                : Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isCorrect! ? Colors.green : Colors.red,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            isCorrect! ? Icons.check_circle : Icons.cancel,
            color: isCorrect! ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            isCorrect! ? '拼写正确！' : '拼写错误，正确答案是：${widget.quiz.correctAnswer}',
            style: TextStyle(
              color: isCorrect! ? Colors.green : Colors.red,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 添加字母到拼写区域
  void _addLetterToSpelling(String letter, int originalIndex) {
    if (hasAnswered) return;

    setState(() {
      userSpelling.add({'letter': letter, 'originalIndex': originalIndex});

      // 更新字母状态为已使用
      for (int i = 0; i < availableLetters.length; i++) {
        if (availableLetters[i]['index'] == originalIndex) {
          availableLetters[i]['isUsed'] = true;
          break;
        }
      }
    });

    // 如果用户已经拼写完整个单词，自动检查答案
    if (userSpelling.length == widget.quiz.correctAnswer.length) {
      _checkAnswer();
    }
  }

  /// 从拼写区域移除字母
  void _removeLetterFromSpelling(int spellingIndex) {
    if (hasAnswered) return;

    final originalIndex = userSpelling[spellingIndex]['originalIndex'] as int;

    setState(() {
      userSpelling.removeAt(spellingIndex);

      // 更新字母状态为未使用
      for (int i = 0; i < availableLetters.length; i++) {
        if (availableLetters[i]['index'] == originalIndex) {
          availableLetters[i]['isUsed'] = false;
          break;
        }
      }
    });
  }

  /// 检查答案
  void _checkAnswer() {
    final userAnswer = userSpelling
        .map((item) => item['letter'] as String)
        .join('');
    final isCorrect =
        userAnswer.toLowerCase() == widget.quiz.correctAnswer.toLowerCase();

    setState(() {
      this.isCorrect = isCorrect;
      hasAnswered = true;
    });

    // 调用回调函数
    widget.onAnswered(userAnswer, isCorrect);
  }

  /// 清除当前拼写
  void _clearSpelling() {
    setState(() {
      // 清空拼写区域
      userSpelling.clear();

      // 重置所有字母为未使用状态
      for (int i = 0; i < availableLetters.length; i++) {
        availableLetters[i]['isUsed'] = false;
      }
    });
  }

  // 辅助方法

  /// 获取拼写区域边框颜色
  Color _getBorderColor() {
    if (!hasAnswered) {
      return userSpelling.isEmpty ? Colors.grey : Colors.blue;
    } else {
      return isCorrect == true ? Colors.green : Colors.red;
    }
  }

  /// 获取字母背景色
  Color _getLetterBackgroundColor(
    String letter,
    bool isInSpellingArea, [
    int? index,
  ]) {
    if (!hasAnswered) {
      return isInSpellingArea
          ? Colors.blue.withValues(alpha: 0.1)
          : Colors.white;
    } else {
      if (!isInSpellingArea) {
        return Colors.white;
      }

      // 拼写区域中的字母
      final correctLetters = widget.quiz.correctAnswer.split('');

      if (index != null &&
          index < correctLetters.length &&
          letter.toLowerCase() == correctLetters[index].toLowerCase()) {
        return Colors.green.withValues(alpha: 0.2);
      }
      return Colors.red.withValues(alpha: 0.2);
    }
  }

  /// 获取字母文本颜色
  Color _getLetterTextColor(String letter, int index) {
    if (!hasAnswered) return Colors.black87;

    final correctLetters = widget.quiz.correctAnswer.split('');
    if (index < correctLetters.length &&
        letter.toLowerCase() == correctLetters[index].toLowerCase()) {
      return Colors.green;
    }
    return Colors.red;
  }
}
