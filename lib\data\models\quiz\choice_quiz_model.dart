import 'package:flutter_study_flow_by_myself/data/models/quiz/quiz_type.dart';
import 'package:isar/isar.dart';

part 'choice_quiz_model.g.dart';

@Collection()
class ChoiceQuizModel {
  Id id = Isar.autoIncrement; // 选择题id，Isar主键
  late int wordId; // 关联的单词id
  late String question; // 题干
  late List<String> options; // 选项
  late int correctOptionIndex; // 正确答案下标
  String? explanation; // 解析（可选）

  // 新增字段，用于区分不同类型的选择题
  @enumerated
  late QuizType type; // 测验题类型

  ChoiceQuizModel({
    required this.wordId,
    required this.question,
    required this.options,
    required this.correctOptionIndex,
    required this.type, // 新增参数
    this.explanation,
  });

  factory ChoiceQuizModel.fromJson(Map<String, dynamic> json) =>
      ChoiceQuizModel(
        wordId: json['wordId'],
        question: json['question'],
        options: List<String>.from(json['options']),
        correctOptionIndex: json['correctOptionIndex'],
        type: QuizType.values.firstWhere(
          // 解析枚举
          (e) => e.toString() == 'QuizType.${json['type']}',
          orElse: () => QuizType.englishToChinese, // 默认类型
        ),
        explanation: json['explanation'],
      )..id = json['id'] ?? Isar.autoIncrement;

  Map<String, dynamic> toJson() => {
    'id': id,
    'wordId': wordId,
    'question': question,
    'options': options,
    'correctOptionIndex': correctOptionIndex,
    'type': type.toString().split('.').last, // 只保存枚举名
    'explanation': explanation,
  };
}
