import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/app/managers/user_word_book_state_manager.dart';
import 'package:flutter_study_flow_by_myself/data/models/user_word_book_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_model.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/review_item_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/user_word_book_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/word_repository.dart';
import 'package:get/get.dart';

class WordBookDetailController extends GetxController {
  // 依赖注入
  final UserWordBookRepository _userWordBookRepository;
  final WordRepository _wordRepository;
  final LogService _log;

  // 可观察状态变量
  final userWordBook = Rx<UserWordBookModel?>(null);
  final isLoading = true.obs;
  final progressPercentage = 0.obs;
  final dailyWordCount = 5.obs;
  final words = <WordModel>[].obs;
  final isLoadingWords = false.obs;
  final learningStats = <String, dynamic>{}.obs;
  final userWordBookId = RxInt(0);

  WordBookDetailController({
    required UserWordBookRepository userWordBookRepository,
    required WordRepository wordRepository,
    required ReviewItemRepository reviewItemRepository,
    required LogService log,
  }) : _userWordBookRepository = userWordBookRepository,
       _wordRepository = wordRepository,
       _log = log;

  @override
  void onInit() {
    super.onInit();
    // 从路由参数获取单词本ID
    final Map<String, dynamic>? args = Get.arguments;
    if (args != null && args.containsKey('userWordBookId')) {
      userWordBookId.value = args['userWordBookId'] as int;
      loadWordBookDetails();
    } else {
      _log.e('未提供单词本ID');
      Get.snackbar('错误', '未提供单词本ID', snackPosition: SnackPosition.BOTTOM);
    }
  }

  // 加载单词本详情
  Future<void> loadWordBookDetails() async {
    try {
      isLoading.value = true;

      // 1. 加载用户单词本信息
      final wordBook = await _userWordBookRepository.getUserWordBookById(
        userWordBookId.value,
      );

      if (wordBook == null) {
        _log.e('找不到指定的单词本: ${userWordBookId.value}');
        Get.snackbar('错误', '找不到指定的单词本', snackPosition: SnackPosition.BOTTOM);
        return;
      }

      userWordBook.value = wordBook;

      // 直接使用数据库中的值，不做限制
      dailyWordCount.value = wordBook.dailyNewWordsCount;

      // 2. 加载学习进度
      loadLearningProgress();

      // 3. 加载单词列表预览
      loadWordPreview();

      _log.i('成功加载单词本详情: ${wordBook.id}');
    } catch (e) {
      _log.e('加载单词本详情失败', error: e);
      Get.snackbar('错误', '加载单词本详情失败', snackPosition: SnackPosition.BOTTOM);
    } finally {
      isLoading.value = false;
    }
  }

  // 加载学习进度
  Future<void> loadLearningProgress() async {
    try {
      final stats = await _userWordBookRepository.getLearningStatistics(
        userWordBookId.value,
        loadRelations: true,
      );

      learningStats.assignAll(stats);
      progressPercentage.value = stats['progressPercentage'] as int;

      _log.i(
        '加载学习进度成功: 已学习 ${stats['learnedCount']}/${stats['totalCount']} 个单词',
      );
    } catch (e) {
      _log.e('加载学习进度失败', error: e);
      Get.snackbar('提示', '加载学习进度失败', snackPosition: SnackPosition.BOTTOM);
    }
  }

  // 加载单词预览
  Future<void> loadWordPreview({int limit = 20}) async {
    try {
      isLoadingWords.value = true;

      // 从单词本加载单词预览
      final wordList = await _wordRepository.getWordsByWordBookId(
        userWordBookId.value,
      );

      // 手动限制数量
      final limitedList = wordList.take(limit).toList();
      words.assignAll(limitedList);

      _log.i('加载单词预览成功: ${limitedList.length}个单词');
    } catch (e) {
      _log.e('加载单词预览失败', error: e);
      Get.snackbar('提示', '加载单词预览失败', snackPosition: SnackPosition.BOTTOM);
    } finally {
      isLoadingWords.value = false;
    }
  }

  // 设置每日学习数量
  Future<void> setDailyNewWordsCount(int count) async {
    try {
      if (userWordBook.value == null) return;

      await _userWordBookRepository.updateDailyNewWordsCount(
        userWordBookId.value,
        count,
      );

      dailyWordCount.value = count;

      // 更新用户单词本
      await loadWordBookDetails();

      Get.snackbar(
        '成功',
        '每日学习数量已设置为 $count',
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      _log.e('设置每日学习数量失败', error: e);
      Get.snackbar('错误', '设置每日学习数量失败', snackPosition: SnackPosition.BOTTOM);
    }
  }

  // 激活单词本
  Future<void> activateWordBook() async {
    try {
      await _userWordBookRepository.activateUserWordBook(userWordBookId.value);

      // 更新单词本状态
      await loadWordBookDetails();

      // 同步全局状态
      await Get.find<UserWordBookStateManager>().syncActiveUserWordBook();

      Get.snackbar('成功', '单词本已激活', snackPosition: SnackPosition.BOTTOM);
    } catch (e) {
      _log.e('激活单词本失败', error: e);
      Get.snackbar('错误', '激活单词本失败', snackPosition: SnackPosition.BOTTOM);
    }
  }

  // 停用单词本
  Future<void> deactivateWordBook() async {
    try {
      await _userWordBookRepository.deactivateUserWordBook(
        userWordBookId.value,
      );

      // 更新单词本状态
      await loadWordBookDetails();

      // 同步全局状态
      await Get.find<UserWordBookStateManager>().syncActiveUserWordBook();

      Get.snackbar('成功', '单词本已停用', snackPosition: SnackPosition.BOTTOM);
    } catch (e) {
      _log.e('停用单词本失败', error: e);
      Get.snackbar('错误', '停用单词本失败', snackPosition: SnackPosition.BOTTOM);
    }
  }
}
