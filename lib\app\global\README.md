# global 目录说明文档

## 目录职责

`global` 目录存放与任何特定业务逻辑无关的通用组件、样式、工具和常量。这些资源在整个应用中共享，提供一致的视觉风格和通用功能支持，确保代码的可重用性和一致性。

## 目录结构

```
global/
├── widgets/                  # 通用 UI 组件
├── themes/                   # 主题设置
├── utils/                    # 工具类、扩展方法
└── constants/                # 常量配置
```

## 设计理念

- **一致性**：提供统一的视觉风格和交互体验
- **可重用性**：减少重复代码，提高开发效率
- **可维护性**：将通用资源集中管理，便于统一修改和更新
- **解耦**：与具体业务逻辑分离，降低耦合度

## 主要文件夹说明

### widgets/

- **职责**：存放可复用的UI组件，这些组件不包含业务逻辑，只关注外观和基本交互
- **内容**：自定义按钮、加载指示器、输入框、卡片等通用UI元素
- **设计原因**：避免重复实现相同的UI组件，确保应用视觉的一致性
- **典型文件**：
  - `custom_button.dart`：应用统一风格的按钮
  - `loading_indicator.dart`：加载状态指示器
  - `error_widget.dart`：错误状态显示组件

### themes/

- **职责**：定义应用的视觉风格，包括颜色、字体、形状等主题元素
- **内容**：`ThemeData` 配置、颜色常量、文字样式、主题切换逻辑
- **设计原因**：集中管理应用外观，支持亮暗模式切换，确保视觉一致性
- **典型文件**：
  - `app_theme.dart`：应用的主题配置
  - `app_colors.dart`：颜色常量定义
  - `text_styles.dart`：文字样式定义

### utils/

- **职责**：提供通用功能和辅助方法，处理常见的非业务逻辑任务
- **内容**：日期格式化、字符串处理、表单验证、设备信息获取等工具函数
- **设计原因**：避免重复实现常用功能，提高代码复用率
- **典型文件**：
  - `date_utils.dart`：日期相关的辅助方法
  - `string_utils.dart`：字符串处理函数
  - `validation_utils.dart`：表单验证逻辑
  - `extensions/`：Dart扩展方法

### constants/

- **职责**：定义应用中使用的常量，避免硬编码和魔法数字
- **内容**：API键、默认值、枚举值、配置参数等
- **设计原因**：集中管理常量，便于维护和更新
- **典型文件**：
  - `api_constants.dart`：API相关常量
  - `app_constants.dart`：应用通用常量
  - `assets_constants.dart`：资源文件路径常量

## 易混淆点

1. **`global/widgets` 与模块内 `widgets` 的区别**：
   - `global/widgets` 包含**全局通用**的UI组件，不依赖于任何特定业务逻辑
   - 模块内的 `widgets` 只在该模块内使用，通常包含特定业务逻辑

2. **`global/constants` 与 `app/routes` 中常量的区别**：
   - `global/constants` 存放应用中的通用常量，如API键、默认值等
   - `app/routes` 特定存放路由相关的常量

3. **`global/utils` 与第三方工具库的关系**：
   - `global/utils` 提供项目特定的工具方法，通常是对第三方库的封装或自定义功能
   - 对于已有完善第三方库的功能，应直接使用库而非重新实现

## 最佳实践

- 全局组件应设计为高度可定制的，支持通过参数配置外观和行为
- 主题配置应支持亮暗模式切换，并考虑不同设备尺寸的适配
- 工具方法应单一职责，专注于解决特定问题
- 常量应按类别组织，避免单个文件过大

## 代码示例

### 创建通用按钮组件
```dart
// lib/app/global/widgets/custom_button.dart
import 'package:flutter/material.dart';
import 'package:flutter_base_app_template/app/global/themes/app_colors.dart';

class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final bool isLoading;
  final bool isOutlined;
  
  const CustomButton({
    Key? key,
    required this.text,
    required this.onPressed,
    this.isLoading = false,
    this.isOutlined = false,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: isOutlined ? Colors.transparent : AppColors.primary,
        side: isOutlined ? BorderSide(color: AppColors.primary) : null,
        // 其他样式...
      ),
      onPressed: isLoading ? null : onPressed,
      child: isLoading
          ? SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: isOutlined ? AppColors.primary : Colors.white,
              ),
            )
          : Text(text),
    );
  }
}
```

### 使用主题配置
```dart
// lib/app/global/themes/app_theme.dart
import 'package:flutter/material.dart';
import 'app_colors.dart';
import 'text_styles.dart';

class AppTheme {
  static ThemeData lightTheme = ThemeData(
    primaryColor: AppColors.primary,
    scaffoldBackgroundColor: AppColors.background,
    textTheme: TextTheme(
      bodyLarge: TextStyles.body,
      titleLarge: TextStyles.title,
      // 其他文字样式...
    ),
    // 其他主题配置...
  );
  
  static ThemeData darkTheme = ThemeData(
    // 暗色主题配置...
  );
}
```

## 相关联文件

- `lib/app/services/theme_service.dart`：与主题相关的服务
- `lib/main.dart`：应用主题的配置
- 各模块的UI文件：使用全局组件和主题

## 常见问题与解决方案

- **主题不一致**：确保所有UI组件都使用主题中定义的颜色和样式，而非硬编码值
- **组件复用问题**：设计通用组件时应考虑各种使用场景，提供足够的自定义选项
- **常量管理混乱**：按功能域将常量分类存放，避免单个常量文件过大
- **工具方法冗余**：在添加新工具方法前，检查是否已有类似功能的方法或第三方库 