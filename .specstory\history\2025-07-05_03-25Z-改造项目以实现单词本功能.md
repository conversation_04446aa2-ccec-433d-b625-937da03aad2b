<!-- Generated by SpecStory -->

# 改造项目以实现单词本功能 (2025-07-05 03:25Z)

_**User**_

@models_documentation.md @flutter_app_architecture.md @word_book_implementation.md 要实现 我们需要改造现有项目，实现单词与单词本的关联，使学习和复习功能基于用户选择的特定单词本进行。 需要做哪些事情 给我一个详细的计划

---

_**Assistant**_

<think><details><summary>Thought Process</summary>Let me analyze the files and understand the task at hand.

Based on the provided files, I see that we need to implement a feature where words are associated with word books, and the learning/review functionality should be based on the specific word book selected by the user. 

From models_documentation.md, I understand that:
- WordModel represents individual words
- WordBookModel represents word books/collections
- ReviewItemModel tracks learning progress for words
- There should be a relationship between words and word books

From flutter_app_architecture.md, I understand the app architecture:
- Service layer provides infrastructure
- Provider layer directly interacts with data sources
- Repository layer coordinates providers and implements business logic
- Controller layer manages UI state and interacts with repositories
- View layer displays UI and handles user interaction

From word_book_implementation.md, I see a detailed plan already exists for implementing this feature, including:
1. Data model modifications
2. Data access layer changes
3. Business logic modifications

Based on this information, I can create a detailed implementation plan.
</details></think>

---

# 单词本关联功能实现计划

## 1. 数据模型修改

### WordModel
```dart
@Collection()
class WordModel {
  // 现有字段保持不变
  
  // 添加与WordBookModel的多对多关系
  final wordBooks = IsarLinks<WordBookModel>();
}
```

### WordBookModel
```dart
@Collection()
class WordBookModel {
  // 现有字段保持不变
  
  // 添加与WordModel的多对多关系
  final words = IsarLinks<WordModel>();
  
  // 添加学习计划相关字段
  int dailyNewWordsCount = 10;
  DateTime? lastLearningDate;
  bool isActive = false;
}
```

### ReviewItemModel
```dart
@Collection()
class ReviewItemModel {
  // 使用IsarLink替代直接存储ID
  final word = IsarLink<WordModel>();
  final wordBook = IsarLink<WordBookModel>();
  
  // 其他字段保持不变
}
```

## 2. 数据访问层实现

### 创建WordBookLocalProvider
- 实现`getAllWordBooks()`方法
- 实现`getActiveWordBook()`方法
- 实现`setActiveWordBook()`方法
- 实现`downloadAndSaveWordBook()`方法
- 实现`updateWordBookSettings()`方法

### 修改ReviewItemLocalProvider
- 添加`getDueReviewItemsForWordBook()`方法
- 添加`getOrCreateReviewItem()`方法

### 创建WordBookRepository
- 实现单词本业务逻辑
- 协调本地和远程数据

## 3. 业务逻辑修改

### 创建WordBookSelectionController
- 实现单词本选择逻辑
- 实现单词本下载功能
- 实现单词本设置功能

### 修改HomeController
- 添加检查激活单词本逻辑
- 引导用户选择单词本

### 修改LearningController
- 基于激活单词本获取学习内容
- 实现单词本学习进度跟踪

### 修改ReviewController
- 基于激活单词本获取复习内容
- 实现单词本复习进度跟踪

## 4. UI实现

### 创建单词本选择页面
- 显示可用单词本列表
- 提供下载功能
- 提供单词本设置功能

### 修改Home页面
- 显示当前激活单词本信息
- 提供切换单词本功能

### 更新学习和复习页面
- 确保显示正确的单词本信息
- 更新学习/复习进度UI

## 5. 具体实施步骤

1. 修改数据模型并生成Isar代码
   ```
   flutter pub run build_runner build
   ```

2. 实现数据访问层和Repository层

3. 实现单词本选择和激活功能

4. 修改学习和复习功能，基于激活单词本

5. 测试和验证功能正常

## 6. 实现顺序

1. **首阶段**: 数据模型修改与Isar代码生成
2. **第二阶段**: 数据访问层实现
3. **第三阶段**: 单词本选择和设置UI
4. **第四阶段**: 业务逻辑修改
5. **最终阶段**: 功能测试和优化

这个计划遵循项目架构规范，确保单词本与单词的关联功能能够顺利实现，使学习和复习功能基于用户选择的特定单词本进行。

---

