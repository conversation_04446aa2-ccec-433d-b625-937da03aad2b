# Quiz Views 目录说明文档

## 目录职责

`quiz/views` 目录负责实现学习模块中测验环节的用户界面部分，提供了单词测验的各种视图组件和页面布局。该目录专注于UI渲染，不包含业务逻辑，遵循MVVM架构中视图层的职责。

## 文件结构

```
quiz/views/
└── quiz_page.dart      # 测验主页面，展示题目、选项和反馈
```

## 核心功能

- **测验界面展示**：展示当前测验题目、选项和交互元素
- **用户答题反馈**：提供视觉和动画反馈，指示答案正确或错误
- **测验进度展示**：显示当前测验进度和剩余题目
- **计时展示**：显示答题倒计时（若启用）
- **结果跳转**：测验完成后提供到结果页的过渡效果

## 主要文件说明

### quiz_page.dart

- **职责**：测验页面的主要UI组件
- **依赖关系**：
  - `QuizController`：获取测验数据和处理用户交互
  - `LearningController`：同步全局学习状态
  - 各种Flutter UI组件
- **主要方法和属性**：
  - `QuizPage`：测验页面的主StatelessWidget
  - `_buildQuestion()`：构建当前题目的展示
  - `_buildOptions()`：构建选项列表
  - `_buildProgressIndicator()`：构建测验进度指示器
  - `_buildFeedback()`：构建答题反馈组件
- **实现的核心功能**：
  - 渲染多种题型（选择题、连线题、排序题等）
  - 处理用户选择行为并提供视觉反馈
  - 动态更新测验进度
  - 实现答题时效的视觉倒计时

## 运行流程

1. 用户进入测验阶段，`LearningController` 将 `currentStage` 设置为测验模式
2. `QuizPage` 被加载，从 `QuizController` 获取初始测验数据
3. 页面渲染当前题目和选项
4. 用户进行选择，页面调用 `QuizController` 的相应方法处理选择
5. 显示正确/错误的视觉反馈
6. 自动或手动切换到下一题，更新进度指示器
7. 测验完成后，显示过渡效果，由 `LearningController` 引导到结果页面

## 状态管理

- 页面主要通过 `GetX` 的 `Obx` 和 `.obs` 变量监听 `QuizController` 中的状态变化
- 监听的关键状态包括：
  - `currentQuestion`：当前显示的题目
  - `selectedOption`：用户当前选择
  - `isAnswerCorrect`：当前答案是否正确
  - `questionProgress`：当前进度
  - `remainingTime`：剩余答题时间(如果有计时功能)

## 与其他模块的关系

- **quiz/controllers**：获取测验数据和逻辑控制
- **learning模块**：通过 `LearningController` 同步全局学习进度
- **widgets**：使用共享UI组件如进度条、按钮等

## 常见混淆点

1. **视图与控制器的分离**：
   - 视图仅负责UI渲染，不包含业务逻辑
   - 所有测验逻辑操作应在 `QuizController` 中实现
   - 视图通过响应式绑定反映控制器状态变化

2. **页面状态vs全局状态**：
   - 页面仅关注当前测验交互所需的状态
   - 学习进度等全局状态由 `LearningController` 管理

## 最佳实践

- **响应式设计**：确保UI在不同屏幕尺寸下正确显示
- **动画过渡**：使用适当的动画使测验体验更流畅
- **样式一致性**：遵循应用的主题和样式指南
- **可访问性**：确保UI元素满足基本的可访问性要求
- **错误处理**：妥善处理加载失败、网络问题等异常情况
- **按需渲染**：对大量选项使用ListView.builder等按需渲染方式

## 代码示例

```dart
class QuizPage extends GetView<QuizController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(() => Text("问题 ${controller.currentIndex + 1}/${controller.totalQuestions}")),
        actions: [
          // 显示计时器或其他操作按钮
          Obx(() => Padding(
            padding: const EdgeInsets.all(8.0),
            child: Center(
              child: Text(
                "${controller.remainingTime}s",
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ),
          )),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return Center(child: CircularProgressIndicator());
        }
        
        return Column(
          children: [
            // 进度指示器
            LinearProgressIndicator(
              value: controller.progress,
              backgroundColor: Colors.grey[200],
              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
            ),
            
            // 题目内容
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(16.0),
                child: _buildCurrentQuestion(),
              ),
            ),
          ],
        );
      }),
    );
  }
  
  Widget _buildCurrentQuestion() {
    final question = controller.currentQuestion;
    
    // 根据题型构建不同的UI
    switch (question.type) {
      case QuestionType.multipleChoice:
        return _buildMultipleChoiceQuestion(question);
      case QuestionType.matching:
        return _buildMatchingQuestion(question);
      default:
        return Text("不支持的题型");
    }
  }
  
  Widget _buildMultipleChoiceQuestion(Question question) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 问题描述
        Text(
          question.prompt,
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 20),
        
        // 选项列表
        ...question.options.map((option) => _buildOptionItem(option)).toList(),
      ],
    );
  }
  
  Widget _buildOptionItem(QuizOption option) {
    final isSelected = controller.selectedOptionId.value == option.id;
    final hasAnswered = controller.hasAnswered.value;
    final isCorrect = hasAnswered && option.id == controller.correctOptionId.value;
    final isWrong = hasAnswered && isSelected && !isCorrect;
    
    // 根据答题状态设置不同的样式
    Color backgroundColor = Colors.white;
    if (isCorrect) backgroundColor = Colors.green[100]!;
    if (isWrong) backgroundColor = Colors.red[100]!;
    if (isSelected && !hasAnswered) backgroundColor = Colors.blue[50]!;
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: InkWell(
        onTap: hasAnswered ? null : () => controller.selectOption(option.id),
        child: Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isSelected ? Colors.blue : Colors.grey[300]!,
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              Expanded(child: Text(option.text, style: TextStyle(fontSize: 16))),
              if (isCorrect) Icon(Icons.check_circle, color: Colors.green),
              if (isWrong) Icon(Icons.cancel, color: Colors.red),
            ],
          ),
        ),
      ),
    );
  }
}
```

## 相关文件

- **lib/modules/learning/quiz/controllers/quiz_controller.dart**: 提供测验数据和业务逻辑
- **lib/modules/learning/controllers/learning_controller.dart**: 管理整体学习流程
- **lib/modules/learning/result/views/result_page.dart**: 测验完成后的结果页面

## 常见问题与解决方案

1. **问题**：页面没有响应用户的选择操作  
   **解决方案**：检查控制器中的selectedOption是否正确声明为.obs变量，确保使用了正确的Obx或GetX小部件进行状态监听

2. **问题**：答题动画卡顿  
   **解决方案**：使用更高效的动画实现，考虑减少不必要的重绘

3. **问题**：题目内容过长导致布局问题  
   **解决方案**：使用SingleChildScrollView包装内容，设置适当的文本缩放策略

4. **问题**：在弱网环境下图片加载缓慢  
   **解决方案**：实现图片预加载和缓存机制，显示占位符 