# 学习流程中断与恢复机制设计

## 1. 需求概述

在单词学习流程中，用户可能随时退出应用或切换到其他任务，需要确保以下功能：

- **自动保存**：在关键点自动保存学习进度
- **状态恢复**：用户重新进入应用时，能够从上次中断的位置继续学习
- **一致性体验**：恢复后的学习体验应与中断前一致
- **无缝切换**：恢复过程对用户透明，无需额外操作

## 2. 实现思路

### 2.1 总体策略

采用"状态快照+动态重建"策略：

1. **状态快照**：在关键点将学习状态保存为快照
2. **存储机制**：将状态快照保存到Isar数据库
3. **检测恢复**：应用启动时检查是否有未完成会话
4. **状态重建**：从数据库加载状态快照并重建内存中的学习状态
5. **动态生成**：基于恢复的状态，动态生成所需的测验题

### 2.2 保存触发点

- **用户完成单词测验时**：
  * 如果不是最后一个单词，保存当前学习状态
  * 如果是最后一个单词，同时更新`isInFinalQuiz`为true并保存，避免重复保存
- **最终测验阶段答题时**：
  * 每次用户在最终测验完成题目后保存状态
  * 特别是当某个单词的`completedQuizzes`达到3次时，保存以记录学习质量


## 3. 数据结构设计

### 3.1 LearningSession 模型

`learning_session.dart` 应包含以下关键属性：

```
LearningSession {
  // 基本信息
  Id id                       // Isar数据库主键
  String sessionId            // 会话唯一标识符
  int userWordBookId          // 关联的单词本ID
  List<int> wordIds           // 学习单词ID列表(保持顺序)
  DateTime startTime          // 开始时间
  DateTime? endTime           // 结束时间(null表示未完成)
  DateTime lastStudyTime      // 最后学习时间
  
  // 学习进度
  int currentWordIndex        // 当前学习单词索引
  bool isInFinalQuiz          // 是否处于最终测验阶段
  
  // 单词学习状态 (JSON字符串)
  String wordLearningStateJson // 所有单词的学习状态JSON
}
```

### 3.2 WordLearningState 内存模型

```
WordLearningState {
  bool isRecognized           // 用户是否认识此单词
  int errorCount              // 错误次数
  int completedQuizzes        // 已完成测验题数
}
```

### 3.3 学习状态JSON结构

```json
{
  "wordId1": {
    "isRecognized": true,
    "errorCount": 0,
    "completedQuizzes": 1
  },
  "wordId2": {
    "isRecognized": false,
    "errorCount": 2,
    "completedQuizzes": 0
  }
}
```

## 4. 涉及文件及职责

### 4.1 数据层

- **`data/models/learning_session.dart`**
  - 定义LearningSession Isar模型
  - 提供JSON与Map转换方法
  - 包含会话状态辅助方法

- **`data/repositories/learning_session_repository.dart`**
  - 提供会话CRUD操作
  - 实现查询最新未完成会话
  - 处理会话完成和学习质量记录

### 4.2 服务层

- **`modules/learning/services/learning_state_service.dart`**
  - 管理内存中的学习状态
  - 提供状态操作方法
  - 状态变更通知

- **`modules/learning/services/learning_persistence_service.dart`**
  - 处理状态快照保存
  - 提供状态恢复功能
  - 实现优化的保存策略（基于关键节点）

- **`modules/learning/services/learning_navigation_service.dart`**
  - 基于状态决定导航目标
  - 处理恢复时的页面跳转

- **`modules/learning/services/quiz_generation_service.dart`**
  - 根据单词学习状态动态生成测验题
  - 支持最终测验阶段的题目重建

### 4.3 控制器层

- **`modules/learning/controllers/learning_controller.dart`**
  - 协调各服务
  - 处理应用生命周期事件
  - 触发状态保存操作

## 5. 关键流程说明

### 5.1 状态保存流程

```
学习状态变更 → LearningStateService更新内存状态
    → LearningController检测关键点 → 调用LearningPersistenceService
    → 将内存状态序列化为JSON → 更新LearningSession实体
    → 保存到Isar数据库
```

### 5.2 状态恢复流程

```
应用启动 → LearningController检查未完成会话 
    → 调用LearningSessionRepository获取最新会话
    → 调用LearningPersistenceService恢复内存状态
    → 更新LearningStateService状态
    → 调用LearningNavigationService导航到正确页面
```

### 5.3 最终测验题重建逻辑

```dart
if (session.isInFinalQuiz) {
    // 1. 遍历所有单词
    for (wordId in session.wordIds) {
        wordState = getWordLearningState(wordId);
        remainingQuizzes = 3 - wordState.completedQuizzes;
        
        // 2. 为每个单词生成剩余数量的测验题
        if (remainingQuizzes > 0) {
            quizzes = quizGenerationService.generateQuizzes(
                wordId, remainingQuizzes);
            finalQuizList.addAll(quizzes);
        }
    }
    
    // 3. 随机打乱测验题顺序
    finalQuizList.shuffle();
    
    // 4. 开始最终测验
    navigateToQuizPage(finalQuizList);
} else {
    navigateToRecallPage(wordIds[currentWordIndex]);
}
```

## 6. 实现注意事项

- **动态测验生成**：每次恢复时重新生成测验题，无需保存测验进度
- **一致性保证**：确保同一单词不会生成重复的测验题
- **学习效果**：保证测验题的随机性和覆盖全面性
- **性能考虑**：测验题生成放在恢复流程中，避免阻塞UI
- **错误处理**：若单词数据不完整，提供合理的回退机制

## 7. 扩展考虑

- **测验策略优化**：可以基于错误次数优先出题
- **学习算法调整**：根据实际使用情况调整测验生成算法
- **进度展示优化**：展示更精确的学习进度指示

通过这种优化的设计，我们不仅简化了状态存储，还提高了测验的灵活性和学习效果。动态重建测验题的方式使得恢复流程更加符合实际学习需求，同时减少了存储开销。 