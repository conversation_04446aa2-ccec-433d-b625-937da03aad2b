# services 目录说明文档

## 目录职责

`services` 目录提供全局性的、贯穿应用生命周期的业务功能或工具服务。这些服务通常是单例的，在应用启动时初始化，并在整个应用中通过依赖注入使用。服务层负责封装复杂的业务逻辑，协调数据层和UI层之间的交互。

## 目录结构

```
services/
├── auth_service.dart         # 鉴权服务（登录、注册、Token管理、用户状态）
├── storage_service.dart      # 本地存储服务
├── log_service.dart          # 日志记录服务
└── theme_service.dart        # 主题切换服务
```

## 设计理念

- **单一职责**：每个服务专注于一个特定的功能域
- **全局可访问**：通过GetX依赖注入在任何地方可访问
- **持久化存在**：通常注册为永久服务（`permanent: true`）
- **业务与技术解耦**：服务层封装技术实现细节，向Controller提供业务友好的接口

## 主要文件说明

### auth_service.dart

- **职责**：管理用户认证状态和相关业务逻辑，包括登录、注册、Token管理和会话维护
- **内容**：用户登录状态跟踪、Token获取与刷新、用户信息管理
- **依赖**：`UserRepository` 获取用户数据，`StorageService` 存储Token
- **设计原因**：将认证逻辑从UI和控制器中分离，提供统一的认证状态访问点
- **典型方法**：
  - `login(String username, String password)`
  - `logout()`
  - `isLoggedIn`
  - `refreshToken()`

### storage_service.dart

- **职责**：提供高层级的、业务导向的本地存储接口
- **内容**：封装底层存储细节，提供业务友好的数据存取方法
- **依赖**：`LocalStorageProvider` 进行实际的底层读写
- **设计原因**：解耦业务逻辑与具体存储技术，提供统一的存储接口
- **典型方法**：
  - `saveUserToken(String token)`
  - `getUserToken()`
  - `saveThemeMode(ThemeMode mode)`
  - `getThemeMode()`

### log_service.dart

- **职责**：提供统一的日志记录接口
- **内容**：不同级别的日志方法（debug, info, warning, error, fatal）
- **设计原因**：统一日志格式，便于调试和错误追踪，可轻松切换日志后端
- **典型方法**：
  - `debug(String message)`
  - `error(String message, [Exception? exception, StackTrace? stackTrace])`

### theme_service.dart

- **职责**：管理应用的主题模式和相关配置
- **内容**：主题切换逻辑、用户主题偏好持久化
- **依赖**：`StorageService` 存储用户选择的主题
- **设计原因**：将主题管理逻辑集中化，便于全局主题切换
- **典型方法**：
  - `switchTheme()`
  - `saveThemeMode(ThemeMode mode)`
  - `getThemeMode()`

## 依赖关系说明

- **服务之间的依赖**：服务可能相互依赖，例如 `AuthService` 使用 `StorageService` 存储Token
- **与数据层的依赖**：服务通常依赖于 `data/repositories` 进行数据访问
- **生命周期**：服务通常在应用启动时通过 `Get.put(..., permanent: true)` 注册，确保全局单例

## 易混淆点

1. **`app/services` 与 `data/providers` 的区别**：
   - `app/services` 提供**业务级服务**，处理高层业务逻辑，如用户认证、主题管理
   - `data/providers` 提供**数据源实现**，处理低层数据获取，如HTTP请求、数据库操作
   - 服务层调用数据层，而非直接处理数据获取细节

2. **`StorageService` 与 `LocalStorageProvider` 的区别**：
   - `StorageService` 提供**业务友好的接口**，如 `saveUserSettings()`
   - `LocalStorageProvider` 提供**底层技术接口**，如 `write(String key, dynamic value)`
   - 这种分层使得业务代码不需关心底层存储技术

## 最佳实践

- 服务应通过 `Get.put(..., permanent: true)` 在应用启动时注册
- 服务应避免直接依赖于UI组件或控制器
- 服务方法应清晰表达业务意图，隐藏实现细节
- 服务应处理自身抛出的异常，避免将技术异常直接暴露给上层

## 代码示例

### 创建和注册服务
```dart
// lib/app/services/auth_service.dart
import 'package:get/get.dart';
import 'package:flutter_base_app_template/data/repositories/user_repository.dart';
import 'package:flutter_base_app_template/app/services/storage_service.dart';

class AuthService extends GetxService {
  final UserRepository _userRepository = Get.find<UserRepository>();
  final StorageService _storageService = Get.find<StorageService>();
  
  final Rx<User?> _currentUser = Rx<User?>(null);
  
  User? get currentUser => _currentUser.value;
  bool get isLoggedIn => currentUser != null;
  
  @override
  void onInit() {
    super.onInit();
    // 从本地存储加载用户信息
    _loadUserFromStorage();
  }
  
  Future<void> login(String username, String password) async {
    try {
      final authResult = await _userRepository.login(username, password);
      _currentUser.value = authResult.user;
      await _storageService.saveUserToken(authResult.token);
    } catch (e) {
      // 处理登录错误
      rethrow; // 或转换为业务友好的错误
    }
  }
  
  Future<void> logout() async {
    await _storageService.removeUserToken();
    _currentUser.value = null;
  }
  
  // 其他认证相关方法...
}

// lib/main.dart 或 initial_binding.dart
void initServices() {
  Get.put(StorageService(), permanent: true);
  Get.put(AuthService(), permanent: true);
  // 其他服务...
}
```

### 使用服务
```dart
// 在控制器或其他需要的地方
class LoginController extends GetxController {
  final AuthService _authService = Get.find<AuthService>();
  final TextEditingController usernameController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final RxBool isLoading = false.obs;
  
  Future<void> login() async {
    isLoading.value = true;
    try {
      await _authService.login(
        usernameController.text,
        passwordController.text,
      );
      Get.offAllNamed(HomeRoutes.HOME);
    } catch (e) {
      Get.snackbar('登录失败', e.toString());
    } finally {
      isLoading.value = false;
    }
  }
}
```

## 相关联文件

- `lib/main.dart`：初始化和注册服务
- `lib/data/repositories/`：服务调用的数据仓储
- `lib/data/providers/`：底层数据提供者
- `lib/modules/*/controllers/`：使用服务的控制器

## 生命周期说明

1. **初始化**：服务通常在应用启动时初始化（通过 `Get.put()`）
2. **使用**：在整个应用生命周期内可通过 `Get.find()` 访问
3. **销毁**：对于 `permanent: true` 的服务，它们将持续存在直到应用关闭

## 常见问题与解决方案

- **服务无法访问**：确保在使用前已通过 `Get.put()` 注册服务
- **服务状态不同步**：使用 `Rx` 变量和 `GetX` 或 `Obx` 观察响应式变化
- **循环依赖**：避免服务之间的循环依赖，可通过分离关注点或使用事件总线解决
- **内存泄漏**：确保长时间运行的异步操作在服务销毁时被取消 