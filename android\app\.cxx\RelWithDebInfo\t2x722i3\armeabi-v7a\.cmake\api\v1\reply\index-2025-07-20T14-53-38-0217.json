{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/AppData/Local/Android/Sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/AppData/Local/Android/Sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/AppData/Local/Android/Sdk/cmake/3.22.1/bin/ctest.exe", "root": "D:/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-36135adcee32f930ea9b.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-a28cb0b4c657fcd91455.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-fb90ed4181e1ab9f3bed.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-a28cb0b4c657fcd91455.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-fb90ed4181e1ab9f3bed.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-36135adcee32f930ea9b.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}