import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/app/services/audio_service.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/word_repository.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_model.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/services/review_state_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/services/review_quiz_generation_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/services/review_navigation_service.dart';

/// 复习详情控制器
///
/// 负责管理复习详情页面的状态和交互逻辑
/// 职责：
/// - 显示当前单词的详细信息
/// - 处理音频播放（进入页面自动播放）
/// - 处理用户点击"继续复习"按钮
/// - 为当前单词继续生成题目
/// - 导航回测验页面
class ReviewDetailController extends GetxController {
  // ==================== 依赖注入 ====================
  final ReviewStateService _stateService;
  final ReviewNavigationService _navigationService;
  final WordRepository _wordRepository;
  final AudioService _audioService;
  final ReviewQuizGenerationService _quizGenerationService;
  final LogService _log;

  ReviewDetailController({
    required ReviewStateService stateService,
    required ReviewNavigationService navigationService,
    required WordRepository wordRepository,
    required AudioService audioService,
    required ReviewQuizGenerationService quizGenerationService,
    required LogService log,
  }) : _stateService = stateService,
       _navigationService = navigationService,
       _wordRepository = wordRepository,
       _audioService = audioService,
       _quizGenerationService = quizGenerationService,
       _log = log;

  // 响应式状态
  final currentWord = Rx<WordModel?>(null);
  final isLoading = false.obs;

  // 自动播放相关状态
  bool _hasPlayedForCurrentWord = false;
  int _lastPlayedWordId = -1;

  @override
  void onInit() {
    super.onInit();
    _log.i('初始化复习详情控制器');

    // 监听状态服务中的详情单词ID变化
    ever(_stateService.currentDetailWordIdObs, (wordId) {
      if (wordId > 0) {
        _log.i('检测到详情页单词ID变化: $wordId');
        _hasPlayedForCurrentWord = false; // 重置播放标志
        _loadWordById(wordId);
      }
    });

    // 🔑 关键：监听单词加载完成，确保页面渲染后再播放音频
    ever(currentWord, (WordModel? word) {
      if (word != null && !_hasPlayedForCurrentWord) {
        _scheduleAutoPlayAudio(word);
      }
    });

    _initializeWord();
  }

  /// 安排自动播放音频
  /// 确保页面渲染完成后再播放，避免音频播放问题
  void _scheduleAutoPlayAudio(WordModel word) {
    _log.i('单词加载完成，准备播放音频: ${word.spelling}');

    // 确保页面渲染完成后再播放
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(milliseconds: 300), () {
        if (!_hasPlayedForCurrentWord && currentWord.value?.id == word.id) {
          _log.i('页面渲染完成，开始播放音频');
          requestPlayWordAudio();
          _hasPlayedForCurrentWord = true;
        }
      });
    });
  }

  /// 初始化单词
  void _initializeWord() {
    final wordId = _stateService.currentDetailWordIdObs.value;
    if (wordId > 0) {
      _loadWordById(wordId);
    } else {
      _log.w('没有指定要显示的单词ID');
    }
  }

  /// 根据ID加载单词
  Future<void> _loadWordById(int wordId) async {
    try {
      isLoading.value = true;
      _log.i('加载单词详情: ID=$wordId');

      final word = await _wordRepository.getWordById(wordId);
      if (word != null) {
        currentWord.value = word;
        _log.i('单词详情加载成功: ${word.spelling}');
      } else {
        _log.w('找不到单词: ID=$wordId');
        Get.snackbar('错误', '找不到单词信息');
      }
    } catch (e) {
      _log.e('加载单词详情失败: $e');
      Get.snackbar('错误', '加载单词详情失败，请重试');
    } finally {
      isLoading.value = false;
    }
  }

  /// 播放单词音频（手动点击）
  void playWordAudio() {
    if (currentWord.value != null) {
      final word = currentWord.value!;
      final String spelling = word.spelling;

      if (word.audioUrl != null && word.audioUrl!.isNotEmpty) {
        _audioService.playWordAudio(spelling, url: word.audioUrl);
      } else {
        _audioService.playWordAudio(spelling);
      }

      _lastPlayedWordId = word.id;
    }
  }

  /// 智能播放音频（自动播放，防重复）
  void requestPlayWordAudio() {
    if (currentWord.value != null &&
        currentWord.value!.id != _lastPlayedWordId) {
      playWordAudio();
    }
  }

  /// 开始测验（从详情页返回测验页面）
  ///
  /// 为当前详情页的单词生成题目，然后返回测验页面
  /// 确保用户返回后继续为同一个单词出题
  void startQuiz() async {
    try {
      _log.i('从复习详情页返回测验页面');

      // 优先使用已加载的单词对象，避免重复获取
      final currentWordObj = currentWord.value;

      if (currentWordObj != null) {
        _log.i('使用已加载的单词 ${currentWordObj.spelling} 生成题目');
        await _quizGenerationService.generateQuizForWord(currentWordObj);
      } else {
        _log.w('当前单词为空，生成下一道题目');
        await _quizGenerationService.generateNextQuiz();
      }

      // 使用导航服务返回测验页面
      await _navigationService.navigateToQuiz();
    } catch (e) {
      _handleNavigationError(e);
    }
  }

  /// 处理导航错误
  void _handleNavigationError(dynamic error) {
    _log.e('返回测验页面失败: $error');
    Get.snackbar('错误', '返回测验失败，请重试');

    // 如果生成题目失败，仍然尝试导航
    _navigationService.navigateToQuiz().catchError((navError) {
      _log.e('导航服务也失败了: $navError');
      // 最后的fallback，直接返回
      Get.back();
    });
  }

  @override
  void onClose() {
    _log.i('ReviewDetailController销毁');
    super.onClose();
  }
}
