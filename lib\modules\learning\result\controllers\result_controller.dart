import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/data/models/learning_session.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/controllers/learning_controller.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/services/learning_navigation_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/services/learning_state_service.dart';
import 'package:get/get.dart';

/// 学习结果页面控制器
///
/// 职责：
/// - 管理学习结果页面的数据统计和展示
/// - 计算学习会话的各项统计指标
/// - 处理用户与结果页面的交互操作
/// - 协调结果页面与其他页面的导航
///
/// 架构说明：
/// 本控制器专注于学习结果的统计分析和展示，通过依赖注入获取所需服务，
/// 在页面准备完成后进行统计计算，并提供用户友好的错误处理机制。
class ResultController extends GetxController {
  // ==================== 依赖注入 ====================
  /// 学习主控制器 - 提供学习会话的数据和状态管理
  ///
  /// 调用位置：
  /// - onInit() 中获取 session.value 学习会话数据
  /// - onReady() 中调用 completeSession() 完成学习会话
  ///
  /// 用途：获取当前学习会话数据和触发会话完成流程
  final LearningController _learningController;

  /// 学习状态服务 - 管理学习过程中的状态数据
  ///
  /// 调用位置：
  /// - _calculateStatistics() 中获取 wordIds.length 和 getAllWordStates()
  ///
  /// 用途：获取学习单词数量和各单词的学习状态统计
  final LearningStateService _stateService;

  /// 学习导航服务 - 处理学习流程中的页面导航
  ///
  /// 调用位置：
  /// - moveToHome() 中调用 navigateToHome() 返回首页
  /// - startNewSession() 中调用 navigateToHome() 开始新会话
  ///
  /// 用途：处理结果页面的导航操作
  final LearningNavigationService _navigationService;

  /// 日志服务 - 记录控制器运行日志
  ///
  /// 调用位置：
  /// - 所有方法中用于记录操作日志、错误信息和调试信息
  ///
  /// 用途：记录统计计算过程和错误处理信息
  final LogService _log;

  // ==================== 构造函数 ====================
  /// 构造函数 - 依赖注入
  ///
  /// 通过构造函数注入所有必要的服务和控制器实例，确保依赖关系清晰
  /// 并提高代码的可测试性和可维护性
  ///
  /// 参数：
  /// - [learningController] 学习主控制器实例
  /// - [stateService] 学习状态服务实例
  /// - [navigationService] 学习导航服务实例
  /// - [log] 日志服务实例
  ///
  /// 注册位置：lib/modules/learning/bindings/learning_binding.dart
  ResultController({
    required LearningController learningController,
    required LearningStateService stateService,
    required LearningNavigationService navigationService,
    required LogService log,
  }) : _learningController = learningController,
       _stateService = stateService,
       _navigationService = navigationService,
       _log = log;

  // ==================== 私有状态变量 ====================
  /// 当前学习会话数据
  ///
  /// 存储从主控制器获取的学习会话信息，用于统计计算
  ///
  /// 使用位置：
  /// - onInit() 中从 _learningController.session.value 获取
  /// - _calculateStatistics() 中用于验证数据有效性
  ///
  /// 设计说明：
  /// - 改为普通变量，因为无需UI监听，仅用于内部计算
  /// - 在统计计算完成后不再需要响应式更新
  ///
  /// 注意：使用普通变量而非响应式变量，因为无需UI监听
  LearningSession? _session;

  // ==================== 响应式状态变量 ====================
  /// 学习单词总数
  ///
  /// 存储本次学习会话中的单词总数量
  ///
  /// UI 使用位置：
  /// - result_page.dart:23,87 - 通过 Obx 监听，显示学习单词数量
  ///
  /// 内部使用：
  /// - _calculateStatistics() 中从 _stateService.wordIds.length 获取并设置
  ///
  /// 响应式监听：当统计计算完成时，自动更新UI显示
  final RxInt totalWords = 0.obs;

  /// 测验题目总数
  ///
  /// 存储本次学习会话中完成的测验题目总数量
  ///
  /// UI 使用位置：
  /// - result_page.dart:25,89 - 通过 Obx 监听，显示测验题数量
  ///
  /// 内部使用：
  /// - _calculateStatistics() 中通过遍历单词状态计算并设置
  ///
  /// 响应式监听：当统计计算完成时，自动更新UI显示
  final RxInt totalQuizzes = 0.obs;

  /// 正确答题总数
  ///
  /// 存储本次学习会话中正确回答的题目数量
  ///
  /// UI 使用位置：
  /// - result_page.dart:91 - 通过 Obx 监听，显示正确答题数
  ///
  /// 内部使用：
  /// - _calculateStatistics() 中通过计算 completedQuizzes - errorCount 得出
  ///
  /// 响应式监听：当统计计算完成时，自动更新UI显示
  final RxInt correctAnswers = 0.obs;

  /// 答题准确率
  ///
  /// 存储本次学习会话的答题准确率（0.0-1.0）
  ///
  /// UI 使用位置：
  /// - result_page.dart:95 - 通过 Obx 监听，显示准确率百分比
  ///
  /// 计算属性依赖：
  /// - performanceRating getter 依赖此变量计算表现评级
  /// - ratingDescription getter 依赖此变量生成评级描述
  ///
  /// 内部使用：
  /// - _calculateStatistics() 中通过 correctAnswers / totalQuizzes 计算
  ///
  /// 响应式监听：当统计计算完成时，自动更新UI显示和计算属性
  final RxDouble accuracy = 0.0.obs;
  // ==================== 生命周期方法 ====================
  /// 控制器初始化方法
  ///
  /// 在控制器创建时自动调用，负责获取学习会话数据
  ///
  /// 调用时机：
  /// - 当用户导航到学习结果页面时，GetX 自动调用此方法
  /// - 由于使用 lazyPut，只有实际进入 result 页面时才会触发
  ///
  /// 执行流程：
  /// 1. 调用父类的 onInit() 方法
  /// 2. 从主控制器获取学习会话数据
  /// 3. 进行基础的数据验证
  ///
  /// 设计说明：
  /// - 只进行数据获取，不进行复杂计算
  /// - 复杂的统计计算放在 onReady() 中执行
  @override
  void onInit() {
    super.onInit();
    _log.i('初始化结果控制器');

    try {
      // 获取学习会话数据
      _session = _learningController.session.value;

      if (_session == null) {
        _log.w('警告：学习会话数据为空');
        Get.snackbar('提示', '无法获取学习数据，请重新开始学习');
      } else {
        _log.i('成功获取学习会话数据');
      }
    } catch (e) {
      _log.e('初始化结果控制器失败', error: e);
      Get.snackbar('错误', '初始化失败，请重试');
    }
  }

  /// 控制器就绪方法
  ///
  /// 在控制器初始化完成且页面渲染完成后调用
  ///
  /// 调用时机：
  /// - 页面完全加载并渲染完成后
  /// - 确保UI已经准备好接收数据更新
  ///
  /// 执行流程：
  /// 1. 调用父类的 onReady() 方法
  /// 2. 执行统计数据计算
  /// 3. 通知主控制器完成学习会话
  ///
  /// 设计说明：
  /// - 在页面渲染完成后进行复杂计算，避免阻塞UI
  /// - 统计计算完成后自动触发UI更新
  /// - 处理会话完成的后续流程
  @override
  void onReady() {
    super.onReady();
    _log.i('结果页面准备完成，开始处理学习结果');

    try {
      // 计算统计数据
      _calculateStatistics();

      // 通知主控制器执行会话完成流程
      _learningController.completeSession();

      _log.i('学习结果处理完成');
    } catch (e) {
      _log.e('处理学习结果失败', error: e);
      Get.snackbar('错误', '处理学习结果失败，请重试');
    }
  }

  // ==================== 私有方法 ====================
  /// 计算学习统计数据
  ///
  /// 根据学习状态服务中的数据计算各项统计指标
  ///
  /// 调用位置：
  /// - onReady() 中在页面准备完成后调用
  ///
  /// 业务逻辑：
  /// 1. 验证学习会话数据的有效性
  /// 2. 获取学习单词总数
  /// 3. 遍历所有单词状态，计算测验相关统计
  /// 4. 计算答题准确率
  /// 5. 更新响应式变量触发UI更新
  ///
  /// 统计算法（改进版）：
  /// - 总单词数：从状态服务的 wordIds 长度获取
  /// - 总测验数：累加所有单词的 (completedQuizzes + errorCount)
  /// - 正确答案数：累加所有单词的 completedQuizzes
  /// - 准确率：correctAnswers / totalQuizzes
  ///
  /// 算法改进说明：
  /// - completedQuizzes：表示该单词正确答题的次数
  /// - errorCount：表示该单词错误答题的次数
  /// - 总尝试次数 = 正确次数 + 错误次数（更准确的统计方式）
  /// - 不再依赖固定的题目数量假设，基于实际答题数据计算
  ///
  /// 异常处理：
  /// - 验证会话数据有效性
  /// - 捕获计算过程中的异常
  /// - 提供用户友好的错误提示
  ///
  /// 返回值：无返回值（void）
  void _calculateStatistics() {
    try {
      if (_session == null) {
        _log.w('学习会话为空，无法计算统计数据');
        Get.snackbar('提示', '学习数据不完整，无法生成统计结果');
        return;
      }

      // 获取学习单词的数量
      totalWords.value = _stateService.wordIds.length;

      // 计算测验相关统计
      int totalAttemptedQuizzes = 0; // 总尝试次数（包括正确和错误）
      int totalCorrectAnswers = 0; // 总正确答案数

      // 遍历所有单词状态，累计统计数据
      final wordStates = _stateService.getAllWordStates();
      for (final entry in wordStates.entries) {
        final wordId = entry.key;
        final state = entry.value;

        // 🔑 关键改进：使用更准确的统计算法
        // completedQuizzes 表示正确答题数量
        // errorCount 表示错误答题数量
        // 总尝试次数 = 正确次数 + 错误次数
        final correctCount = state.completedQuizzes;
        final errorCount = state.errorCount;
        final attemptedCount = correctCount + errorCount;

        totalCorrectAnswers += correctCount;
        totalAttemptedQuizzes += attemptedCount;

        _log.d(
          '单词 $wordId 统计: 正确=$correctCount, 错误=$errorCount, 总尝试=$attemptedCount',
        );
      }

      // 更新响应式统计数据，触发UI更新
      totalQuizzes.value = totalAttemptedQuizzes;
      correctAnswers.value = totalCorrectAnswers;

      // 计算答题准确率
      accuracy.value =
          totalQuizzes.value > 0
              ? correctAnswers.value / totalQuizzes.value
              : 0.0;

      _log.i(
        '学习结果统计计算完成: 单词数量=${totalWords.value}, 测验题数量=${totalQuizzes.value}, 正确答案数量=${correctAnswers.value}, 准确率=${(accuracy.value * 100).toStringAsFixed(1)}%',
      );
    } catch (e) {
      _log.e('计算学习统计数据失败', error: e);
      Get.snackbar('错误', '计算学习结果失败，请重试');

      // 设置默认值，确保UI不会显示异常数据
      totalWords.value = 0;
      totalQuizzes.value = 0;
      correctAnswers.value = 0;
      accuracy.value = 0.0;
    }
  }

  // ==================== 用户交互方法 ====================
  /// 返回首页
  ///
  /// 用户点击返回首页按钮时调用
  ///
  /// 调用位置：
  /// - result_page.dart:174 - "返回首页"按钮的点击事件
  ///
  /// 业务逻辑：
  /// - 通过导航服务导航到首页
  /// - 结束当前学习流程
  ///
  /// 🔧 修复说明：
  /// - 改为异步方法，因为导航服务的 navigateToHome() 现在是异步的
  /// - 使用 await 等待导航完成
  ///
  /// 返回值：无返回值（void）
  Future<void> moveToHome() async {
    try {
      _log.i('用户选择返回首页');
      await _navigationService.navigateToHome();
    } catch (e) {
      _log.e('导航到首页失败', error: e);
      Get.snackbar('错误', '返回首页失败，请重试');
    }
  }

  /// 开始新的学习会话
  ///
  /// 用户点击开始新学习按钮时调用
  ///
  /// 调用位置：
  /// - result_page.dart:159 - "开始新学习"按钮的点击事件
  ///
  /// 业务逻辑：
  /// - 返回首页，让用户重新选择学习参数
  /// - 为用户提供开始新学习会话的入口
  ///
  /// 设计说明：
  /// - 与 moveToHome() 功能相同，但语义不同
  /// - 表示用户主动开始新的学习会话
  ///
  /// 🔧 修复说明：
  /// - 改为异步方法，因为导航服务的 navigateToHome() 现在是异步的
  /// - 使用 await 等待导航完成
  ///
  /// 返回值：无返回值（void）
  Future<void> startNewSession() async {
    try {
      _log.i('用户选择开始新的学习会话，返回首页');
      await _navigationService.navigateToHome();
    } catch (e) {
      _log.e('开始新学习会话失败', error: e);
      Get.snackbar('错误', '开始新学习失败，请重试');
    }
  }

  // ==================== 计算属性 ====================
  /// 获取表现评级
  ///
  /// 根据答题准确率计算用户的学习表现评级
  ///
  /// UI 使用位置：
  /// - result_page.dart:134 - 通过 Obx 监听，显示表现评级
  ///
  /// 评级标准：
  /// - 准确率 >= 80%：优秀
  /// - 准确率 >= 60%：良好
  /// - 准确率 < 60%：需要努力
  ///
  /// 依赖变量：
  /// - accuracy.value：答题准确率
  ///
  /// 返回值：String - 表现评级文本
  String get performanceRating {
    final double rate = accuracy.value;
    if (rate >= 0.8) {
      return '优秀';
    } else if (rate >= 0.6) {
      return '良好';
    } else {
      return '需要努力';
    }
  }

  /// 获取评级描述
  ///
  /// 根据答题准确率生成鼓励性的评级描述文本
  ///
  /// UI 使用位置：
  /// - result_page.dart:143 - 通过 Obx 监听，显示评级描述
  ///
  /// 描述内容：
  /// - 准确率 >= 80%：太棒了！你已经掌握了大部分单词。
  /// - 准确率 >= 60%：不错的表现！再多练习一下，你会做得更好。
  /// - 准确率 < 60%：继续努力！多加练习，你会进步的。
  ///
  /// 依赖变量：
  /// - accuracy.value：答题准确率
  ///
  /// 设计说明：
  /// - 提供正面鼓励，激励用户继续学习
  /// - 根据不同表现给出针对性建议
  ///
  /// 返回值：String - 评级描述文本
  String get ratingDescription {
    final double rate = accuracy.value;
    if (rate >= 0.8) {
      return '太棒了！你已经掌握了大部分单词。';
    } else if (rate >= 0.6) {
      return '不错的表现！再多练习一下，你会做得更好。';
    } else {
      return '继续努力！多加练习，你会进步的。';
    }
  }
}
