import 'package:flutter_study_flow_by_myself/data/models/word_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/user_word_book_model.dart';
import 'package:isar/isar.dart';

part 'review_item_model.g.dart';

/// 复习项模型
///
/// 存储某个用户的某个单词本里的某个单词的学习状态
/// 包含下次复习时间、复习次数、难度系数等具体学习细节
/// 表示"用户-单词本-单词"三维关系中的学习状态
@Collection()
class ReviewItemModel {
  /// Isar数据库主键，自动递增
  Id id = Isar.autoIncrement;

  /// 关联的单词ID
  late int wordId;

  /// 关联的单词
  ///
  /// 使用IsarLink建立与WordModel的一对一关联，
  /// 通过这个关联可以获取单词的详细信息，如拼写、释义等
  final word = IsarLink<WordModel>();

  /// 关联的用户单词本ID
  late int userWordBookId;

  /// 关联的用户单词本
  ///
  /// 使用IsarLink建立与UserWordBookModel的一对一关联，
  /// 通过这个关联可以获取用户单词本的信息，如学习计划、状态等
  ///
  /// 注意：这个关联与UserWordBookModel中的reviewItems形成双向关联，
  /// 确保数据的完整性和一致性
  final userWordBook = IsarLink<UserWordBookModel>();

  // SM2算法核心参数
  /// 成功复习次数，用于SM2算法计算
  int repetitionCount = 0;

  /// 难易度因子(初始值2.5)，用于SM2算法计算
  double easeFactor = 2.5;

  /// 下次复习时间，添加索引以便高效查询到期复习项
  @Index()
  late DateTime nextReviewDate;

  /// 上次间隔天数(天)，用于SM2算法计算
  int previousInterval = 0;

  /// 是否为新词（未学过）
  ///
  /// true表示这个单词是新词，用户尚未学习过
  /// false表示用户已经学习过这个单词，处于复习阶段
  bool isNewWord = true;

  // 学习时间记录字段
  /// 上次复习时间（用户关心的重要信息）
  ///
  /// 记录用户最后一次复习这个单词的时间
  /// null表示从未复习过
  DateTime? lastReviewDate;

  /// 总学习次数（包括失败的复习）
  ///
  /// 记录用户对这个单词的总学习次数，包括答对和答错的情况
  /// 用于统计学习频率和难度分析
  int totalReviewCount = 0;

  /// 首次学习时间（而不是创建时间）
  ///
  /// 记录用户第一次学习这个单词的时间
  /// 与创建时间不同，创建时间是下载单词本时，首次学习时间是用户真正开始学习时
  /// null表示尚未开始学习
  DateTime? firstStudyDate;

  /// 标准构造函数
  ReviewItemModel({
    this.repetitionCount = 0,
    this.easeFactor = 2.5,
    required this.wordId,
    required this.userWordBookId,
    required this.nextReviewDate,
    this.previousInterval = 0,
    this.isNewWord = true,
    this.lastReviewDate,
    this.totalReviewCount = 0,
    this.firstStudyDate,
  });

  /// 便捷工厂方法，创建新的复习项
  ///
  /// @param wordId 单词ID
  /// @param userWordBookId 用户单词本ID
  /// @param repetitionCount 初始复习次数，默认为0
  /// @param easeFactor 初始难易度因子，默认为2.5
  /// @param previousInterval 初始间隔天数，默认为0
  /// @param nextReviewDate 下次复习时间，默认为当前时间
  /// @param isNewWord 是否为新词，默认为true
  /// @param lastReviewDate 上次复习时间，默认为null
  /// @param totalReviewCount 总学习次数，默认为0
  /// @param firstStudyDate 首次学习时间，默认为null
  /// @return 创建的复习项实例
  factory ReviewItemModel.create({
    required int wordId,
    required int userWordBookId,
    int repetitionCount = 0,
    double easeFactor = 2.5,
    int previousInterval = 0,
    DateTime? nextReviewDate,
    bool isNewWord = true,
    DateTime? lastReviewDate,
    int totalReviewCount = 0,
    DateTime? firstStudyDate,
  }) {
    return ReviewItemModel(
      wordId: wordId,
      userWordBookId: userWordBookId,
      repetitionCount: repetitionCount,
      easeFactor: easeFactor,
      previousInterval: previousInterval,
      nextReviewDate: nextReviewDate ?? DateTime.now(),
      isNewWord: isNewWord,
      lastReviewDate: lastReviewDate,
      totalReviewCount: totalReviewCount,
      firstStudyDate: firstStudyDate,
    );
  }
}
