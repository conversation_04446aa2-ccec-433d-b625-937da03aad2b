# app 目录说明文档

## 目录职责

`app` 目录存放整个应用的共享资源和全局配置，与特定业务模块解耦。这里包含了通用的UI组件、全局服务、主题配置和路由管理，为整个应用提供基础设施支持。

## 目录结构

```
app/
├── routes/                       # 路由管理
│   ├── app_pages.dart            # GetX 全局路由配置（所有 GetPage 列表）
│   └── app_routes.dart           # 全局路由路径常量定义
├── global/                       # 全局共享的通用资源
│   ├── widgets/                  # 通用 UI 组件
│   ├── themes/                   # 主题设置
│   ├── utils/                    # 工具类、扩展方法
│   └── constants/                # 常量配置
└── services/                     # 全局服务类
    ├── auth_service.dart         # 鉴权服务
    ├── storage_service.dart      # 本地存储服务
    ├── log_service.dart          # 日志记录服务
    └── theme_service.dart        # 主题切换服务
```

## 设计理念

`app` 目录的设计基于以下原则：

- **关注点分离**：将应用的全局基础设施与具体业务逻辑分离
- **可重用性**：提供可在多个业务模块间共享的组件和服务
- **全局状态管理**：管理应用级别的状态和配置
- **依赖注入支持**：为GetX的依赖注入机制提供全局服务

## 主要文件/文件夹说明

### routes/

路由管理目录，负责管理应用的导航系统。

- **app_pages.dart**：汇集所有模块的 `GetPage` 列表，形成一个完整的应用路由表
- **app_routes.dart**：集中定义所有模块的路由路径字符串常量

### global/

全局共享的通用资源，不包含业务逻辑。

- **widgets/**：可复用的UI组件，如自定义按钮、加载指示器等
- **themes/**：应用主题配置，包括颜色、字体、暗黑模式等
- **utils/**：通用工具类和扩展方法，如日期格式化、字符串处理等
- **constants/**：应用中使用的常量，如API键、默认配置等

### services/

全局服务类，通常继承自 `GetxService` 并通过依赖注入在应用中使用。

- **auth_service.dart**：管理用户认证状态、登录流程和Token处理
- **storage_service.dart**：封装本地存储操作，提供业务层存取接口
- **log_service.dart**：统一的日志记录服务
- **theme_service.dart**：管理应用主题切换和持久化

## 依赖关系说明

- **routes/** 依赖于各模块内的路由定义
- **services/** 通常依赖于 `data/repositories` 和 `data/providers` 实现数据访问
- **global/widgets/** 可能依赖于 `global/themes` 进行样式设置

## 易混淆点

1. **`app/services` 与 `data/providers` 的区别**：
   - `app/services` 提供**业务级服务**，通常是对某个功能域的抽象，如认证、存储管理
   - `data/providers` 提供**数据源实现**，直接与API或本地存储交互
   - 例如，`StorageService` 提供业务友好的存储接口，而 `LocalStorageProvider` 负责底层的读写操作

2. **路由文件的职责区分**：
   - `app_routes.dart` 仅定义**路径常量**
   - `app_pages.dart` 将路径与具体的**页面和绑定**关联，构成 `GetPage` 对象列表

## 最佳实践

- 全局服务应通过 `Get.put(..., permanent: true)` 在应用启动时注册
- 避免在全局服务中包含UI相关逻辑
- 常量应分类存放在 `constants` 目录下的不同文件中
- 通用组件应设计为高度可配置的，以支持不同场景的使用

## 代码示例

### 注册全局服务
```dart
// 在 main.dart 或 initial_binding.dart 中
void initServices() {
  Get.put(LogService(), permanent: true);
  Get.put(AuthService(), permanent: true);
  Get.put(StorageService(), permanent: true);
  Get.put(ThemeService(), permanent: true);
}
```

### 使用全局服务
```dart
// 在任何需要的地方
final authService = Get.find<AuthService>();
if (authService.isLoggedIn) {
  // 执行需要登录的操作
}
```

### 添加新路由
```dart
// 在对应模块的 routes 文件中定义路径和页面
// 然后在 app_pages.dart 中添加
static final routes = [
  // 现有路由...
  ...AuthRoutes.routes,
  ...HomeRoutes.routes,
  ...YourNewModuleRoutes.routes, // 添加新模块的路由
];
```

## 相关联文件

- `lib/main.dart`：初始化全局服务和配置
- `lib/data/providers/`：为全局服务提供数据源
- `lib/modules/*/routes/`：各模块的路由定义，被 `app_routes.dart` 和 `app_pages.dart` 引用

## 常见问题与解决方案

- **全局服务无法访问**：确保服务在应用启动时通过 `Get.put()` 正确注册
- **路由冲突**：确保各模块的路由路径不重复，推荐使用模块名作为路径前缀
- **主题切换不生效**：检查 `ThemeService` 是否正确更新了 `Get.changeThemeMode()` 

// ---

// ## 1. **IsarService 和 StorageService 的定位**

// - **IsarService**：专门负责结构化数据的本地数据库存储（如表、对象、关系等），适合复杂数据、批量操作、查询等。
// - **StorageService**（基于 SharedPreferences）：适合存储**简单的键值对**，如用户设置、token、偏好、布尔开关等。

// 它们虽然都属于“本地存储”，但**用途和适用场景不同**。

// ---

// ## 2. **命名是否冲突？**

// - 在实际项目中，`StorageService` 通常指“轻量级的键值对存储”，而像 Isar、Hive 这类数据库服务会单独命名为 `IsarService`、`HiveService` 等。
// - 只要你在项目中**明确区分用途**，命名不会造成实际冲突。

// ### 业界常见做法
// - `StorageService`/`LocalStorageService`/`PreferencesService`：用于封装 SharedPreferences、NSUserDefaults、MMKV 等轻量存储。
// - `IsarService`/`DatabaseService`/`SqliteService`：用于封装结构化数据库。

// ---

// ## 3. **如何避免歧义？**

// - 如果你担心歧义，可以用更具体的名字，比如：
//   - `PreferencesService` 或 `PrefsService`（专指用户偏好/设置）
//   - `KeyValueStorageService`（强调键值对存储）
// - 但如果你的 `StorageService` 只负责 SharedPreferences 相关内容，且文档/注释清楚，直接用 `StorageService` 也没问题。

// ---

// ## 4. **推荐方案**

// - **IsarService**：负责结构化数据（如单词、题库等）。
// - **StorageService**（或 `PreferencesService`）：负责简单键值对（如主题、token、登录状态等）。

// 这样分工明确，命名不会冲突，且便于后期维护和扩展。

// ---

// ## 5. **总结**

// - 命名不会冲突，只要职责清晰。
// - 推荐继续用 `IsarService` 和 `StorageService`，如需更细致可用 `PreferencesService`。
// - 路径都放在 `lib/app/services/` 下即可。

// ---