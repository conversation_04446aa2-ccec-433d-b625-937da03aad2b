import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/app/services/user_word_book_service.dart';
import 'package:flutter_study_flow_by_myself/app/services/review_plan_service.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/learning_session_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/word_repository.dart';
import 'package:flutter_study_flow_by_myself/modules/home/<USER>/home_controller.dart';
import 'package:get/get.dart';

class HomeBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<HomeController>(
      () => HomeController(
        userWordBookService: Get.find<UserWordBookService>(),
        reviewPlanService: Get.find<ReviewPlanService>(),
        learningSessionRepository: Get.find<LearningSessionRepository>(),
        wordRepository: Get.find<WordRepository>(),
        log: Get.find<LogService>(),
      ),
    );
    // 注意：LearningSessionRepository等共享服务已在全局注册，这里不需要重复注册
  }
}
