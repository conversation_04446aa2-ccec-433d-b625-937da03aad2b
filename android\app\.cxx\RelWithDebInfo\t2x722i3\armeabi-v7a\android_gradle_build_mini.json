{"buildFiles": ["D:\\flutter_sdk\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\coding\\flutter_demo\\flutter_study_flow_by_myself\\android\\app\\.cxx\\RelWithDebInfo\\t2x722i3\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["D:\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\coding\\flutter_demo\\flutter_study_flow_by_myself\\android\\app\\.cxx\\RelWithDebInfo\\t2x722i3\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}