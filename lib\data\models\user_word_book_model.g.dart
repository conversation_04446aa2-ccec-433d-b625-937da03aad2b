// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_word_book_model.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetUserWordBookModelCollection on Isar {
  IsarCollection<UserWordBookModel> get userWordBookModels => this.collection();
}

const UserWordBookModelSchema = CollectionSchema(
  name: r'UserWordBookModel',
  id: -7029818910569239463,
  properties: {
    r'addedDate': PropertySchema(
      id: 0,
      name: r'addedDate',
      type: IsarType.dateTime,
    ),
    r'completedAt': PropertySchema(
      id: 1,
      name: r'completedAt',
      type: IsarType.dateTime,
    ),
    r'completionRate': PropertySchema(
      id: 2,
      name: r'completionRate',
      type: IsarType.double,
    ),
    r'dailyNewWordsCount': PropertySchema(
      id: 3,
      name: r'dailyNewWordsCount',
      type: IsarType.long,
    ),
    r'hashCode': PropertySchema(
      id: 4,
      name: r'hashCode',
      type: IsarType.long,
    ),
    r'isActive': PropertySchema(
      id: 5,
      name: r'isActive',
      type: IsarType.bool,
    ),
    r'isCompleted': PropertySchema(
      id: 6,
      name: r'isCompleted',
      type: IsarType.bool,
    ),
    r'lastLearningDate': PropertySchema(
      id: 7,
      name: r'lastLearningDate',
      type: IsarType.dateTime,
    ),
    r'remoteWordBookId': PropertySchema(
      id: 8,
      name: r'remoteWordBookId',
      type: IsarType.long,
    ),
    r'totalLearnedWords': PropertySchema(
      id: 9,
      name: r'totalLearnedWords',
      type: IsarType.long,
    ),
    r'totalWords': PropertySchema(
      id: 10,
      name: r'totalWords',
      type: IsarType.long,
    ),
    r'wordBookId': PropertySchema(
      id: 11,
      name: r'wordBookId',
      type: IsarType.long,
    )
  },
  estimateSize: _userWordBookModelEstimateSize,
  serialize: _userWordBookModelSerialize,
  deserialize: _userWordBookModelDeserialize,
  deserializeProp: _userWordBookModelDeserializeProp,
  idName: r'id',
  indexes: {},
  links: {
    r'wordBook': LinkSchema(
      id: 1231187019073547395,
      name: r'wordBook',
      target: r'WordBookModel',
      single: true,
    ),
    r'reviewItems': LinkSchema(
      id: -9022992664410552645,
      name: r'reviewItems',
      target: r'ReviewItemModel',
      single: false,
    ),
    r'learningSessions': LinkSchema(
      id: 747146639530807749,
      name: r'learningSessions',
      target: r'LearningSession',
      single: false,
    )
  },
  embeddedSchemas: {},
  getId: _userWordBookModelGetId,
  getLinks: _userWordBookModelGetLinks,
  attach: _userWordBookModelAttach,
  version: '3.1.0+1',
);

int _userWordBookModelEstimateSize(
  UserWordBookModel object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  return bytesCount;
}

void _userWordBookModelSerialize(
  UserWordBookModel object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeDateTime(offsets[0], object.addedDate);
  writer.writeDateTime(offsets[1], object.completedAt);
  writer.writeDouble(offsets[2], object.completionRate);
  writer.writeLong(offsets[3], object.dailyNewWordsCount);
  writer.writeLong(offsets[4], object.hashCode);
  writer.writeBool(offsets[5], object.isActive);
  writer.writeBool(offsets[6], object.isCompleted);
  writer.writeDateTime(offsets[7], object.lastLearningDate);
  writer.writeLong(offsets[8], object.remoteWordBookId);
  writer.writeLong(offsets[9], object.totalLearnedWords);
  writer.writeLong(offsets[10], object.totalWords);
  writer.writeLong(offsets[11], object.wordBookId);
}

UserWordBookModel _userWordBookModelDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = UserWordBookModel(
    completedAt: reader.readDateTimeOrNull(offsets[1]),
    dailyNewWordsCount: reader.readLongOrNull(offsets[3]) ?? 10,
    isActive: reader.readBoolOrNull(offsets[5]) ?? false,
    isCompleted: reader.readBoolOrNull(offsets[6]) ?? false,
    lastLearningDate: reader.readDateTimeOrNull(offsets[7]),
    remoteWordBookId: reader.readLong(offsets[8]),
    totalLearnedWords: reader.readLongOrNull(offsets[9]) ?? 0,
    totalWords: reader.readLong(offsets[10]),
    wordBookId: reader.readLong(offsets[11]),
  );
  object.addedDate = reader.readDateTime(offsets[0]);
  object.id = id;
  return object;
}

P _userWordBookModelDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readDateTime(offset)) as P;
    case 1:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 2:
      return (reader.readDouble(offset)) as P;
    case 3:
      return (reader.readLongOrNull(offset) ?? 10) as P;
    case 4:
      return (reader.readLong(offset)) as P;
    case 5:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    case 6:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    case 7:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 8:
      return (reader.readLong(offset)) as P;
    case 9:
      return (reader.readLongOrNull(offset) ?? 0) as P;
    case 10:
      return (reader.readLong(offset)) as P;
    case 11:
      return (reader.readLong(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _userWordBookModelGetId(UserWordBookModel object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _userWordBookModelGetLinks(
    UserWordBookModel object) {
  return [object.wordBook, object.reviewItems, object.learningSessions];
}

void _userWordBookModelAttach(
    IsarCollection<dynamic> col, Id id, UserWordBookModel object) {
  object.id = id;
  object.wordBook
      .attach(col, col.isar.collection<WordBookModel>(), r'wordBook', id);
  object.reviewItems
      .attach(col, col.isar.collection<ReviewItemModel>(), r'reviewItems', id);
  object.learningSessions.attach(
      col, col.isar.collection<LearningSession>(), r'learningSessions', id);
}

extension UserWordBookModelQueryWhereSort
    on QueryBuilder<UserWordBookModel, UserWordBookModel, QWhere> {
  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension UserWordBookModelQueryWhere
    on QueryBuilder<UserWordBookModel, UserWordBookModel, QWhereClause> {
  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterWhereClause>
      idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterWhereClause>
      idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterWhereClause>
      idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension UserWordBookModelQueryFilter
    on QueryBuilder<UserWordBookModel, UserWordBookModel, QFilterCondition> {
  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      addedDateEqualTo(DateTime value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'addedDate',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      addedDateGreaterThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'addedDate',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      addedDateLessThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'addedDate',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      addedDateBetween(
    DateTime lower,
    DateTime upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'addedDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      completedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'completedAt',
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      completedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'completedAt',
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      completedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'completedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      completedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'completedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      completedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'completedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      completedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'completedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      completionRateEqualTo(
    double value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'completionRate',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      completionRateGreaterThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'completionRate',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      completionRateLessThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'completionRate',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      completionRateBetween(
    double lower,
    double upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'completionRate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      dailyNewWordsCountEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dailyNewWordsCount',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      dailyNewWordsCountGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'dailyNewWordsCount',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      dailyNewWordsCountLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'dailyNewWordsCount',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      dailyNewWordsCountBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'dailyNewWordsCount',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      hashCodeEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'hashCode',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      hashCodeGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'hashCode',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      hashCodeLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'hashCode',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      hashCodeBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'hashCode',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      isActiveEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isActive',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      isCompletedEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isCompleted',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      lastLearningDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'lastLearningDate',
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      lastLearningDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'lastLearningDate',
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      lastLearningDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'lastLearningDate',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      lastLearningDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'lastLearningDate',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      lastLearningDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'lastLearningDate',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      lastLearningDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'lastLearningDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      remoteWordBookIdEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'remoteWordBookId',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      remoteWordBookIdGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'remoteWordBookId',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      remoteWordBookIdLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'remoteWordBookId',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      remoteWordBookIdBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'remoteWordBookId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      totalLearnedWordsEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'totalLearnedWords',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      totalLearnedWordsGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'totalLearnedWords',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      totalLearnedWordsLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'totalLearnedWords',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      totalLearnedWordsBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'totalLearnedWords',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      totalWordsEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'totalWords',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      totalWordsGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'totalWords',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      totalWordsLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'totalWords',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      totalWordsBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'totalWords',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      wordBookIdEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'wordBookId',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      wordBookIdGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'wordBookId',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      wordBookIdLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'wordBookId',
        value: value,
      ));
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      wordBookIdBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'wordBookId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension UserWordBookModelQueryObject
    on QueryBuilder<UserWordBookModel, UserWordBookModel, QFilterCondition> {}

extension UserWordBookModelQueryLinks
    on QueryBuilder<UserWordBookModel, UserWordBookModel, QFilterCondition> {
  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      wordBook(FilterQuery<WordBookModel> q) {
    return QueryBuilder.apply(this, (query) {
      return query.link(q, r'wordBook');
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      wordBookIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'wordBook', 0, true, 0, true);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      reviewItems(FilterQuery<ReviewItemModel> q) {
    return QueryBuilder.apply(this, (query) {
      return query.link(q, r'reviewItems');
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      reviewItemsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'reviewItems', length, true, length, true);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      reviewItemsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'reviewItems', 0, true, 0, true);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      reviewItemsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'reviewItems', 0, false, 999999, true);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      reviewItemsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'reviewItems', 0, true, length, include);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      reviewItemsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'reviewItems', length, include, 999999, true);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      reviewItemsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(
          r'reviewItems', lower, includeLower, upper, includeUpper);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      learningSessions(FilterQuery<LearningSession> q) {
    return QueryBuilder.apply(this, (query) {
      return query.link(q, r'learningSessions');
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      learningSessionsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'learningSessions', length, true, length, true);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      learningSessionsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'learningSessions', 0, true, 0, true);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      learningSessionsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'learningSessions', 0, false, 999999, true);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      learningSessionsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'learningSessions', 0, true, length, include);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      learningSessionsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(
          r'learningSessions', length, include, 999999, true);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterFilterCondition>
      learningSessionsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(
          r'learningSessions', lower, includeLower, upper, includeUpper);
    });
  }
}

extension UserWordBookModelQuerySortBy
    on QueryBuilder<UserWordBookModel, UserWordBookModel, QSortBy> {
  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      sortByAddedDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'addedDate', Sort.asc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      sortByAddedDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'addedDate', Sort.desc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      sortByCompletedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'completedAt', Sort.asc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      sortByCompletedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'completedAt', Sort.desc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      sortByCompletionRate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'completionRate', Sort.asc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      sortByCompletionRateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'completionRate', Sort.desc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      sortByDailyNewWordsCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dailyNewWordsCount', Sort.asc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      sortByDailyNewWordsCountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dailyNewWordsCount', Sort.desc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      sortByHashCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hashCode', Sort.asc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      sortByHashCodeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hashCode', Sort.desc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      sortByIsActive() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isActive', Sort.asc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      sortByIsActiveDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isActive', Sort.desc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      sortByIsCompleted() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isCompleted', Sort.asc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      sortByIsCompletedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isCompleted', Sort.desc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      sortByLastLearningDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastLearningDate', Sort.asc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      sortByLastLearningDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastLearningDate', Sort.desc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      sortByRemoteWordBookId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'remoteWordBookId', Sort.asc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      sortByRemoteWordBookIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'remoteWordBookId', Sort.desc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      sortByTotalLearnedWords() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalLearnedWords', Sort.asc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      sortByTotalLearnedWordsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalLearnedWords', Sort.desc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      sortByTotalWords() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalWords', Sort.asc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      sortByTotalWordsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalWords', Sort.desc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      sortByWordBookId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'wordBookId', Sort.asc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      sortByWordBookIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'wordBookId', Sort.desc);
    });
  }
}

extension UserWordBookModelQuerySortThenBy
    on QueryBuilder<UserWordBookModel, UserWordBookModel, QSortThenBy> {
  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      thenByAddedDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'addedDate', Sort.asc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      thenByAddedDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'addedDate', Sort.desc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      thenByCompletedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'completedAt', Sort.asc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      thenByCompletedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'completedAt', Sort.desc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      thenByCompletionRate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'completionRate', Sort.asc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      thenByCompletionRateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'completionRate', Sort.desc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      thenByDailyNewWordsCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dailyNewWordsCount', Sort.asc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      thenByDailyNewWordsCountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dailyNewWordsCount', Sort.desc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      thenByHashCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hashCode', Sort.asc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      thenByHashCodeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hashCode', Sort.desc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      thenByIsActive() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isActive', Sort.asc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      thenByIsActiveDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isActive', Sort.desc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      thenByIsCompleted() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isCompleted', Sort.asc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      thenByIsCompletedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isCompleted', Sort.desc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      thenByLastLearningDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastLearningDate', Sort.asc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      thenByLastLearningDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastLearningDate', Sort.desc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      thenByRemoteWordBookId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'remoteWordBookId', Sort.asc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      thenByRemoteWordBookIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'remoteWordBookId', Sort.desc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      thenByTotalLearnedWords() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalLearnedWords', Sort.asc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      thenByTotalLearnedWordsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalLearnedWords', Sort.desc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      thenByTotalWords() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalWords', Sort.asc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      thenByTotalWordsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalWords', Sort.desc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      thenByWordBookId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'wordBookId', Sort.asc);
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QAfterSortBy>
      thenByWordBookIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'wordBookId', Sort.desc);
    });
  }
}

extension UserWordBookModelQueryWhereDistinct
    on QueryBuilder<UserWordBookModel, UserWordBookModel, QDistinct> {
  QueryBuilder<UserWordBookModel, UserWordBookModel, QDistinct>
      distinctByAddedDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'addedDate');
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QDistinct>
      distinctByCompletedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'completedAt');
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QDistinct>
      distinctByCompletionRate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'completionRate');
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QDistinct>
      distinctByDailyNewWordsCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'dailyNewWordsCount');
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QDistinct>
      distinctByHashCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'hashCode');
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QDistinct>
      distinctByIsActive() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isActive');
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QDistinct>
      distinctByIsCompleted() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isCompleted');
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QDistinct>
      distinctByLastLearningDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'lastLearningDate');
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QDistinct>
      distinctByRemoteWordBookId() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'remoteWordBookId');
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QDistinct>
      distinctByTotalLearnedWords() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'totalLearnedWords');
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QDistinct>
      distinctByTotalWords() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'totalWords');
    });
  }

  QueryBuilder<UserWordBookModel, UserWordBookModel, QDistinct>
      distinctByWordBookId() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'wordBookId');
    });
  }
}

extension UserWordBookModelQueryProperty
    on QueryBuilder<UserWordBookModel, UserWordBookModel, QQueryProperty> {
  QueryBuilder<UserWordBookModel, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<UserWordBookModel, DateTime, QQueryOperations>
      addedDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'addedDate');
    });
  }

  QueryBuilder<UserWordBookModel, DateTime?, QQueryOperations>
      completedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'completedAt');
    });
  }

  QueryBuilder<UserWordBookModel, double, QQueryOperations>
      completionRateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'completionRate');
    });
  }

  QueryBuilder<UserWordBookModel, int, QQueryOperations>
      dailyNewWordsCountProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'dailyNewWordsCount');
    });
  }

  QueryBuilder<UserWordBookModel, int, QQueryOperations> hashCodeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'hashCode');
    });
  }

  QueryBuilder<UserWordBookModel, bool, QQueryOperations> isActiveProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isActive');
    });
  }

  QueryBuilder<UserWordBookModel, bool, QQueryOperations>
      isCompletedProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isCompleted');
    });
  }

  QueryBuilder<UserWordBookModel, DateTime?, QQueryOperations>
      lastLearningDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'lastLearningDate');
    });
  }

  QueryBuilder<UserWordBookModel, int, QQueryOperations>
      remoteWordBookIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'remoteWordBookId');
    });
  }

  QueryBuilder<UserWordBookModel, int, QQueryOperations>
      totalLearnedWordsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'totalLearnedWords');
    });
  }

  QueryBuilder<UserWordBookModel, int, QQueryOperations> totalWordsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'totalWords');
    });
  }

  QueryBuilder<UserWordBookModel, int, QQueryOperations> wordBookIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'wordBookId');
    });
  }
}
