import 'package:flutter/material.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/result/controllers/review_result_controller.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/services/review_state_service.dart';
import 'package:get/get.dart';

/// 复习结果页面
///
/// 显示复习完成后的统计信息，参考ResultPage的设计
/// 包含复习统计、操作按钮等功能
class ReviewResultPage extends GetView<ReviewResultController> {
  const ReviewResultPage({super.key});

  @override
  Widget build(BuildContext context) {
    final stateService = Get.find<ReviewStateService>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('复习完成'),
        elevation: 0,
        automaticallyImplyLeading: false, // 禁用默认返回按钮
      ),
      body: Obx(() {
        // 加载状态
        if (controller.isLoading.value) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('正在统计复习结果...'),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // 完成图标和标题
              const Icon(Icons.check_circle, size: 80, color: Colors.green),
              const SizedBox(height: 16),
              const Text(
                '复习完成！',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '恭喜您完成了本次复习',
                style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              ),
              const SizedBox(height: 32),

              // 统计信息卡片
              _buildStatsCard(stateService),
              const SizedBox(height: 32),

              // 操作按钮
              _buildActionButtons(),
            ],
          ),
        );
      }),
    );
  }

  /// 构建统计信息卡片
  Widget _buildStatsCard(ReviewStateService stateService) {
    final completedWords = stateService.completedWords.length;
    final totalGroups = stateService.wordGroups.length;

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Text(
              '复习统计',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 20),

            // 统计项目
            _buildStatItem(
              icon: Icons.book,
              label: '复习单词',
              value: '$completedWords 个',
              color: Colors.blue,
            ),
            const SizedBox(height: 16),
            _buildStatItem(
              icon: Icons.group_work,
              label: '完成组数',
              value: '$totalGroups 组',
              color: Colors.orange,
            ),
            const SizedBox(height: 16),
            _buildStatItem(
              icon: Icons.trending_up,
              label: '正确率',
              value: controller.accuracyText,
              color: Colors.green,
            ),
            const SizedBox(height: 16),
            _buildStatItem(
              icon: Icons.schedule,
              label: '复习时间',
              value: _formatDuration(controller.reviewDuration),
              color: Colors.purple,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建统计项目
  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 24),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Text(
            label,
            style: TextStyle(fontSize: 16, color: Colors.grey[700]),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Column(
      children: [
        // 返回首页按钮（主要操作）
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: controller.navigateToHome,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 2,
            ),
            child: const Text(
              '返回首页',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
          ),
        ),
      ],
    );
  }

  /// 格式化时长
  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;

    if (minutes > 0) {
      return '$minutes分$seconds秒';
    } else {
      return '$seconds秒';
    }
  }
}
