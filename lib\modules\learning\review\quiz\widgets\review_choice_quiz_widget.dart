import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/data/models/quiz/choice_quiz_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/quiz/quiz_type.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/quiz/controllers/review_quiz_controller.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/quiz/widgets/sentence_widget.dart';

/// 复习专用选择题组件
///
/// 基于ChoiceQuizWidgetStateless，适配ReviewQuizController
class ReviewChoiceQuizWidget extends StatelessWidget {
  /// 选择题模型
  final ChoiceQuizModel quiz;

  /// 复习Quiz控制器
  final ReviewQuizController controller;

  const ReviewChoiceQuizWidget({
    super.key,
    required this.quiz,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 题目区域
          Expanded(flex: 2, child: _buildQuestionArea()),
          const SizedBox(height: 24),

          // 选项区域
          Expanded(flex: 3, child: _buildOptionsArea()),
        ],
      ),
    );
  }

  /// 构建题目区域
  Widget _buildQuestionArea() {
    switch (quiz.type) {
      case QuizType.chineseToEnglish:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '请选择与下列中文含义对应的英文单词：',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
              ),
              child: Text(
                quiz.question,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        );

      case QuizType.englishToChinese:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '请选择下列英文单词的中文含义：',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
              ),
              child: Text(
                quiz.question,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        );

      case QuizType.audioToMeaning:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '请听音频并选择正确的中文含义：',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.volume_up, color: Colors.blue[700]),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '点击播放: ${quiz.question}',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.blue[700],
                        fontWeight: FontWeight.w500,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ],
        );

      case QuizType.sentenceCompletion:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '请选择适合填入句子空白处的单词：',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            SentenceWidget(sentence: quiz.question),
          ],
        );

      default:
        return Container(
          padding: const EdgeInsets.all(16),
          child: Text(quiz.question, style: const TextStyle(fontSize: 18)),
        );
    }
  }

  /// 构建选项区域
  Widget _buildOptionsArea() {
    return ListView.builder(
      itemCount: quiz.options.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: _buildOptionButton(index),
        );
      },
    );
  }

  /// 构建选项按钮
  Widget _buildOptionButton(int index) {
    return Obx(() {
      final isSelected = controller.selectedOptionIndex == index;
      final hasAnswered = controller.hasAnswered;
      final isCorrect = index == quiz.correctOptionIndex;

      Color backgroundColor;
      Color borderColor;
      Color textColor;

      if (!hasAnswered) {
        // 未答题状态
        backgroundColor =
            isSelected ? Colors.blue.withValues(alpha: 0.1) : Colors.white;
        borderColor =
            isSelected ? Colors.blue : Colors.grey.withValues(alpha: 0.3);
        textColor = isSelected ? Colors.blue : Colors.black87;
      } else {
        // 已答题状态
        if (isCorrect) {
          backgroundColor = Colors.green.withValues(alpha: 0.1);
          borderColor = Colors.green;
          textColor = Colors.green[700]!;
        } else if (isSelected) {
          backgroundColor = Colors.red.withValues(alpha: 0.1);
          borderColor = Colors.red;
          textColor = Colors.red[700]!;
        } else {
          backgroundColor = Colors.grey.withValues(alpha: 0.1);
          borderColor = Colors.grey.withValues(alpha: 0.3);
          textColor = Colors.grey[600]!;
        }
      }

      return GestureDetector(
        onTap:
            hasAnswered ? null : () => controller.onChoiceQuizAnswered(index),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: borderColor, width: 2),
          ),
          child: Row(
            children: [
              // 选项标号
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: borderColor,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    String.fromCharCode(65 + index), // A, B, C, D
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),

              // 选项内容
              Expanded(
                child: Text(
                  quiz.options[index],
                  style: TextStyle(
                    fontSize: 16,
                    color: textColor,
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
              ),

              // 答题后的状态图标
              if (hasAnswered) ...[
                const SizedBox(width: 8),
                Icon(
                  isCorrect
                      ? Icons.check_circle
                      : (isSelected ? Icons.cancel : null),
                  color:
                      isCorrect
                          ? Colors.green
                          : (isSelected ? Colors.red : null),
                  size: 24,
                ),
              ],
            ],
          ),
        ),
      );
    });
  }
}
