import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/controllers/review_controller.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/services/review_state_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/services/review_quality_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/services/review_navigation_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/services/review_quiz_generation_service.dart';
import 'package:flutter_study_flow_by_myself/data/models/quiz/choice_quiz_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/quiz/spelling_quiz_model.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/quiz/models/review_quiz_answer_state.dart';

/// 复习测验控制器
///
/// 负责管理复习测验页面的状态和交互逻辑，参考QuizController的设计
///
/// 核心职责：
/// - 管理测验题的生成和展示
/// - 处理用户答题交互（选择题/拼写题）
/// - 管理答题状态和结果展示
/// - 与主控制器协调答题结果处理
/// - 处理复习流程的导航逻辑
class ReviewQuizController extends GetxController {
  // ==================== 依赖注入 ====================
  final ReviewController _reviewController;
  final ReviewStateService _stateService;
  final ReviewQualityService _qualityService;
  final ReviewNavigationService _navigationService;
  final ReviewQuizGenerationService _quizGenerationService;
  final LogService _log;

  ReviewQuizController({
    required ReviewController reviewController,
    required ReviewStateService stateService,
    required ReviewQualityService qualityService,
    required ReviewNavigationService navigationService,
    required ReviewQuizGenerationService quizGenerationService,
    required LogService log,
  }) : _reviewController = reviewController,
       _stateService = stateService,
       _qualityService = qualityService,
       _navigationService = navigationService,
       _quizGenerationService = quizGenerationService,
       _log = log;

  // ==================== 响应式状态 ====================

  /// 页面加载状态
  final isLoading = false.obs;

  /// 唯一的真实状态：当前答题状态
  /// 使用状态模式管理答题状态，确保状态一致性
  final Rx<ReviewQuizAnswerState> answerState = Rx<ReviewQuizAnswerState>(
    const ReviewQuizUnansweredState(),
  );

  // ==================== 计算属性 ====================

  /// 是否已回答当前题目
  bool get hasAnswered => answerState.value.hasAnswered;

  /// 选择题的选中选项索引
  int? get selectedOptionIndex => answerState.value.selectedOptionIndex;

  /// 上次答题是否正确
  bool? get isLastAnswerCorrect => answerState.value.isCorrect;

  // ==================== 生命周期管理 ====================

  @override
  void onInit() {
    super.onInit();
    _log.i('初始化复习测验控制器');

    _setupQuizStateListener();
    // 不立即加载测验题，等待状态服务的题目更新
  }

  /// 设置题目状态监听器
  /// 当状态服务产生新题目时，自动重置答题状态
  void _setupQuizStateListener() {
    ever(_stateService.currentQuiz, (quiz) {
      if (quiz != null) {
        _log.d('检测到新题目生成，重置为未答题状态');
        _resetAnswerState();
      }
    });
  }

  /// 重置答题状态为未答题状态
  void _resetAnswerState() {
    answerState.value = const ReviewQuizUnansweredState();
  }

  // ==================== 题目加载管理 ====================

  /// 请求下一道测验题（供UI调用）
  Future<void> requestNextQuiz() async {
    try {
      _log.i('请求下一道测验题');

      // 调用题目生成服务生成下一道题
      await _quizGenerationService.generateNextQuiz();
    } catch (e) {
      _log.e('请求测验题失败: $e');
      Get.snackbar('错误', '加载测验题失败，请重试');
    }
  }

  // ==================== 答题处理 ====================

  /// 获取指定类型的当前题目
  /// 返回强类型的题目对象，如果类型不匹配则返回null
  T? _getCurrentQuizAs<T>() {
    final quiz = _stateService.currentQuiz.value;
    return quiz is T ? quiz : null;
  }

  /// 处理选择题答题
  /// [selectedIndex] 用户选择的选项索引
  void onChoiceQuizAnswered(int selectedIndex) {
    if (hasAnswered) {
      _log.w('用户重复答题，忽略操作');
      return;
    }

    final quiz = _getCurrentQuizAs<ChoiceQuizModel>();
    if (quiz == null) {
      _log.e('当前题目不是选择题，无法处理选择题答题');
      return;
    }

    final isCorrect = selectedIndex == quiz.correctOptionIndex;
    _log.i(
      '用户选择选项 $selectedIndex，正确答案: ${quiz.correctOptionIndex}，结果: ${isCorrect ? '正确' : '错误'}',
    );

    // 一次性原子更新答题状态
    answerState.value = ReviewQuizAnsweredState.choice(
      selectedIndex: selectedIndex,
      isCorrect: isCorrect,
    );

    // 处理答题结果
    _processQuizResult(quiz.wordId, isCorrect);
  }

  /// 处理拼写题答题
  /// [userInput] 用户输入的拼写
  /// [isCorrect] 拼写是否正确
  void onSpellingQuizAnswered(String userInput, bool isCorrect) {
    if (hasAnswered) {
      _log.w('用户重复答题，忽略操作');
      return;
    }

    final quiz = _getCurrentQuizAs<SpellingQuizModel>();
    if (quiz == null) {
      _log.e('当前题目不是拼写题，无法处理拼写题答题');
      return;
    }

    _log.i(
      '用户输入拼写: "$userInput"，正确答案: "${quiz.correctAnswer}"，结果: ${isCorrect ? '正确' : '错误'}',
    );

    // 一次性原子更新答题状态
    answerState.value = ReviewQuizAnsweredState.spelling(
      userInput: userInput,
      isCorrect: isCorrect,
    );

    // 处理答题结果
    _processQuizResult(quiz.wordId, isCorrect);
  }

  // ==================== 答题结果处理 ====================

  /// 处理测验结果
  /// [wordId] 单词ID
  /// [isCorrect] 是否答对
  Future<void> _processQuizResult(int wordId, bool isCorrect) async {
    try {
      _log.i('处理测验结果: 单词ID=$wordId, 正确=$isCorrect');

      if (isCorrect) {
        await _handleCorrectAnswer(wordId);
      } else {
        await _handleIncorrectAnswer(wordId);
      }
    } catch (e) {
      _log.e('处理测验结果失败: $e');
      Get.snackbar('错误', '处理答题结果失败');
    }
  }

  /// 处理答对情况
  /// [wordId] 单词ID
  Future<void> _handleCorrectAnswer(int wordId) async {
    // 记录答对次数
    _stateService.recordCorrectAnswer(wordId);
    _log.i('单词 $wordId 答对次数: ${_stateService.getWordCorrectCount(wordId)}');

    // 检查是否完成该单词（答对3次）
    if (_stateService.isWordCompleted(wordId)) {
      await _handleWordCompleted(wordId);
    }
  }

  /// 处理答错情况
  /// [wordId] 单词ID
  Future<void> _handleIncorrectAnswer(int wordId) async {
    // 记录答错次数，保持答对次数不变
    _stateService.recordIncorrectAnswer(wordId);
    _log.i('单词 $wordId 答错，累计错误次数: ${_stateService.getWordErrorCount(wordId)}');

    // 不自动导航，等待用户点击"查看详情"按钮
    _log.i('用户答错，等待点击查看详情按钮');
  }

  /// 处理单词完成（答对3次）
  /// [wordId] 单词ID
  Future<void> _handleWordCompleted(int wordId) async {
    try {
      _log.i('单词 $wordId 已完成复习（答对3次），更新复习计划');

      // 调用质量服务更新复习项
      await _qualityService.updateWordReviewItem(
        wordId,
        _reviewController.currentUserWordBookId.value,
      );

      // 检查是否完成当前组的逻辑现在由 QuizGenerationService 处理
    } catch (e) {
      _log.e('处理单词完成失败: $e');
      Get.snackbar('错误', '更新复习进度失败');
    }
  }

  // ==================== 用户交互处理 ====================

  /// 处理用户点击操作按钮
  /// 根据答题结果决定下一步操作：答对加载下一题，答错查看详情
  Future<void> onActionButtonPressed() async {
    if (!hasAnswered) {
      _log.w('用户未答题就点击操作按钮，忽略操作');
      return;
    }

    try {
      final isCorrect = isLastAnswerCorrect ?? false;
      if (isCorrect) {
        // 答对了，加载下一题
        _log.i('用户答对，加载下一题');
        await requestNextQuiz();
      } else {
        // 答错了，导航到详情页面
        _log.i('用户答错，导航到详情页面');
        await _navigateToWordDetail();
      }
    } catch (e) {
      _log.e('处理操作按钮点击失败: $e');
      Get.snackbar('错误', '操作失败，请重试');
    }
  }

  /// 导航到单词详情页面
  Future<void> _navigateToWordDetail() async {
    final wordId = _extractWordIdFromCurrentQuiz();
    if (wordId > 0) {
      _log.i('用户点击查看详情按钮，导航到详情页面，单词ID: $wordId');
      await _navigationService.navigateToDetail(wordId);
    } else {
      _log.w('无法获取单词ID，无法导航到详情页面');
      Get.snackbar('错误', '无法查看详情，请重试');
    }
  }

  /// 从当前题目中提取单词ID
  int _extractWordIdFromCurrentQuiz() {
    final choiceQuiz = _getCurrentQuizAs<ChoiceQuizModel>();
    if (choiceQuiz != null) {
      return choiceQuiz.wordId;
    }

    final spellingQuiz = _getCurrentQuizAs<SpellingQuizModel>();
    if (spellingQuiz != null) {
      return spellingQuiz.wordId;
    }

    _log.w('未知的题目类型，无法提取单词ID');
    return -1;
  }

  // ==================== UI辅助方法 ====================

  /// 获取操作按钮文本
  String get actionButtonText {
    if (!hasAnswered) return '';
    final isCorrect = isLastAnswerCorrect ?? false;
    return isCorrect ? '下一题' : '查看详情';
  }

  @override
  void onClose() {
    _log.i('ReviewQuizController销毁');
    super.onClose();
  }
}
