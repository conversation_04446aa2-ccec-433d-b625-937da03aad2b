# 单词本功能实现计划

## 一、项目背景

当前项目是一个Flutter单词学习应用，已实现了基本的单词学习和复习功能。目前系统中，**单词本和单词是完全独立的**，用户学习和复习时直接获取一组与单词本无关的单词。我们需要改造现有项目，实现单词与单词本的关联，使学习和复习功能基于用户选择的特定单词本进行。

## 二、功能需求

1. **单词本选择与激活**：
   - 用户可以查看可用单词本列表
   - 下载新的单词本到本地
   - 激活特定单词本作为当前学习对象
   - 设置单词本的每日学习单词数量

2. **单词与单词本关联**：
   - 建立单词和单词本之间的多对多关系
   - 每个单词可以属于多个单词本
   - 单词学习状态与特定单词本关联

3. **基于单词本的学习功能**：
   - 从当前激活的单词本获取学习单词
   - 每日学习计划基于单词本设置
   - 学习进度独立记录于各单词本

4. **基于单词本的复习功能**：
   - 复习项与特定单词本关联
   - 每个单词本有独立的复习计划
   - 复习统计限定在单词本范围内

5. **单词本信息展示**：
   - 首页显示当前激活单词本的信息
   - 展示学习进度、单词总量等统计数据
   - 提供快速切换单词本的入口

## 三、数据模型架构

已实现的数据模型关系如下：

1. **WordModel与WordBookModel**：
   - 多对多关系（通过IsarLinks实现）
   - 单词可以属于多个单词本，单词本包含多个单词

2. **UserWordBookModel**：
   - 连接用户与单词本的桥梁模型
   - 包含学习状态、设置等用户特定信息
   - 存储isActive、dailyNewWordsCount等设置

3. **ReviewItemModel**：
   - 与UserWordBookModel建立关联，而非直接关联WordBookModel
   - 表示"用户-单词本-单词"三维关系中的学习状态
   - 存储下次复习时间、难度系数等学习细节

## 四、需要修改的文件

### 1. 数据Provider层

#### 1.1 ReviewItemLocalProvider
**文件路径**: `lib/data/providers/review_item/review_item_local_provider.dart`
**需修改内容**:
- 添加`getDueReviewItemsForUserWordBook(int userWordBookId, DateTime now)`方法
- 添加`getLearnedWordsCountByUserWordBook(int userWordBookId)`方法
- 添加`getReviewItemByWordIdAndUserWordBook(int wordId, int userWordBookId)`方法
- **删除或弃用**：不再依赖单词本的`getDueReviewItems(DateTime now)`方法
**理由**: 支持基于单词本的复习项查询，是实现单词本独立复习计划的基础

#### 1.2 LearningSessionLocalProvider
**文件路径**: `lib/data/providers/learning_session/learning_session_local_provider.dart`
**需修改内容**:
- 添加`getSessionsByUserWordBookId(int userWordBookId)`方法
- 修改创建会话的方法，包含userWordBook关联
- **删除或弃用**：不依赖单词本的会话相关方法
**理由**: 学习会话需要关联到特定单词本，以支持基于单词本的学习

### 2. 数据Repository层

#### 2.1 ReviewItemRepository
**文件路径**: `lib/data/repositories/review_item_repository.dart`
**需修改内容**:
- 假设所有复习项在下载单词本时已经创建
- 修改`getDueReviewWords()`方法，基于激活的单词本
- 修改`processLearningResult()`和`processReviewSuccess()/processReviewFailure()`方法，考虑单词本关系
- 添加`getTodayNewWords(int limit)`方法，从当前激活单词本中获取今日学习的单词
- **删除或弃用**：所有不依赖单词本的旧方法，例如不关联单词本的复习方法
**理由**: 核心业务逻辑需要支持单词本概念，确保学习数据与单词本关联

#### 2.2 WordRepository
**文件路径**: `lib/data/repositories/word_repository.dart`
**需修改内容**:
- 添加`getNewWordsFromWordBook(int wordBookId, int limit, {List<int> excludeIds})`方法
- 添加`getLearnedWordCountByWordBookId(int wordBookId)`方法
- 添加`getWordsByWordBookId(int wordBookId)`方法
- **删除或弃用**：不依赖单词本的`getNewWords(int limit)`方法
**理由**: 需要支持从特定单词本获取单词，是基于单词本学习的基础

#### 2.3 UserWordBookRepository
**文件路径**: `lib/data/repositories/user_word_book_repository.dart`
**需修改内容**:
- 修改`downloadAndCreateUserWordBook()`方法，**在下载时为所有单词批量创建复习项**
- 添加进度回调参数，用于UI显示复习项创建进度
- 使用分批处理减少内存压力并提供进度反馈
**理由**: 在下载阶段就创建所有复习项，避免后续学习时的延迟，简化学习流程

#### 2.4 LearningSessionRepository
**文件路径**: `lib/data/repositories/learning_session_repository.dart`
**需修改内容**:
- 修改`createNewSession()`方法，添加userWordBook参数
- 修改`getOngoingSession()`方法，限定在当前激活单词本范围内
- **删除或弃用**：不依赖单词本的会话管理方法
**理由**: 学习会话需要与单词本关联，支持多单词本独立学习

### 3. 控制器层

#### 3.1 HomeController
**文件路径**: `lib/modules/home/<USER>/home_controller.dart`
**需修改内容**:
- 添加`Rx<UserWordBookModel?> activeWordBook`变量
- 实现`loadActiveWordBook()`方法
- 修改待学习和待复习数量统计，限定当前单词本范围
- 增加检查是否有激活单词本的逻辑
- 直接从首页启动学习/复习流程时传递单词本信息
**理由**: 首页需要展示当前激活单词本信息，并直接启动基于单词本的学习/复习流程

#### 3.2 LearningController
**文件路径**: `lib/modules/learning/controllers/learning_controller.dart`
**需修改内容**:
- 修改`startNewSession()`方法，基于当前激活单词本获取单词
- 将学习结果与单词本关联
- 添加单词本学习进度更新逻辑
- **删除或弃用**：不依赖单词本的旧方法
**理由**: 学习功能需要基于当前激活的单词本进行

#### 3.3 ReviewController
**文件路径**: `lib/modules/learning/review/controllers/review_controller.dart`
**需修改内容**:
- 修改`loadDueReviewWords()`方法，基于当前激活单词本
- 调整复习统计，限定在单词本范围内
- **删除或弃用**：不依赖单词本的旧方法
**理由**: 复习功能需要针对特定单词本进行

#### 3.4 VocabularyController (新增)
**文件路径**: `lib/modules/vocabulary/controllers/vocabulary_controller.dart`
**需实现内容**:
- 获取本地和远程单词本列表
- 实现单词本下载和激活功能
- 实现单词本搜索和过滤功能
**理由**: 管理词汇库页面，处理单词本列表显示和操作

#### 3.5 WordBookDetailController (新增)
**文件路径**: `lib/modules/vocabulary/controllers/word_book_detail_controller.dart`
**需实现内容**:
- 获取单词本详细信息和学习统计
- 实现每日学习单词数量设置功能
- 处理激活单词本的逻辑
**理由**: 管理单词本详情页面，显示学习进度和单词本设置

### 4. UI层

#### 4.1 HomePage
**文件路径**: `lib/modules/home/<USER>/home_page.dart`
**需修改内容**:
- 添加当前单词本信息展示区域
- 展示单词本学习进度
- 增加单词本快速切换入口
- 修改学习和复习按钮，直接启动基于当前单词本的学习/复习流程
**理由**: 首页需要展示当前激活单词本信息，并直接启动相关学习流程

#### 4.2 VocabularyPage (新增)
**文件路径**: `lib/modules/vocabulary/views/vocabulary_page.dart`
**需实现内容**:
- 实现单词本列表展示
- 添加下载进度展示
- 实现单词本筛选和搜索功能
**理由**: 用户需要查看和管理单词本列表

#### 4.3 WordBookDetailPage (新增)
**文件路径**: `lib/modules/vocabulary/views/word_book_detail_page.dart`
**需实现内容**:
- 设计单词本详情页UI
- 实现学习设置修改功能
- 添加单词预览功能
**理由**: 用户需要查看单词本详情和修改设置

### 5. 路由和依赖注入

#### 5.1 Routes
**文件路径**: `lib/app/routes/app_pages.dart`
**需修改内容**:
- 添加词汇库和单词本详情页路由
- 更新初始路由逻辑，检查是否有激活单词本
- 修改学习/复习路由，确保传递单词本信息
**理由**: 新增页面需要加入路由系统，同时路由传参需要包含单词本信息

#### 5.2 Bindings
**文件路径**: `lib/app/di/app_binding.dart`
**需修改内容**:
- 注册新增的Repository和Controller
- 确保依赖关系正确配置
**理由**: 新增组件需要纳入依赖注入系统

## 五、实施步骤和优先级

### 步骤1：数据层基础设施
1. 完成ReviewItemLocalProvider的修改，添加单词本相关方法并标记弃用旧方法
2. 实现ReviewItemRepository中的"按需创建"策略，移除不依赖单词本的旧方法
3. 修改WordRepository添加单词本相关方法，移除独立的旧方法
4. 修改LearningSessionRepository支持单词本关联，处理旧数据兼容

### 步骤2：控制器层核心逻辑
1. 修改HomeController添加单词本加载逻辑，更新首页直接启动学习/复习流程
2. 修改LearningController基于单词本获取学习内容，删除旧版逻辑
3. 修改ReviewController基于单词本获取复习内容，删除旧版逻辑
4. 实现VocabularyController基本功能

### 步骤3：UI层实现
1. 修改HomePage添加单词本信息展示，更新学习/复习按钮逻辑
2. 实现VocabularyPage单词本列表
3. 实现WordBookDetailPage单词本详情和设置

### 步骤4：完善和优化
1. 实现路由和依赖注入配置
2. 添加首次启动单词本选择引导
3. 优化性能，特别是大词库情况
4. 添加全面的异常处理和用户反馈
5. 清理代码，移除所有冗余和不再需要的方法

## 六、潜在挑战和解决方案

### 1. 数据一致性问题
**挑战**: 确保ReviewItemModel同时与Word和UserWordBook保持正确关联
**解决方案**: 
- 在事务中完成关联操作
- 实现全面的数据验证
- 添加关系完整性检查

### 2. 性能优化
**挑战**: 大词库情况下保证性能
**解决方案**:
- 为关键查询字段添加索引
- 实现数据缓存策略
- 使用批量操作代替循环单次操作
- 优化关联查询逻辑
- **分批处理单词本下载和复习项创建，提供进度反馈**

### 3. 多词书业务逻辑
**挑战**: 处理单词在多个单词本中出现的情况
**解决方案**:
- 确保每个单词本有独立的学习进度
- 使用UserWordBook和ReviewItem的关联明确区分单词本关系
- 实现清晰的单词本切换流程

### 4. 下载时间较长
**挑战**: 为所有单词创建复习项会延长下载时间
**解决方案**:
- 提供明确的进度指示和分阶段反馈
- 分批处理大量数据，每批500个单词
- 优化数据库操作，使用批量插入
- 提供取消下载的选项

### 5. 用户体验
**挑战**: 确保多词书管理界面简洁易用
**解决方案**:
- 设计直观的单词本列表和详情页
- 提供清晰的激活状态指示
- 添加适当的引导和提示
- 优化单词本切换的流畅度

### 6. 代码清理和兼容性
**挑战**: 处理旧版不依赖单词本的功能代码
**解决方案**:
- 明确标记要删除的冗余方法
- 为可能仍需要的方法提供兼容性包装
- 确保新旧数据模型的平滑迁移
- 使用代码审查确保删除的方法没有被其他地方引用

## 七、总结

单词本功能是应用核心功能的重要升级，将为用户提供更个性化的学习体验。通过建立单词与单词本的关联，实现基于单词本的学习和复习功能，用户可以根据自己的需求选择不同的单词本进行学习，并拥有独立的学习进度和统计。

实施过程将严格遵循分层架构，通过Provider层访问数据，Repository层处理业务逻辑，Controller层管理状态，UI层展示界面，确保代码结构清晰，易于维护和扩展。同时将删除不再需要的旧版独立方法，确保代码简洁性。

本方案充分考虑了性能优化和用户体验，确保在大词库情况下仍能提供流畅的使用体验，为后续更多高级功能的实现奠定基础。 