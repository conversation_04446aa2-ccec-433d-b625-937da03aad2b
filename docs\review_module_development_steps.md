# 复习模块详细开发步骤

## 📋 开发概述

本文档提供复习模块的详细开发步骤，确保按照项目规范实现符合需求的复习功能。

### 开发顺序原则
1. **数据层优先**：先确保数据访问正常
2. **控制器后实现**：基于数据层实现业务逻辑
3. **UI最后完成**：基于控制器实现用户界面
4. **路由和绑定**：最后配置导航和依赖注入

## 🚀 第一阶段：基础架构搭建

### 步骤1：创建目录结构
**目标**：建立复习模块的完整目录结构

**操作**：创建以下目录和文件
```
lib/modules/learning/review/
├── controllers/
│   └── review_controller.dart
├── detail/
│   ├── controllers/
│   │   └── review_detail_controller.dart
│   └── views/
│       └── review_detail_page.dart
├── quiz/
│   ├── controllers/
│   │   └── review_quiz_controller.dart
│   └── views/
│       └── review_quiz_page.dart
├── result/
│   ├── controllers/
│   │   └── review_result_controller.dart
│   └── views/
│       └── review_result_page.dart
└── bindings/
    └── review_binding.dart
```

### 步骤2：实现主控制器 - ReviewController
**文件路径**：`lib/modules/learning/review/controllers/review_controller.dart`

**职责**：
- 管理复习会话的整体流程
- 处理5个单词一组的分组逻辑
- 跟踪每个单词的答对次数
- 协调子控制器之间的交互

**核心内容**：
```dart
import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_model.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/review_item_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/word_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/quiz_repository.dart';

/// 复习流程状态枚举
enum ReviewFlowState {
  idle,        // 空闲状态
  loading,     // 加载中
  reviewing,   // 复习中
  completed,   // 已完成
  error,       // 错误状态
}

/// 复习主控制器
///
/// 负责管理整个复习流程，包括：
/// - 5个单词一组的分组管理
/// - 每个单词答对3次的进度跟踪
/// - 复习会话的状态管理
class ReviewController extends GetxController {
  // 依赖注入
  final ReviewItemRepository _reviewItemRepository;
  final WordRepository _wordRepository;
  final QuizRepository _quizRepository;
  final LogService _log;

  ReviewController({
    required ReviewItemRepository reviewItemRepository,
    required WordRepository wordRepository,
    required QuizRepository quizRepository,
    required LogService log,
  }) : _reviewItemRepository = reviewItemRepository,
       _wordRepository = wordRepository,
       _quizRepository = quizRepository,
       _log = log;

  // 响应式状态变量
  final state = ReviewFlowState.idle.obs;
  final currentUserWordBookId = 0.obs;

  // 分组相关状态
  final allReviewWords = <WordModel>[].obs;           // 所有待复习单词
  final wordGroups = <List<WordModel>>[].obs;         // 分组后的单词列表
  final currentGroupIndex = 0.obs;                    // 当前组索引
  final currentWordIndex = 0.obs;                     // 当前组内单词索引

  // 进度跟踪
  final wordCorrectCounts = <int, int>{}.obs;         // 每个单词的答对次数 Map<wordId, count>
  final wordErrorCounts = <int, int>{}.obs;           // 每个单词的错误次数 Map<wordId, count>
  final completedWords = <int>{}.obs;                 // 已完成的单词ID集合

  // 统计数据
  final totalQuizCount = 0.obs;                       // 总题目数
  final completedQuizCount = 0.obs;                   // 已完成题目数
  final correctAnswers = 0.obs;                       // 正确答案数

  // 计算属性
  List<WordModel> get currentGroup =>
      wordGroups.isNotEmpty && currentGroupIndex.value < wordGroups.length
          ? wordGroups[currentGroupIndex.value]
          : [];

  WordModel? get currentWord =>
      currentGroup.isNotEmpty && currentWordIndex.value < currentGroup.length
          ? currentGroup[currentWordIndex.value]
          : null;

  bool get isLastGroup => currentGroupIndex.value >= wordGroups.length - 1;
  bool get isGroupCompleted => completedWords.length >= currentGroup.length;

  String get progressText =>
      '第${currentGroupIndex.value + 1}组 / 共${wordGroups.length}组';

  double get overallProgress {
    if (allReviewWords.isEmpty) return 0.0;
    return completedWords.length / allReviewWords.length;
  }

  /// 开始复习流程
  Future<void> startReview(int userWordBookId) async {
    try {
      state.value = ReviewFlowState.loading;
      currentUserWordBookId.value = userWordBookId;

      _log.i('开始复习流程，单词本ID: $userWordBookId');

      // 1. 获取待复习单词
      await _loadReviewWords();

      // 2. 分组处理
      _groupWords();

      // 3. 初始化状态
      _initializeReviewState();

      state.value = ReviewFlowState.reviewing;
      _log.i('复习流程初始化完成，共${allReviewWords.length}个单词，${wordGroups.length}组');

    } catch (e) {
      _log.e('开始复习流程失败: $e');
      state.value = ReviewFlowState.error;
    }
  }

  /// 处理答题结果
  Future<void> handleQuizAnswer(int wordId, bool isCorrect) async {
    try {
      _log.i('处理答题结果: 单词ID=$wordId, 正确=${isCorrect}');

      if (isCorrect) {
        // 答对处理
        await _handleCorrectAnswer(wordId);
      } else {
        // 答错处理
        await _handleIncorrectAnswer(wordId);
      }

    } catch (e) {
      _log.e('处理答题结果失败: $e');
    }
  }

  /// 获取下一个测验题目
  Future<dynamic> getNextQuiz() async {
    try {
      final word = _selectNextWord();
      if (word == null) {
        _log.i('没有更多单词需要复习');
        return null;
      }

      // 生成随机测验题
      final quiz = await _quizRepository.generateRandomQuizForWord(word);
      _log.i('为单词${word.spelling}生成测验题');
      return quiz;

    } catch (e) {
      _log.e('获取下一个测验题失败: $e');
      return null;
    }
  }

  // 私有方法实现...
  Future<void> _loadReviewWords() async {
    final words = await _reviewItemRepository
        .getDueReviewWordsByUserWordBook(currentUserWordBookId.value);
    allReviewWords.assignAll(words);
    _log.i('加载待复习单词: ${words.length}个');
  }

  void _groupWords() {
    wordGroups.clear();
    const groupSize = 5;

    for (int i = 0; i < allReviewWords.length; i += groupSize) {
      final end = (i + groupSize < allReviewWords.length)
          ? i + groupSize
          : allReviewWords.length;
      wordGroups.add(allReviewWords.sublist(i, end));
    }

    _log.i('单词分组完成: ${wordGroups.length}组');
  }

  void _initializeReviewState() {
    currentGroupIndex.value = 0;
    currentWordIndex.value = 0;
    wordCorrectCounts.clear();
    wordErrorCounts.clear();
    completedWords.clear();
    totalQuizCount.value = allReviewWords.length * 3; // 每个单词需要答对3次
    completedQuizCount.value = 0;
    correctAnswers.value = 0;
  }

  Future<void> _handleCorrectAnswer(int wordId) async {
    // 增加答对次数
    wordCorrectCounts[wordId] = (wordCorrectCounts[wordId] ?? 0) + 1;
    correctAnswers.value++;
    completedQuizCount.value++;

    // 检查是否完成该单词
    if (wordCorrectCounts[wordId]! >= 3) {
      completedWords.add(wordId);

      // 计算质量评分并更新复习时间
      final errorCount = wordErrorCounts[wordId] ?? 0;
      final quality = _calculateQuality(errorCount);
      await _reviewItemRepository.processReviewResult(wordId, quality);

      _log.i('单词$wordId已完成复习，错误次数: $errorCount，质量评分: $quality');
    }

    // 检查是否完成当前组
    if (isGroupCompleted) {
      await _handleGroupCompleted();
    }
  }

  Future<void> _handleIncorrectAnswer(int wordId) async {
    // 重置答对次数，增加错误次数
    wordCorrectCounts[wordId] = 0;
    wordErrorCounts[wordId] = (wordErrorCounts[wordId] ?? 0) + 1;
    completedQuizCount.value++;

    _log.i('单词$wordId答错，重置答对次数，错误次数: ${wordErrorCounts[wordId]}');
  }

  Future<void> _handleGroupCompleted() async {
    if (isLastGroup) {
      // 所有组都完成了
      state.value = ReviewFlowState.completed;
      _log.i('所有复习组已完成');
    } else {
      // 进入下一组
      currentGroupIndex.value++;
      currentWordIndex.value = 0;
      _log.i('进入下一组: ${currentGroupIndex.value + 1}');
    }
  }

  WordModel? _selectNextWord() {
    // 从当前组中选择下一个未完成的单词
    for (final word in currentGroup) {
      if (!completedWords.contains(word.id)) {
        return word;
      }
    }
    return null;
  }

  double _calculateQuality(int errorCount) {
    // 根据错误次数计算1-5分的质量评分
    switch (errorCount) {
      case 0: return 5.0;  // 完美
      case 1: return 4.0;  // 很好
      case 2: return 3.0;  // 良好
      default: return 2.0; // 需要加强
    }
  }

  @override
  void onClose() {
    _log.i('ReviewController销毁');
    super.onClose();
  }
}
```

**关键设计点**：
1. **分组逻辑**：5个单词一组，支持动态分组
2. **进度跟踪**：使用Map记录每个单词的答对/错误次数
3. **状态管理**：清晰的状态枚举和响应式变量
4. **错误处理**：完整的try-catch和日志记录

### 步骤3：实现复习测验控制器 - ReviewQuizController
**文件路径**：`lib/modules/learning/review/quiz/controllers/review_quiz_controller.dart`

**职责**：
- 处理测验页面的UI交互
- 管理答题状态和反馈
- 与主控制器协调答题结果

**核心内容**：
```dart
import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/data/models/choice_quiz_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_model.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/controllers/review_controller.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/routes/learning_routes.dart';

/// 复习测验控制器
///
/// 负责处理复习测验页面的UI交互和状态管理
class ReviewQuizController extends GetxController {
  // 依赖注入
  final ReviewController _reviewController = Get.find<ReviewController>();
  final LogService _log = Get.find<LogService>();

  // 响应式状态
  final currentQuiz = Rx<dynamic>(null);              // 当前测验题
  final hasAnswered = false.obs;                      // 是否已答题
  final selectedOptionIndex = (-1).obs;               // 选中的选项索引
  final isLoading = false.obs;                        // 加载状态
  final actionButtonText = '提交答案'.obs;             // 操作按钮文本

  // 计算属性
  WordModel? get currentWord => _reviewController.currentWord;
  String get progressText => _reviewController.progressText;
  double get progress => _reviewController.overallProgress;

  @override
  void onInit() {
    super.onInit();
    _log.i('ReviewQuizController初始化');
    _loadNextQuiz();
  }

  /// 加载下一个测验题
  Future<void> _loadNextQuiz() async {
    try {
      isLoading.value = true;

      final quiz = await _reviewController.getNextQuiz();
      if (quiz == null) {
        // 没有更多题目，跳转到结果页面
        _navigateToResult();
        return;
      }

      currentQuiz.value = quiz;
      _resetQuizState();

      _log.i('加载测验题成功');

    } catch (e) {
      _log.e('加载测验题失败: $e');
      Get.snackbar('错误', '加载题目失败，请重试');
    } finally {
      isLoading.value = false;
    }
  }

  /// 选择答案选项
  void selectOption(int index) {
    if (hasAnswered.value) return;

    selectedOptionIndex.value = index;
    _log.i('选择选项: $index');
  }

  /// 提交答案
  Future<void> submitAnswer() async {
    if (hasAnswered.value) {
      // 已答题，进入下一题
      await _loadNextQuiz();
      return;
    }

    if (selectedOptionIndex.value == -1) {
      Get.snackbar('提示', '请选择一个答案');
      return;
    }

    try {
      hasAnswered.value = true;
      actionButtonText.value = '下一题';

      // 检查答案是否正确
      final isCorrect = _checkAnswer();

      // 处理答题结果
      await _reviewController.handleQuizAnswer(
        currentWord!.id,
        isCorrect
      );

      if (!isCorrect) {
        // 答错了，跳转到详情页面
        await _navigateToDetail();
      }

      _log.i('提交答案: ${isCorrect ? '正确' : '错误'}');

    } catch (e) {
      _log.e('提交答案失败: $e');
      Get.snackbar('错误', '提交答案失败，请重试');
    }
  }

  /// 检查答案是否正确
  bool _checkAnswer() {
    final quiz = currentQuiz.value;
    if (quiz is ChoiceQuizModel) {
      return selectedOptionIndex.value == quiz.correctOptionIndex;
    }
    // 其他题型的检查逻辑...
    return false;
  }

  /// 重置测验状态
  void _resetQuizState() {
    hasAnswered.value = false;
    selectedOptionIndex.value = -1;
    actionButtonText.value = '提交答案';
  }

  /// 导航到详情页面
  Future<void> _navigateToDetail() async {
    final result = await Get.toNamed(
      LearningRoutes.REVIEW_DETAIL,
      arguments: {'wordId': currentWord!.id},
    );

    // 从详情页面返回后，继续当前单词的测验
    if (result == 'continue') {
      await _loadNextQuiz();
    }
  }

  /// 导航到结果页面
  void _navigateToResult() {
    Get.offNamed(LearningRoutes.REVIEW_RESULT);
  }

  @override
  void onClose() {
    _log.i('ReviewQuizController销毁');
    super.onClose();
  }
}
```

### 步骤4：实现复习详情控制器 - ReviewDetailController
**文件路径**：`lib/modules/learning/review/detail/controllers/review_detail_controller.dart`

**职责**：
- 展示单词详细信息
- 处理音频播放
- 管理返回复习的逻辑

**核心内容**：
```dart
import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_model.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/word_repository.dart';

/// 复习详情控制器
///
/// 负责处理复习模式下的单词详情展示
class ReviewDetailController extends GetxController {
  // 依赖注入
  final WordRepository _wordRepository = Get.find<WordRepository>();
  final LogService _log = Get.find<LogService>();

  // 响应式状态
  final currentWord = Rx<WordModel?>(null);
  final isLoading = false.obs;
  final isPlayingAudio = false.obs;

  @override
  void onInit() {
    super.onInit();
    _log.i('ReviewDetailController初始化');
    _loadWordDetail();
  }

  /// 加载单词详情
  Future<void> _loadWordDetail() async {
    try {
      isLoading.value = true;

      final arguments = Get.arguments as Map<String, dynamic>?;
      final wordId = arguments?['wordId'] as int?;

      if (wordId == null) {
        _log.e('未提供单词ID');
        Get.back();
        return;
      }

      final word = await _wordRepository.getWordById(wordId);
      if (word == null) {
        _log.e('未找到单词: $wordId');
        Get.back();
        return;
      }

      currentWord.value = word;
      _log.i('加载单词详情成功: ${word.spelling}');

    } catch (e) {
      _log.e('加载单词详情失败: $e');
      Get.snackbar('错误', '加载单词详情失败');
      Get.back();
    } finally {
      isLoading.value = false;
    }
  }

  /// 播放单词音频
  Future<void> playAudio() async {
    try {
      if (currentWord.value?.audioUrl == null) {
        Get.snackbar('提示', '该单词暂无音频');
        return;
      }

      isPlayingAudio.value = true;
      _log.i('播放音频: ${currentWord.value!.spelling}');

      // TODO: 实现音频播放逻辑
      await Future.delayed(const Duration(seconds: 2)); // 模拟播放

    } catch (e) {
      _log.e('播放音频失败: $e');
      Get.snackbar('错误', '播放音频失败');
    } finally {
      isPlayingAudio.value = false;
    }
  }

  /// 继续复习
  void continueReview() {
    _log.i('继续复习');
    Get.back(result: 'continue');
  }

  @override
  void onClose() {
    _log.i('ReviewDetailController销毁');
    super.onClose();
  }
}
```

### 步骤5：实现复习结果控制器 - ReviewResultController
**文件路径**：`lib/modules/learning/review/result/controllers/review_result_controller.dart`

**职责**：
- 展示复习结果统计
- 处理返回首页的逻辑

**核心内容**：
```dart
import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/controllers/review_controller.dart';
import 'package:flutter_study_flow_by_myself/app/routes/app_routes.dart';

/// 复习结果控制器
///
/// 负责处理复习结果页面的数据展示和操作
class ReviewResultController extends GetxController {
  // 依赖注入
  final ReviewController _reviewController = Get.find<ReviewController>();
  final LogService _log = Get.find<LogService>();

  // 响应式状态
  final reviewDuration = 0.obs;                       // 复习用时（秒）
  final startTime = DateTime.now().obs;               // 开始时间

  // 计算属性
  int get totalWords => _reviewController.allReviewWords.length;
  int get completedWords => _reviewController.completedWords.length;
  int get totalQuizCount => _reviewController.totalQuizCount.value;
  int get correctAnswers => _reviewController.correctAnswers.value;
  int get incorrectAnswers => totalQuizCount - correctAnswers;

  double get accuracy => totalQuizCount > 0
      ? (correctAnswers / totalQuizCount * 100)
      : 0.0;

  String get formattedDuration {
    final minutes = reviewDuration.value ~/ 60;
    final seconds = reviewDuration.value % 60;
    return '${minutes}分${seconds}秒';
  }

  @override
  void onInit() {
    super.onInit();
    _log.i('ReviewResultController初始化');
    _calculateDuration();
  }

  /// 计算复习用时
  void _calculateDuration() {
    // 这里应该从ReviewController获取实际的开始时间
    // 暂时使用模拟数据
    reviewDuration.value = 300; // 5分钟
    _log.i('复习用时: ${formattedDuration}');
  }

  /// 返回首页
  void returnToHome() {
    _log.i('返回首页');
    Get.offAllNamed(AppRoutes.MAIN);
  }

  /// 重新复习错误单词（可选功能）
  void reviewIncorrectWords() {
    _log.i('重新复习错误单词');
    // TODO: 实现重新复习错误单词的逻辑
    Get.snackbar('提示', '该功能正在开发中');
  }

  @override
  void onClose() {
    _log.i('ReviewResultController销毁');
    super.onClose();
  }
}
```

## 🚀 第二阶段：UI界面实现

### 步骤6：实现复习测验页面 - ReviewQuizPage
**文件路径**：`lib/modules/learning/review/quiz/views/review_quiz_page.dart`

**职责**：
- 展示测验题目和选项
- 显示进度条和统计信息
- 处理用户答题交互

**核心内容**：
```dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/quiz/controllers/review_quiz_controller.dart';
import 'package:flutter_study_flow_by_myself/data/models/choice_quiz_model.dart';

/// 复习测验页面
///
/// 展示复习模式下的测验界面
class ReviewQuizPage extends GetView<ReviewQuizController> {
  const ReviewQuizPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('复习测验'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Get.back(),
        ),
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        return Column(
          children: [
            // 进度指示器
            _buildProgressIndicator(),

            // 测验内容
            Expanded(
              child: _buildQuizContent(),
            ),

            // 操作按钮
            _buildActionButton(),
          ],
        );
      }),
    );
  }

  /// 构建进度指示器
  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Column(
        children: [
          // 进度条
          LinearProgressIndicator(
            value: controller.progress,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              Get.theme.primaryColor,
            ),
          ),
          const SizedBox(height: 8),

          // 进度文本
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                controller.progressText,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '${(controller.progress * 100).toInt()}%',
                style: TextStyle(
                  fontSize: 14,
                  color: Get.theme.primaryColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建测验内容
  Widget _buildQuizContent() {
    final quiz = controller.currentQuiz.value;

    if (quiz == null) {
      return const Center(
        child: Text('加载中...'),
      );
    }

    if (quiz is ChoiceQuizModel) {
      return _buildChoiceQuiz(quiz);
    }

    // 其他题型的UI实现...
    return const Center(
      child: Text('不支持的题型'),
    );
  }

  /// 构建选择题UI
  Widget _buildChoiceQuiz(ChoiceQuizModel quiz) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 题目
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    quiz.question,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (quiz.questionAudioUrl != null) ...[
                    const SizedBox(height: 8),
                    IconButton(
                      icon: const Icon(Icons.volume_up),
                      onPressed: () {
                        // TODO: 播放题目音频
                      },
                    ),
                  ],
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // 选项
          Expanded(
            child: ListView.builder(
              itemCount: quiz.options.length,
              itemBuilder: (context, index) {
                return _buildOptionItem(quiz, index);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建选项项
  Widget _buildOptionItem(ChoiceQuizModel quiz, int index) {
    final isSelected = controller.selectedOptionIndex.value == index;
    final hasAnswered = controller.hasAnswered.value;
    final isCorrect = index == quiz.correctOptionIndex;

    Color? backgroundColor;
    Color? borderColor;

    if (hasAnswered) {
      if (isSelected) {
        backgroundColor = isCorrect ? Colors.green[100] : Colors.red[100];
        borderColor = isCorrect ? Colors.green : Colors.red;
      } else if (isCorrect) {
        backgroundColor = Colors.green[100];
        borderColor = Colors.green;
      }
    } else if (isSelected) {
      backgroundColor = Get.theme.primaryColor.withOpacity(0.1);
      borderColor = Get.theme.primaryColor;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: hasAnswered ? null : () => controller.selectOption(index),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: backgroundColor,
            border: Border.all(
              color: borderColor ?? Colors.grey[300]!,
              width: 2,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isSelected
                      ? (hasAnswered
                          ? (isCorrect ? Colors.green : Colors.red)
                          : Get.theme.primaryColor)
                      : Colors.transparent,
                  border: Border.all(
                    color: isSelected
                        ? Colors.transparent
                        : Colors.grey[400]!,
                  ),
                ),
                child: isSelected
                    ? Icon(
                        hasAnswered
                            ? (isCorrect ? Icons.check : Icons.close)
                            : Icons.circle,
                        color: Colors.white,
                        size: 16,
                      )
                    : Text(
                        String.fromCharCode(65 + index), // A, B, C, D
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  quiz.options[index],
                  style: const TextStyle(fontSize: 16),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SizedBox(
        width: double.infinity,
        height: 48,
        child: ElevatedButton(
          onPressed: controller.submitAnswer,
          style: ElevatedButton.styleFrom(
            backgroundColor: Get.theme.primaryColor,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            controller.actionButtonText.value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }
}
```

### 步骤7：实现复习详情页面 - ReviewDetailPage
**文件路径**：`lib/modules/learning/review/detail/views/review_detail_page.dart`

**职责**：
- 展示单词的详细信息
- 提供音频播放功能
- 提供继续复习按钮

**核心内容**：参考新学流程的DetailPage，实现复习专用的详情页面

### 步骤8：实现复习结果页面 - ReviewResultPage
**文件路径**：`lib/modules/learning/review/result/views/review_result_page.dart`

**职责**：
- 展示复习统计数据
- 提供返回首页和重新复习选项

## 🚀 第三阶段：路由和绑定配置

### 步骤9：更新路由配置
**文件路径**：`lib/modules/learning/routes/learning_routes.dart`

**操作**：添加复习模块的路由常量
```dart
class LearningRoutes {
  // 现有路由...

  // 复习模块路由
  static const REVIEW_QUIZ = '/review_quiz';
  static const REVIEW_DETAIL = '/review_detail';
  static const REVIEW_RESULT = '/review_result';
}
```

### 步骤10：更新路由页面配置
**文件路径**：`lib/modules/learning/routes/learning_pages.dart`

**操作**：添加复习模块的GetPage配置
```dart
import 'package:flutter_study_flow_by_myself/modules/learning/review/bindings/review_binding.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/quiz/views/review_quiz_page.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/detail/views/review_detail_page.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/result/views/review_result_page.dart';

final learningPages = [
  // 现有页面...

  // 复习模块页面
  GetPage(
    name: LearningRoutes.REVIEW_QUIZ,
    page: () => const ReviewQuizPage(),
    binding: ReviewBinding(),
  ),
  GetPage(
    name: LearningRoutes.REVIEW_DETAIL,
    page: () => const ReviewDetailPage(),
    binding: ReviewBinding(),
  ),
  GetPage(
    name: LearningRoutes.REVIEW_RESULT,
    page: () => const ReviewResultPage(),
    binding: ReviewBinding(),
  ),
];
```

### 步骤11：实现依赖注入绑定
**文件路径**：`lib/modules/learning/review/bindings/review_binding.dart`

**职责**：
- 注册复习模块的所有控制器
- 确保依赖关系正确配置

**核心内容**：
```dart
import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/controllers/review_controller.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/quiz/controllers/review_quiz_controller.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/detail/controllers/review_detail_controller.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/result/controllers/review_result_controller.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/review_item_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/word_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/quiz_repository.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';

/// 复习模块依赖注入绑定
class ReviewBinding extends Bindings {
  @override
  void dependencies() {
    // 确保全局依赖已注册
    _ensureGlobalDependencies();

    // 注册主控制器
    Get.lazyPut<ReviewController>(
      () => ReviewController(
        reviewItemRepository: Get.find<ReviewItemRepository>(),
        wordRepository: Get.find<WordRepository>(),
        quizRepository: Get.find<QuizRepository>(),
        log: Get.find<LogService>(),
      ),
      fenix: true,
    );

    // 注册子控制器
    Get.lazyPut<ReviewQuizController>(
      () => ReviewQuizController(),
      fenix: true,
    );

    Get.lazyPut<ReviewDetailController>(
      () => ReviewDetailController(),
      fenix: true,
    );

    Get.lazyPut<ReviewResultController>(
      () => ReviewResultController(),
      fenix: true,
    );
  }

  /// 确保全局依赖已注册
  void _ensureGlobalDependencies() {
    if (!Get.isRegistered<ReviewItemRepository>()) {
      Get.lazyPut<ReviewItemRepository>(() => ReviewItemRepository());
    }

    if (!Get.isRegistered<WordRepository>()) {
      Get.lazyPut<WordRepository>(() => WordRepository());
    }

    if (!Get.isRegistered<QuizRepository>()) {
      Get.lazyPut<QuizRepository>(() => QuizRepository());
    }

    if (!Get.isRegistered<LogService>()) {
      Get.lazyPut<LogService>(() => LogService());
    }
  }
}
```

## 🚀 第四阶段：首页集成

### 步骤12：更新首页控制器
**文件路径**：`lib/modules/home/<USER>/home_controller.dart`

**操作**：添加复习相关的方法和状态
```dart
// 在HomeController中添加以下方法和属性

// 复习相关状态
final reviewWordsCount = 0.obs;                    // 待复习单词数量
final canStartReview = false.obs;                  // 是否可以开始复习

/// 检查复习状态
Future<void> checkReviewStatus() async {
  try {
    final activeUserWordBook = userWordBookStateManager.activeUserWordBook.value;

    if (activeUserWordBook == null) {
      reviewWordsCount.value = 0;
      canStartReview.value = false;
      return;
    }

    // 获取待复习单词数量
    final count = await reviewItemRepository.getDueReviewWordsCountByUserWordBook(
      activeUserWordBook.id
    );

    reviewWordsCount.value = count;
    canStartReview.value = count > 0;

    log.i('待复习单词数量: $count');

  } catch (e) {
    log.e('检查复习状态失败: $e');
    reviewWordsCount.value = 0;
    canStartReview.value = false;
  }
}

/// 开始复习
Future<void> startReview() async {
  try {
    final activeUserWordBook = userWordBookStateManager.activeUserWordBook.value;

    if (activeUserWordBook == null) {
      Get.snackbar('提示', '请先激活一个单词本');
      return;
    }

    if (!canStartReview.value) {
      Get.snackbar('提示', '当前没有待复习的单词');
      return;
    }

    log.i('开始复习，单词本ID: ${activeUserWordBook.id}');

    // 导航到复习页面，传递单词本ID
    await Get.toNamed(
      LearningRoutes.REVIEW_QUIZ,
      arguments: {'userWordBookId': activeUserWordBook.id},
    );

    // 复习完成后刷新状态
    await checkReviewStatus();

  } catch (e) {
    log.e('开始复习失败: $e');
    Get.snackbar('错误', '开始复习失败，请重试');
  }
}
```

### 步骤13：更新首页UI
**文件路径**：`lib/modules/home/<USER>/home_page.dart`

**操作**：更新复习按钮的显示逻辑
```dart
// 在HomePage的build方法中更新复习按钮

ElevatedButton(
  onPressed: controller.canStartReview.value
      ? controller.startReview
      : null,
  child: Obx(() {
    if (controller.userWordBookStateManager.activeUserWordBook.value == null) {
      return const Text('请先激活单词本');
    } else if (controller.canStartReview.value) {
      return Text('开始复习 (${controller.reviewWordsCount.value}个单词)');
    } else {
      return const Text('暂无待复习单词');
    }
  }),
),
```

## 🎯 开发验证清单

### 功能验证
- [ ] 复习流程能正确启动
- [ ] 5个单词一组的分组逻辑正确
- [ ] 每个单词需要答对3次的逻辑正确
- [ ] 答错后能正确跳转到详情页面
- [ ] 详情页面能正确返回测验页面
- [ ] 复习完成后能正确跳转到结果页面
- [ ] 结果页面能正确显示统计数据
- [ ] 首页能正确显示待复习单词数量

### 技术验证
- [ ] 所有控制器能正确注入依赖
- [ ] 路由配置正确，页面能正常导航
- [ ] 响应式状态更新正常
- [ ] 错误处理和日志记录完整
- [ ] 内存管理正确，无泄漏

### UI验证
- [ ] 进度条显示正确
- [ ] 测验页面布局合理
- [ ] 详情页面信息完整
- [ ] 结果页面统计准确
- [ ] 所有按钮状态正确

这个开发步骤确保了复习模块的完整实现，遵循项目的架构规范，并提供了详细的代码示例和验证清单。
```
```