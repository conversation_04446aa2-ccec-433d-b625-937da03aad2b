// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_item_model.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetReviewItemModelCollection on Isar {
  IsarCollection<ReviewItemModel> get reviewItemModels => this.collection();
}

const ReviewItemModelSchema = CollectionSchema(
  name: r'ReviewItemModel',
  id: 8810633943658974267,
  properties: {
    r'easeFactor': PropertySchema(
      id: 0,
      name: r'easeFactor',
      type: IsarType.double,
    ),
    r'firstStudyDate': PropertySchema(
      id: 1,
      name: r'firstStudyDate',
      type: IsarType.dateTime,
    ),
    r'isNewWord': PropertySchema(
      id: 2,
      name: r'isNewWord',
      type: IsarType.bool,
    ),
    r'lastReviewDate': PropertySchema(
      id: 3,
      name: r'lastReviewDate',
      type: IsarType.dateTime,
    ),
    r'nextReviewDate': PropertySchema(
      id: 4,
      name: r'nextReviewDate',
      type: IsarType.dateTime,
    ),
    r'previousInterval': PropertySchema(
      id: 5,
      name: r'previousInterval',
      type: IsarType.long,
    ),
    r'repetitionCount': PropertySchema(
      id: 6,
      name: r'repetitionCount',
      type: IsarType.long,
    ),
    r'totalReviewCount': PropertySchema(
      id: 7,
      name: r'totalReviewCount',
      type: IsarType.long,
    ),
    r'userWordBookId': PropertySchema(
      id: 8,
      name: r'userWordBookId',
      type: IsarType.long,
    ),
    r'wordId': PropertySchema(
      id: 9,
      name: r'wordId',
      type: IsarType.long,
    )
  },
  estimateSize: _reviewItemModelEstimateSize,
  serialize: _reviewItemModelSerialize,
  deserialize: _reviewItemModelDeserialize,
  deserializeProp: _reviewItemModelDeserializeProp,
  idName: r'id',
  indexes: {
    r'nextReviewDate': IndexSchema(
      id: 4152658090540413903,
      name: r'nextReviewDate',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'nextReviewDate',
          type: IndexType.value,
          caseSensitive: false,
        )
      ],
    )
  },
  links: {
    r'word': LinkSchema(
      id: 1844187441337223121,
      name: r'word',
      target: r'WordModel',
      single: true,
    ),
    r'userWordBook': LinkSchema(
      id: 5856611980366907849,
      name: r'userWordBook',
      target: r'UserWordBookModel',
      single: true,
    )
  },
  embeddedSchemas: {},
  getId: _reviewItemModelGetId,
  getLinks: _reviewItemModelGetLinks,
  attach: _reviewItemModelAttach,
  version: '3.1.0+1',
);

int _reviewItemModelEstimateSize(
  ReviewItemModel object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  return bytesCount;
}

void _reviewItemModelSerialize(
  ReviewItemModel object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeDouble(offsets[0], object.easeFactor);
  writer.writeDateTime(offsets[1], object.firstStudyDate);
  writer.writeBool(offsets[2], object.isNewWord);
  writer.writeDateTime(offsets[3], object.lastReviewDate);
  writer.writeDateTime(offsets[4], object.nextReviewDate);
  writer.writeLong(offsets[5], object.previousInterval);
  writer.writeLong(offsets[6], object.repetitionCount);
  writer.writeLong(offsets[7], object.totalReviewCount);
  writer.writeLong(offsets[8], object.userWordBookId);
  writer.writeLong(offsets[9], object.wordId);
}

ReviewItemModel _reviewItemModelDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = ReviewItemModel(
    easeFactor: reader.readDoubleOrNull(offsets[0]) ?? 2.5,
    firstStudyDate: reader.readDateTimeOrNull(offsets[1]),
    isNewWord: reader.readBoolOrNull(offsets[2]) ?? true,
    lastReviewDate: reader.readDateTimeOrNull(offsets[3]),
    nextReviewDate: reader.readDateTime(offsets[4]),
    previousInterval: reader.readLongOrNull(offsets[5]) ?? 0,
    repetitionCount: reader.readLongOrNull(offsets[6]) ?? 0,
    totalReviewCount: reader.readLongOrNull(offsets[7]) ?? 0,
    userWordBookId: reader.readLong(offsets[8]),
    wordId: reader.readLong(offsets[9]),
  );
  object.id = id;
  return object;
}

P _reviewItemModelDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readDoubleOrNull(offset) ?? 2.5) as P;
    case 1:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 2:
      return (reader.readBoolOrNull(offset) ?? true) as P;
    case 3:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 4:
      return (reader.readDateTime(offset)) as P;
    case 5:
      return (reader.readLongOrNull(offset) ?? 0) as P;
    case 6:
      return (reader.readLongOrNull(offset) ?? 0) as P;
    case 7:
      return (reader.readLongOrNull(offset) ?? 0) as P;
    case 8:
      return (reader.readLong(offset)) as P;
    case 9:
      return (reader.readLong(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _reviewItemModelGetId(ReviewItemModel object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _reviewItemModelGetLinks(ReviewItemModel object) {
  return [object.word, object.userWordBook];
}

void _reviewItemModelAttach(
    IsarCollection<dynamic> col, Id id, ReviewItemModel object) {
  object.id = id;
  object.word.attach(col, col.isar.collection<WordModel>(), r'word', id);
  object.userWordBook.attach(
      col, col.isar.collection<UserWordBookModel>(), r'userWordBook', id);
}

extension ReviewItemModelQueryWhereSort
    on QueryBuilder<ReviewItemModel, ReviewItemModel, QWhere> {
  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterWhere>
      anyNextReviewDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        const IndexWhereClause.any(indexName: r'nextReviewDate'),
      );
    });
  }
}

extension ReviewItemModelQueryWhere
    on QueryBuilder<ReviewItemModel, ReviewItemModel, QWhereClause> {
  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterWhereClause> idEqualTo(
      Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterWhereClause> idLessThan(
      Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterWhereClause>
      nextReviewDateEqualTo(DateTime nextReviewDate) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'nextReviewDate',
        value: [nextReviewDate],
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterWhereClause>
      nextReviewDateNotEqualTo(DateTime nextReviewDate) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'nextReviewDate',
              lower: [],
              upper: [nextReviewDate],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'nextReviewDate',
              lower: [nextReviewDate],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'nextReviewDate',
              lower: [nextReviewDate],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'nextReviewDate',
              lower: [],
              upper: [nextReviewDate],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterWhereClause>
      nextReviewDateGreaterThan(
    DateTime nextReviewDate, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'nextReviewDate',
        lower: [nextReviewDate],
        includeLower: include,
        upper: [],
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterWhereClause>
      nextReviewDateLessThan(
    DateTime nextReviewDate, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'nextReviewDate',
        lower: [],
        upper: [nextReviewDate],
        includeUpper: include,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterWhereClause>
      nextReviewDateBetween(
    DateTime lowerNextReviewDate,
    DateTime upperNextReviewDate, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'nextReviewDate',
        lower: [lowerNextReviewDate],
        includeLower: includeLower,
        upper: [upperNextReviewDate],
        includeUpper: includeUpper,
      ));
    });
  }
}

extension ReviewItemModelQueryFilter
    on QueryBuilder<ReviewItemModel, ReviewItemModel, QFilterCondition> {
  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      easeFactorEqualTo(
    double value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'easeFactor',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      easeFactorGreaterThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'easeFactor',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      easeFactorLessThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'easeFactor',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      easeFactorBetween(
    double lower,
    double upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'easeFactor',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      firstStudyDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'firstStudyDate',
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      firstStudyDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'firstStudyDate',
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      firstStudyDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'firstStudyDate',
        value: value,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      firstStudyDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'firstStudyDate',
        value: value,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      firstStudyDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'firstStudyDate',
        value: value,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      firstStudyDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'firstStudyDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      isNewWordEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isNewWord',
        value: value,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      lastReviewDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'lastReviewDate',
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      lastReviewDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'lastReviewDate',
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      lastReviewDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'lastReviewDate',
        value: value,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      lastReviewDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'lastReviewDate',
        value: value,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      lastReviewDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'lastReviewDate',
        value: value,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      lastReviewDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'lastReviewDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      nextReviewDateEqualTo(DateTime value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'nextReviewDate',
        value: value,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      nextReviewDateGreaterThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'nextReviewDate',
        value: value,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      nextReviewDateLessThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'nextReviewDate',
        value: value,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      nextReviewDateBetween(
    DateTime lower,
    DateTime upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'nextReviewDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      previousIntervalEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'previousInterval',
        value: value,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      previousIntervalGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'previousInterval',
        value: value,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      previousIntervalLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'previousInterval',
        value: value,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      previousIntervalBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'previousInterval',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      repetitionCountEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'repetitionCount',
        value: value,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      repetitionCountGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'repetitionCount',
        value: value,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      repetitionCountLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'repetitionCount',
        value: value,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      repetitionCountBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'repetitionCount',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      totalReviewCountEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'totalReviewCount',
        value: value,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      totalReviewCountGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'totalReviewCount',
        value: value,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      totalReviewCountLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'totalReviewCount',
        value: value,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      totalReviewCountBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'totalReviewCount',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      userWordBookIdEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'userWordBookId',
        value: value,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      userWordBookIdGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'userWordBookId',
        value: value,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      userWordBookIdLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'userWordBookId',
        value: value,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      userWordBookIdBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'userWordBookId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      wordIdEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'wordId',
        value: value,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      wordIdGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'wordId',
        value: value,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      wordIdLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'wordId',
        value: value,
      ));
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      wordIdBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'wordId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension ReviewItemModelQueryObject
    on QueryBuilder<ReviewItemModel, ReviewItemModel, QFilterCondition> {}

extension ReviewItemModelQueryLinks
    on QueryBuilder<ReviewItemModel, ReviewItemModel, QFilterCondition> {
  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition> word(
      FilterQuery<WordModel> q) {
    return QueryBuilder.apply(this, (query) {
      return query.link(q, r'word');
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      wordIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'word', 0, true, 0, true);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      userWordBook(FilterQuery<UserWordBookModel> q) {
    return QueryBuilder.apply(this, (query) {
      return query.link(q, r'userWordBook');
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterFilterCondition>
      userWordBookIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'userWordBook', 0, true, 0, true);
    });
  }
}

extension ReviewItemModelQuerySortBy
    on QueryBuilder<ReviewItemModel, ReviewItemModel, QSortBy> {
  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      sortByEaseFactor() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'easeFactor', Sort.asc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      sortByEaseFactorDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'easeFactor', Sort.desc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      sortByFirstStudyDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'firstStudyDate', Sort.asc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      sortByFirstStudyDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'firstStudyDate', Sort.desc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      sortByIsNewWord() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isNewWord', Sort.asc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      sortByIsNewWordDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isNewWord', Sort.desc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      sortByLastReviewDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastReviewDate', Sort.asc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      sortByLastReviewDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastReviewDate', Sort.desc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      sortByNextReviewDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'nextReviewDate', Sort.asc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      sortByNextReviewDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'nextReviewDate', Sort.desc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      sortByPreviousInterval() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'previousInterval', Sort.asc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      sortByPreviousIntervalDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'previousInterval', Sort.desc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      sortByRepetitionCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'repetitionCount', Sort.asc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      sortByRepetitionCountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'repetitionCount', Sort.desc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      sortByTotalReviewCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalReviewCount', Sort.asc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      sortByTotalReviewCountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalReviewCount', Sort.desc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      sortByUserWordBookId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userWordBookId', Sort.asc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      sortByUserWordBookIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userWordBookId', Sort.desc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy> sortByWordId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'wordId', Sort.asc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      sortByWordIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'wordId', Sort.desc);
    });
  }
}

extension ReviewItemModelQuerySortThenBy
    on QueryBuilder<ReviewItemModel, ReviewItemModel, QSortThenBy> {
  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      thenByEaseFactor() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'easeFactor', Sort.asc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      thenByEaseFactorDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'easeFactor', Sort.desc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      thenByFirstStudyDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'firstStudyDate', Sort.asc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      thenByFirstStudyDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'firstStudyDate', Sort.desc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy> thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      thenByIsNewWord() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isNewWord', Sort.asc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      thenByIsNewWordDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isNewWord', Sort.desc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      thenByLastReviewDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastReviewDate', Sort.asc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      thenByLastReviewDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastReviewDate', Sort.desc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      thenByNextReviewDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'nextReviewDate', Sort.asc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      thenByNextReviewDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'nextReviewDate', Sort.desc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      thenByPreviousInterval() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'previousInterval', Sort.asc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      thenByPreviousIntervalDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'previousInterval', Sort.desc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      thenByRepetitionCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'repetitionCount', Sort.asc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      thenByRepetitionCountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'repetitionCount', Sort.desc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      thenByTotalReviewCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalReviewCount', Sort.asc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      thenByTotalReviewCountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalReviewCount', Sort.desc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      thenByUserWordBookId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userWordBookId', Sort.asc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      thenByUserWordBookIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userWordBookId', Sort.desc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy> thenByWordId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'wordId', Sort.asc);
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QAfterSortBy>
      thenByWordIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'wordId', Sort.desc);
    });
  }
}

extension ReviewItemModelQueryWhereDistinct
    on QueryBuilder<ReviewItemModel, ReviewItemModel, QDistinct> {
  QueryBuilder<ReviewItemModel, ReviewItemModel, QDistinct>
      distinctByEaseFactor() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'easeFactor');
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QDistinct>
      distinctByFirstStudyDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'firstStudyDate');
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QDistinct>
      distinctByIsNewWord() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isNewWord');
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QDistinct>
      distinctByLastReviewDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'lastReviewDate');
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QDistinct>
      distinctByNextReviewDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'nextReviewDate');
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QDistinct>
      distinctByPreviousInterval() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'previousInterval');
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QDistinct>
      distinctByRepetitionCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'repetitionCount');
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QDistinct>
      distinctByTotalReviewCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'totalReviewCount');
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QDistinct>
      distinctByUserWordBookId() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'userWordBookId');
    });
  }

  QueryBuilder<ReviewItemModel, ReviewItemModel, QDistinct> distinctByWordId() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'wordId');
    });
  }
}

extension ReviewItemModelQueryProperty
    on QueryBuilder<ReviewItemModel, ReviewItemModel, QQueryProperty> {
  QueryBuilder<ReviewItemModel, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<ReviewItemModel, double, QQueryOperations> easeFactorProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'easeFactor');
    });
  }

  QueryBuilder<ReviewItemModel, DateTime?, QQueryOperations>
      firstStudyDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'firstStudyDate');
    });
  }

  QueryBuilder<ReviewItemModel, bool, QQueryOperations> isNewWordProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isNewWord');
    });
  }

  QueryBuilder<ReviewItemModel, DateTime?, QQueryOperations>
      lastReviewDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'lastReviewDate');
    });
  }

  QueryBuilder<ReviewItemModel, DateTime, QQueryOperations>
      nextReviewDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'nextReviewDate');
    });
  }

  QueryBuilder<ReviewItemModel, int, QQueryOperations>
      previousIntervalProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'previousInterval');
    });
  }

  QueryBuilder<ReviewItemModel, int, QQueryOperations>
      repetitionCountProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'repetitionCount');
    });
  }

  QueryBuilder<ReviewItemModel, int, QQueryOperations>
      totalReviewCountProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'totalReviewCount');
    });
  }

  QueryBuilder<ReviewItemModel, int, QQueryOperations>
      userWordBookIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'userWordBookId');
    });
  }

  QueryBuilder<ReviewItemModel, int, QQueryOperations> wordIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'wordId');
    });
  }
}
