import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/data/models/quiz/choice_quiz_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/quiz/quiz_type.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/quiz/controllers/quiz_controller.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/quiz/widgets/sentence_widget.dart';

/// 无状态选择题组件
///
/// 所有状态由QuizController管理，Widget只负责UI展示
class ChoiceQuizWidgetStateless extends StatelessWidget {
  /// 选择题模型
  final ChoiceQuizModel quiz;

  /// Quiz控制器
  final QuizController controller;

  const ChoiceQuizWidgetStateless({
    super.key,
    required this.quiz,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 题干部分
          _buildQuestionWidget(),
          const SizedBox(height: 24),
          // 选项列表
          ..._buildOptions(),
          const SizedBox(height: 16),
          // 答题后的解析
          Obx(() {
            if (controller.hasAnswered && quiz.explanation != null) {
              return _buildExplanation();
            }
            return const SizedBox.shrink();
          }),
          // 答题后显示结果反馈
          Obx(() {
            if (controller.hasAnswered) {
              return _buildResult();
            }
            return const SizedBox.shrink();
          }),
          const Spacer(),
        ],
      ),
    );
  }

  /// 构建题干部分
  Widget _buildQuestionWidget() {
    switch (quiz.type) {
      case QuizType.englishToChinese:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '请选择下列英文单词的中文含义：',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              quiz.question,
              style: const TextStyle(fontSize: 28, fontWeight: FontWeight.bold),
            ),
          ],
        );

      case QuizType.chineseToEnglish:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '请选择下列中文的英文单词：',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              quiz.question,
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
          ],
        );

      case QuizType.audioToMeaning:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '请听音频并选择正确的中文含义：',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            // 这里可以添加音频播放组件
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.volume_up, color: Colors.blue[700]),
                  const SizedBox(width: 8),
                  Text(
                    '点击播放: ${quiz.question}',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.blue[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        );

      case QuizType.sentenceCompletion:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '请选择适合填入句子空白处的单词：',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            SentenceWidget(sentence: quiz.question),
          ],
        );

      default:
        return Text(
          quiz.question,
          style: const TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
        );
    }
  }

  /// 构建选项列表
  List<Widget> _buildOptions() {
    return List.generate(
      quiz.options.length,
      (index) => Padding(
        padding: const EdgeInsets.only(bottom: 12),
        child: Obx(() {
          return InkWell(
            onTap:
                controller.hasAnswered
                    ? null
                    : () => controller.onChoiceQuizAnswered(index),
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: _getOptionBackgroundColor(index),
                border: Border.all(color: _getOptionBorderColor(index)),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  // 选项字母
                  Container(
                    width: 32,
                    height: 32,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: _getOptionBorderColor(index)),
                    ),
                    child: Text(
                      String.fromCharCode(65 + index), // A, B, C, D...
                      style: TextStyle(
                        color: _getOptionTextColor(index),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  // 选项内容
                  Expanded(
                    child: Text(
                      quiz.options[index],
                      style: TextStyle(
                        color: _getOptionTextColor(index),
                        fontSize: 16,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }

  /// 构建解析部分
  Widget _buildExplanation() {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue, width: 1),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(Icons.lightbulb_outline, color: Colors.blue[700], size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              quiz.explanation!,
              style: TextStyle(
                color: Colors.blue[700],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建答题结果反馈
  Widget _buildResult() {
    return Obx(() {
      if (!controller.hasAnswered || controller.selectedOptionIndex == null) {
        return const SizedBox();
      }

      final isCorrect =
          controller.selectedOptionIndex == quiz.correctOptionIndex;

      return Container(
        margin: const EdgeInsets.only(top: 16),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color:
              isCorrect
                  ? Colors.green.withValues(alpha: 0.1)
                  : Colors.red.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isCorrect ? Colors.green : Colors.red,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              isCorrect ? Icons.check_circle : Icons.cancel,
              color: isCorrect ? Colors.green : Colors.red,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              isCorrect ? '回答正确！' : '回答错误',
              style: TextStyle(
                color: isCorrect ? Colors.green : Colors.red,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    });
  }

  // ==================== UI逻辑方法 ====================

  /// 获取选项背景色
  Color _getOptionBackgroundColor(int index) {
    if (!controller.hasAnswered) {
      // 答题前
      return controller.selectedOptionIndex == index
          ? Colors.blue.withValues(alpha: 0.1)
          : Colors.grey.withValues(alpha: 0.05);
    } else {
      // 答题后
      if (index == quiz.correctOptionIndex) {
        return Colors.green.withValues(alpha: 0.1);
      } else if (index == controller.selectedOptionIndex) {
        return Colors.red.withValues(alpha: 0.1);
      }
      return Colors.grey.withValues(alpha: 0.05);
    }
  }

  /// 获取选项边框色
  Color _getOptionBorderColor(int index) {
    if (!controller.hasAnswered) {
      // 答题前
      return controller.selectedOptionIndex == index
          ? Colors.blue
          : Colors.grey.withValues(alpha: 0.3);
    } else {
      // 答题后
      if (index == quiz.correctOptionIndex) {
        return Colors.green;
      } else if (index == controller.selectedOptionIndex) {
        return Colors.red;
      }
      return Colors.grey.withValues(alpha: 0.3);
    }
  }

  /// 获取选项文本色
  Color _getOptionTextColor(int index) {
    if (!controller.hasAnswered) {
      return Colors.black87;
    } else {
      // 答题后
      if (index == quiz.correctOptionIndex) {
        return Colors.green;
      } else if (index == controller.selectedOptionIndex) {
        return Colors.red;
      }
      return Colors.black87;
    }
  }
}
