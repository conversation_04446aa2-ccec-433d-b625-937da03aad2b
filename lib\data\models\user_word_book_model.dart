// ignore_for_file: constant_identifier_names

import 'package:isar/isar.dart';
import 'word_book_model.dart';
import 'review_item_model.dart';
import 'learning_session.dart';

part 'user_word_book_model.g.dart';

/// 用户单词本关系模型
/// 存储用户与单词本之间的关系，包括学习状态、学习计划、统计信息等
///
/// 该模型是连接用户学习行为与单词本内容的桥梁，记录了用户对特定单词本的
/// 学习状态、进度和设置。一个单词本可以被多个用户使用，每个用户对同一单词本
/// 有自己独立的学习状态和进度。
@Collection()
class UserWordBookModel {
  /// Isar数据库主键，自动递增
  Id id = Isar.autoIncrement;

  /// 关联的本地单词本ID
  ///
  /// 重要说明：
  /// 1. 此字段存储的是本地数据库中WordBookModel的ID，而非远程后端ID
  /// 2. 仅用于建立本地数据关系，不应用于与远程数据的比较
  late int wordBookId;

  /// 关联的远程单词本ID
  ///
  /// 重要说明：
  /// 1. 此字段存储远程后端系统中的词书ID
  /// 2. 用于与远程API交互和识别词书
  /// 3. 在比较远程词书是否已下载时，应使用此字段
  late int remoteWordBookId;

  /// 关联的单词本
  ///
  /// 使用IsarLink建立与WordBookModel的一对一关联，
  /// 通过这个关联可以获取单词本的基本信息，如名称、描述、总单词数等
  final wordBook = IsarLink<WordBookModel>();

  /// 与该用户单词本关联的所有复习项
  ///
  /// 使用IsarLinks建立与ReviewItemModel的一对多关联，
  /// 通过这个关联可以直接获取该单词本中所有的复习项，
  /// 便于查询待复习单词、统计学习进度等
  ///
  /// 示例用法：
  /// ```dart
  /// // 获取所有待复习的单词
  /// final dueItems = userWordBook.reviewItems
  ///     .where((item) => item.nextReviewDate.isBefore(DateTime.now()))
  ///     .toList();
  /// ```
  final reviewItems = IsarLinks<ReviewItemModel>();

  /// 与该用户单词本关联的所有学习会话
  ///
  /// 使用IsarLinks建立与LearningSession的一对多关联，
  /// 通过这个关联可以直接获取用户在该单词本上的所有学习记录，
  /// 便于生成学习统计、分析学习模式等
  ///
  /// 示例用法：
  /// ```dart
  /// // 计算总学习时间
  /// final totalStudyTime = userWordBook.learningSessions.fold(
  ///     Duration.zero,
  ///     (total, session) => total + (session.endTime?.difference(session.startTime) ?? Duration.zero)
  /// );
  /// ```
  final learningSessions = IsarLinks<LearningSession>();

  /// 是否当前激活（正在学习/复习的单词本）
  bool isActive = false;

  /// 是否已完成（所有单词都学过一遍）
  bool isCompleted = false;

  /// 每日学习新词数量
  int dailyNewWordsCount = 10;

  /// 最后学习日期
  DateTime? lastLearningDate;

  /// 单词本完成时间
  DateTime? completedAt;

  /// 已学单词数
  int totalLearnedWords = 0;

  /// 单词本总单词数（缓存，避免频繁查询）
  int totalWords = 0;

  /// 添加到书桌的时间
  late DateTime addedDate;

  /// 构造函数
  UserWordBookModel({
    required this.wordBookId,
    this.isActive = false,
    this.isCompleted = false,
    this.dailyNewWordsCount = 10,
    this.lastLearningDate,
    this.completedAt,
    this.totalLearnedWords = 0,
    required this.totalWords,
    required this.remoteWordBookId,
  }) {
    addedDate = DateTime.now();
  }

  /// 从JSON创建实例
  factory UserWordBookModel.fromJson(Map<String, dynamic> json) {
    try {
      final model = UserWordBookModel(
        wordBookId: json['wordBookId'] as int,
        isActive: json['isActive'] as bool? ?? false,
        isCompleted: json['isCompleted'] as bool? ?? false,
        dailyNewWordsCount: json['dailyNewWordsCount'] as int? ?? 10,
        lastLearningDate:
            json['lastLearningDate'] != null
                ? DateTime.parse(json['lastLearningDate'])
                : null,
        completedAt:
            json['completedAt'] != null
                ? DateTime.parse(json['completedAt'])
                : null,
        totalLearnedWords: json['totalLearnedWords'] as int? ?? 0,
        totalWords: json['totalWords'] as int? ?? 0,
        remoteWordBookId:
            json['remoteWordBookId'] as int? ?? json['wordBookId'] as int,
      );

      if (json['addedDate'] != null) {
        model.addedDate = DateTime.parse(json['addedDate']);
      }

      if (json['id'] != null) {
        model.id = json['id'];
      }

      return model;
    } catch (e) {
      throw FormatException('Invalid JSON format for UserWordBookModel: $e');
    }
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'wordBookId': wordBookId,
      'remoteWordBookId': remoteWordBookId,
      'isActive': isActive,
      'isCompleted': isCompleted,
      'dailyNewWordsCount': dailyNewWordsCount,
      'lastLearningDate': lastLearningDate?.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'totalLearnedWords': totalLearnedWords,
      'totalWords': totalWords,
      'addedDate': addedDate.toIso8601String(),
    };
  }

  /// 复制当前实例并允许修改部分字段
  UserWordBookModel copyWith({
    int? id,
    int? wordBookId,
    int? remoteWordBookId,
    bool? isActive,
    bool? isCompleted,
    int? dailyNewWordsCount,
    DateTime? lastLearningDate,
    DateTime? completedAt,
    int? totalLearnedWords,
    int? totalWords,
  }) {
    final model = UserWordBookModel(
      wordBookId: wordBookId ?? this.wordBookId,
      isActive: isActive ?? this.isActive,
      isCompleted: isCompleted ?? this.isCompleted,
      dailyNewWordsCount: dailyNewWordsCount ?? this.dailyNewWordsCount,
      lastLearningDate: lastLearningDate ?? this.lastLearningDate,
      completedAt: completedAt ?? this.completedAt,
      totalLearnedWords: totalLearnedWords ?? this.totalLearnedWords,
      totalWords: totalWords ?? this.totalWords,
      remoteWordBookId: remoteWordBookId ?? this.remoteWordBookId,
    );

    model.addedDate = addedDate;

    if (id != null) {
      model.id = id;
    } else {
      model.id = this.id;
    }

    return model;
  }

  /// 更新学习进度
  ///
  /// 更新已学单词数量并记录最后学习日期
  /// @param learnedCount 已学单词数量
  void updateLearningProgress(int learnedCount) {
    if (learnedCount >= 0) {
      totalLearnedWords = learnedCount;
      lastLearningDate = DateTime.now();
    }
  }

  /// 激活单词本
  ///
  /// 将当前单词本状态设置为激活状态
  /// 注意：调用此方法前应确保其他单词本已被停用，
  /// 以保证全局只有一个激活的单词本
  void activate() {
    isActive = true;
  }

  /// 停用单词本
  ///
  /// 将当前单词本状态设置为已下载但未激活状态
  void deactivate() {
    isActive = false;
  }

  /// 获取学习完成率
  double get completionRate =>
      totalWords > 0 ? totalLearnedWords / totalWords : 0.0;

  /// 标记单词本为已完成
  void markAsCompleted() {
    isCompleted = true;
    completedAt = DateTime.now();
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserWordBookModel &&
        other.id == id &&
        other.wordBookId == wordBookId &&
        other.remoteWordBookId == remoteWordBookId &&
        other.isActive == isActive &&
        other.isCompleted == isCompleted &&
        other.dailyNewWordsCount == dailyNewWordsCount &&
        other.lastLearningDate == lastLearningDate &&
        other.completedAt == completedAt &&
        other.totalLearnedWords == totalLearnedWords &&
        other.totalWords == totalWords &&
        other.addedDate == addedDate;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      wordBookId,
      remoteWordBookId,
      isActive,
      isCompleted,
      dailyNewWordsCount,
      lastLearningDate,
      completedAt,
      totalLearnedWords,
      totalWords,
      addedDate,
    );
  }

  @override
  String toString() {
    return 'UserWordBookModel(id: $id, wordBookId: $wordBookId, remoteWordBookId: $remoteWordBookId, isActive: $isActive, isCompleted: $isCompleted, totalLearnedWords: $totalLearnedWords, totalWords: $totalWords)';
  }
}
