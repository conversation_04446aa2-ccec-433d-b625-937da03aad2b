import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/app/services/user_word_book_service.dart';
import 'package:flutter_study_flow_by_myself/data/models/review_item_model.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/review_item_repository.dart';

/// 复习计划全局状态服务
///
/// 职责：
/// - 管理全局的复习计划状态
/// - 提供复习数据给其他页面监听
/// - 响应单词本变化自动刷新复习数据
///
/// 使用场景：
/// - 首页监听待复习数量
/// - 复习计划页面获取复习数据
/// - 学习完成后刷新复习状态
class ReviewPlanService extends GetxService {
  final ReviewItemRepository _repository;
  final UserWordBookService _userWordBookService;
  final LogService _log;

  ReviewPlanService({
    required ReviewItemRepository repository,
    required UserWordBookService userWordBookService,
    required LogService log,
  }) : _repository = repository,
       _userWordBookService = userWordBookService,
       _log = log;

  // ==================== 全局状态 ====================
  /// 所有复习项列表
  final RxList<ReviewItemModel> reviewItems = <ReviewItemModel>[].obs;

  /// 待复习数量（今日到期）
  final RxInt dueReviewCount = 0.obs;

  /// 即将到期数量（明日到期）
  final RxInt upcomingReviewCount = 0.obs;

  /// 新词数量（从未复习过）
  final RxInt newWordsCount = 0.obs;

  /// 加载状态
  final RxBool isLoading = false.obs;

  /// 错误信息
  final RxString errorMessage = ''.obs;

  // ==================== 服务初始化 ====================
  @override
  Future<void> onInit() async {
    super.onInit();
    _log.i('ReviewPlanService 初始化');

    // 监听激活单词本变化，自动刷新复习数据
    _setupActiveWordBookListener();

    // 加载初始数据
    await loadReviewPlan();
  }

  /// 设置激活单词本变化监听
  void _setupActiveWordBookListener() {
    ever(_userWordBookService.activeWordBook, (activeWordBook) {
      _log.i('检测到激活单词本变化，自动刷新复习计划');
      loadReviewPlan();
    });
  }

  // ==================== 核心业务方法 ====================
  /// 加载复习计划数据
  ///
  /// 🔑 重要说明：
  /// - 使用getReviewItemsByUserWordBook()获取所有复习项（包括新词）供页面显示和统计
  /// - 使用getDueReviewItemsForUserWordBook()获取真正待复习的项目进行准确计数
  /// - 这样既保证页面显示完整，又确保待复习数量统计正确
  Future<void> loadReviewPlan() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      // 获取当前激活的单词本
      final activeWordBook = _userWordBookService.activeWordBook.value;
      if (activeWordBook == null) {
        _log.i('没有激活的单词本，清空复习数据');
        _clearReviewData();
        return;
      }

      _log.i('加载复习计划，单词本ID: ${activeWordBook.id}');

      // 🔑 关键修复：分别获取不同用途的数据

      // 1. 获取所有复习项（包括新词）- 用于页面显示、过滤、排序等
      final allItems = await _repository.getReviewItemsByUserWordBook(
        activeWordBook.id,
      );

      // 2. 获取真正待复习的项目（已学习且到期）- 用于准确的待复习数量统计
      final now = DateTime.now();
      final dueItems = await _repository.getDueReviewItemsForUserWordBook(
        activeWordBook.id,
        now,
      );

      // 更新全局状态
      reviewItems.assignAll(allItems); // 页面显示使用完整数据
      _updateReviewCountsWithCorrectData(allItems, dueItems); // 使用正确数据进行统计

      _log.i('复习计划加载完成 - 总数: ${allItems.length}, 待复习: ${dueItems.length}');
    } catch (e) {
      _log.e('加载复习计划失败', error: e);
      errorMessage.value = '加载复习计划失败: ${e.toString()}';
      _clearReviewData();
    } finally {
      isLoading.value = false;
    }
  }

  /// 刷新复习计划数据
  ///
  /// 供外部调用，比如学习完成后刷新复习状态
  Future<void> refreshReviewPlan() async {
    _log.i('刷新复习计划数据');
    await loadReviewPlan();
  }

  /// 更新复习项状态
  ///
  /// 当用户完成复习后调用
  Future<void> updateReviewItem(int reviewItemId) async {
    try {
      _log.i('更新复习项状态: $reviewItemId');

      // 这里可以添加具体的更新逻辑
      // 比如更新复习次数、下次复习时间等

      // 更新完成后刷新数据
      await refreshReviewPlan();

      _log.i('复习项状态更新完成: $reviewItemId');
    } catch (e) {
      _log.e('更新复习项状态失败', error: e);
      rethrow;
    }
  }

  // ==================== 私有辅助方法 ====================
  /// 使用正确数据更新复习数量统计
  ///
  /// 🔑 重要修复：
  /// - allItems: 用于统计新词数量和总数（包含所有复习项）
  /// - dueItems: 用于统计真正的待复习数量（已过滤新词）
  /// - 这样避免了将新词错误计算为待复习的bug
  ///
  /// [allItems] 所有复习项（包括新词），用于完整统计
  /// [dueItems] 待复习项（已过滤新词），用于准确的待复习统计
  void _updateReviewCountsWithCorrectData(
    List<ReviewItemModel> allItems,
    List<ReviewItemModel> dueItems,
  ) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));

    // 🔑 关键修复：直接使用已过滤的dueItems计算待复习数量
    // 不再从allItems中计算，避免将新词误算为待复习
    final dueCount = dueItems.length;

    // 计算即将到期数量（明日到期）- 从dueItems中筛选明日到期的
    final upcomingCount =
        dueItems.where((item) {
          final reviewDate = DateTime(
            item.nextReviewDate.year,
            item.nextReviewDate.month,
            item.nextReviewDate.day,
          );
          return reviewDate.isAtSameMomentAs(tomorrow);
        }).length;

    // 计算新词数量（从未复习过）- 从allItems中统计新词
    final newCount = allItems.where((item) => item.isNewWord).length;

    // 更新状态
    dueReviewCount.value = dueCount;
    upcomingReviewCount.value = upcomingCount;
    newWordsCount.value = newCount;

    _log.i('复习数量统计（修复后）- 待复习: $dueCount, 即将到期: $upcomingCount, 新词: $newCount');
  }

  /// 清空复习数据
  void _clearReviewData() {
    reviewItems.clear();
    dueReviewCount.value = 0;
    upcomingReviewCount.value = 0;
    newWordsCount.value = 0;
  }

  // ==================== 便捷方法 ====================
  /// 获取今日待复习的项目
  List<ReviewItemModel> get todayDueItems {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));

    return reviewItems.where((item) {
      final reviewDate = DateTime(
        item.nextReviewDate.year,
        item.nextReviewDate.month,
        item.nextReviewDate.day,
      );
      return reviewDate.isBefore(tomorrow);
    }).toList();
  }

  // ==================== 便捷的Getter方法 ====================
  /// 获取新词项目
  ///
  /// 🔑 说明：从reviewItems中筛选新词，用于页面显示和统计
  List<ReviewItemModel> get newWordItems {
    return reviewItems.where((item) => item.isNewWord).toList();
  }

  /// 获取已掌握的项目
  ///
  /// 🔑 说明：从reviewItems中筛选已掌握的词（复习次数>=5），用于统计分析
  List<ReviewItemModel> get masteredItems {
    return reviewItems.where((item) => item.totalReviewCount >= 5).toList();
  }

  /// 获取真正待复习的项目
  ///
  /// 🔑 说明：从reviewItems中筛选真正需要复习的词（非新词且到期）
  /// 注意：这个方法用于页面显示，实际的待复习数量统计使用dueReviewCount
  List<ReviewItemModel> get actualDueItems {
    final now = DateTime.now();
    return reviewItems
        .where((item) => !item.isNewWord && item.nextReviewDate.isBefore(now))
        .toList();
  }
}
