# 应用架构分层设计

## 📋 目录结构

```
lib/app/
├── controllers/          # 页面控制器（UI状态管理）
├── services/            # 业务服务（无状态功能提供者）
├── managers/            # 全局状态管理器 ⭐
├── config/              # 配置文件
└── bindings/            # 依赖注入绑定
```

## 🏗️ 架构分层

### **1. Controllers（控制器层）**

**职责**：管理特定页面/组件的UI状态和用户交互

**特点**：
- 与页面生命周期绑定
- 管理UI相关的响应式状态
- 处理用户交互事件
- 调用业务服务和状态管理器

**命名规范**：`{PageName}Controller`

**示例**：
```dart
class HomeController extends GetxController {
  // UI状态
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  
  // 用户交互处理
  void onButtonPressed() { /* ... */ }
  void onPageChanged() { /* ... */ }
}
```

### **2. Services（服务层）**

**职责**：提供无状态的业务功能和基础设施服务

**特点**：
- 无状态（不保存UI相关状态）
- 提供纯业务逻辑或基础功能
- 可被多个控制器复用
- 全局单例（permanent: true）

**命名规范**：`{Function}Service`

**分类**：
- **基础设施服务**：`NetworkService`、`AudioService`、`StorageService`
- **算法服务**：`SpacedRepetitionService`
- **工具服务**：`LogService`

**示例**：
```dart
class SpacedRepetitionService {
  // 纯业务逻辑，无UI状态
  double calculateNextInterval(int repetitions, double easeFactor) { /* ... */ }
  DateTime getNextReviewDate(ReviewItem item) { /* ... */ }
}
```

### **3. Managers（状态管理器层）** ⭐

**职责**：管理全局应用状态，确保跨页面状态同步

**特点**：
- 管理全局状态（如当前激活的用户单词本）
- 提供状态同步接口
- 使用响应式编程自动通知状态变化
- 全局单例（permanent: true）

**命名规范**：`{Domain}StateManager` 或 `{Domain}Manager`

**示例**：
```dart
class UserWordBookStateManager extends GetxController {
  // 全局状态
  final Rx<UserWordBookModel?> activeUserWordBook = Rx(null);
  
  // 状态同步方法
  Future<void> syncActiveUserWordBook() async { /* ... */ }
}
```

## 🔄 交互模式

### **状态同步流程**

```
用户操作 → Controller → Repository → Manager.sync() → 其他Controller自动更新
```

**具体示例**：
```
用户停用单词本 → VocabularyController.deactivateWordBook() 
→ UserWordBookRepository.deactivateUserWordBook() 
→ UserWordBookStateManager.syncActiveUserWordBook() 
→ HomeController自动收到通知并更新UI
```

### **依赖关系**

```
Controllers (页面级)
    ↓ 调用
Managers (全局状态)
    ↓ 调用  
Services (业务功能)
    ↓ 调用
Repositories (数据访问)
```

## 📝 使用指南

### **在Controller中监听全局状态**

```dart
class HomeController extends GetxController {
  @override
  void onInit() {
    super.onInit();
    
    final manager = Get.find<UserWordBookStateManager>();
    
    // 监听全局状态变化
    ever(manager.activeUserWordBook, (userWordBook) {
      _handleUserWordBookChanged(userWordBook);
    });
  }
  
  void _handleUserWordBookChanged(UserWordBookModel? userWordBook) {
    if (userWordBook == null) {
      _resetToNoWordBookState();
    } else {
      _loadWordBookRelatedData(userWordBook);
    }
  }
}
```

### **在业务操作后同步状态**

```dart
class VocabularyController extends GetxController {
  Future<void> activateWordBook(int userWordBookId) async {
    // 1. 执行业务操作
    await _userWordBookRepository.activateUserWordBook(userWordBookId);
    
    // 2. 更新本页面状态
    await loadWordBooks();
    
    // 3. 同步全局状态（其他页面会自动更新）
    await Get.find<UserWordBookStateManager>().syncActiveUserWordBook();
    
    // 4. 用户反馈
    Get.snackbar('成功', '词书已激活');
  }
}
```

## 🎯 设计原则

### **单一职责原则**
- **Controller**：只管理UI状态和用户交互
- **Service**：只提供业务功能，不保存状态
- **Manager**：只管理全局状态，不处理UI逻辑

### **依赖注入原则**
- 所有依赖通过构造函数注入
- 在AppInitializer中统一注册
- 明确的依赖关系和注册顺序

### **响应式编程原则**
- 使用Rx实现状态变化的自动通知
- 避免手动调用更新方法
- 确保状态变化的一致性

## 🚀 扩展指南

### **添加新的Manager**

1. 创建Manager类：`lib/app/managers/new_state_manager.dart`
2. 在AppInitializer中注册：`_registerGlobalManagers()`
3. 在需要的Controller中监听状态变化

### **添加新的Service**

1. 创建Service类：`lib/app/services/new_service.dart`
2. 在AppInitializer中注册：`_registerAlgorithmServices()`
3. 在需要的地方通过依赖注入使用

这种架构设计确保了代码的可维护性、可扩展性和一致性。
