# themes 目录说明文档

## 目录职责

`themes` 目录负责定义和管理应用程序的主题相关配置，包括颜色方案、文本样式、组件主题等。这个目录的设计目的是将应用的视觉风格集中管理，确保整个应用的UI风格统一一致，同时支持多主题切换（如亮色/暗色模式）及动态主题更新。

## 文件结构

```
themes/
├── app_theme.dart             # 主题管理的主文件，导出所有主题相关内容
├── app_colors.dart            # 定义应用颜色方案
├── app_text_styles.dart       # 定义文本样式
├── app_button_styles.dart     # 定义按钮样式
├── light_theme.dart           # 亮色主题配置
└── dark_theme.dart            # 暗色主题配置
```

## 主要功能

### 主题定义

```dart
// app_theme.dart
import 'package:flutter/material.dart';
import 'light_theme.dart';
import 'dark_theme.dart';

class AppTheme {
  // 获取当前主题
  static ThemeData getTheme(bool isDarkMode) {
    return isDarkMode ? darkTheme : lightTheme;
  }
  
  // 其他主题相关方法...
}
```

### 颜色定义

```dart
// app_colors.dart
import 'package:flutter/material.dart';

class AppColors {
  // 主色调
  static const Color primaryLight = Color(0xFF2196F3);
  static const Color primaryDark = Color(0xFF1976D2);
  
  // 背景色
  static const Color backgroundLight = Color(0xFFF5F5F5);
  static const Color backgroundDark = Color(0xFF121212);
  
  // 文本颜色
  static const Color textLight = Color(0xFF212121);
  static const Color textDark = Color(0xFFEEEEEE);
  
  // 其他颜色...
}
```

### 文本样式定义

```dart
// app_text_styles.dart
import 'package:flutter/material.dart';
import 'app_colors.dart';

class AppTextStyles {
  // 标题样式
  static TextStyle headline1(bool isDarkMode) {
    return TextStyle(
      fontSize: 24,
      fontWeight: FontWeight.bold,
      color: isDarkMode ? AppColors.textDark : AppColors.textLight,
    );
  }
  
  // 正文样式
  static TextStyle bodyText(bool isDarkMode) {
    return TextStyle(
      fontSize: 16,
      color: isDarkMode ? AppColors.textDark : AppColors.textLight,
    );
  }
  
  // 其他文本样式...
}
```

### 主题配置

```dart
// light_theme.dart
import 'package:flutter/material.dart';
import 'app_colors.dart';

final ThemeData lightTheme = ThemeData(
  brightness: Brightness.light,
  primaryColor: AppColors.primaryLight,
  scaffoldBackgroundColor: AppColors.backgroundLight,
  // 其他主题配置...
);

// dark_theme.dart 结构类似，但使用暗色值
```

## 使用方式

### 主题设置

在应用的根部（如 `main.dart`）设置主题：

```dart
import 'package:flutter/material.dart';
import 'app/global/themes/app_theme.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    bool isDarkMode = false; // 通常从状态管理获取
    
    return MaterialApp(
      title: '单词学习',
      theme: AppTheme.getTheme(isDarkMode),
      // 其他配置...
    );
  }
}
```

### 组件中使用主题

```dart
import 'package:flutter/material.dart';
import '../app/global/themes/app_colors.dart';
import '../app/global/themes/app_text_styles.dart';

class MyComponent extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final bool isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      color: Theme.of(context).primaryColor,
      child: Text(
        '示例文本',
        style: AppTextStyles.bodyText(isDarkMode),
      ),
    );
  }
}
```

## 最佳实践

1. **主题一致性**：
   - 在整个应用中使用主题定义的颜色和样式，避免硬编码颜色值
   - 所有组件都应响应主题变化

2. **主题结构**：
   - 将颜色、文本样式和组件样式分离到不同文件
   - 使用命名规范清晰表达颜色和样式的用途
   - 为复杂组件创建专门的主题文件

3. **主题切换**：
   - 支持运行时无缝切换主题
   - 提供平滑的主题切换动画
   - 保存用户主题偏好

4. **可扩展性**：
   - 设计主题系统以支持未来添加新主题
   - 使用工厂模式或策略模式实现主题管理
   - 考虑支持自定义主题元素

## 与其他目录的关系

- **constants目录**：主题可能使用常量定义的基础值
- **services目录**：可能有专门的ThemeService管理主题状态
- **widgets目录**：组件使用主题定义的样式
- **各业务模块**：遵循全局主题规范，确保视觉一致性

## 扩展计划

- 支持多种预设主题（不仅是亮/暗模式）
- 添加主题编辑器，允许用户自定义主题
- 支持特定页面或组件的主题覆盖
- 实现基于时间或地理位置的自动主题切换 