# Quiz Controllers 目录说明文档

## 目录职责

`quiz/controllers` 目录负责实现学习模块中测验环节的业务逻辑控制，管理测验数据、用户答题行为和测验结果统计。该目录是MVVM架构中的ViewModel层，连接测验数据模型和测验视图。

## 文件结构

```
quiz/controllers/
└── quiz_controller.dart      # 测验控制器，处理测验流程和状态管理
```

## 核心功能

- **测验数据管理**：加载和管理测验题目及选项
- **答题逻辑处理**：检查答案正确性、记录答题结果
- **进度跟踪**：管理测验进度、题目切换
- **计时控制**：实现答题时间限制功能
- **结果统计**：汇总测验结果数据

## 主要文件说明

### quiz_controller.dart

- **职责**：测验流程的主控制器
- **依赖关系**：
  - `LearningController`：同步学习会话状态
  - `WordRepository`：获取单词和测验数据
  - 可能的测验服务类
- **主要方法和属性**：
  - **状态变量**：
    - `questions.obs`：测验题目列表
    - `currentIndex.obs`：当前题目索引
    - `selectedOptionId.obs`：当前选择的选项ID
    - `isLoading.obs`：加载状态标志
    - `score.obs`：当前得分
    - `correctAnswers.obs`：正确答案数量
    - `remainingTime.obs`：答题剩余时间
    - `hasAnswered.obs`：是否已答题
  - **方法**：
    - `init()`：初始化测验数据
    - `selectOption()`：处理选项选择
    - `nextQuestion()`：切换到下一题
    - `finishQuiz()`：完成测验并提交结果
    - `startTimer()`：启动答题计时器
  - **计算属性**：
    - `currentQuestion`：当前题目
    - `progress`：测验进度百分比
    - `isLastQuestion`：是否为最后一题
    - `correctOptionId`：当前题目的正确答案ID

## 运行流程

1. **初始化阶段**：
   - `QuizController` 在用户进入测验页面时初始化
   - 从 `LearningController` 获取当前学习会话信息
   - 根据会话中已学单词生成测验题目集
   - 初始化测验状态变量

2. **测验进行阶段**：
   - 显示当前题目和选项
   - 处理用户选择操作
   - 验证答案并更新得分
   - 自动或手动切换到下一题
   - 更新测验进度和时间

3. **测验结束阶段**：
   - 汇总测验结果数据
   - 将结果提交到 `LearningController`
   - 引导用户进入结果页面

## 状态管理

`QuizController` 使用GetX的响应式状态管理，主要包括：

- **.obs变量**：所有需要UI响应变化的状态都声明为.obs类型
- **依赖注入**：通过Get.find()获取其他控制器和服务
- **生命周期管理**：在onInit()中初始化，在onClose()中清理资源
- **计时器管理**：使用Timer管理答题时间限制

## 与其他模块的关系

- **learning/controllers**：`LearningController`提供学习会话数据，接收测验结果
- **quiz/views**：为测验视图层提供数据和行为支持
- **data/repositories**：从`WordRepository`获取单词和生成测验题目
- **models**：使用各种数据模型如Question、QuizOption等

## 常见混淆点

1. **控制器层级关系**：
   - `QuizController` 是局部控制器，专注于测验流程
   - 不要在此处管理整体学习会话状态，应通过`LearningController`协调

2. **状态更新和UI刷新**：
   - 确保所有需要反映到UI的状态都是.obs类型
   - 状态变化时，自动触发UI更新，无需手动刷新

3. **答案验证逻辑**：
   - 答案验证逻辑应在控制器中实现，而非视图层
   - 验证结果通过状态变量传递给视图层显示

## 最佳实践

- **错误处理**：实现完善的错误处理机制，避免测验中断
- **性能优化**：避免频繁重建大型数据结构
- **测试覆盖**：为关键业务逻辑编写单元测试
- **分离关注点**：将复杂逻辑拆分为多个方法，保持单一职责
- **注释完善**：为复杂算法和业务逻辑添加清晰注释
- **内存管理**：在onClose()中释放资源，防止内存泄漏

## 代码示例

```dart
class QuizController extends GetxController {
  // 依赖项
  final LearningController learningController = Get.find<LearningController>();
  final WordRepository wordRepository = Get.find<WordRepository>();
  
  // 状态变量
  final RxList<Question> questions = <Question>[].obs;
  final RxInt currentIndex = 0.obs;
  final RxString selectedOptionId = ''.obs;
  final RxBool isLoading = true.obs;
  final RxInt score = 0.obs;
  final RxInt correctAnswers = 0.obs;
  final RxInt remainingTime = 30.obs; // 每题30秒
  final RxBool hasAnswered = false.obs;
  final RxString correctOptionId = ''.obs;
  
  Timer? _timer;
  
  // 计算属性
  Question get currentQuestion => questions[currentIndex.value];
  double get progress => questions.isEmpty ? 0 : (currentIndex.value + 1) / questions.length;
  bool get isLastQuestion => currentIndex.value >= questions.length - 1;
  int get totalQuestions => questions.length;
  
  @override
  void onInit() {
    super.onInit();
    generateQuizQuestions();
  }
  
  @override
  void onClose() {
    _timer?.cancel();
    super.onClose();
  }
  
  // 生成测验题目
  Future<void> generateQuizQuestions() async {
    isLoading.value = true;
    try {
      // 获取当前学习会话中的单词
      final learnedWords = [
        ...learningController.session.value.learnedNewWords,
        ...learningController.session.value.learnedReviewWords,
      ];
      
      if (learnedWords.isEmpty) {
        throw Exception('没有可用的单词生成测验题');
      }
      
      // 根据单词生成不同类型的测验题
      final generatedQuestions = await wordRepository.generateQuizQuestions(
        words: learnedWords,
        count: learningController.session.value.settings.quizQuestionCount,
      );
      
      questions.assignAll(generatedQuestions);
      
      // 重置状态
      currentIndex.value = 0;
      score.value = 0;
      correctAnswers.value = 0;
      
      // 设置第一道题的正确答案
      if (questions.isNotEmpty) {
        correctOptionId.value = questions[0].correctOptionId;
        startTimer();
      }
    } catch (e) {
      Get.snackbar('错误', '生成测验题失败: $e');
    } finally {
      isLoading.value = false;
    }
  }
  
  // 选择选项
  void selectOption(String optionId) {
    if (hasAnswered.value) return;
    
    selectedOptionId.value = optionId;
    hasAnswered.value = true;
    _timer?.cancel(); // 停止计时
    
    // 检查答案并更新得分
    final isCorrect = optionId == correctOptionId.value;
    if (isCorrect) {
      correctAnswers.value++;
      
      // 计算得分 (根据剩余时间给予奖励)
      int timeBonus = remainingTime.value > 15 ? 5 : (remainingTime.value > 5 ? 3 : 0);
      score.value += 10 + timeBonus;
    }
    
    // 延迟一段时间后进入下一题
    Future.delayed(Duration(seconds: 2), () {
      if (!isLastQuestion) {
        nextQuestion();
      } else {
        finishQuiz();
      }
    });
  }
  
  // 进入下一题
  void nextQuestion() {
    if (isLastQuestion) return;
    
    // 重置题目状态
    hasAnswered.value = false;
    selectedOptionId.value = '';
    currentIndex.value++;
    correctOptionId.value = questions[currentIndex.value].correctOptionId;
    
    // 重新开始计时
    remainingTime.value = 30;
    startTimer();
  }
  
  // 开始计时
  void startTimer() {
    _timer?.cancel();
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (remainingTime.value > 0) {
        remainingTime.value--;
      } else {
        // 时间用完，自动处理为未答题
        timer.cancel();
        if (!hasAnswered.value) {
          selectOption(''); // 传入空选项表示未选择
        }
      }
    });
  }
  
  // 完成测验
  void finishQuiz() {
    _timer?.cancel();
    
    // 提交测验结果
    learningController.recordQuizResult(
      score: score.value,
      correctAnswers: correctAnswers.value,
      totalQuestions: totalQuestions,
    );
    
    // 引导到结果页面
    learningController.currentStage.value = LearningStage.RESULT;
  }
  
  // 手动跳过当前题目
  void skipQuestion() {
    hasAnswered.value = true;
    _timer?.cancel();
    
    // 延迟后进入下一题或结束
    Future.delayed(Duration(seconds: 1), () {
      if (!isLastQuestion) {
        nextQuestion();
      } else {
        finishQuiz();
      }
    });
  }
}
```

## 相关文件

- **lib/modules/learning/quiz/views/quiz_page.dart**: 测验页面UI实现
- **lib/modules/learning/controllers/learning_controller.dart**: 整体学习会话控制
- **lib/modules/learning/models/question.dart**: 测验题目数据模型
- **lib/data/repositories/word_repository.dart**: 单词数据源

## 常见问题与解决方案

1. **问题**：测验题目重复或质量不高  
   **解决方案**：改进题目生成算法，增加题型多样性，确保每次测验题目选择不重复

2. **问题**：测验性能问题，特别是在题目数量较多时  
   **解决方案**：实现分批加载机制，避免一次性加载所有题目

3. **问题**：答题计时器不准确  
   **解决方案**：使用更精确的计时方法，考虑应用在后台时暂停计时

4. **问题**：状态在页面重建后丢失  
   **解决方案**：确保所有状态都正确使用.obs变量，并正确使用GetX的依赖注入

5. **问题**：测验结果统计不正确  
   **解决方案**：添加详细日志跟踪答题过程，确保得分计算逻辑正确 