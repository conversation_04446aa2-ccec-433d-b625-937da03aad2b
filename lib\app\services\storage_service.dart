import 'package:shared_preferences/shared_preferences.dart';
import 'package:get/get.dart';

/// 本地存储服务类，封装SharedPreferences提供简单的数据存取接口
///
/// 继承GetxService的原因：
/// 1. 管理SharedPreferences资源，需要生命周期管理
/// 2. 作为全局基础服务，在整个应用生命周期中持续存在
/// 3. 便于通过Get.find()在应用任何位置访问
class StorageService extends GetxService {
  late SharedPreferences _prefs;

  /// 异步初始化存储服务
  Future<StorageService> init() async {
    _prefs = await SharedPreferences.getInstance();
    return this;
  }

  // 获取字符串
  String? getString(String key) => _prefs.getString(key);
  // 设置字符串
  Future<bool> setString(String key, String value) async =>
      await _prefs.setString(key, value);
  // 获取int
  int? getInt(String key) => _prefs.getInt(key);
  // 设置int
  Future<bool> setInt(String key, int value) async =>
      await _prefs.setInt(key, value);

  // 获取bool
  bool? getBool(String key) => _prefs.getBool(key);
  // 设置bool
  Future<bool> setBool(String key, bool value) async =>
      await _prefs.setBool(key, value);
  // 获取double
  double? getDouble(String key) => _prefs.getDouble(key);
  // 设置double
  Future<bool> setDouble(String key, double value) async =>
      await _prefs.setDouble(key, value);
  // 获取字符串列表
  List<String>? getStringList(String key) => _prefs.getStringList(key);
  // 设置字符串列表
  Future<bool> setStringList(String key, List<String> value) async =>
      _prefs.setStringList(key, value);
  // 移除某个key
  Future<bool> remove(String key) async => await _prefs.remove(key);
  // 清空所有数据
  Future<bool> clear() async => await _prefs.clear();
}
