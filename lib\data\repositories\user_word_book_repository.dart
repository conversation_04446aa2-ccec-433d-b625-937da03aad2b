import 'package:flutter_study_flow_by_myself/app/services/isar_service.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/app/exceptions/app_exception.dart';
import 'package:flutter_study_flow_by_myself/data/models/user_word_book_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_book_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/review_item_model.dart';
import 'package:flutter_study_flow_by_myself/data/providers/user_word_book/user_word_book_local_provider.dart';
// import 'package:flutter_study_flow_by_myself/data/providers/word_book/word_book_local_provider.dart';
import 'package:flutter_study_flow_by_myself/data/providers/word_book/word_book_remote_provider.dart';

/// UserWordBookRepository 负责处理用户与单词本关联的业务逻辑
///
/// 这个Repository是单词本功能的核心，管理用户与单词本之间的关系。
/// 它协调多个Provider，实现复杂的业务流程，为Controller层提供简洁的接口。
///
/// 主要功能：
/// - 下载并创建用户单词本（完整的事务操作，确保数据一致性）
/// - 激活/停用单词本管理（确保全局只有一个激活单词本）
/// - 学习进度管理（记录和更新用户的学习状态）
/// - 用户单词本设置管理（每日学习量等个性化设置）
/// - 提供统一的用户单词本业务接口（隐藏底层复杂性）
///
/// 架构说明：
/// - 对于复杂的业务流程（如下载），直接操作IsarService以确保事务完整性
/// - 对于简单的CRUD操作，通过Provider层以遵循分层架构
/// - 所有方法都有完整的错误处理和日志记录
class UserWordBookRepository {
  final UserWordBookLocalProvider _localProvider;
  final WordBookRemoteProvider _remoteProvider;
  final IsarService _isarService;
  final LogService _log;

  UserWordBookRepository({
    required UserWordBookLocalProvider localProvider,
    required WordBookRemoteProvider remoteProvider,
    required IsarService isarService,
    required LogService log,
  }) : _localProvider = localProvider,
       _remoteProvider = remoteProvider,
       _isarService = isarService,
       _log = log;

  /// 获取所有用户单词本
  Future<List<UserWordBookModel>> getAllUserWordBooks() async {
    try {
      return await _localProvider.getAll();
    } catch (e) {
      _log.e('获取所有用户单词本失败: $e', error: e);
      rethrow;
    }
  }

  /// 根据ID获取用户单词本
  /// [id] 用户单词本的本地数据库ID
  Future<UserWordBookModel?> getUserWordBookById(int id) async {
    try {
      return await _localProvider.getById(id);
    } catch (e) {
      _log.e('根据ID获取用户单词本失败: $e', error: e);
      rethrow;
    }
  }

  /// 获取当前激活的用户单词本
  Future<UserWordBookModel?> getActiveUserWordBook() async {
    try {
      return await _localProvider.getActiveUserWordBook();
    } catch (e) {
      _log.e('获取激活用户单词本失败: $e', error: e);
      rethrow;
    }
  }

  /// 激活指定的用户单词本，同时停用其他单词本
  ///
  /// 这是词库功能的核心业务方法，确保同一时间只有一本单词本处于激活状态。
  ///
  /// [userWordBookId] 要激活的用户单词本ID
  ///
  /// 业务规则：
  /// - 只有已下载的单词本才能激活
  /// - 一次只能有一本单词本处于激活状态
  /// - 激活过程必须是原子的，确保不会出现多本激活的情况
  /// - 如果激活失败，原先已激活的单词本应保持激活状态
  ///
  /// 技术注意：
  /// - 使用单一事务确保原子操作，解决嵌套事务问题
  /// - 直接在事务中执行所有操作，避免依赖可能包含事务的Provider方法
  ///
  /// 抛出异常：
  /// - AppException: 当单词本不存在或状态不允许激活时
  Future<void> activateUserWordBook(int userWordBookId) async {
    try {
      // 开启一个大事务，确保所有操作要么全部成功，要么全部失败
      await _isarService.isar.writeTxn(() async {
        // 步骤1：检查用户单词本是否存在
        final userWordBook = await _isarService.isar.userWordBookModels.get(
          userWordBookId,
        );
        if (userWordBook == null) {
          throw AppException(AppExceptionType.unknown, '用户单词本不存在');
        }

        // 步骤2：UserWordBookModel 的存在本身就表示已下载，无需额外检查

        // 步骤3：将所有单词本设置为非激活状态（直接在事务中操作）
        // 不调用Provider方法，避免嵌套事务
        final allBooks = await _isarService.getAll<UserWordBookModel>();
        for (var book in allBooks) {
          book.deactivate(); // 停用所有其他单词本
          await _isarService.isar.userWordBookModels.put(book);
        }

        // 步骤4：激活指定的单词本
        userWordBook.activate();
        await _isarService.isar.userWordBookModels.put(userWordBook);

        _log.i('成功激活用户单词本: $userWordBookId');
      });
    } catch (e) {
      _log.e('激活用户单词本失败: $e', error: e);
      rethrow;
    }
  }

  /// 停用指定的用户单词本
  /// [userWordBookId] 要停用的用户单词本ID
  Future<void> deactivateUserWordBook(int userWordBookId) async {
    try {
      final userWordBook = await _localProvider.getById(userWordBookId);
      if (userWordBook == null) {
        throw AppException(AppExceptionType.unknown, '用户单词本不存在');
      }

      userWordBook.deactivate();
      await _localProvider.save(userWordBook);

      _log.i('成功停用用户单词本: $userWordBookId');
    } catch (e) {
      _log.e('停用用户单词本失败: $e', error: e);
      rethrow;
    }
  }

  /// 下载并创建用户单词本
  ///
  /// 这是一个复杂的业务流程，涉及多个数据表的原子操作。
  /// 为了保证数据一致性，在单个事务中直接操作数据库，没有通过Provider层。
  /// 这种做法在复杂业务场景中是合理的架构选择。
  ///
  /// [remoteWordBookId] 远程单词本ID
  /// [onProgress] 可选的进度回调，用于UI显示下载进度
  /// [onCreatingReviewItems] 可选的复习项创建进度回调
  ///
  /// 完整流程：
  /// 1. 检查是否已存在该单词本的用户关系（避免重复下载）
  /// 2. 从远程下载单词本数据（包含单词本信息和单词列表）
  /// 3. 在一个大事务中保存所有数据并建立关联关系
  /// 4. 为单词本中的所有单词创建复习项
  /// 5. 返回创建的用户单词本
  ///
  /// 抛出异常：
  /// - AppException: 当下载失败或数据保存失败时
  Future<UserWordBookModel> downloadAndCreateUserWordBook(
    int remoteWordBookId, {
    Function(double progress)? onProgress,
    Function(String message, double progress)? onCreatingReviewItems,
  }) async {
    try {
      _log.i('开始下载并创建用户单词本: $remoteWordBookId');

      // 步骤1：检查是否已存在该单词本的用户关系
      // 避免重复下载，提高用户体验和性能
      // 注意：这里需要使用远程ID检查，而不是本地ID
      final existingUserWordBook = await _localProvider.getByRemoteWordBookId(
        remoteWordBookId,
      );

      if (existingUserWordBook != null) {
        _log.i('用户单词本已存在，直接返回: ${existingUserWordBook.id}');
        return existingUserWordBook;
      }

      // 步骤2：从远程下载单词本数据
      // 这里会下载单词本基本信息和完整的单词列表
      final downloadResult = await _remoteProvider.downloadWordBook(
        remoteWordBookId,
        onProgress: onProgress,
      );

      // 初始化变量以避免编译错误
      late UserWordBookModel userWordBook;
      final reviewItems = <ReviewItemModel>[];

      // 步骤3：在一个大事务中保存所有数据并建立关联关系
      // 使用事务确保数据一致性，要么全部成功，要么全部回滚
      await _isarService.isar.writeTxn(() async {
        // 3.1 保存单词本基本信息到本地数据库
        final wordBookId = await _isarService.isar.wordBookModels.put(
          downloadResult.wordBook,
        );
        downloadResult.wordBook.id = wordBookId;

        // 3.2 批量保存单词列表到本地数据库
        // 注意：这里直接操作IsarService是为了在同一事务中处理大量数据
        final wordIds = <int>[];
        for (final word in downloadResult.words) {
          final wordId = await _isarService.isar.wordModels.put(word);
          word.id = wordId;
          wordIds.add(wordId);
        }

        // 3.3 建立单词本与单词的多对多关联关系
        // 这样可以支持一个单词属于多个单词本的场景
        downloadResult.wordBook.words.clear();
        downloadResult.wordBook.words.addAll(downloadResult.words);
        await downloadResult.wordBook.words.save();

        // 3.4 创建用户单词本关系记录
        // 这个记录存储用户对该单词本的学习状态和个人设置
        // 确保remoteWordBookId不为空，如果为空则使用本地传入的remoteWordBookId参数
        final remoteId = downloadResult.wordBook.wordBookId ?? remoteWordBookId;

        userWordBook = UserWordBookModel(
          wordBookId: downloadResult.wordBook.id, // 本地词书ID
          remoteWordBookId: remoteId, // 远程词书ID
          isActive: false, // 默认未激活，用户需手动激活
          isCompleted: false, // 默认未完成
          totalWords: downloadResult.words.length,
          dailyNewWordsCount: 10, // 默认每日学习10个新词，用户可后续修改
        );

        // 3.5 保存用户单词本到数据库
        final userWordBookId = await _isarService.isar.userWordBookModels.put(
          userWordBook,
        );
        userWordBook.id = userWordBookId;

        // 3.6 建立用户单词本与单词本的一对一关联关系
        // 这样可以通过用户单词本直接访问单词本的详细信息
        userWordBook.wordBook.value = downloadResult.wordBook;
        await userWordBook.wordBook.save();

        // 3.7 为所有单词批量创建复习项 (关键变更)
        if (onCreatingReviewItems != null) {
          onCreatingReviewItems('正在创建单词复习项...', 0.0);
        }

        // 分批处理，每批500个单词
        final batchSize = 500;
        final words = downloadResult.words;
        final totalWords = words.length;

        for (var i = 0; i < totalWords; i += batchSize) {
          final end = (i + batchSize < totalWords) ? i + batchSize : totalWords;
          final batch = words.sublist(i, end);

          // 为这批单词创建复习项
          final batchItems =
              batch
                  .map(
                    (word) => ReviewItemModel(
                      wordId: word.id,
                      userWordBookId: userWordBook.id,
                      isNewWord: true, // 标记为新词
                      nextReviewDate: DateTime.now(), // 初始复习日期为当前时间
                    ),
                  )
                  .toList();

          // 建立关联关系并批量保存复习项（在当前事务中执行，避免嵌套事务）
          // 先保存复习项
          await _isarService.isar.reviewItemModels.putAll(batchItems);

          // 建立关联关系
          for (int j = 0; j < batchItems.length; j++) {
            final item = batchItems[j];
            final word = batch[j];

            // 建立单词关联
            item.word.value = word;
            await item.word.save();

            // 建立用户单词本关联
            item.userWordBook.value = userWordBook;
            await item.userWordBook.save();
          }

          reviewItems.addAll(batchItems);

          // 更新进度
          if (onCreatingReviewItems != null) {
            final progress = (i + batch.length) / totalWords;
            onCreatingReviewItems(
              '正在创建复习项 ${i + batch.length}/$totalWords',
              progress,
            );
          }
        }

        _log.i(
          '成功下载并创建用户单词本: ${userWordBook.id}, 创建复习项: ${reviewItems.length}个',
        );
      });

      return userWordBook;
    } catch (e) {
      _log.e('下载并创建用户单词本失败: $e', error: e);
      rethrow;
    }
  }

  /// 更新学习进度
  ///
  /// 这是学习功能的核心方法，用于记录用户的学习进度。
  /// 通过Provider层操作，遵循标准的分层架构。
  ///
  /// [userWordBookId] 用户单词本ID
  /// [learnedCount] 已学习单词数量，必须大于等于0
  ///
  /// 注意：这个方法会同时更新已学单词数和最后学习日期，
  /// 避免了多次数据库操作，提高了性能。
  ///
  /// 抛出异常：
  /// - AppException: 当用户单词本不存在时
  Future<void> updateLearningProgress(
    int userWordBookId,
    int learnedCount,
  ) async {
    try {
      final userWordBook = await _localProvider.getById(userWordBookId);
      if (userWordBook == null) {
        throw AppException(AppExceptionType.unknown, '用户单词本不存在');
      }

      // 使用模型的updateLearningProgress方法更新进度
      // 这个方法会同时更新已学单词数和最后学习日期，保证数据一致性
      userWordBook.updateLearningProgress(learnedCount);
      await _localProvider.save(userWordBook);

      _log.i('成功更新学习进度: 用户单词本=$userWordBookId, 已学单词=$learnedCount');
    } catch (e) {
      _log.e('更新学习进度失败: $e', error: e);
      rethrow;
    }
  }

  /// 更新每日学习单词数量设置
  /// [userWordBookId] 用户单词本ID
  /// [dailyCount] 每日学习单词数量
  ///
  /// 业务规则：
  /// - 每日学习单词数量必须在合理范围内（1-100）
  Future<void> updateDailyNewWordsCount(
    int userWordBookId,
    int dailyCount,
  ) async {
    try {
      // 验证参数范围
      if (dailyCount < 1 || dailyCount > 100) {
        throw AppException(AppExceptionType.unknown, '每日学习单词数量必须在1-100之间');
      }

      final userWordBook = await _localProvider.getById(userWordBookId);
      if (userWordBook == null) {
        throw AppException(AppExceptionType.unknown, '用户单词本不存在');
      }

      userWordBook.dailyNewWordsCount = dailyCount;
      await _localProvider.save(userWordBook);

      _log.i('成功更新每日学习单词数量: 用户单词本=$userWordBookId, 每日数量=$dailyCount');
    } catch (e) {
      _log.e('更新每日学习单词数量失败: $e', error: e);
      rethrow;
    }
  }

  /// 更新最后学习日期
  /// [userWordBookId] 用户单词本ID
  /// [date] 学习日期
  Future<void> updateLastLearningDate(int userWordBookId, DateTime date) async {
    try {
      final userWordBook = await _localProvider.getById(userWordBookId);
      if (userWordBook == null) {
        throw AppException(AppExceptionType.unknown, '用户单词本不存在');
      }

      userWordBook.lastLearningDate = date;
      await _localProvider.save(userWordBook);

      _log.i('成功更新最后学习日期: 用户单词本=$userWordBookId, 日期=$date');
    } catch (e) {
      _log.e('更新最后学习日期失败: $e', error: e);
      rethrow;
    }
  }

  /// 标记单词本为已完成
  /// [userWordBookId] 用户单词本ID
  Future<void> markWordBookAsCompleted(int userWordBookId) async {
    try {
      final userWordBook = await _localProvider.getById(userWordBookId);
      if (userWordBook == null) {
        throw AppException(AppExceptionType.unknown, '用户单词本不存在');
      }

      // 使用模型的便利方法标记为完成
      userWordBook.markAsCompleted();
      await _localProvider.save(userWordBook);

      _log.i('成功标记单词本为完成状态: 用户单词本=$userWordBookId');
    } catch (e) {
      _log.e('标记单词本完成失败: $e', error: e);
      rethrow;
    }
  }

  /// 获取用户单词本的学习统计信息
  /// [userWordBookId] 用户单词本ID
  /// [loadRelations] 是否加载关联数据，如词书和复习项
  Future<Map<String, dynamic>> getLearningStatistics(
    int userWordBookId, {
    bool loadRelations = false,
  }) async {
    try {
      // 获取用户单词本信息
      UserWordBookModel? userWordBook = await _localProvider.getById(
        userWordBookId,
      );
      if (userWordBook == null) {
        throw AppException(AppExceptionType.database, '找不到指定的单词本');
      }

      // 如果需要，加载关联数据
      if (loadRelations) {
        await _localProvider.loadRelations(
          userWordBook,
          loadWordBook: true,
          loadReviewItems: false,
          loadLearningSessions: false,
        );
      }

      // 获取学习进度基础数据
      final learnedCount = userWordBook.totalLearnedWords;
      final totalCount = userWordBook.totalWords;

      // 计算进度百分比，避免除零错误
      final progressPercentage =
          totalCount > 0 ? ((learnedCount / totalCount) * 100).round() : 0;

      // 返回统计信息
      return {
        'learnedCount': learnedCount,
        'totalCount': totalCount,
        'progressPercentage': progressPercentage,
        'lastLearningDate': userWordBook.lastLearningDate,
      };
    } catch (e) {
      _log.e('获取学习统计信息失败: $e', error: e);
      // 返回默认值避免UI崩溃
      return {
        'learnedCount': 0,
        'totalCount': 0,
        'progressPercentage': 0,
        'lastLearningDate': null,
      };
    }
  }

  /// 删除用户单词本
  /// [userWordBookId] 要删除的用户单词本ID
  ///
  /// 注意：这只会删除用户与单词本的关联关系，不会删除单词本本身
  Future<void> deleteUserWordBook(int userWordBookId) async {
    try {
      final userWordBook = await _localProvider.getById(userWordBookId);
      if (userWordBook == null) {
        throw AppException(AppExceptionType.unknown, '用户单词本不存在');
      }

      // 如果是激活状态的单词本，不允许删除
      if (userWordBook.isActive) {
        throw AppException(AppExceptionType.unknown, '不能删除激活状态的单词本，请先切换到其他单词本');
      }

      await _localProvider.deleteById(userWordBookId);
      _log.i('成功删除用户单词本: $userWordBookId');
    } catch (e) {
      _log.e('删除用户单词本失败: $e', error: e);
      rethrow;
    }
  }

  /// 检查用户是否已添加指定的单词本
  /// [remoteWordBookId] 远程单词本ID
  /// 返回 true 表示已添加，false 表示未添加
  Future<bool> hasUserWordBook(int remoteWordBookId) async {
    try {
      return await _localProvider.hasWordBook(remoteWordBookId);
    } catch (e) {
      _log.e('检查用户单词本是否存在失败: $e', error: e);
      rethrow;
    }
  }

  /// 获取已下载的用户单词本列表
  Future<List<UserWordBookModel>> getDownloadedUserWordBooks() async {
    try {
      return await _localProvider.getDownloadedUserWordBooks();
    } catch (e) {
      _log.e('获取已下载用户单词本列表失败: $e', error: e);
      rethrow;
    }
  }

  /// 获取用户单词本数量
  Future<int> getUserWordBookCount() async {
    try {
      return await _localProvider.count();
    } catch (e) {
      _log.e('获取用户单词本数量失败: $e', error: e);
      rethrow;
    }
  }
}
