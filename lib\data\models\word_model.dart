import 'package:isar/isar.dart';
import 'word_book_model.dart';

// 这行是必须的，告诉 build_runner 为这个文件生成 Isar 代码
part 'word_model.g.dart';

// 使用 @Collection() 注解将 WordModel 标记为 Isar 集合
@Collection()
class WordModel {
  // Isar 的主键。你的 id 是 int 类型，可以直接用作 Isar ID。
  // @Id() 注解表示这个字段是集合的主键。
  // 注意：Isar 的 @Id() 字段必须是非空的 int 类型。
  // 如果你的 id 可能为 null，你需要处理一下。通常，后端 id 不会为 null。
  // 如果你希望 Isar 自动生成 ID，可以写成 Id id = Isar.autoIncrement;
  Id id = Isar.autoIncrement;

  // Isar 字段名通常遵循 camelCase，但如果你希望与后端 snake_case 匹配，
  // 也可以通过 @Name('backend_field_name') 来指定存储时的字段名。
  // 不过对于普通属性，直接用 camelCase 即可，Isar 默认会转换。
  late String spelling; // 单词本身

  late String phonetic; // 音标
  late String definition; // 释义

  // List<String> 在 Isar 中可以直接存储
  late List<String> examples; // 例句

  String? audioUrl; // 音频url 可选
  String? imageUrl; // 图片url 可选
  List<String>? tags; // 标签 可选

  // 添加与WordBookModel的多对多关联
  final wordBooks = IsarLinks<WordBookModel>();

  // 构造函数保持不变
  WordModel({
    required this.spelling,
    required this.phonetic,
    required this.definition,
    required this.examples,
    this.audioUrl,
    this.imageUrl,
    this.tags,
  });

  // 对于从后端获取的 JSON 数据，你仍然需要 fromJson 工厂方法来创建 Dart 对象。
  // 然后将这个 Dart 对象保存到 Isar。
  factory WordModel.fromJson(Map<String, dynamic> json) => WordModel(
    spelling: json['spelling'] as String,
    phonetic: json['phonetic'] as String,
    definition: json['definition'] as String,
    examples: List<String>.from(json['examples'] ?? []),
    audioUrl: json['audioUrl'] as String?,
    imageUrl: json['imageUrl'] as String?,
    tags: json['tags'] != null ? List<String>.from(json['tags']) : null,
  )..id = json['id'] ?? Isar.autoIncrement;

  // toJson 方法也可以保留，如果你需要将 Isar 中的数据再转换回 JSON 格式发给后端。
  Map<String, dynamic> toJson() => {
    'id': id,
    'spelling': spelling,
    'phonetic': phonetic,
    'definition': definition,
    'examples': examples,
    'audioUrl': audioUrl,
    'imageUrl': imageUrl,
    'tags': tags,
  };

  // 可选：添加 toString() 方法方便调试
  @override
  String toString() {
    return 'WordModel(id: $id, spelling: $spelling, phonetic: $phonetic, definition: $definition)';
  }
}
