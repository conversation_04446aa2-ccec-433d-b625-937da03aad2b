// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'learning_session.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetLearningSessionCollection on Isar {
  IsarCollection<LearningSession> get learningSessions => this.collection();
}

const LearningSessionSchema = CollectionSchema(
  name: r'LearningSession',
  id: 8239719336843205505,
  properties: {
    r'currentWordIndex': PropertySchema(
      id: 0,
      name: r'currentWordIndex',
      type: IsarType.long,
    ),
    r'endTime': PropertySchema(
      id: 1,
      name: r'endTime',
      type: IsarType.dateTime,
    ),
    r'isInFinalQuiz': PropertySchema(
      id: 2,
      name: r'isInFinalQuiz',
      type: IsarType.bool,
    ),
    r'lastStudyTime': PropertySchema(
      id: 3,
      name: r'lastStudyTime',
      type: IsarType.dateTime,
    ),
    r'sessionId': PropertySchema(
      id: 4,
      name: r'sessionId',
      type: IsarType.string,
    ),
    r'startTime': PropertySchema(
      id: 5,
      name: r'startTime',
      type: IsarType.dateTime,
    ),
    r'userWordBookId': PropertySchema(
      id: 6,
      name: r'userWordBookId',
      type: IsarType.long,
    ),
    r'wordIds': PropertySchema(
      id: 7,
      name: r'wordIds',
      type: IsarType.longList,
    ),
    r'wordLearningStateJson': PropertySchema(
      id: 8,
      name: r'wordLearningStateJson',
      type: IsarType.string,
    )
  },
  estimateSize: _learningSessionEstimateSize,
  serialize: _learningSessionSerialize,
  deserialize: _learningSessionDeserialize,
  deserializeProp: _learningSessionDeserializeProp,
  idName: r'id',
  indexes: {},
  links: {
    r'userWordBook': LinkSchema(
      id: 8317018007563594930,
      name: r'userWordBook',
      target: r'UserWordBookModel',
      single: true,
    )
  },
  embeddedSchemas: {},
  getId: _learningSessionGetId,
  getLinks: _learningSessionGetLinks,
  attach: _learningSessionAttach,
  version: '3.1.0+1',
);

int _learningSessionEstimateSize(
  LearningSession object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  bytesCount += 3 + object.sessionId.length * 3;
  bytesCount += 3 + object.wordIds.length * 8;
  bytesCount += 3 + object.wordLearningStateJson.length * 3;
  return bytesCount;
}

void _learningSessionSerialize(
  LearningSession object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeLong(offsets[0], object.currentWordIndex);
  writer.writeDateTime(offsets[1], object.endTime);
  writer.writeBool(offsets[2], object.isInFinalQuiz);
  writer.writeDateTime(offsets[3], object.lastStudyTime);
  writer.writeString(offsets[4], object.sessionId);
  writer.writeDateTime(offsets[5], object.startTime);
  writer.writeLong(offsets[6], object.userWordBookId);
  writer.writeLongList(offsets[7], object.wordIds);
  writer.writeString(offsets[8], object.wordLearningStateJson);
}

LearningSession _learningSessionDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = LearningSession(
    currentWordIndex: reader.readLongOrNull(offsets[0]) ?? 0,
    endTime: reader.readDateTimeOrNull(offsets[1]),
    isInFinalQuiz: reader.readBoolOrNull(offsets[2]) ?? false,
    lastStudyTime: reader.readDateTime(offsets[3]),
    sessionId: reader.readString(offsets[4]),
    startTime: reader.readDateTime(offsets[5]),
    userWordBookId: reader.readLong(offsets[6]),
    wordIds: reader.readLongList(offsets[7]) ?? [],
    wordLearningStateJson: reader.readString(offsets[8]),
  );
  object.id = id;
  return object;
}

P _learningSessionDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readLongOrNull(offset) ?? 0) as P;
    case 1:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 2:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    case 3:
      return (reader.readDateTime(offset)) as P;
    case 4:
      return (reader.readString(offset)) as P;
    case 5:
      return (reader.readDateTime(offset)) as P;
    case 6:
      return (reader.readLong(offset)) as P;
    case 7:
      return (reader.readLongList(offset) ?? []) as P;
    case 8:
      return (reader.readString(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _learningSessionGetId(LearningSession object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _learningSessionGetLinks(LearningSession object) {
  return [object.userWordBook];
}

void _learningSessionAttach(
    IsarCollection<dynamic> col, Id id, LearningSession object) {
  object.id = id;
  object.userWordBook.attach(
      col, col.isar.collection<UserWordBookModel>(), r'userWordBook', id);
}

extension LearningSessionQueryWhereSort
    on QueryBuilder<LearningSession, LearningSession, QWhere> {
  QueryBuilder<LearningSession, LearningSession, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension LearningSessionQueryWhere
    on QueryBuilder<LearningSession, LearningSession, QWhereClause> {
  QueryBuilder<LearningSession, LearningSession, QAfterWhereClause> idEqualTo(
      Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterWhereClause> idLessThan(
      Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension LearningSessionQueryFilter
    on QueryBuilder<LearningSession, LearningSession, QFilterCondition> {
  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      currentWordIndexEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'currentWordIndex',
        value: value,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      currentWordIndexGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'currentWordIndex',
        value: value,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      currentWordIndexLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'currentWordIndex',
        value: value,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      currentWordIndexBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'currentWordIndex',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      endTimeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'endTime',
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      endTimeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'endTime',
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      endTimeEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'endTime',
        value: value,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      endTimeGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'endTime',
        value: value,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      endTimeLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'endTime',
        value: value,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      endTimeBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'endTime',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      isInFinalQuizEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isInFinalQuiz',
        value: value,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      lastStudyTimeEqualTo(DateTime value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'lastStudyTime',
        value: value,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      lastStudyTimeGreaterThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'lastStudyTime',
        value: value,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      lastStudyTimeLessThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'lastStudyTime',
        value: value,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      lastStudyTimeBetween(
    DateTime lower,
    DateTime upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'lastStudyTime',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      sessionIdEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sessionId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      sessionIdGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'sessionId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      sessionIdLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'sessionId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      sessionIdBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'sessionId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      sessionIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'sessionId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      sessionIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'sessionId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      sessionIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'sessionId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      sessionIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'sessionId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      sessionIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sessionId',
        value: '',
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      sessionIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'sessionId',
        value: '',
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      startTimeEqualTo(DateTime value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'startTime',
        value: value,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      startTimeGreaterThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'startTime',
        value: value,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      startTimeLessThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'startTime',
        value: value,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      startTimeBetween(
    DateTime lower,
    DateTime upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'startTime',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      userWordBookIdEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'userWordBookId',
        value: value,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      userWordBookIdGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'userWordBookId',
        value: value,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      userWordBookIdLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'userWordBookId',
        value: value,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      userWordBookIdBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'userWordBookId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      wordIdsElementEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'wordIds',
        value: value,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      wordIdsElementGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'wordIds',
        value: value,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      wordIdsElementLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'wordIds',
        value: value,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      wordIdsElementBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'wordIds',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      wordIdsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'wordIds',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      wordIdsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'wordIds',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      wordIdsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'wordIds',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      wordIdsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'wordIds',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      wordIdsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'wordIds',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      wordIdsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'wordIds',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      wordLearningStateJsonEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'wordLearningStateJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      wordLearningStateJsonGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'wordLearningStateJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      wordLearningStateJsonLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'wordLearningStateJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      wordLearningStateJsonBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'wordLearningStateJson',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      wordLearningStateJsonStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'wordLearningStateJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      wordLearningStateJsonEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'wordLearningStateJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      wordLearningStateJsonContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'wordLearningStateJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      wordLearningStateJsonMatches(String pattern,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'wordLearningStateJson',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      wordLearningStateJsonIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'wordLearningStateJson',
        value: '',
      ));
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      wordLearningStateJsonIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'wordLearningStateJson',
        value: '',
      ));
    });
  }
}

extension LearningSessionQueryObject
    on QueryBuilder<LearningSession, LearningSession, QFilterCondition> {}

extension LearningSessionQueryLinks
    on QueryBuilder<LearningSession, LearningSession, QFilterCondition> {
  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      userWordBook(FilterQuery<UserWordBookModel> q) {
    return QueryBuilder.apply(this, (query) {
      return query.link(q, r'userWordBook');
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterFilterCondition>
      userWordBookIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'userWordBook', 0, true, 0, true);
    });
  }
}

extension LearningSessionQuerySortBy
    on QueryBuilder<LearningSession, LearningSession, QSortBy> {
  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      sortByCurrentWordIndex() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'currentWordIndex', Sort.asc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      sortByCurrentWordIndexDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'currentWordIndex', Sort.desc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy> sortByEndTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endTime', Sort.asc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      sortByEndTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endTime', Sort.desc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      sortByIsInFinalQuiz() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isInFinalQuiz', Sort.asc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      sortByIsInFinalQuizDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isInFinalQuiz', Sort.desc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      sortByLastStudyTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastStudyTime', Sort.asc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      sortByLastStudyTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastStudyTime', Sort.desc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      sortBySessionId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sessionId', Sort.asc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      sortBySessionIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sessionId', Sort.desc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      sortByStartTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startTime', Sort.asc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      sortByStartTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startTime', Sort.desc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      sortByUserWordBookId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userWordBookId', Sort.asc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      sortByUserWordBookIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userWordBookId', Sort.desc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      sortByWordLearningStateJson() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'wordLearningStateJson', Sort.asc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      sortByWordLearningStateJsonDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'wordLearningStateJson', Sort.desc);
    });
  }
}

extension LearningSessionQuerySortThenBy
    on QueryBuilder<LearningSession, LearningSession, QSortThenBy> {
  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      thenByCurrentWordIndex() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'currentWordIndex', Sort.asc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      thenByCurrentWordIndexDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'currentWordIndex', Sort.desc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy> thenByEndTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endTime', Sort.asc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      thenByEndTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endTime', Sort.desc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy> thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      thenByIsInFinalQuiz() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isInFinalQuiz', Sort.asc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      thenByIsInFinalQuizDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isInFinalQuiz', Sort.desc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      thenByLastStudyTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastStudyTime', Sort.asc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      thenByLastStudyTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastStudyTime', Sort.desc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      thenBySessionId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sessionId', Sort.asc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      thenBySessionIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sessionId', Sort.desc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      thenByStartTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startTime', Sort.asc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      thenByStartTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startTime', Sort.desc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      thenByUserWordBookId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userWordBookId', Sort.asc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      thenByUserWordBookIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userWordBookId', Sort.desc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      thenByWordLearningStateJson() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'wordLearningStateJson', Sort.asc);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QAfterSortBy>
      thenByWordLearningStateJsonDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'wordLearningStateJson', Sort.desc);
    });
  }
}

extension LearningSessionQueryWhereDistinct
    on QueryBuilder<LearningSession, LearningSession, QDistinct> {
  QueryBuilder<LearningSession, LearningSession, QDistinct>
      distinctByCurrentWordIndex() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'currentWordIndex');
    });
  }

  QueryBuilder<LearningSession, LearningSession, QDistinct>
      distinctByEndTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'endTime');
    });
  }

  QueryBuilder<LearningSession, LearningSession, QDistinct>
      distinctByIsInFinalQuiz() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isInFinalQuiz');
    });
  }

  QueryBuilder<LearningSession, LearningSession, QDistinct>
      distinctByLastStudyTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'lastStudyTime');
    });
  }

  QueryBuilder<LearningSession, LearningSession, QDistinct> distinctBySessionId(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'sessionId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<LearningSession, LearningSession, QDistinct>
      distinctByStartTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'startTime');
    });
  }

  QueryBuilder<LearningSession, LearningSession, QDistinct>
      distinctByUserWordBookId() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'userWordBookId');
    });
  }

  QueryBuilder<LearningSession, LearningSession, QDistinct>
      distinctByWordIds() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'wordIds');
    });
  }

  QueryBuilder<LearningSession, LearningSession, QDistinct>
      distinctByWordLearningStateJson({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'wordLearningStateJson',
          caseSensitive: caseSensitive);
    });
  }
}

extension LearningSessionQueryProperty
    on QueryBuilder<LearningSession, LearningSession, QQueryProperty> {
  QueryBuilder<LearningSession, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<LearningSession, int, QQueryOperations>
      currentWordIndexProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'currentWordIndex');
    });
  }

  QueryBuilder<LearningSession, DateTime?, QQueryOperations> endTimeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'endTime');
    });
  }

  QueryBuilder<LearningSession, bool, QQueryOperations>
      isInFinalQuizProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isInFinalQuiz');
    });
  }

  QueryBuilder<LearningSession, DateTime, QQueryOperations>
      lastStudyTimeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'lastStudyTime');
    });
  }

  QueryBuilder<LearningSession, String, QQueryOperations> sessionIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'sessionId');
    });
  }

  QueryBuilder<LearningSession, DateTime, QQueryOperations>
      startTimeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'startTime');
    });
  }

  QueryBuilder<LearningSession, int, QQueryOperations>
      userWordBookIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'userWordBookId');
    });
  }

  QueryBuilder<LearningSession, List<int>, QQueryOperations> wordIdsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'wordIds');
    });
  }

  QueryBuilder<LearningSession, String, QQueryOperations>
      wordLearningStateJsonProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'wordLearningStateJson');
    });
  }
}
