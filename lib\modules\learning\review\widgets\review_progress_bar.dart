import 'package:flutter/material.dart';

/// 复习进度条组件
///
/// 显示复习进度信息，参考LearningProgressBar的设计
/// 包含当前组进度和整体进度的展示
class ReviewProgressBar extends StatelessWidget {
  /// 已完成的单词数量
  final int completedWords;

  /// 总单词数量
  final int totalWords;

  /// 当前组索引（从0开始）
  final int currentGroupIndex;

  /// 总组数
  final int totalGroups;

  /// 当前组已完成的单词数
  final int currentGroupCompleted;

  /// 当前组总单词数
  final int currentGroupTotal;

  const ReviewProgressBar({
    super.key,
    required this.completedWords,
    required this.totalWords,
    required this.currentGroupIndex,
    required this.totalGroups,
    required this.currentGroupCompleted,
    required this.currentGroupTotal,
  });

  @override
  Widget build(BuildContext context) {
    // 计算整体进度
    final overallProgress = totalWords > 0 ? completedWords / totalWords : 0.0;

    // 计算当前组进度
    final groupProgress =
        currentGroupTotal > 0 ? currentGroupCompleted / currentGroupTotal : 0.0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 整体进度信息
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '复习进度',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
              Text(
                '$completedWords/$totalWords 次答对',
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // 整体进度条
          LinearProgressIndicator(
            value: overallProgress,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(
              overallProgress >= 1.0 ? Colors.green : Colors.blue,
            ),
            minHeight: 6,
          ),
          const SizedBox(height: 4),

          // 整体进度百分比
          Text(
            '${(overallProgress * 100).toInt()}% 完成',
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
          ),
          const SizedBox(height: 12),

          // 当前组进度信息
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  '第 ${currentGroupIndex + 1} 组 / 共 $totalGroups 组',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[700],
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Text(
                '$currentGroupCompleted/$currentGroupTotal 次答对',
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ],
          ),
          const SizedBox(height: 6),

          // 当前组进度条
          LinearProgressIndicator(
            value: groupProgress,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(
              groupProgress >= 1.0 ? Colors.green : Colors.orange,
            ),
            minHeight: 4,
          ),
          const SizedBox(height: 4),

          // 当前组进度百分比
          Text(
            '当前组 ${(groupProgress * 100).toInt()}% 完成',
            style: TextStyle(fontSize: 11, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }
}
