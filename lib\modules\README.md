# modules 目录说明文档

## 目录职责

`modules` 目录包含应用程序的各个独立功能模块，每个模块代表一个主要业务功能。每个模块自成体系，拥有自己的视图、控制器、绑定和子页面等，实现了业务逻辑的模块化和关注点分离。这种组织方式使得大型应用更易于管理、扩展和维护。

## 目录结构

```
modules/
├── auth/                      # 认证模块（登录、注册、找回密码等）
│   ├── bindings/
│   ├── controllers/
│   ├── views/
│   └── routes/
├── home/                      # 首页模块
│   ├── ...（类似auth）
└── learning/                  # 学习模块
    ├── bindings/
    ├── controllers/
    ├── views/
    ├── models/
    ├── widgets/
    ├── routes/
    └── submodules/            # 子模块（可选）
        ├── recall/
        ├── quiz/
        └── ...
```

## 设计理念

- **功能内聚**：每个模块包含完整的功能实现，内部高内聚
- **模块解耦**：模块之间通过清晰的接口交互，低耦合
- **MVVM架构**：每个模块内部遵循MVVM架构模式
- **可独立开发**：不同团队成员可以独立开发不同模块
- **代码组织**：相关功能代码放在一起，便于查找和维护

## 模块结构详解

每个功能模块通常包含以下组件：

### bindings/

- **职责**：注册模块所需的依赖项，用于依赖注入
- **内容**：包含一个或多个继承自`Bindings`的类
- **典型文件**：`home_binding.dart`, `auth_binding.dart`
- **典型用法**：
  ```dart
  class HomeBinding extends Bindings {
    @override
    void dependencies() {
      Get.lazyPut<HomeController>(() => HomeController());
      Get.lazyPut<ProductRepository>(() => ProductRepositoryImpl());
      // 其他依赖...
    }
  }
  ```

### controllers/

- **职责**：包含模块的业务逻辑和状态管理，是MVVM中的ViewModel
- **内容**：继承自`GetxController`的控制器类
- **典型文件**：`home_controller.dart`, `login_controller.dart`
- **典型用法**：
  ```dart
  class HomeController extends GetxController {
    final ProductRepository _repository = Get.find<ProductRepository>();
    final RxList<Product> products = <Product>[].obs;
    final RxBool isLoading = false.obs;
    
    @override
    void onInit() {
      super.onInit();
      fetchProducts();
    }
    
    Future<void> fetchProducts() async {
      isLoading.value = true;
      try {
        products.value = await _repository.getProducts();
      } catch (e) {
        Get.snackbar('错误', '获取产品列表失败');
      } finally {
        isLoading.value = false;
      }
    }
  }
  ```

### views/

- **职责**：包含模块的UI界面，是MVVM中的View
- **内容**：通常是继承自`GetView<T>`的Widget类
- **典型文件**：`home_page.dart`, `login_page.dart`
- **典型用法**：
  ```dart
  class HomePage extends GetView<HomeController> {
    @override
    Widget build(BuildContext context) {
      return Scaffold(
        appBar: AppBar(title: Text('首页')),
        body: Obx(() {
          if (controller.isLoading.value) {
            return Center(child: CircularProgressIndicator());
          }
          return ListView.builder(
            itemCount: controller.products.length,
            itemBuilder: (context, index) {
              final product = controller.products[index];
              return ListTile(
                title: Text(product.name),
                subtitle: Text('¥${product.price}'),
                onTap: () => Get.toNamed('/product/${product.id}'),
              );
            },
          );
        }),
        floatingActionButton: FloatingActionButton(
          onPressed: controller.fetchProducts,
          child: Icon(Icons.refresh),
        ),
      );
    }
  }
  ```

### routes/

- **职责**：定义模块的路由配置
- **内容**：包含路由路径常量和GetPage列表
- **典型文件**：`home_routes.dart`, `auth_routes.dart`
- **典型用法**：
  ```dart
  abstract class HomeRoutes {
    static const HOME = '/home';
    static const PRODUCT_DETAIL = '/home/<USER>/:id';
    
    static final routes = [
      GetPage(
        name: HOME,
        page: () => HomePage(),
        binding: HomeBinding(),
      ),
      GetPage(
        name: PRODUCT_DETAIL,
        page: () => ProductDetailPage(),
        binding: ProductDetailBinding(),
      ),
    ];
  }
  ```

### models/ (可选)

- **职责**：定义模块特有的数据模型
- **内容**：与模块紧密相关的临时数据结构
- **典型文件**：`learning_session.dart`（仅用于学习模块的临时会话数据）

### widgets/ (可选)

- **职责**：定义模块特有的可复用UI组件
- **内容**：在模块内多次使用的Widget
- **典型文件**：`word_card.dart`, `progress_bar.dart`

### submodules/ (可选)

- **职责**：进一步拆分大型模块
- **内容**：子模块的视图、控制器等（结构类似主模块）
- **典型用例**：learning模块中的recall、quiz等子模块

## 模块间通信方式

在GetX MVVM架构中，模块间可以通过以下方式通信：

1. **服务层通信**：
   - 通过全局服务（如`AuthService`）共享状态和功能
   - 服务注册为全局单例，可被任何模块访问

2. **路由参数**：
   - 通过`Get.toNamed(route, arguments: data)`传递参数
   - 在目标页面通过`Get.arguments`获取参数

3. **GetX状态管理**：
   - 通过`.obs`变量和响应式编程实现自动UI更新
   - 使用`GetX<Controller>`或`Obx()`观察控制器状态变化

4. **依赖注入**：
   - 使用`Get.put()`和`Get.find()`实现依赖共享
   - 控制器可以注入服务和仓库以访问共享功能

## 典型模块示例

### 认证模块 (auth)

```
auth/
├── bindings/
│   └── auth_binding.dart       # 注册AuthController、UserRepository等
├── controllers/
│   ├── auth_controller.dart    # 管理认证状态（如已登录用户）
│   ├── login_controller.dart   # 处理登录逻辑
│   └── register_controller.dart # 处理注册逻辑
├── views/
│   ├── login_page.dart         # 登录页面UI
│   └── register_page.dart      # 注册页面UI
└── routes/
    └── auth_routes.dart        # 定义/login、/register等路由
```

### 学习模块 (learning)

```
learning/
├── bindings/
│   └── learning_binding.dart   # 注册LearningController、WordRepository等
├── controllers/
│   └── learning_controller.dart # 管理整体学习会话
├── models/
│   └── learning_session.dart   # 学习会话临时数据
├── views/
│   └── learning_page.dart      # 学习流程主页面
├── widgets/
│   ├── word_card.dart          # 单词卡片组件
│   └── progress_bar.dart       # 学习进度组件
├── routes/
│   └── learning_routes.dart    # 定义/learning及子路由
└── recall/                     # 单词回忆子模块
    ├── controllers/
    │   └── recall_controller.dart
    └── views/
        └── recall_page.dart
```

## 最佳实践

### 模块划分原则

1. **功能边界清晰**：
   - 每个模块应有明确的业务职责
   - 避免模块间职责重叠

2. **合理粒度**：
   - 模块不宜过大（考虑拆分）
   - 也不宜过小（避免过度碎片化）

3. **独立性**：
   - 模块应尽可能独立，减少对其他模块的依赖
   - 通过服务层和数据层共享功能，避免直接依赖

### 控制器设计

1. **分离关注点**：
   - 主控制器（如`LearningController`）管理模块整体状态和流程
   - 子控制器（如`RecallController`）管理具体页面的交互

2. **生命周期管理**：
   - 在`onInit()`中初始化资源和状态
   - 在`onClose()`中释放资源（取消订阅、关闭流等）

3. **状态管理**：
   - 使用`.obs`变量使状态响应式
   - 页面状态变量应定义在控制器中，而非视图中

### 视图设计

1. **纯展示**：
   - 视图应尽量不包含业务逻辑
   - 通过`GetView<T>`将视图与控制器绑定

2. **响应式UI**：
   - 使用`Obx()`或`GetX<Controller>()`观察状态变化
   - 避免直接在视图中修改控制器状态

3. **组件复用**：
   - 提取可复用的UI组件到`widgets/`目录
   - 区分全局组件（`app/global/widgets/`）和模块内组件

## 添加新模块流程

1. **创建目录结构**：
   ```
   modules/new_module/
   ├── bindings/
   ├── controllers/
   ├── views/
   └── routes/
   ```

2. **定义路由**：
   - 在`routes/new_module_routes.dart`定义路由常量和GetPage列表
   - 在`app/routes/app_pages.dart`中添加新模块路由

3. **创建控制器**：
   - 实现业务逻辑和状态管理
   - 通过依赖注入获取所需服务和仓库

4. **创建视图**：
   - 实现UI界面，通过控制器获取和更新状态
   - 使用响应式编程自动更新UI

5. **创建绑定**：
   - 设置依赖注入，注册控制器和其他依赖

## 子模块与主模块的关系

1. **路由管理**：
   - 子模块无需单独的routes文件
   - 子模块路由在主模块的routes文件中定义

2. **依赖注入**：
   - 子模块控制器通常在主模块的绑定中注册
   - 子模块可以访问主模块注册的依赖

3. **状态共享**：
   - 子模块可以通过`Get.find()`获取主模块控制器
   - 主模块控制器可以协调多个子模块

## 调试技巧

1. **控制器调试**：
   - 使用`print()`或专用日志工具记录控制器状态变化
   - 考虑实现简单的状态历史跟踪

2. **依赖注入调试**：
   - 使用`Get.isRegistered<T>()`检查依赖是否正确注册
   - 使用`Get.delete<T>()`手动清除依赖以测试重新创建

3. **路由调试**：
   - 使用`Get.currentRoute`查看当前路由
   - 实现简单的路由历史记录器

## 常见问题与解决方案

- **控制器过大**：
  - 将大型控制器拆分为多个小型控制器
  - 使用服务层分担业务逻辑

- **模块间状态同步**：
  - 使用全局服务管理共享状态
  - 考虑实现简单的事件总线

- **页面间数据传递**：
  - 对于简单数据，使用路由参数
  - 对于复杂数据，考虑通过服务层或控制器共享 