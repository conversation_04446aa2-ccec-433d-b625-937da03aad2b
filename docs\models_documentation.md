# Flutter单词学习应用 - 数据模型说明文档

## 概述

本文档详细说明应用中的数据模型结构、各模型的职责和关系，以及使用过程中的注意事项。应用使用Isar作为本地数据库，利用IsarLinks实现模型间的关联关系。

## 数据模型关系图

```
┌─────────────┐       ┌────────────────┐       ┌──────────────┐
│  WordModel  │◄──────┤  WordBookModel │◄──────┤ UserWordBook │
└─────┬───────┘       └────────┬────────┘       └──────┬───────┘
      │                        │                       │
      │                        │                       │
      ▼                        │                       │
┌─────────────┐                │                       │
│ChoiceQuizModel              │                       │
└─────────────┘                ▼                       ▼
                         ┌─────────────────────────────────┐
                         │       ReviewItemModel           │
                         └───────────────┬─────────────────┘
                                         │
                                         ▼
                                  ┌─────────────┐
                                  │LearningSession
                                  └─────────────┘
```

## 核心模型详解

### 1. WordModel（单词模型）

**职责**：
- 存储单词的基本信息，如拼写、释义、音标等
- 与单词本建立多对多关系

**核心字段**：
```dart
@Collection()
class WordModel {
  Id id = Isar.autoIncrement;
  late String spelling;     // 单词拼写
  late String phonetic;     // 音标
  late String definition;   // 释义
  late List<String> examples; // 例句
  String? audioUrl;         // 音频URL（可选）
  String? imageUrl;         // 图片URL（可选）
  List<String>? tags;       // 标签（可选）
  
  // 关联关系
  final wordBooks = IsarLinks<WordBookModel>(); // 所属单词本（多对多）
}
```

**注意事项**：
- 单词可以同时属于多个单词本，通过IsarLinks实现多对多关系
- 导入单词时应避免重复，可使用spelling字段作为唯一性检查
- 音频和图片URL存储的是资源路径，需要单独实现资源下载逻辑

### 2. WordBookModel（单词本模型）

**职责**：
- 存储单词本的基本信息，如名称、描述等
- 维护与单词的多对多关系

**核心字段**：
```dart
@Collection()
class WordBookModel {
  Id id = Isar.autoIncrement;
  int? wordBookId;          // 后端词书ID
  late String name;         // 词书名称
  late String description;  // 词书描述
  late int wordCount;       // 词书包含的单词数量
  late bool isCustom;       // 是否为用户自定义词书
  String? coverImageUrl;    // 封面图片URL
  String? category;         // 分类
  
  // 关联关系
  final words = IsarLinks<WordModel>(); // 包含的单词（多对多）
}
```

**注意事项**：
- WordBookModel只存储单词本的基本信息和内容结构
- 不包含用户特定的学习状态（如是否已下载、学习进度等）
- wordCount应与words.length保持一致，但为提高性能，单独存储
- isCustom字段区分预置单词本和用户自定义单词本

### 3. UserWordBookModel（用户单词本关系模型）

**职责**：
- 存储用户某个单词本的使用状态
- 包含学习状态、学习计划、统计信息等
- 表示"用户-单词本"之间的关系

**核心字段**：
```dart
@Collection()
class UserWordBookModel {
  Id id = Isar.autoIncrement;
  
  // 关联单词本
  late int wordBookId;
  final wordBook = IsarLink<WordBookModel>();
  
  // 状态管理
  @enumerated
  late WordBookStatus status;    // 枚举: NOT_DOWNLOADED, DOWNLOADED, ACTIVE
  
  // 学习计划
  int dailyNewWordsCount = 10;   // 每日学习新词数量
  DateTime? lastLearningDate;    // 最后学习日期
  
  // 统计信息
  int totalLearnedWords = 0;     // 已学单词数
  int totalWords = 0;            // 单词本总单词数
  
  // 元数据
  late DateTime addedDate;       // 添加到书桌的时间
}

enum WordBookStatus {
  NOT_DOWNLOADED,  // 未下载
  DOWNLOADED,      // 已下载但未激活
  ACTIVE           // 当前激活（学习中）
}
```

**易混淆点**：
- UserWordBookModel与WordBookModel的区别：
  - WordBookModel：描述单词本本身（名称、内容等）
  - UserWordBookModel：描述用户与单词本的关系（下载状态、学习设置等）

**注意事项**：
- 同一时刻只能有一个单词本处于ACTIVE状态
- 切换激活单词本时，需要将所有单词本设为DOWNLOADED，再将选中的设为ACTIVE
- 每个WordBookModel可以关联多个UserWordBookModel（多用户场景）
- totalLearnedWords应定期更新，通过查询ReviewItemModel统计

### 4. ReviewItemModel（复习项模型）

**职责**：
- 存储某个用户的某个单词本里的某个单词的学习状态
- 包含下次复习时间、复习次数、难度系数等具体学习细节
- 表示"用户-单词本-单词"三维关系中的学习状态

**核心字段**：
```dart
@Collection()
class ReviewItemModel {
  Id id = Isar.autoIncrement;
  
  // 关联
  final word = IsarLink<WordModel>();           // 关联的单词
  final userWordBook = IsarLink<UserWordBookModel>(); // 关联的用户单词本
  
  // SM2算法参数
  int repetitionCount = 0;      // 成功复习次数
  double easeFactor = 2.5;      // 难易度因子(初始值2.5)
  int previousInterval = 0;     // 上次间隔天数
  
  @Index()
  late DateTime nextReviewDate; // 下次复习时间
  
  // 学习状态
  bool isNewWord = true;        // 是否为新词（未学过）
}
```

**易混淆点**：
- ReviewItemModel关联到UserWordBookModel而非直接关联到WordBookModel：
  - 这样设计支持多用户场景（不同用户登录同一应用）
  - 确保每个用户在同一单词本中对同一个单词有独立的学习状态

**注意事项**：
- 采用"按需创建"策略，仅在用户开始学习单词时创建
- 使用isNewWord区分新词和已学习的词
- 通过nextReviewDate查询待复习单词

### 5. LearningSession（学习会话模型）

**职责**：
- 记录一次完整的学习会话
- 存储学习过程中的交互数据和结果

**核心字段**：
```dart
@Collection()
class LearningSession {
  Id id = Isar.autoIncrement;
  late String sessionId;        // 会话唯一ID
  
  // 关联
  final userWordBook = IsarLink<UserWordBookModel>(); // 关联的用户单词本
  
  // 学习内容
  late List<int> wordIds;       // 学习的单词ID列表
  late String recallResultsJson; // 回忆结果JSON
  
  // 测验内容
  late List<int> quizItemIds;   // 测验题ID列表
  late String quizResultsJson;  // 测验结果JSON
  
  // 时间记录
  late DateTime startTime;      // 开始时间
  DateTime? endTime;            // 结束时间（可选）
  
  // 进度记录
  late int currentWordIndex;    // 当前学习的单词索引
  late int currentQuizIndex;    // 当前测验题索引
}
```

**注意事项**：
- 学习会话必须关联到特定的UserWordBookModel
- recallResultsJson和quizResultsJson存储格式为`{"wordId": boolean}`
- 会话可能未完成(endTime为null)，应支持恢复未完成会话

### 6. ChoiceQuizModel（选择题模型）

**职责**：
- 存储单词的选择题信息
- 用于测试用户对单词的掌握程度

**核心字段**：
```dart
@Collection()
class ChoiceQuizModel {
  Id id = Isar.autoIncrement;
  late int wordId;              // 关联的单词ID
  late String question;         // 题干
  late List<String> options;    // 选项
  late int correctOptionIndex;  // 正确答案索引
  String? explanation;          // 解析（可选）
  
  // 可选关联到UserWordBook，如果需要为不同单词本生成不同的题目
  // final userWordBook = IsarLink<UserWordBookModel>();
}
```

**注意事项**：
- 选择题通常与单词直接关联，不依赖于UserWordBookModel
- 如果题库需要按单词本区分，可添加userWordBook关联

## 模型使用流程示例

### 1. 单词本下载流程

```dart
Future<void> downloadWordBook(int remoteWordBookId) async {
  // 1. 从API获取完整的单词本JSON（包含单词本信息和单词列表）
  final wordBookData = await apiService.fetchCompleteWordBook(remoteWordBookId);
  
  // 2. 解析并保存单词本信息
  final wordBook = WordBookModel.fromJson(wordBookData['bookInfo']);
  await wordBookRepository.saveWordBook(wordBook);
  
  // 3. 解析并保存单词列表
  final wordsList = (wordBookData['words'] as List)
      .map((w) => WordModel.fromJson(w))
      .toList();
  await wordRepository.saveWords(wordsList);
  
  // 4. 建立单词与单词本的关联
  await wordBookRepository.associateWordsWithBook(wordsList, wordBook);
  
  // 5. 创建用户单词本记录
  final userWordBook = UserWordBookModel(
    wordBookId: wordBook.id,
    status: WordBookStatus.DOWNLOADED,
    totalWords: wordsList.length,
  );
  await userWordBookRepository.saveUserWordBook(userWordBook);
}
```

### 2. 激活单词本流程

```dart
Future<void> activateWordBook(int userWordBookId) async {
  // 1. 将所有单词本设置为非激活
  await userWordBookRepository.deactivateAllWordBooks();
  
  // 2. 激活选中的单词本
  final userWordBook = await userWordBookRepository.getUserWordBookById(userWordBookId);
  userWordBook.status = WordBookStatus.ACTIVE;
  await userWordBookRepository.saveUserWordBook(userWordBook);
}
```

### 3. 获取学习单词流程

```dart
Future<List<WordModel>> getTodayNewWords() async {
  // 1. 获取激活的用户单词本
  final activeBook = await userWordBookRepository.getActiveUserWordBook();
  if (activeBook == null) {
    throw Exception("未选择单词本");
  }
  
  // 2. 获取该单词本中已学习的单词ID列表
  final learnedWordIds = await reviewItemRepository.getLearnedWordIds(activeBook.id);
  
  // 3. 从单词本中获取指定数量的未学习单词
  return await wordRepository.getNewWordsFromWordBook(
    activeBook.wordBook.value!.id,
    activeBook.dailyNewWordsCount,  // 使用用户设置的每日学习数量
    excludeIds: learnedWordIds,
  );
}
```

## 注意事项总结

1. **关联关系管理**
   - 使用IsarLinks建立模型间关联，而非直接存储ID
   - 关联操作需要在事务中执行并调用save()方法

2. **按需创建ReviewItemModel**
   - 不要在导入单词本时为所有单词创建ReviewItemModel
   - 只在用户实际学习单词时创建，提高性能和节省空间

3. **单词本状态管理**
   - 同一时刻只能有一个激活的单词本(WordBookStatus.ACTIVE)
   - 切换单词本时需要更新所有相关单词本的状态

4. **数据一致性**
   - WordBookModel.wordCount应与实际关联的单词数量一致
   - UserWordBookModel.totalLearnedWords应定期更新

5. **性能优化**
   - 避免N+1查询问题，合理使用IsarLinks.load()批量加载关联对象
   - 为频繁查询的字段添加@Index注解

## 未来可能的扩展

1. **用户自定义单词本**
   - 可通过现有模型支持，利用WordBookModel.isCustom区分

2. **高级学习计划**
   - 可能需要单独的LearningPlanModel来支持更复杂的学习计划

3. **多设备同步**
   - 需要为模型添加lastSyncTime字段
   - 所有模型需要添加用户ID外键以支持多用户 