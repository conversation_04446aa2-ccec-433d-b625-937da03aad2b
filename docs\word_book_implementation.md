# 单词本功能实现方案

## 项目背景

当前项目是一个Flutter单词学习应用，已实现了基本的单词学习和复习功能。目前，**单词本和单词是完全独立的**，用户学习和复习时直接获取一组与单词本无关的单词。我们需要改造现有项目，实现单词与单词本的关联，使学习和复习功能基于用户选择的特定单词本进行。

项目已经包含了一个基础的词汇库页面（VocabularyPage），但目前仅有UI框架，缺乏实际功能实现。同时，首页（HomePage）也需要增加当前激活单词本的展示区域，以便用户了解当前学习的是哪个单词本以及每日学习设置。

## 需求分析

1. **单词本选择与初始化**：
   - 用户首次进入应用时，需要选择一个单词本
   - 选择单词本后，下载并保存单词到本地数据库
   - 设置该单词本的每日学习单词数量

2. **单词与单词本关联**：
   - 建立单词和单词本之间的关联关系
   - 每个单词可以属于一个或多个单词本

3. **学习功能与单词本绑定**：
   - 用户点击"开始学习"时，从已选单词本中获取单词
   - 每日学习单词数量基于用户为该单词本设置的数值

4. **复习功能与单词本绑定**：
   - 单词复习状态与特定单词本关联
   - 每个单词本有独立的复习计划和待复习单词列表

5. **词汇库页面功能完善**：
   - 展示所有可用的单词本列表
   - 支持下载新的单词本
   - 支持激活/切换当前使用的单词本
   - 显示单词本详细信息（单词数量、学习进度等）
   - 提供单词本设置功能（如每日学习单词数量）

6. **首页单词本信息展示**：
   - 在首页添加当前激活单词本的信息展示区域
   - 显示单词本名称、总单词数、学习进度等关键信息
   - 提供快速切换单词本的入口按钮，点击后跳转到词汇库页面
   - 每日学习单词数量设置与当前激活的单词本关联

7. **暂不实现**：
   - 自定义单词本功能（将在后期版本中实现）

## 现状分析

项目已有以下相关模型：

- `WordModel`：单词模型，包含单词的基本信息
- `WordBookModel`：词书模型，包含词书的基本信息
- `ReviewItemModel`：复习项模型，已与单词模型建立关联

目前这些模型相互独立，没有建立关联关系。学习和复习功能直接操作单词集合，不考虑单词本的存在。

项目已有的页面结构：

- `HomePage`：首页，显示学习进度和复习提醒，但缺少当前单词本信息
- `VocabularyPage`：词汇库页面，已有基础UI框架，但缺乏实际功能实现
- 学习和复习相关页面：需要修改以支持基于单词本的学习和复习

## 技术选择

本项目使用Isar作为本地数据库，相比传统关系型数据库：

- 不需要创建物理的中间表来实现多对多关系
- 可以使用`IsarLinks`来建立模型之间的多对多关联
- Isar会自动管理关系的存储和查询

### IsarLinks的关键优势

选择IsarLinks而非手动维护ID列表有以下重要优势：

1. **自动化的引用管理**：当使用`IsarLinks.add()`或`IsarLinks.remove()`时，Isar自动处理双向引用，确保数据一致性。

2. **高性能的关联查询**：IsarLinks提供优化的加载机制，如`myBook.words.load()`执行高效的批量查询，避免了N+1查询问题。

3. **类型安全**：`IsarLinks<WordModel>`明确表示这是指向WordModel对象的链接，编译器可进行类型检查。

4. **事务支持**：链接操作可在Isar事务中执行，保证数据一致性。

5. **内存优化**：Isar智能管理链接在内存中的加载，避免一次性加载大量数据。

## 实现方案

### 1. 数据模型修改

#### WordModel修改

```dart
@Collection()
class WordModel {
  Id id = Isar.autoIncrement;
  // 现有字段...
  
  // 添加与WordBookModel的多对多关系
  final wordBooks = IsarLinks<WordBookModel>();
}
```

#### WordBookModel修改

```dart
@Collection()
class WordBookModel {
  Id id = Isar.autoIncrement;
  // 现有字段...
  
  // 添加与WordModel的多对多关系
  final words = IsarLinks<WordModel>();
  
  // 添加学习计划相关字段
  int dailyNewWordsCount = 10; // 每天计划学习的新词数量
  DateTime? lastLearningDate; // 最后一次学习日期，用于重置每日计划
  bool isActive = false; // 是否为当前激活的单词本
}
```

#### ReviewItemModel优化

现有的ReviewItemModel已经包含了记录单词学习状态的核心功能，需要添加与词书的关联：

```dart
@Collection()
class ReviewItemModel {
  Id id = Isar.autoIncrement;
  
  // 使用IsarLink替代直接存储ID，提供更高效的加载机制
  final word = IsarLink<WordModel>();
  
  // 添加与词书的关联
  final wordBook = IsarLink<WordBookModel>();
  
  // SM2算法核心参数
  int repetitionCount = 0; // 成功复习次数
  double easeFactor = 2.5; // 难易度因子(初始值2.5)
  
  @Index() // 添加索引以便高效查询到期复习项
  late DateTime nextReviewDate; // 下次复习时间
  
  int previousInterval = 0; // 上次间隔天数(天)
  
  // 添加新词标记，便于管理学习状态
  bool isNewWord = true; // 默认为新词，学习后设为false
  
  // 更新构造函数，移除wordId参数
  ReviewItemModel({
    this.repetitionCount = 0,
    this.easeFactor = 2.5,
    required this.nextReviewDate,
    this.previousInterval = 0,
    this.isNewWord = true,
  });
  
  // 便捷工厂方法，创建新的复习项
  factory ReviewItemModel.create({
    required WordModel word,
    required WordBookModel wordBook,
    int repetitionCount = 0,
    double easeFactor = 2.5,
    int previousInterval = 0,
    DateTime? nextReviewDate,
    bool isNewWord = true,
  }) {
    final reviewItem = ReviewItemModel(
      repetitionCount: repetitionCount,
      easeFactor: easeFactor,
      previousInterval: previousInterval,
      nextReviewDate: nextReviewDate ?? DateTime.now(),
      isNewWord: isNewWord,
    );
    reviewItem.word.value = word; // 建立单词链接
    reviewItem.wordBook.value = wordBook; // 建立词书链接
    return reviewItem;
  }
}
```

### 2. 单词本选择和初始化流程

1. **首次启动应用或无激活单词本时**：
   - 展示单词本选择界面
   - 用户选择一个单词本后，下载并保存单词到数据库
   - 建立单词与单词本的关联关系
   - 将该单词本标记为激活状态（isActive = true）

2. **单词本设置流程**：
   - 用户设置该单词本的每日学习单词数量（dailyNewWordsCount）
   - 设置完成后，用户可开始学习该单词本中的单词

3. **激活的单词本信息持久化**：
   - 存储当前激活的单词本ID
   - 应用启动时，检查是否有激活的单词本
   - 如无激活单词本，引导用户选择单词本

### 3. 数据访问层修改

#### WordBookLocalProvider添加

```dart
class WordBookLocalProvider {
  final IsarService _isarService;
  final LogService _log;

  // 获取所有单词本
  Future<List<WordBookModel>> getAllWordBooks() async {
    // 实现代码...
  }

  // 获取当前激活的单词本
  Future<WordBookModel?> getActiveWordBook() async {
    try {
      return await _isarService.isar.wordBookModels
          .filter()
          .isActiveEqualTo(true)
          .findFirst();
    } catch (e) {
      _log.e('获取激活单词本失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：获取激活单词本失败',
        type: AppExceptionType.database,
      );
    }
  }

  // 设置激活的单词本
  Future<void> setActiveWordBook(int wordBookId) async {
    await _isarService.isar.writeTxn(() async {
      // 将所有单词本设置为非激活
      var allBooks = await _isarService.isar.wordBookModels.where().findAll();
      for (var book in allBooks) {
        book.isActive = false;
        await _isarService.isar.wordBookModels.put(book);
      }

      // 设置选中的单词本为激活状态
      var selectedBook = await _isarService.isar.wordBookModels.get(wordBookId);
      if (selectedBook != null) {
        selectedBook.isActive = true;
        await _isarService.isar.wordBookModels.put(selectedBook);
      }
    });
  }

  // 下载并保存单词本
  Future<WordBookModel> downloadAndSaveWordBook(int remoteId) async {
    // 实现代码...
  }

  // 更新单词本设置
  Future<void> updateWordBookSettings(int wordBookId, {int? dailyNewWordsCount}) async {
    // 实现代码...
  }
}
```

#### ReviewItemLocalProvider修改

```dart
// 添加查询指定单词本中待复习单词的方法
Future<List<ReviewItemModel>> getDueReviewItemsForWordBook(
  int wordBookId, 
  DateTime dateTime
) async {
  try {
    return await _isarService.isar.reviewItemModels
        .filter()
        .wordBook((q) => q.idEqualTo(wordBookId))
        .nextReviewDateLessThan(dateTime)
        .isNewWordEqualTo(false)
        .findAll();
  } catch (e) {
    _log.e('获取单词本待复习单词失败: $e', error: e);
    throw AppException.fromException(
      e,
      defaultMessage: '本地数据：获取单词本待复习单词失败',
      type: AppExceptionType.database,
    );
  }
}
```

### 4. 按需创建ReviewItemModel策略

我们将采用"按需创建ReviewItemModel"策略，而非在添加单词到词书时立即创建：

**优势：**
- 词书导入速度更快，特别是大型词库
- 节省存储空间，只为实际学习的单词创建记录
- 符合实际学习逻辑，未学习的单词不需要复习状态

**实现辅助方法：**

```dart
Future<ReviewItemModel> getOrCreateReviewItem(WordModel word, WordBookModel book) async {
  // 先尝试查找现有记录
  final existingItem = await isar.reviewItemModels
      .filter()
      .word((q) => q.idEqualTo(word.id))
      .wordBook((q) => q.idEqualTo(book.id))
      .findFirst();
      
  if (existingItem != null) {
    return existingItem;
  }
  
  // 不存在则创建新记录
  final newItem = ReviewItemModel.create(
    word: word,
    wordBook: book,
    nextReviewDate: DateTime.now(),
    isNewWord: true
  );
  
  // 保存并返回
  await isar.writeTxn(() async {
    await isar.reviewItemModels.put(newItem);
    await newItem.word.save();
    await newItem.wordBook.save();
  });
  
  return newItem;
}
```

### 5. 业务逻辑修改

#### HomeController修改

```dart
class HomeController extends GetxController {
  final WordBookRepository _wordBookRepository;
  final Rx<WordBookModel?> activeWordBook = Rx<WordBookModel?>(null);
  
  @override
  void onInit() {
    super.onInit();
    _loadActiveWordBook();
  }
  
  Future<void> _loadActiveWordBook() async {
    try {
      activeWordBook.value = await _wordBookRepository.getActiveWordBook();
    } catch (e) {
      // 错误处理...
    }
  }
  
  // 开始学习时检查是否有激活单词本
  Future<void> startLearning() async {
    if (activeWordBook.value == null) {
      // 引导用户选择单词本
      Get.toNamed(Routes.WORDBOOK_SELECTION);
    } else {
      // 开始学习
      Get.toNamed(Routes.LEARNING);
    }
  }
}
```

#### LearningController修改

```dart
class LearningController extends GetxController {
  // 修改获取今日学习单词的逻辑，基于激活的单词本
  Future<List<WordModel>> getTodayNewWords() async {
    final activeBook = await _wordBookRepository.getActiveWordBook();
    if (activeBook == null) {
      throw AppException('未选择单词本', type: AppExceptionType.business);
    }
    
    final DateTime today = DateTime.now().dateOnly(); // 去除时间部分，只保留日期
    
    // 检查是否需要重置每日计划
    if (activeBook.lastLearningDate == null || 
        activeBook.lastLearningDate!.dateOnly().isBefore(today)) {
      // 重置单词本的每日计划
      await _wordBookRepository.updateLastLearningDate(activeBook.id, today);
    }
    
    // 从激活的单词本中获取未学习的单词
    return await _wordRepository.getNewWordsFromWordBook(
      activeBook.id, 
      activeBook.dailyNewWordsCount
    );
  }
}
```

#### ReviewController修改

```dart
class ReviewController extends GetxController {
  // 修改获取待复习单词的逻辑，基于激活的单词本
  Future<List<WordModel>> getDueReviewWords() async {
    final activeBook = await _wordBookRepository.getActiveWordBook();
    if (activeBook == null) {
      throw AppException('未选择单词本', type: AppExceptionType.business);
    }
    
    final DateTime now = DateTime.now();
    
    // 从激活的单词本中获取待复习的单词
    final reviewItems = await _reviewItemRepository.getDueReviewItemsForWordBook(
      activeBook.id,
      now
    );
    
    // 提取单词ID
    final wordIds = reviewItems.map((item) => item.word.value!.id).toList();
    
    // 获取单词详情
    return await _wordRepository.getWordsByIds(wordIds);
  }
}
```

## 实施步骤

1. **数据模型修改**
   - 为WordModel和WordBookModel添加IsarLinks关联关系
   - 修改WordBookModel，添加isActive、dailyNewWordsCount和lastLearningDate字段
   - 优化ReviewItemModel，添加与WordBookModel的关联，移除直接存储的ID
   - 运行`flutter pub run build_runner build`生成更新后的Isar代码

2. **数据访问层实现**
   - 实现WordBookLocalProvider和WordBookRepository，包含单词本下载、激活和设置功能
   - 修改ReviewItemLocalProvider，添加按单词本查询待复习项的方法
   - 修改WordRepository，添加从特定单词本获取单词的方法

3. **单词本选择和初始化功能**
   - 创建单词本选择页面
   - 实现单词本下载和设置功能
   - 实现单词本激活状态的管理

4. **业务逻辑修改**
   - 修改HomeController，添加检查激活单词本的逻辑
   - 修改LearningController，基于激活单词本获取学习内容
   - 修改ReviewController，基于激活单词本获取复习内容
   - 实现按需创建ReviewItemModel的辅助方法

5. **UI实现**
   - 创建单词本选择页面
   - 创建单词本设置页面
   - 修改Home页面，显示当前激活的单词本信息
   - 更新学习和复习页面，确保显示正确的单词本信息

## 预期结果

1. 用户首次使用应用时，需要选择一个单词本并设置每日学习单词数量
2. 单词与所选单词本建立关联，学习和复习功能基于当前激活的单词本
3. 用户可以随时切换激活的单词本，学习和复习状态会相应变化
4. 每个单词本有独立的学习进度和复习计划
5. 导入单词本时性能良好，不会因为词库过大而卡顿

## 注意事项

1. 修改数据模型时需确保向后兼容，避免丢失现有数据
2. 测试多对多关系的正确性，特别是单词在多个词书中的情况
3. 注意性能优化，特别是大量单词和词书时的查询性能
4. 确保按需创建ReviewItemModel的逻辑在各处一致，避免逻辑冲突
5. 当前版本暂不实现自定义单词本功能，将在后续版本中添加

## 性能优化策略

在实现单词本功能时，需要特别注意性能优化，尤其是在处理大量数据时。以下是针对关键操作的优化建议：

### 1. 高效获取学习单词流程

从大量单词（可能达到10万级别）中获取特定单词本的未学习单词，是一个潜在的性能瓶颈。以下是优化后的实现方案：

```dart
Future<List<WordModel>> getTodayNewWords() async {
  try {
    // 1. 获取激活的用户单词本
    final activeUserWordBook = await _userWordBookRepository.getActiveUserWordBook();
    if (activeUserWordBook == null) {
      throw AppException('未选择单词本', type: AppExceptionType.business);
    }
    
    // 2. 检查是否需要重置每日计划
    final DateTime today = DateTime.now().dateOnly(); // 去除时间部分
    if (activeUserWordBook.lastLearningDate == null || 
        activeUserWordBook.lastLearningDate!.dateOnly().isBefore(today)) {
      // 重置单词本的每日计划
      await _userWordBookRepository.updateLastLearningDate(activeUserWordBook.id, today);
    }
    
    // 3. 获取已学习的单词ID列表
    final learnedWordIds = await _reviewItemRepository.getLearnedWordIdsFromUserWordBook(
      activeUserWordBook.id
    );
    
    // 4. 关键优化点：一次查询完成所有过滤
    final candidateWords = await _isarService.isar.wordModels
        .filter()
        .wordBooks((q) => q.idEqualTo(activeUserWordBook.wordBookId)) // 属于特定单词本
        .idNotIn(learnedWordIds) // 未学习的单词
        .limit(activeUserWordBook.dailyNewWordsCount * 3) // 获取所需数量的3倍作为候选
        .findAll();
    
    // 5. 如果候选单词不足，返回所有可用单词
    if (candidateWords.length <= activeUserWordBook.dailyNewWordsCount) {
      return candidateWords;
    }
    
    // 6. 随机选择指定数量的单词
    candidateWords.shuffle();
    return candidateWords.take(activeUserWordBook.dailyNewWordsCount).toList();
  } catch (e) {
    _log.e('获取今日学习单词失败: $e', error: e);
    rethrow;
  }
}
```

**性能优化要点**：
- 使用Isar的链接查询直接过滤属于特定单词本的单词
- 在数据库层面排除已学习的单词，避免加载不必要的数据
- 限制查询结果数量，减少数据传输和处理量
- 使用Set进行O(1)的包含检查，避免O(n²)的操作

### 2. 复习单词获取优化

类似地，获取待复习单词时也应采用高效的查询方式：

```dart
Future<List<WordModel>> getDueReviewWords() async {
  try {
    // 1. 获取激活的用户单词本
    final activeUserWordBook = await _userWordBookRepository.getActiveUserWordBook();
    if (activeUserWordBook == null) {
      throw AppException('未选择单词本', type: AppExceptionType.business);
    }
    
    // 2. 关键优化点：直接查询符合条件的复习项
    final dueReviewItems = await _isarService.isar.reviewItemModels
        .filter()
        .userWordBook((q) => q.idEqualTo(activeUserWordBook.id)) // 关联到当前用户单词本
        .nextReviewDateLessThan(DateTime.now()) // 到期复习
        .isNewWordEqualTo(false) // 非新词
        .findAll();
    
    // 3. 高效获取关联的单词
    final wordIds = dueReviewItems
        .map((item) => item.word.value?.id)
        .where((id) => id != null)
        .cast<int>()
        .toList();
    
    if (wordIds.isEmpty) {
      return [];
    }
    
    // 4. 批量获取单词详情
    return await _isarService.isar.wordModels
        .filter()
        .anyOf(wordIds, (q, id) => q.idEqualTo(id))
        .findAll();
  } catch (e) {
    _log.e('获取待复习单词失败: $e', error: e);
    rethrow;
  }
}
```

### 3. 缓存策略实现

为进一步提升性能，可以实现缓存机制：

```dart
class WordRepository {
  // 缓存字段
  final Map<String, List<WordModel>> _todayNewWordsCache = {};
  final Map<String, List<WordModel>> _dueReviewWordsCache = {};
  
  // 获取今日学习单词（带缓存）
  Future<List<WordModel>> getTodayNewWords() async {
    final activeUserWordBook = await _userWordBookRepository.getActiveUserWordBook();
    if (activeUserWordBook == null) {
      throw AppException('未选择单词本', type: AppExceptionType.business);
    }
    
    // 生成缓存键
    final cacheKey = '${activeUserWordBook.id}-${DateTime.now().dateOnly()}';
    
    // 检查缓存
    if (_todayNewWordsCache.containsKey(cacheKey)) {
      return _todayNewWordsCache[cacheKey]!;
    }
    
    // 原有获取逻辑...
    
    // 保存到缓存
    _todayNewWordsCache[cacheKey] = result;
    return result;
  }
  
  // 缓存清理方法
  void clearCaches() {
    _todayNewWordsCache.clear();
    _dueReviewWordsCache.clear();
  }
}
```

**缓存优化要点**：
- 使用日期作为缓存键的一部分，确保每天刷新缓存
- 在用户切换单词本或修改设置时清除缓存
- 考虑设置缓存过期时间，避免内存占用过多

### 4. 按需创建ReviewItemModel的优化实现

```dart
Future<ReviewItemModel> getOrCreateReviewItem(WordModel word, UserWordBookModel userWordBook) async {
  try {
    // 1. 使用复合条件一次查询
    final existingItem = await _isarService.isar.reviewItemModels
        .filter()
        .word((q) => q.idEqualTo(word.id))
        .userWordBook((q) => q.idEqualTo(userWordBook.id))
        .findFirst();
        
    if (existingItem != null) {
      return existingItem;
    }
    
    // 2. 创建新的复习项
    final newItem = ReviewItemModel();
    newItem.nextReviewDate = DateTime.now();
    newItem.isNewWord = true;
    
    // 3. 在单个事务中完成所有操作
    await _isarService.isar.writeTxn(() async {
      // 保存复习项
      await _isarService.isar.reviewItemModels.put(newItem);
      
      // 建立关联
      newItem.word.value = word;
      newItem.userWordBook.value = userWordBook;
      
      // 保存关联
      await newItem.word.save();
      await newItem.userWordBook.save();
    });
    
    return newItem;
  } catch (e) {
    _log.e('获取或创建复习项失败: $e', error: e);
    throw AppException.fromException(e);
  }
}
```

**优化要点**：
- 使用单个事务完成所有操作，确保数据一致性
- 避免多次数据库交互，减少开销
- 使用复合条件查询，提高查询效率

### 5. 单词本数据导入优化

导入大量单词时，应采用批量操作以提高效率：

```dart
Future<void> importWordsToWordBook(List<WordModel> words, WordBookModel wordBook) async {
  try {
    await _isarService.isar.writeTxn(() async {
      // 1. 批量保存单词
      await _isarService.isar.wordModels.putAll(words);
      
      // 2. 建立关联关系
      for (var word in words) {
        word.wordBooks.add(wordBook);
      }
      
      // 3. 批量保存关联
      for (var word in words) {
        await word.wordBooks.save();
      }
      
      // 4. 更新单词本的单词数量
      wordBook.wordCount = (await wordBook.words.count()) + words.length;
      await _isarService.isar.wordBookModels.put(wordBook);
    });
  } catch (e) {
    _log.e('导入单词失败: $e', error: e);
    throw AppException.fromException(e);
  }
}
```

**优化要点**：
- 使用putAll批量保存单词，而非逐个保存
- 在单个事务中完成所有操作，减少数据库交互
- 更新单词数量统计，避免后续需要重新计算

## 性能测试基准

在典型场景下，优化后的性能表现应该达到以下基准：

- **获取今日学习单词**：20-60ms（10万单词库中查找20个未学习单词）
- **获取待复习单词**：15-50ms（从数千个复习项中查找到期项）
- **缓存命中时**：<1ms（几乎无延迟）

这些性能指标在大多数移动设备上都能提供流畅的用户体验，不会造成明显的UI延迟。

## 注意事项补充

1. **索引优化**：
   - 为ReviewItemModel.nextReviewDate添加索引，加速复习查询
   - 为UserWordBookModel.status添加索引，快速查找激活的单词本

2. **批量加载**：
   - 使用Isar的批量API，如findAll()、putAll()等
   - 避免循环中执行数据库操作

3. **事务使用**：
   - 将相关操作组合在单个事务中执行
   - 减少事务数量，每个事务都有开销

4. **内存管理**：
   - 避免一次加载过多数据到内存
   - 使用limit()限制查询结果数量
   - 定期清理不再需要的缓存

5. **异步加载**：
   - 考虑在应用启动时预加载常用数据
   - 使用异步操作避免阻塞UI线程

### 5. UI实现

#### 词汇库页面（VocabularyPage）完善

词汇库页面是单词本功能的核心页面，需要完善以下功能：

```dart
class VocabularyPage extends GetView<VocabularyController> {
  const VocabularyPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('词汇库'),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              // 添加单词本功能（后期版本实现）
              Get.snackbar('提示', '自定义单词本功能将在后续版本中推出');
            },
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }
        
        if (controller.wordBooks.isEmpty) {
          return const Center(child: Text('暂无可用单词本，请点击右上角添加'));
        }
        
        return ListView.builder(
          itemCount: controller.wordBooks.length,
          itemBuilder: (context, index) {
            final book = controller.wordBooks[index];
            final isActive = controller.activeWordBookId.value == book.id;
            
            return Card(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              elevation: isActive ? 4 : 1,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: isActive 
                    ? BorderSide(color: Theme.of(context).primaryColor, width: 2)
                    : BorderSide.none,
              ),
              child: ListTile(
                contentPadding: const EdgeInsets.all(16),
                leading: Icon(
                  book.isCustom ? Icons.edit : Icons.library_books,
                  color: book.isCustom ? Colors.blue : Colors.green,
                  size: 36,
                ),
                title: Text(
                  book.name,
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('${book.wordCount}个单词'),
                    if (isActive)
                      Container(
                        margin: const EdgeInsets.only(top: 8),
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Text('当前使用', style: TextStyle(fontSize: 12)),
                      ),
                  ],
                ),
                trailing: isActive
                    ? const Icon(Icons.check_circle, color: Colors.green)
                    : TextButton(
                        child: const Text('使用'),
                        onPressed: () => controller.activateWordBook(book.id),
                      ),
                onTap: () {
                  // 进入单词本详情页
                  controller.navigateToWordBookDetail(book);
                },
              ),
            );
          },
        );
      }),
      bottomNavigationBar: Obx(() {
        if (controller.isDownloading.value) {
          return Container(
            height: 50,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            alignment: Alignment.center,
            child: Row(
              children: [
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                const SizedBox(width: 16),
                Text('下载中: ${controller.downloadProgress.value}%'),
              ],
            ),
          );
        }
        return const SizedBox.shrink();
      }),
    );
  }
}
```

#### 首页单词本信息展示区域

在首页（HomePage）添加单词本信息展示区域：

```dart
// 在HomePage的build方法中添加
Widget _buildActiveWordBookCard() {
  return Obx(() {
    final activeWordBook = controller.activeWordBook.value;
    
    if (activeWordBook == null) {
      return Card(
        margin: const EdgeInsets.only(bottom: 24),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '未选择单词本',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text('请先选择一个单词本开始学习'),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => Get.toNamed(Routes.VOCABULARY),
                  child: const Text('选择单词本'),
                ),
              ),
            ],
          ),
        ),
      );
    }
    
    return Card(
      margin: const EdgeInsets.only(bottom: 24),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.book, color: Colors.blue),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    activeWordBook.name,
                    style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.swap_horiz),
                  tooltip: '切换单词本',
                  onPressed: () => Get.toNamed(Routes.VOCABULARY),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text('总单词数: ${activeWordBook.wordCount}'),
            Text('已学习: ${controller.learnedWordCount.value}/${activeWordBook.wordCount}'),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: activeWordBook.wordCount > 0
                  ? controller.learnedWordCount.value / activeWordBook.wordCount
                  : 0,
              backgroundColor: Colors.grey[200],
              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
            ),
          ],
        ),
      ),
    );
  });
}
```

#### 单词本详情页面

新增单词本详情页面，展示单词本的详细信息和设置：

```dart
class WordBookDetailPage extends GetView<WordBookDetailController> {
  const WordBookDetailPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(() => Text(controller.wordBook.value?.name ?? '单词本详情')),
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }
        
        final wordBook = controller.wordBook.value;
        if (wordBook == null) {
          return const Center(child: Text('单词本不存在'));
        }
        
        return ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // 单词本信息卡片
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      wordBook.name,
                      style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text(wordBook.description),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text('总单词数'),
                              Text(
                                '${wordBook.wordCount}',
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text('已学习'),
                              Text(
                                '${controller.learnedWordCount.value}',
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text('完成度'),
                              Text(
                                '${(controller.learnedWordCount.value / wordBook.wordCount * 100).toStringAsFixed(1)}%',
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // 每日学习设置
            const Text(
              '学习设置',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('每日学习新单词数量'),
                    const SizedBox(height: 8),
                    Slider(
                      value: controller.dailyNewWordsCount.value.toDouble(),
                      min: 5,
                      max: 50,
                      divisions: 45,
                      label: controller.dailyNewWordsCount.value.toString(),
                      onChanged: (value) => 
                          controller.setDailyNewWordsCount(value.round()),
                    ),
                    Center(
                      child: Text(
                        '${controller.dailyNewWordsCount.value} 个单词/天',
                        style: const TextStyle(fontSize: 16),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // 操作按钮
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: controller.isActive.value
                        ? null
                        : () => controller.activateWordBook(),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: Text(
                      controller.isActive.value ? '当前使用中' : '设为当前单词本',
                    ),
                  ),
                ),
              ],
            ),
          ],
        );
      }),
    );
  }
}
```

### 6. 路由配置更新

需要更新应用的路由配置，添加单词本相关页面：

```dart
class AppRoutes {
  // 现有路由
  static const HOME = '/home';
  static const LEARNING = '/learning';
  // 新增路由
  static const VOCABULARY = '/vocabulary';
  static const WORDBOOK_DETAIL = '/wordbook-detail';
  static const WORDBOOK_SETTINGS = '/wordbook-settings';
}

class AppPages {
  static final routes = [
    // 现有路由
    GetPage(
      name: AppRoutes.HOME,
      page: () => const HomePage(),
      binding: HomeBinding(),
    ),
    // 新增路由
    GetPage(
      name: AppRoutes.VOCABULARY,
      page: () => const VocabularyPage(),
      binding: VocabularyBinding(),
    ),
    GetPage(
      name: AppRoutes.WORDBOOK_DETAIL,
      page: () => const WordBookDetailPage(),
      binding: WordBookDetailBinding(),
    ),
    GetPage(
      name: AppRoutes.WORDBOOK_SETTINGS,
      page: () => const WordBookSettingsPage(),
      binding: WordBookSettingsBinding(),
    ),
  ];
}
```

### 7. 应用启动流程修改

需要修改应用启动流程，检查是否有激活的单词本：

```dart
class SplashController extends GetxController {
  final WordBookRepository _wordBookRepository;
  
  SplashController(this._wordBookRepository);
  
  @override
  void onInit() {
    super.onInit();
    _checkInitialRoute();
  }
  
  Future<void> _checkInitialRoute() async {
    try {
      // 检查是否有激活的单词本
      final activeWordBook = await _wordBookRepository.getActiveWordBook();
      
      // 延迟一段时间显示启动页
      await Future.delayed(const Duration(seconds: 1));
      
      if (activeWordBook == null) {
        // 没有激活的单词本，跳转到词汇库页面
        Get.offAllNamed(AppRoutes.VOCABULARY);
      } else {
        // 有激活的单词本，跳转到首页
        Get.offAllNamed(AppRoutes.HOME);
      }
    } catch (e) {
      // 出错时默认跳转到首页
      Get.offAllNamed(AppRoutes.HOME);
    }
  }
}
```

### 8. 控制器实现

#### VocabularyController 完善

需要完善 VocabularyController，添加单词本激活、下载和详情页导航等功能：

```dart
class VocabularyController extends GetxController {
  final LogService _logService;
  final WordBookRepository _wordBookRepository;
  final UserPreferenceRepository _preferenceRepository;
  
  VocabularyController({
    required LogService logService,
    required WordBookRepository wordBookRepository,
    required UserPreferenceRepository preferenceRepository,
  }) : _logService = logService,
       _wordBookRepository = wordBookRepository,
       _preferenceRepository = preferenceRepository;
  
  // 可观察变量
  final RxList<WordBookModel> wordBooks = <WordBookModel>[].obs;
  final RxBool isLoading = true.obs;
  final RxBool isDownloading = false.obs;
  final RxInt downloadProgress = 0.obs;
  final RxString activeWordBookId = ''.obs;
  
  @override
  void onInit() {
    super.onInit();
    loadWordBooks();
    loadActiveWordBookId();
  }
  
  // 加载单词本列表
  Future<void> loadWordBooks() async {
    try {
      isLoading.value = true;
      final books = await _wordBookRepository.getAllWordBooks();
      wordBooks.value = books;
    } catch (e) {
      _logService.error('加载单词本失败: $e');
      Get.snackbar('错误', '加载单词本失败，请重试');
    } finally {
      isLoading.value = false;
    }
  }
  
  // 加载当前激活的单词本ID
  Future<void> loadActiveWordBookId() async {
    try {
      final id = await _preferenceRepository.getActiveWordBookId();
      if (id != null && id.isNotEmpty) {
        activeWordBookId.value = id;
      }
    } catch (e) {
      _logService.error('加载激活单词本ID失败: $e');
    }
  }
  
  // 激活单词本
  Future<void> activateWordBook(String wordBookId) async {
    try {
      // 检查单词本是否已下载
      final wordBook = wordBooks.firstWhere((book) => book.id == wordBookId);
      
      if (!wordBook.isDownloaded) {
        // 如果单词本未下载，先下载
        await downloadWordBook(wordBook);
      }
      
      // 设置为活动单词本
      await _preferenceRepository.setActiveWordBookId(wordBookId);
      activeWordBookId.value = wordBookId;
      
      Get.snackbar('成功', '已将 "${wordBook.name}" 设为当前单词本');
      
      // 如果是从首页来的，返回首页
      if (Get.previousRoute == AppRoutes.HOME) {
        Get.back();
      }
    } catch (e) {
      _logService.error('激活单词本失败: $e');
      Get.snackbar('错误', '激活单词本失败，请重试');
    }
  }
  
  // 下载单词本
  Future<void> downloadWordBook(WordBookModel wordBook) async {
    if (isDownloading.value) {
      Get.snackbar('提示', '正在下载中，请稍后再试');
      return;
    }
    
    try {
      isDownloading.value = true;
      downloadProgress.value = 0;
      
      // 模拟下载进度
      final timer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
        if (downloadProgress.value < 99) {
          downloadProgress.value += 1;
        }
      });
      
      // 实际下载逻辑
      await _wordBookRepository.downloadWordBook(
        wordBook.id,
        onProgress: (progress) {
          downloadProgress.value = (progress * 100).round();
        },
      );
      
      timer.cancel();
      downloadProgress.value = 100;
      
      // 更新单词本状态
      final index = wordBooks.indexWhere((book) => book.id == wordBook.id);
      if (index >= 0) {
        final updatedBook = wordBook.copyWith(isDownloaded: true);
        wordBooks[index] = updatedBook;
      }
      
      Get.snackbar('成功', '单词本 "${wordBook.name}" 下载完成');
    } catch (e) {
      _logService.error('下载单词本失败: $e');
      Get.snackbar('错误', '下载单词本失败，请重试');
    } finally {
      isDownloading.value = false;
    }
  }
  
  // 导航到单词本详情页
  void navigateToWordBookDetail(WordBookModel wordBook) {
    Get.toNamed(
      AppRoutes.WORDBOOK_DETAIL,
      arguments: {'wordBookId': wordBook.id},
    );
  }
}
```

#### WordBookDetailController 实现

新增 WordBookDetailController 控制单词本详情页面：

```dart
class WordBookDetailController extends GetxController {
  final LogService _logService;
  final WordBookRepository _wordBookRepository;
  final UserPreferenceRepository _preferenceRepository;
  final WordRepository _wordRepository;
  
  WordBookDetailController({
    required LogService logService,
    required WordBookRepository wordBookRepository,
    required UserPreferenceRepository preferenceRepository,
    required WordRepository wordRepository,
  }) : _logService = logService,
       _wordBookRepository = wordBookRepository,
       _preferenceRepository = preferenceRepository,
       _wordRepository = wordRepository;
  
  // 可观察变量
  final Rx<WordBookModel?> wordBook = Rx<WordBookModel?>(null);
  final RxBool isLoading = true.obs;
  final RxBool isActive = false.obs;
  final RxInt learnedWordCount = 0.obs;
  final RxInt dailyNewWordsCount = 20.obs;
  
  late String wordBookId;
  
  @override
  void onInit() {
    super.onInit();
    
    // 从参数中获取单词本ID
    final args = Get.arguments;
    if (args != null && args['wordBookId'] != null) {
      wordBookId = args['wordBookId'];
      loadWordBookDetails();
      loadUserPreferences();
    } else {
      _logService.error('未提供单词本ID');
      Get.back();
    }
  }
  
  // 加载单词本详情
  Future<void> loadWordBookDetails() async {
    try {
      isLoading.value = true;
      
      // 加载单词本信息
      final book = await _wordBookRepository.getWordBookById(wordBookId);
      if (book == null) {
        Get.snackbar('错误', '未找到单词本信息');
        Get.back();
        return;
      }
      
      wordBook.value = book;
      
      // 检查是否为当前激活的单词本
      final activeId = await _preferenceRepository.getActiveWordBookId();
      isActive.value = activeId == wordBookId;
      
      // 加载已学习单词数量
      final learnedCount = await _wordRepository.getLearnedWordCountByBookId(wordBookId);
      learnedWordCount.value = learnedCount;
    } catch (e) {
      _logService.error('加载单词本详情失败: $e');
      Get.snackbar('错误', '加载单词本详情失败');
    } finally {
      isLoading.value = false;
    }
  }
  
  // 加载用户偏好设置
  Future<void> loadUserPreferences() async {
    try {
      // 加载每日学习单词数量
      final count = await _preferenceRepository.getDailyNewWordsCount();
      if (count != null) {
        dailyNewWordsCount.value = count;
      }
    } catch (e) {
      _logService.error('加载用户偏好设置失败: $e');
    }
  }
  
  // 设置每日学习新单词数量
  Future<void> setDailyNewWordsCount(int count) async {
    try {
      dailyNewWordsCount.value = count;
      await _preferenceRepository.setDailyNewWordsCount(count);
    } catch (e) {
      _logService.error('设置每日学习单词数量失败: $e');
    }
  }
  
  // 激活单词本
  Future<void> activateWordBook() async {
    try {
      if (wordBook.value == null) return;
      
      await _preferenceRepository.setActiveWordBookId(wordBookId);
      isActive.value = true;
      
      Get.snackbar('成功', '已将 "${wordBook.value!.name}" 设为当前单词本');
    } catch (e) {
      _logService.error('激活单词本失败: $e');
      Get.snackbar('错误', '激活单词本失败，请重试');
    }
  }
}
```

### 9. 数据层实现

#### WordBookRepository 实现

```dart
class WordBookRepository {
  final DatabaseService _databaseService;
  final NetworkService _networkService;
  final LocalStorageService _localStorageService;
  
  WordBookRepository({
    required DatabaseService databaseService,
    required NetworkService networkService,
    required LocalStorageService localStorageService,
  }) : _databaseService = databaseService,
       _networkService = networkService,
       _localStorageService = localStorageService;
  
  // 获取所有单词本
  Future<List<WordBookModel>> getAllWordBooks() async {
    try {
      // 先从本地数据库获取
      final localBooks = await _databaseService.getWordBooks();
      
      if (localBooks.isEmpty) {
        // 如果本地没有，从网络获取
        final remoteBooks = await _networkService.fetchWordBooks();
        
        // 保存到本地数据库
        await _databaseService.saveWordBooks(remoteBooks);
        
        return remoteBooks;
      }
      
      return localBooks;
    } catch (e) {
      // 出错时返回默认单词本
      return [
        WordBookModel(
          id: 'default',
          wordBookId: 'default',
          name: '编程基础词汇',
          description: '包含编程入门必备的基础词汇',
          wordCount: 1000,
          isCustom: false,
          isDownloaded: true,
        ),
        WordBookModel(
          id: 'advanced',
          wordBookId: 'advanced',
          name: '编程进阶词汇',
          description: '包含编程进阶所需的专业词汇',
          wordCount: 2000,
          isCustom: false,
          isDownloaded: false,
        ),
      ];
    }
  }
  
  // 根据ID获取单词本
  Future<WordBookModel?> getWordBookById(String id) async {
    try {
      return await _databaseService.getWordBookById(id);
    } catch (e) {
      // 如果出错，尝试从所有单词本中查找
      final allBooks = await getAllWordBooks();
      return allBooks.firstWhereOrNull((book) => book.id == id);
    }
  }
  
  // 获取当前激活的单词本
  Future<WordBookModel?> getActiveWordBook() async {
    try {
      final activeId = await _localStorageService.getString('active_wordbook_id');
      if (activeId == null || activeId.isEmpty) return null;
      
      return await getWordBookById(activeId);
    } catch (e) {
      return null;
    }
  }
  
  // 下载单词本
  Future<void> downloadWordBook(String wordBookId, {Function(double)? onProgress}) async {
    try {
      // 获取单词本信息
      final wordBook = await getWordBookById(wordBookId);
      if (wordBook == null) throw Exception('单词本不存在');
      
      // 下载单词本中的单词
      final words = await _networkService.downloadWordBookWords(
        wordBookId,
        onProgress: onProgress,
      );
      
      // 保存单词到本地数据库
      await _databaseService.saveWords(words);
      
      // 更新单词本下载状态
      final updatedWordBook = wordBook.copyWith(isDownloaded: true);
      await _databaseService.updateWordBook(updatedWordBook);
    } catch (e) {
      throw Exception('下载单词本失败: $e');
    }
  }
}
```

#### UserPreferenceRepository 实现

```dart
class UserPreferenceRepository {
  final LocalStorageService _localStorageService;
  
  UserPreferenceRepository({
    required LocalStorageService localStorageService,
  }) : _localStorageService = localStorageService;
  
  // 获取当前激活的单词本ID
  Future<String?> getActiveWordBookId() async {
    return await _localStorageService.getString('active_wordbook_id');
  }
  
  // 设置当前激活的单词本ID
  Future<void> setActiveWordBookId(String id) async {
    await _localStorageService.setString('active_wordbook_id', id);
  }
  
  // 获取每日学习新单词数量
  Future<int?> getDailyNewWordsCount() async {
    final value = await _localStorageService.getInt('daily_new_words_count');
    return value ?? 20; // 默认每日20个新单词
  }
  
  // 设置每日学习新单词数量
  Future<void> setDailyNewWordsCount(int count) async {
    await _localStorageService.setInt('daily_new_words_count', count);
  }
}
```

#### WordRepository 修改

需要修改 WordRepository，添加与单词本相关的方法：

```dart
class WordRepository {
  final DatabaseService _databaseService;
  
  WordRepository({
    required DatabaseService databaseService,
  }) : _databaseService = databaseService;
  
  // 获取指定单词本中的单词总数
  Future<int> getWordCountByBookId(String wordBookId) async {
    return await _databaseService.getWordCountByBookId(wordBookId);
  }
  
  // 获取指定单词本中已学习的单词数量
  Future<int> getLearnedWordCountByBookId(String wordBookId) async {
    return await _databaseService.getLearnedWordCountByBookId(wordBookId);
  }
  
  // 获取指定单词本中待学习的单词
  Future<List<WordModel>> getWordsToLearn(String wordBookId, int limit) async {
    return await _databaseService.getWordsToLearn(wordBookId, limit);
  }
  
  // 获取指定单词本中待复习的单词
  Future<List<WordModel>> getWordsToReview(String wordBookId) async {
    return await _databaseService.getWordsToReview(wordBookId);
  }
}
```

### 10. 数据库表结构修改

需要修改数据库表结构，添加单词本相关的表和字段：

```dart
// 单词本表
final wordBooksTable = '''
CREATE TABLE IF NOT EXISTS word_books (
  id TEXT PRIMARY KEY,
  word_book_id TEXT NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  word_count INTEGER NOT NULL DEFAULT 0,
  is_custom INTEGER NOT NULL DEFAULT 0,
  is_downloaded INTEGER NOT NULL DEFAULT 0,
  created_at INTEGER NOT NULL,
  updated_at INTEGER NOT NULL
)
''';

// 单词表添加单词本ID字段
final wordsTable = '''
CREATE TABLE IF NOT EXISTS words (
  id TEXT PRIMARY KEY,
  word TEXT NOT NULL,
  phonetic TEXT,
  definition TEXT NOT NULL,
  translation TEXT NOT NULL,
  example TEXT,
  example_translation TEXT,
  word_book_id TEXT NOT NULL,
  difficulty INTEGER NOT NULL DEFAULT 0,
  status INTEGER NOT NULL DEFAULT 0,
  created_at INTEGER NOT NULL,
  updated_at INTEGER NOT NULL,
  FOREIGN KEY (word_book_id) REFERENCES word_books (id) ON DELETE CASCADE
)
''';
```

### 11. 依赖注入配置

需要更新依赖注入配置，添加单词本相关的服务和控制器：

```dart
class AppBinding extends Bindings {
  @override
  void dependencies() {
    // 服务
    Get.lazyPut<DatabaseService>(() => DatabaseService(), fenix: true);
    Get.lazyPut<NetworkService>(() => NetworkService(), fenix: true);
    Get.lazyPut<LocalStorageService>(() => LocalStorageService(), fenix: true);
    Get.lazyPut<LogService>(() => LogService(), fenix: true);
    
    // 数据仓库
    Get.lazyPut<WordRepository>(
      () => WordRepository(databaseService: Get.find()),
      fenix: true,
    );
    Get.lazyPut<WordBookRepository>(
      () => WordBookRepository(
        databaseService: Get.find(),
        networkService: Get.find(),
        localStorageService: Get.find(),
      ),
      fenix: true,
    );
    Get.lazyPut<UserPreferenceRepository>(
      () => UserPreferenceRepository(localStorageService: Get.find()),
      fenix: true,
    );
  }
}

class VocabularyBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<VocabularyController>(
      () => VocabularyController(
        logService: Get.find(),
        wordBookRepository: Get.find(),
        preferenceRepository: Get.find(),
      ),
    );
  }
}

class WordBookDetailBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<WordBookDetailController>(
      () => WordBookDetailController(
        logService: Get.find(),
        wordBookRepository: Get.find(),
        preferenceRepository: Get.find(),
        wordRepository: Get.find(),
      ),
    );
  }
}
```

### 12. 实施计划

1. **数据库结构修改**：
   - 创建单词本表
   - 修改单词表，添加单词本ID字段
   - 更新数据库迁移脚本

2. **数据层实现**：
   - 实现 WordBookRepository
   - 修改 WordRepository，添加单词本相关方法
   - 实现 UserPreferenceRepository

3. **控制器实现**：
   - 完善 VocabularyController
   - 实现 WordBookDetailController
   - 修改 HomeController，添加单词本相关功能

4. **UI实现**：
   - 完善 VocabularyPage
   - 实现 WordBookDetailPage
   - 修改 HomePage，添加单词本信息展示区域

5. **路由配置更新**：
   - 添加单词本相关页面路由
   - 更新应用启动流程

6. **测试与优化**：
   - 单元测试
   - 集成测试
   - 性能优化

### 13. 总结

单词本功能的实现将显著提升应用的学习体验，通过将单词组织到不同的单词本中，用户可以根据自己的学习需求和水平选择合适的单词本进行学习。同时，单词本功能也为后续自定义单词本、单词本分享等功能提供了基础。

在实现过程中，需要注意以下几点：

1. 数据一致性：确保单词与单词本之间的关系正确无误
2. 用户体验：提供流畅的单词本切换和管理体验
3. 性能优化：针对大量单词的情况进行优化，确保应用响应迅速
4. 离线支持：支持单词本的离线使用，提高应用可用性
5. 数据同步：在多设备间同步单词本数据，保持学习进度一致




Provider层开发计划
首先，我将分析需要新建和修改的文件，然后为每个步骤提供详细说明。
1. 需要新建的文件
lib/data/providers/word_book/word_book_local_provider.dart
原因：需要实现获取所有单词本、激活单词本等功能
内容：用于与本地数据库交互，管理WordBookModel的CRUD操作
lib/data/providers/word_book/word_book_remote_provider.dart
原因：需要实现从远程获取和下载单词本功能
内容：实现与远程API交互获取单词本数据的方法
lib/data/providers/user_preference/user_preference_local_provider.dart
原因：需要实现保存用户偏好设置的功能
内容：用于与本地数据库交互，管理用户偏好相关的操作
lib/data/providers/user_word_book/user_word_book_local_provider.dart
原因：管理用户和单词本之间的关联，包括激活单词本、获取学习进度等
内容：实现与UserWordBookModel相关的CRUD操作
lib/data/repositories/word_book_repository.dart
原因：统一管理单词本相关的业务逻辑，协调本地和远程数据源
内容：提供获取、下载、激活单词本的高级功能
lib/data/repositories/user_preference_repository.dart
原因：统一管理用户偏好设置的业务逻辑
内容：提供保存和获取用户偏好设置的方法
lib/data/repositories/user_word_book_repository.dart
原因：处理用户与单词本关联的业务逻辑
内容：提供设置激活单词本、获取学习进度等功能
2. 需要修改的文件
lib/data/providers/review_item/review_item_local_provider.dart
修改原因：添加按单词本查询待复习项的方法
修改内容：增加根据单词本ID过滤复习项的查询方法
lib/data/repositories/word_repository.dart
修改原因：添加从特定单词本获取单词的方法
修改内容：增加按单词本ID过滤单词的方法
lib/data/repositories/review_item_repository.dart
修改原因：实现"按需创建ReviewItemModel"策略
修改内容：增加创建和管理与单词本关联的复习项的方法
开发步骤详细计划
步骤1：实现WordBook相关的Provider和Repository
文件：lib/data/providers/word_book/word_book_local_provider.dart
内容：
实现获取所有单词本的方法
实现根据ID获取单词本的方法
实现保存单词本的方法
实现删除单词本的方法
错误处理和日志记录
文件：lib/data/providers/word_book/word_book_remote_provider.dart
内容：
实现从远程API获取可用单词本列表的方法
实现下载单词本内容的方法
网络错误处理和重试机制
文件：lib/data/repositories/word_book_repository.dart
内容：
协调本地和远程Provider的单词本操作
实现获取可下载单词本列表的方法
实现下载单词本的方法
实现获取本地已下载单词本列表的方法
缓存和错误处理逻辑
理由：
单词本是系统的核心数据结构，需要首先实现
用户需要能够获取和下载单词本才能进行后续学习
需要区分本地和远程操作，以支持离线使用和数据同步
步骤2：实现UserWordBook相关的Provider和Repository
文件：lib/data/providers/user_word_book/user_word_book_local_provider.dart
内容：
实现获取用户所有单词本的方法
实现获取当前激活单词本的方法
实现更新单词本状态的方法
实现更新学习进度的方法
文件：lib/data/repositories/user_word_book_repository.dart
内容：
实现设置激活单词本的方法（同时确保其他单词本被停用）
实现获取当前激活单词本的方法
实现获取单词本学习进度的方法
实现更新单词本学习设置的方法（如每日新单词数量）
理由：
UserWordBookModel是连接用户学习行为与单词本的桥梁
需要支持多词书管理，包括切换激活词书、查看学习进度等
用户可以自定义不同单词本的学习计划和设置
步骤3：修改ReviewItem相关Provider和Repository
文件：lib/data/providers/review_item/review_item_local_provider.dart
修改内容：
增加根据单词本ID查询待复习项的方法
增加创建与特定单词本关联的复习项方法
文件：lib/data/repositories/review_item_repository.dart
修改内容：
实现"按需创建ReviewItemModel"策略
当用户首次学习某个单词时，创建对应的ReviewItem
根据单词本和学习进度生成复习计划
增加获取特定单词本待复习项的方法
更新复习算法以支持多单词本场景
理由：
复习项需要与单词本关联，以支持多词书学习场景
"按需创建"策略可以避免一次性创建大量复习项，优化性能和存储
需要确保复习项与用户当前学习的单词本相匹配
步骤4：修改Word相关Repository
文件：lib/data/repositories/word_repository.dart
修改内容：
增加从特定单词本获取单词的方法
实现获取单词本中已学习单词数量的方法
优化单词获取逻辑，支持按单词本过滤
理由：
需要支持多词书场景下的单词获取
已学习单词数量是显示学习进度的关键指标
单词需要与当前激活的单词本关联
步骤5：实现用户偏好设置相关功能
文件：lib/data/providers/user_preference/user_preference_local_provider.dart
内容：
实现保存通用用户偏好设置的方法
实现获取用户偏好设置的方法
支持常用设置项如界面主题、提醒时间等
文件：lib/data/repositories/user_preference_repository.dart
内容：
实现高级用户偏好管理方法
提供默认设置和设置验证
实现设置迁移和兼容性处理
理由：
用户偏好设置是提升用户体验的重要部分
需要统一管理并保证设置的一致性
设置需要在应用重启后仍然有效
实施优先级和建议
首先实现单词本和用户单词本功能
这是实现多词书管理的基础
所有其他功能都依赖于此
其次修改复习项功能
确保复习项与单词本正确关联
实现"按需创建"策略优化性能
然后修改单词Repository
添加按单词本获取单词的功能
实现获取学习进度的方法
最后实现用户偏好设置功能
这是相对独立的功能，可以后实现
实现后可以提升用户体验
通过以上步骤的实施，可以构建一个支持多词书管理、按需创建复习项、获取学习进度的完整数据访问层，为后续UI开发提供坚实的数据基础。