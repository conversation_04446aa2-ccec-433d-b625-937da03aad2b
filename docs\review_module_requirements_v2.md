# 复习模块重构需求文档 v2.0

## 📋 项目背景

### 当前状况
- ✅ **单词本关联功能已完成**：用户可以激活单词本，所有学习数据都与特定单词本关联
- ✅ **新学流程已完善**：回忆→详情→测验的完整流程，支持单词本关联和会话恢复
- ❌ **复习模块严重过时**：仍使用早期架构，包含复杂的分组逻辑，未与单词本关联

### 重构目标
彻底重构复习模块，使其与新学流程保持一致的架构和用户体验，基于激活的单词本进行复习。

## 🎯 核心需求

### 1. 基础功能需求

#### 1.1 单词本关联机制
- **前置条件**：必须有激活的单词本才能进入复习
- **数据源**：所有复习数据仅来源于当前激活的单词本
- **首页集成**：
  - 实时显示待复习单词数量
  - 根据状态动态控制复习按钮：
    - 有待复习单词：`开始复习 (X个单词)`
    - 无待复习单词：`暂无待复习单词`（禁用）
    - 无激活单词本：`请先激活单词本`（禁用）

#### 1.2 复习流程设计
- **简化流程**：取消回忆阶段，直接进入测验环节
- **随机测验**：每次展示随机单词的随机题型的随机题目
- **答题处理**：
  - **答对**：进入下一题（随机一个单词的随机一个题型的随机一道题）
  - **答错**：立即进入该单词的详情页面
- **继续机制**：详情页面查看后，点击"继续复习"返回测验页面，展示该错误单词的另外一个随机题目

### 2. 分组管理机制

#### 2.1 分组策略
- **分组规则**：待复习单词随机分组，每组5个单词
- **组内完成条件**：每个单词都答对3次后，该组完成
- **组间切换**：完成一组后自动进入下一组

#### 2.2 分组优势
- **渐进式完成**：用户可以分批完成复习，避免一次性复习过多单词
- **状态持久化**：已完成的组不会在下次复习中出现
- **用户友好**：即使有200个待复习单词，用户也可以分10组完成

### 3. 答题逻辑和状态管理

#### 3.1 答对逻辑
- 记录该单词答对次数+1
- 检查是否达到3次：
  - 达到3次：标记为已完成，更新下次复习时间
  - 未达到：继续该单词的复习
- 进入下一个随机题目

#### 3.2 答错逻辑
- 重置该单词答对次数为0
- 立即导航到单词详情页面
- 详情页面返回后，继续测验同一单词的其他题目

#### 3.3 复习完成条件
- **组内完成**：当前组的所有5个单词都答对3次
- **全部完成**：所有组都完成后，进入复习结果页面

### 4. 复习时间计算

#### 4.1 SM-2算法应用
- 单词答对3次后，立即调用`ReviewItemRepository.processReviewResult()`
- 根据该单词在本次复习中的错误次数计算质量评分：
  - 0次错误：5分（完美）
  - 1次错误：4分（很好）
  - 2次错误：3分（良好）
  - 3次及以上错误：2分（需要加强）

#### 4.2 下次复习时间
- 使用现有的SM-2间隔重复算法
- 根据质量评分计算下次复习间隔
- 更新ReviewItem的nextReviewDate

### 5. 用户界面需求

#### 5.1 进度指示器
- **顶部进度条**：显示整体复习进度
- **进度计算**：
  - 当前组进度：(当前组已答对题目数) / (当前组单词数 × 3) × 100%
  - 整体进度：(已完成组数 × 5 + 当前组已完成单词数) / (总单词数) × 100%
- **信息展示**：
  - 当前组：第X组/共Y组
  - 当前组进度：已完成X个单词/共5个单词
  - 总体进度：已完成X个单词/共Y个单词

#### 5.2 测验页面
- **页面布局**：
  - 顶部：进度条和组信息
  - 中部：题目内容区域
  - 底部：答题选项
- **视觉一致性**：与新学流程的测验页面保持一致

#### 5.3 详情页面
- **独立实现**：在`review/detail/`目录下实现复习专用的详情页面
- **参考设计**：UI布局和交互逻辑参考新学流程的DetailPage
- **返回按钮**：底部显示"继续复习"按钮
- **上下文保持**：返回时保持复习会话状态

#### 5.4 结果页面
- **统计展示**：
  - 复习单词总数
  - 答对题目总数
  - 答错题目总数
  - 复习用时
  - 正确率
- **操作按钮**：
  - 返回首页（主要操作）
  - 重新复习错误单词（可选功能）

### 6. 技术架构

#### 6.1 控制器设计
在`lib/modules/learning/review/`目录下独立实现，参考新学流程的架构：

- **ReviewController**（主控制器）
  - 位置：`review/controllers/review_controller.dart`
  - 职责：管理复习会话、分组逻辑、进度跟踪
  - 参考：LearningController的简化版本
  - 移除：复杂的分组缓存和状态管理

- **ReviewQuizController**（测验控制器）
  - 位置：`review/quiz/controllers/review_quiz_controller.dart`
  - 职责：处理复习测验页面的UI交互
  - 参考：新学流程的QuizController架构和逻辑
  - 独立实现：不与新学流程的QuizController混合

- **ReviewDetailController**（详情控制器）
  - 位置：`review/detail/controllers/review_detail_controller.dart`
  - 职责：处理复习模式下的单词详情展示
  - 参考：新学流程的DetailController架构和逻辑
  - 独立实现：不与新学流程的DetailController混合

- **ReviewResultController**（结果控制器）
  - 位置：`review/result/controllers/review_result_controller.dart`
  - 职责：处理复习结果页面的数据展示和统计

#### 6.2 目录结构设计
```
lib/modules/learning/review/
├── controllers/
│   └── review_controller.dart           # 主控制器
├── detail/
│   ├── controllers/
│   │   └── review_detail_controller.dart # 复习详情控制器
│   └── views/
│       └── review_detail_page.dart       # 复习详情页面
├── quiz/
│   ├── controllers/
│   │   └── review_quiz_controller.dart   # 复习测验控制器
│   └── views/
│       └── review_quiz_page.dart         # 复习测验页面
├── result/
│   ├── controllers/
│   │   └── review_result_controller.dart # 复习结果控制器
│   └── views/
│       └── review_result_page.dart       # 复习结果页面
└── bindings/
    └── review_binding.dart               # 复习模块依赖注入
```

#### 6.3 数据流设计
- **数据获取**：`ReviewItemRepository.getDueReviewWordsByUserWordBook(userWordBookId)`
- **题目生成**：复用`QuizRepository`的随机题目生成逻辑
- **状态更新**：实时更新ReviewItem的学习状态

#### 6.4 路由配置
- **路由路径**：
  - `LearningRoutes.REVIEW_QUIZ = '/review_quiz'`：复习测验页面
  - `LearningRoutes.REVIEW_DETAIL = '/review_detail'`：复习详情页面
  - `LearningRoutes.REVIEW_RESULT = '/review_result'`：复习结果页面
- **导航流程**：
  - 首页 → 复习测验页面
  - 测验页面 ⇄ 复习详情页面（答错时）
  - 测验页面 → 复习结果页面（完成时）

### 7. 简化设计原则

#### 7.1 无状态持久化
- **设计理念**：每次点击"开始复习"都重新筛选待复习单词
- **优势**：
  - 无需复杂的会话恢复逻辑
  - 已完成的单词自动从下次复习中排除
  - 用户可以随时退出，下次重新开始

#### 7.2 MVP错误处理
- **基础处理**：网络异常、数据加载失败的友好提示
- **暂不考虑**：复杂的异常恢复、数据一致性检查
- **重点**：确保核心功能稳定可用

### 8. 与新学流程的对比

| 功能点 | 新学流程 | 复习流程 |
|--------|----------|----------|
| 目录结构 | `learning/recall/`, `learning/detail/`, `learning/quiz/` | `learning/review/detail/`, `learning/review/quiz/`, `learning/review/result/` |
| 控制器 | `RecallController`, `DetailController`, `QuizController` | `ReviewDetailController`, `ReviewQuizController`, `ReviewResultController` |
| 入口页面 | 回忆页面 | 测验页面 |
| 流程阶段 | 回忆→详情→测验 | 测验→详情（答错时） |
| 会话管理 | 支持中断恢复 | 无状态，每次重新开始 |
| 完成条件 | 每个单词测验1次 | 每个单词答对3次 |
| 分组机制 | 无分组 | 5个单词一组 |
| 进度计算 | 基于单词索引 | 基于答对次数 |
| 代码复用 | 独立实现 | 参考架构，独立实现 |

### 9. 开发计划

#### 9.1 第一阶段：核心重构（3-4天）
- [ ] 重构ReviewController，移除复杂分组逻辑
- [ ] 实现基于单词本的数据获取
- [ ] 实现5个单词一组的分组机制
- [ ] 在`review/detail/`下独立实现ReviewDetailController和ReviewDetailPage
- [ ] 在`review/quiz/`下独立实现ReviewQuizController和ReviewQuizPage

#### 9.2 第二阶段：UI优化（2-3天）
- [ ] 完善进度指示器
- [ ] 优化复习测验页面布局
- [ ] 在`review/result/`下实现ReviewResultController和ReviewResultPage
- [ ] 确保与新学流程的视觉一致性

#### 9.3 第三阶段：测试完善（1-2天）
- [ ] 全流程测试
- [ ] 边界情况处理
- [ ] 性能优化
- [ ] 代码清理

## 🎯 成功标准

### 功能完整性
- [ ] 复习功能完全基于激活的单词本
- [ ] 5个单词一组的分组机制正常工作
- [ ] 每个单词必须答对3次才能完成
- [ ] 答错后正确跳转到详情页面

### 用户体验
- [ ] 界面风格与新学流程保持一致
- [ ] 进度指示清晰准确
- [ ] 操作流程自然流畅

### 技术质量
- [ ] 代码架构清晰，在独立目录下实现
- [ ] 参考新学流程架构，但不混合代码
- [ ] 性能稳定，无明显卡顿
- [ ] 错误处理友好

## 🏗️ 架构设计原则

### 独立性原则
- **目录独立**：复习模块在`lib/modules/learning/review/`下独立实现
- **控制器独立**：不与新学流程的控制器混合，避免代码耦合
- **页面独立**：每个复习页面都有独立的实现

### 参考性原则
- **架构参考**：学习新学流程的MVVM架构设计
- **逻辑参考**：参考新学流程的数据流和状态管理方式
- **UI参考**：保持与新学流程一致的视觉风格和交互模式

### 复用性原则
- **Repository复用**：复用现有的数据访问层
- **Service复用**：复用QuizRepository、ReviewItemRepository等服务
- **组件复用**：复用通用的UI组件，如进度条、按钮等

这个需求文档明确了复习模块的独立架构设计，确保与新学流程保持一致性的同时避免代码混合。
