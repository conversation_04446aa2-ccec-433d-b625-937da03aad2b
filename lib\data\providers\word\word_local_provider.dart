///
/// 职责：与本地数据库（通过 IsarService）交互，处理与 WordModel 相关的本地数据操作。
/// 这里的操作是针对具体业务实体的（如 Word），而不是 IsarService 中的通用泛型操作。
/// 它应该包含所有与单词相关的本地数据业务逻辑，例如：
/// - 获取所有单词
/// - 获取特定状态的单词（已学习，未学习等）
/// - 更新单词的学习进度
/// - 批量保存/删除单词
library;

import 'package:flutter_study_flow_by_myself/app/services/isar_service.dart'; // 导入 IsarService
import 'package:flutter_study_flow_by_myself/data/models/word_model.dart'; // 导入 WordModel
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart'; // 导入 LogService
import 'package:flutter_study_flow_by_myself/app/exceptions/app_exception.dart'; // 导入 AppException
import 'package:flutter_study_flow_by_myself/data/providers/word_book/word_book_local_provider.dart'; // 导入 WordBookLocalProvider

class WordLocalProvider {
  // 通过 Get.find() 获取 IsarService 实例，因为它是一个全局服务，已经在 Get.put() 中注册
  final IsarService _isarService;
  final LogService _log;
  final WordBookLocalProvider _wordBookProvider; // 可选依赖

  WordLocalProvider({
    required IsarService isarService,
    required LogService log,
    required WordBookLocalProvider wordBookProvider,
  }) : _isarService = isarService,
       _log = log,
       _wordBookProvider = wordBookProvider;

  /// 获取所有单词
  /// 返回一个包含所有 WordModel 对象的列表
  Future<List<WordModel>> getAllWords() async {
    try {
      // 调用 IsarService 的 getAll 泛型方法来获取 WordModel 集合的所有数据
      final words = await _isarService.getAll<WordModel>();
      // 可以在这里进行额外的业务逻辑处理，例如数据过滤或排序
      return words;
    } catch (e) {
      _log.e('获取所有单词失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：获取所有单词失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 根据 ID 获取单个单词
  /// [wordId] 单词的 Isar ID
  /// 返回对应的 WordModel 对象，如果不存在则返回 null
  Future<WordModel?> getWordById(int wordId) async {
    try {
      // 调用 IsarService 的 get 泛型方法来根据 ID 获取单个 WordModel
      return await _isarService.get<WordModel>(wordId);
    } catch (e) {
      _log.e('根据ID获取单词失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：根据ID获取单词失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 根据 ID 列表批量获取单词
  /// [wordIds] 单词的 Isar ID 列表
  /// 返回一个包含查询到的 WordModel 对象的列表，会过滤掉未找到的单词
  Future<List<WordModel>> getWordsByIds(List<int> wordIds) async {
    try {
      final words = await _isarService.getByIds<WordModel>(wordIds);
      // 过滤掉 null (未找到的单词)，并转换类型
      return words.where((word) => word != null).cast<WordModel>().toList();
    } catch (e) {
      _log.e('根据ID列表获取单词失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：根据ID列表获取单词失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 保存单个单词到本地数据库
  /// [word] 要保存的 WordModel 对象
  /// 返回保存成功后单词的 Isar ID
  Future<int> saveWord(WordModel word) async {
    try {
      // 调用 IsarService 的 put 泛型方法来保存单个 WordModel
      return await _isarService.put<WordModel>(word);
    } catch (e) {
      _log.e('保存单词失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：保存单词失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 批量保存单词到本地数据库
  /// [words] 要保存的 WordModel 对象列表
  /// 返回保存成功后单词的 Isar ID 列表
  Future<List<int>> saveAllWords(List<WordModel> words) async {
    try {
      // 调用 IsarService 的 putAll 泛型方法来批量保存 WordModel
      return await _isarService.putAll<WordModel>(words);
    } catch (e) {
      _log.e('批量保存单词失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：批量保存单词失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 根据 ID 删除单个单词
  /// [wordId] 要删除单词的 Isar ID
  /// 返回 true 表示删除成功，false 表示删除失败或单词不存在
  Future<bool> deleteWordById(int wordId) async {
    try {
      // 调用 IsarService 的 delete 泛型方法来根据 ID 删除单个 WordModel
      return await _isarService.delete<WordModel>(wordId);
    } catch (e) {
      _log.e('删除单词失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：删除单词失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 批量删除单词
  /// [wordIds] 要删除单词的 Isar ID 列表
  /// 返回成功删除的数量
  Future<int> deleteWordsByIds(List<int> wordIds) async {
    try {
      // 调用 IsarService 的 deleteAll 泛型方法来批量删除 WordModel
      return await _isarService.deleteByIds<WordModel>(wordIds);
    } catch (e) {
      _log.e('批量删除单词失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：批量删除单词失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 清空所有单词数据
  Future<void> clearAllWords() async {
    try {
      // 调用 IsarService 的 clearCollection 泛型方法来清空 WordModel 集合
      await _isarService.clearCollection<WordModel>();
    } catch (e) {
      _log.e('清空所有单词失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：清空所有单词失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 查询单词数量
  Future<int> countWords() async {
    try {
      return await _isarService.count<WordModel>();
    } catch (e) {
      _log.e('查询单词数量失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：查询单词数量失败',
        type: AppExceptionType.database,
      );
    }
  }

  // --- 示例：特定业务逻辑查询 ---

  /// 获取所有标记为"已掌握"的单词
  Future<List<WordModel>> getMasteredWords() async {
    try {
      // 这是一个示例业务逻辑，需要根据 WordModel 的实际字段来构建查询
      // 假设 WordModel 有一个 isMastered 字段
      // return await _isarService.isar.wordModels.filter().isMasteredEqualTo(true).findAll();
      // 由于 WordModel 中暂时没有 isMastered 字段，这里先返回所有单词
      // 实际开发中，需要根据 WordModel 的属性来构建查询
      _log.i('获取所有已掌握单词 (示例方法)');
      return await getAllWords(); // 示例：目前返回所有单词
    } catch (e) {
      _log.e('获取已掌握单词失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：获取已掌握单词失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 获取指定数量的未学习新单词
  Future<List<WordModel>> getNewWordsForLearning({int limit = 10}) async {
    try {
      // 获取所有单词
      List<WordModel> allWords = await getAllWords();
      _log.i('获取未学习新单词，请求数量: $limit, 可用单词总数: ${allWords.length}');

      // 如果请求数量大于可用单词数量，返回所有单词
      if (limit >= allWords.length) {
        _log.i('返回所有可用单词: ${allWords.length}个');
        return allWords;
      }

      // 否则，返回前limit个单词
      _log.i('返回前$limit个单词');
      return allWords.take(limit).toList();
    } catch (e) {
      _log.e('获取新单词失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：获取新单词失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 更新单词的学习进度/状态
  /// [word] 需要更新的单词对象，其中包含了新的学习进度或状态信息
  Future<int> updateWordLearningProgress(WordModel word) async {
    try {
      // 直接调用 save 方法，Isar 会根据 ID 自动进行更新操作
      return await _isarService.put<WordModel>(word);
    } catch (e) {
      _log.e('更新单词学习进度失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：更新单词学习进度失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 获取指定单词本中的所有单词
  /// [wordBookId] 单词本ID
  /// 返回该单词本中包含的所有单词列表
  Future<List<WordModel>> getWordsByWordBookId(int wordBookId) async {
    try {
      // 如果提供了WordBookLocalProvider，使用它的方法
      _log.i('使用WordBookLocalProvider获取单词本($wordBookId)中的单词');
      final words = await _wordBookProvider.getWordsFromWordBook(wordBookId);
      _log.i('单词本($wordBookId)中获取到${words.length}个单词');
      return words;
    } catch (e) {
      _log.e('获取单词本中的单词失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：获取单词本中的单词失败',
        type: AppExceptionType.database,
      );
    }
  }
}
