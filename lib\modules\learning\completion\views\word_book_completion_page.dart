import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/word_book_completion_controller.dart';

/// 单词本完成庆祝页面
class WordBookCompletionPage extends StatelessWidget {
  const WordBookCompletionPage({super.key});

  @override
  Widget build(BuildContext context) {
    // 获取控制器（已在 LearningBinding 中注册）
    final controller = Get.find<WordBookCompletionController>();

    return Scaffold(
      backgroundColor: Colors.orange.shade50,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 庆祝图标
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: Colors.orange.shade100,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.celebration,
                  size: 60,
                  color: Colors.orange,
                ),
              ),

              const SizedBox(height: 32),

              // 恭喜文字
              const Text(
                '🎉 恭喜完成！',
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Colors.orange,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 16),

              // 单词本名称
              Obx(() {
                if (controller.isLoading.value) {
                  return const Text(
                    '加载中...',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                    textAlign: TextAlign.center,
                  );
                }

                return Text(
                  '「${controller.wordBookName}」',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                );
              }),

              const SizedBox(height: 32),

              // 学习统计卡片
              _buildStatsCard(controller),

              const SizedBox(height: 48),

              // 返回首页按钮
              SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  onPressed: () => Get.offAllNamed('/main'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    '返回首页',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // 提示文字
              const Text(
                '您可以在首页选择新的单词本继续学习',
                style: TextStyle(fontSize: 14, color: Colors.grey),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建学习统计卡片
  Widget _buildStatsCard(WordBookCompletionController controller) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            const Text(
              '学习统计',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 16),
            Obx(() {
              if (controller.isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }

              return Column(
                children: [
                  _buildStatRow('完成时间', controller.completedTime),
                  const SizedBox(height: 12),
                  _buildStatRow('学习单词', '${controller.totalWords} 个'),
                  const SizedBox(height: 12),
                  _buildStatRow(
                    '完成率',
                    '${(controller.completionRate * 100).toInt()}%',
                  ),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }

  /// 构建统计行
  Widget _buildStatRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(fontSize: 16, color: Colors.black54),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }
}
