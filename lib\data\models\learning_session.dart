/// 文件名: lib/data/models/learning_session.dart
///
/// **重要说明：Isar 数据库模型设计与代码生成器问题总结与解决方案**
///
/// 在与 Isar 数据库进行模型交互时，我们遇到了以下几个常见问题及其解决方案：
///
/// **1. 不支持直接存储复杂类型（如 Map 和 Duration）**
///    - **问题原因**：Isar 不支持将 Dart 的 `Map` 类型（例如 `Map<int, bool>`)
///      和 `Duration` 类型作为其集合（Collection）的属性进行持久化存储。
///      Isar 期望属性是其支持的基本类型（如 `int`, `bool`, `String`, `DateTime` 等）
///      或它们的列表，以及 `@Embedded` 对象。
///    - **解决方案**：
///      - 对于 `Map<int, bool>` 这种需要存储的复杂 Map 类型，我们将其转换为 JSON 字符串 (`String`)
///        进行存储（例如 `recallResultsJson` 和 `quizResultsJson` 字段）。
///        在需要使用 Map 时，通过 Getter 方法进行 JSON 解码。
///      - 对于 `Duration` 这种不需要直接存储但又作为 Getter 返回的类型，
///        以及其他不需要持久化存储的计算属性，需要在其 Getter 方法前添加 `@ignore` 注解，
///        明确告诉 Isar 代码生成器忽略这些属性的 Schema 生成。
///
/// **2. Isar 自动检测所有公共属性和 Getter 进行持久化**
///    - **问题原因**：Isar 的代码生成器默认会扫描 `@Collection()` 注解的类中所有公共（非私有）的字段和 Getter 方法，
///      并尝试为它们生成数据库 Schema。如果这些字段或 Getter 的返回类型不被 Isar 支持，就会导致编译错误。
///    - **解决方案**：
///      - 对于所有不需要直接持久化到数据库的计算属性（例如 `quizCorrectRate`,
///        `learningDuration`, `wrongQuizItemIds`），以及那些从其他字段派生出来且返回类型不被 Isar 直接支持的 Getter
///        (例如 `recallResults`, `quizResults`），我们都在其定义前明确添加了 `@ignore` 注解。
///        这告诉 Isar 生成器不要为这些属性生成 Schema。
///
/// **3. 构造函数参数与类属性的直接映射要求**
///    - **问题原因**：Isar 生成器在处理 `@Collection()` 类时，期望构造函数中的参数能够直接、清晰地映射到类中要持久化的字段。
///      如果构造函数参数与实际存储的字段之间存在间接转换（例如构造函数接收 `Map` 而内部存储 `String`），
///      或者字段是私有的（例如 `_recallResultsJson`），生成器可能无法正确识别这种映射关系，
///      导致 `Constructor parameter does not match a property` 错误。
///    - **解决方案**：
///      - 将用于存储 Isar 数据库数据的字段（例如 `recallResultsJson` 和 `quizResultsJson`）声明为**公开字段**。
///      - 调整构造函数，使其直接接收这些公开字段的值作为参数，并直接初始化它们。
///        这样，构造函数的参数名就与实际存储的公开字段名直接匹配，满足了 Isar 生成器的映射要求。
///        在外部使用该构造函数创建对象时，需要先将原始 `Map` 转换为 JSON 字符串再传入。
///
/// 通过以上调整，我们确保了 `LearningSession` 模型完全符合 Isar 的数据存储和代码生成要求，
/// 从而能够顺利生成 `learning_session.g.dart` 文件。
///
/// ---
// ignore_for_file: unintended_html_in_doc_comment

library;

import 'package:isar/isar.dart'; // 导入 Isar 库
import 'dart:convert'; // 导入 dart:convert 用于 JSON 编解码
import 'user_word_book_model.dart'; // 导入UserWordBookModel

part 'learning_session.g.dart'; // 声明这个文件是 learning_session.g.dart 的一部分，用于代码生成

/// 学习会话模型
/// 使用 @Collection() 注解将其标记为 Isar 数据库的集合
/// 这个模型用于记录一次完整的学习过程和结果，便于持久化和分析
@Collection()
class LearningSession {
  /// Isar 数据库的主键，使用 autoIncrement 实现自动增长 ID
  Id id = Isar.autoIncrement;

  /// 本次学习会话的唯一ID
  /// 可以用于追踪特定的学习过程
  late String sessionId;

  /// 关联的用户单词本ID
  late int userWordBookId;

  /// 关联的用户单词本
  ///
  /// 使用IsarLink建立与UserWordBookModel的一对一关联，
  /// 通过这个关联可以获取该学习会话所属的用户单词本信息
  final userWordBook = IsarLink<UserWordBookModel>();

  /// 本次学习的单词的 Isar ID 列表
  /// 注意：这里存储的是单词的 ID，而不是 WordModel 对象本身
  late List<int> wordIds;

  /// 学习开始时间
  late DateTime startTime;

  /// 学习结束时间（可选，学习未完成时为 null）
  DateTime? endTime;

  /// 上次学习时间
  /// 用于计算学习间隔和提醒功能
  late DateTime lastStudyTime;

  /// 当前正在学习的单词在 `wordIds` 列表中的索引
  /// 用于在恢复会话时，知道用户学到了哪个单词
  late int currentWordIndex;

  /// 是否处于最终测验阶段
  late bool isInFinalQuiz;

  /// 单词学习状态的JSON字符串
  /// 包含单词认知状态、错误次数和已完成测验题数
  late String wordLearningStateJson;

  /// 构造函数
  LearningSession({
    required this.sessionId,
    required this.userWordBookId,
    required this.wordIds,
    required this.startTime,
    this.endTime,
    required this.lastStudyTime,
    this.currentWordIndex = 0,
    this.isInFinalQuiz = false,
    required this.wordLearningStateJson,
  });

  /// 从 JSON 数据创建 LearningSession 对象的工厂方法
  factory LearningSession.fromJson(Map<String, dynamic> json) =>
      LearningSession(
        sessionId: json['sessionId'] as String,
        userWordBookId: json['userWordBookId'] as int,
        wordIds: List<int>.from(json['wordIds'] ?? []),
        startTime: DateTime.parse(json['startTime'] as String),
        endTime:
            json['endTime'] != null
                ? DateTime.parse(json['endTime'] as String)
                : null,
        lastStudyTime: DateTime.parse(
          json['lastStudyTime'] as String? ?? json['startTime'] as String,
        ),
        currentWordIndex: json['currentWordIndex'] as int? ?? 0,
        isInFinalQuiz: json['isInFinalQuiz'] as bool? ?? false,
        wordLearningStateJson: json['wordLearningStateJson'] as String? ?? '{}',
      )..id = json['id'] ?? Isar.autoIncrement;

  /// 将 LearningSession 对象转换为 JSON 数据
  Map<String, dynamic> toJson() => {
    'id': id,
    'sessionId': sessionId,
    'userWordBookId': userWordBookId,
    'wordIds': wordIds,
    'startTime': startTime.toIso8601String(),
    'endTime': endTime?.toIso8601String(),
    'lastStudyTime': lastStudyTime.toIso8601String(),
    'currentWordIndex': currentWordIndex,
    'isInFinalQuiz': isInFinalQuiz,
    'wordLearningStateJson': wordLearningStateJson,
  };

  /// 获取单词学习状态Map
  @ignore
  Map<String, dynamic> get wordLearningState {
    try {
      if (wordLearningStateJson.isEmpty) return {};
      return jsonDecode(wordLearningStateJson) as Map<String, dynamic>;
    } catch (e) {
      // 如果解析失败，返回空Map
      return {};
    }
  }

  /// 获取指定单词的学习状态
  Map<String, dynamic>? getWordState(int wordId) {
    final states = wordLearningState;
    final key = wordId.toString();
    if (states.containsKey(key)) {
      return states[key] as Map<String, dynamic>;
    }
    return null;
  }

  /// 设置单词学习状态
  void setWordLearningState(Map<String, dynamic> state) {
    wordLearningStateJson = jsonEncode(state);
  }

  /// 更新单个单词的学习状态
  void updateWordState(
    int wordId, {
    bool? isRecognized,
    int? errorCount,
    int? completedQuizzes,
  }) {
    final states = Map<String, dynamic>.from(wordLearningState);
    final key = wordId.toString();

    // 如果不存在该单词状态，则创建一个
    if (!states.containsKey(key)) {
      states[key] = {
        'isRecognized': false,
        'errorCount': 0,
        'completedQuizzes': 0,
      };
    }

    final wordState = Map<String, dynamic>.from(
      states[key] as Map<String, dynamic>,
    );

    // 更新属性
    if (isRecognized != null) wordState['isRecognized'] = isRecognized;
    if (errorCount != null) wordState['errorCount'] = errorCount;
    if (completedQuizzes != null) {
      wordState['completedQuizzes'] = completedQuizzes;
    }

    // 更新状态
    states[key] = wordState;
    wordLearningStateJson = jsonEncode(states);
  }

  /// 获取学习进度百分比
  @ignore
  double get progressPercentage {
    if (isCompleted) return 1.0;
    if (wordIds.isEmpty) return 0.0;

    if (!isInFinalQuiz) {
      // 学习阶段进度 = 当前单词索引 / 总单词数
      return currentWordIndex / wordIds.length;
    } else {
      // 最终测验阶段，假设占总进度的20%
      final baseProgress = 0.8;

      // 计算已完成最终测验的单词数量
      int completedCount = 0;
      final states = wordLearningState;
      for (final key in states.keys) {
        final state = states[key] as Map<String, dynamic>;
        if ((state['completedQuizzes'] as int) >= 3) {
          completedCount++;
        }
      }

      // 测验阶段进度 = 0.8 + 0.2 * (已完成最终测验的单词数 / 总单词数)
      return baseProgress + 0.2 * (completedCount / wordIds.length);
    }
  }

  /// 使用 @ignore 注解告诉 Isar 忽略这个 getter，因为它是一个计算属性
  /// 用于判断当前会话是否已经完成
  @ignore
  bool get isCompleted => endTime != null;

  /// 创建一个空的学习会话（用于初始化）
  factory LearningSession.empty() => LearningSession(
    sessionId: DateTime.now().millisecondsSinceEpoch.toString(),
    userWordBookId: 0,
    wordIds: [],
    startTime: DateTime.now(),
    lastStudyTime: DateTime.now(),
    wordLearningStateJson: '{}',
  );

  /// 从现有会话创建副本并更新特定字段
  LearningSession copyWith({
    String? sessionId,
    int? userWordBookId,
    List<int>? wordIds,
    DateTime? startTime,
    DateTime? endTime,
    DateTime? lastStudyTime,
    int? currentWordIndex,
    bool? isInFinalQuiz,
    String? wordLearningStateJson,
  }) {
    return LearningSession(
      sessionId: sessionId ?? this.sessionId,
      userWordBookId: userWordBookId ?? this.userWordBookId,
      wordIds: wordIds ?? this.wordIds,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      lastStudyTime: lastStudyTime ?? this.lastStudyTime,
      currentWordIndex: currentWordIndex ?? this.currentWordIndex,
      isInFinalQuiz: isInFinalQuiz ?? this.isInFinalQuiz,
      wordLearningStateJson:
          wordLearningStateJson ?? this.wordLearningStateJson,
    )..id = id;
  }

  @override
  String toString() {
    return 'LearningSession(id: $id, sessionId: $sessionId, userWordBookId: $userWordBookId, wordCount: ${wordIds.length}, currentWordIndex: $currentWordIndex, isInFinalQuiz: $isInFinalQuiz, progress: ${(progressPercentage * 100).toStringAsFixed(1)}%)';
  }

  /// 获取学习时长
  @ignore
  Duration get learningDuration {
    if (endTime == null) {
      // 如果会话未结束，使用当前时间计算
      return DateTime.now().difference(startTime);
    }
    // 如果会话已结束，使用结束时间计算
    return endTime!.difference(startTime);
  }

  /// 获取已学习的单词数量
  @ignore
  int get learnedWordCount {
    // 如果在最终测验阶段，所有单词都已经学习过
    if (isInFinalQuiz) return wordIds.length;

    // 如果在学习阶段，已学习的单词数为当前索引
    return currentWordIndex;
  }
}
