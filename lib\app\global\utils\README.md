# utils 目录说明文档

## 目录职责

`utils` 目录负责存放应用程序中使用的各种工具函数和辅助类，这些工具和辅助类通常是纯函数或无状态的辅助方法，可以在应用的不同部分重复使用。它们不依赖于特定的业务逻辑或UI组件，而是提供通用的功能支持。

## 文件结构

```
utils/
├── date_utils.dart         # 日期和时间处理工具
├── string_utils.dart       # 字符串处理工具
├── network_utils.dart      # 网络相关工具
├── storage_utils.dart      # 本地存储工具
├── validation_utils.dart   # 表单验证工具
├── format_utils.dart       # 格式化工具（数字、货币等）
├── file_utils.dart         # 文件操作工具
└── extensions/             # Dart扩展方法
    ├── string_extensions.dart    # String类扩展
    ├── datetime_extensions.dart  # DateTime类扩展
    └── int_extensions.dart       # int类扩展
```

## 使用方式

工具函数通常以静态方法的形式提供，可以直接导入并使用：

```dart
// 引入工具类
import '../app/global/utils/date_utils.dart';
import '../app/global/utils/string_utils.dart';

// 使用日期工具
String formattedDate = DateUtils.formatDate(DateTime.now(), 'yyyy-MM-dd');

// 使用字符串工具
String truncatedText = StringUtils.truncate('这是一段很长的文本...', 10);

// 使用扩展方法
import '../app/global/utils/extensions/string_extensions.dart';

String capitalizedText = 'hello world'.capitalize(); // 'Hello world'
```

## 代码示例

### 日期工具

```dart
// date_utils.dart
class DateUtils {
  static String formatDate(DateTime date, String pattern) {
    // 实现日期格式化逻辑
    return ''; // 返回格式化后的日期字符串
  }
  
  static DateTime parseDate(String dateStr, String pattern) {
    // 实现日期解析逻辑
    return DateTime.now(); // 返回解析后的DateTime对象
  }
  
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && date.month == now.month && date.day == now.day;
  }
  
  static int daysBetween(DateTime from, DateTime to) {
    from = DateTime(from.year, from.month, from.day);
    to = DateTime(to.year, to.month, to.day);
    return (to.difference(from).inHours / 24).round();
  }
}
```

### 字符串工具

```dart
// string_utils.dart
class StringUtils {
  static String truncate(String text, int maxLength, {String suffix = '...'}) {
    if (text.length <= maxLength) {
      return text;
    }
    return text.substring(0, maxLength) + suffix;
  }
  
  static bool isNullOrEmpty(String? text) {
    return text == null || text.isEmpty;
  }
  
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }
  
  static String maskText(String text, {int visibleChars = 4, String mask = '*'}) {
    if (text.length <= visibleChars) return text;
    
    final visible = text.substring(text.length - visibleChars);
    final masked = mask * (text.length - visibleChars);
    
    return masked + visible;
  }
}
```

### 扩展方法

```dart
// extensions/string_extensions.dart
extension StringExtensions on String {
  String capitalize() {
    if (isEmpty) return this;
    return this[0].toUpperCase() + substring(1);
  }
  
  bool get isValidEmail => RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(this);
  
  String truncate(int maxLength, {String suffix = '...'}) {
    if (length <= maxLength) return this;
    return substring(0, maxLength) + suffix;
  }
  
  String maskSensitive({int visibleChars = 4, String mask = '*'}) {
    if (length <= visibleChars) return this;
    
    final visible = substring(length - visibleChars);
    final masked = mask * (length - visibleChars);
    
    return masked + visible;
  }
}
```

## 最佳实践

1. **功能聚焦**：
   - 每个工具类或函数应该专注于一个特定领域的功能
   - 避免创建"万能"工具类，应按功能领域分离

2. **纯函数设计**：
   - 尽可能使用纯函数，即相同输入始终产生相同输出
   - 避免依赖外部状态或产生副作用
   - 这样可以使函数更易于测试和重用

3. **命名规范**：
   - 函数名应清晰表达其功能
   - 使用一致的命名规范，如工具类使用`XxxUtils`命名
   - 为复杂功能提供良好的文档注释

4. **可测试性**：
   - 所有工具函数应易于单元测试
   - 考虑编写测试来验证功能的正确性

5. **扩展方法使用**：
   - 对于频繁使用的操作，考虑使用Dart扩展方法
   - 扩展方法可以使代码更简洁易读

## 注意事项

- 避免在工具类中包含业务逻辑
- 避免在工具类中使用全局状态
- 工具函数应该是自包含的，不应依赖于特定的环境配置
- 考虑性能影响，特别是对于频繁调用的工具函数
- 优先使用Dart和Flutter提供的标准功能，避免重复造轮子

## 与其他目录的关系

- **constants目录**：工具函数可能使用常量目录中定义的值
- **models目录**：工具函数可能用于处理模型数据
- **services目录**：服务可能使用工具函数实现功能
- **widgets目录**：组件可能使用工具函数处理数据

## 扩展计划

- 增加更多特定领域的工具类，如动画工具、图片处理工具等
- 增强现有工具类的功能，支持更多常见场景
- 添加更多扩展方法，简化常见操作
- 创建更全面的单元测试套件，确保工具函数的可靠性