import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/modules/home/<USER>/home_controller.dart';

/// 主控制器 - 负责管理底部导航栏状态
class MainController extends GetxController {
  // 当前选中的tab索引，使用响应式变量
  final currentIndex = 0.obs;

  /// 切换底部导航tab
  ///
  /// 实现方案1：监听IndexedStack切换，主动触发页面刷新
  /// 当切换到首页时，触发数据刷新以确保显示最新的激活单词本数据
  void changeTab(int index) {
    final previousIndex = currentIndex.value;
    currentIndex.value = index;

    // 当切换到首页时，刷新首页数据
    if (index == 0 && previousIndex != 0) {
      _refreshHomePageData();
    }
  }

  /// 刷新首页相关数据
  ///
  /// 当从其他页面（如词汇库）切换回首页时调用
  /// 确保首页显示最新的激活单词本数据和复习计划
  ///
  /// 🔑 优化说明：
  /// - 在新的Service架构下，Service状态变化会自动通知Controller
  /// - 但用户从词汇库切换回首页时，可能需要主动触发一次数据同步
  /// - 避免重复刷新，只在必要时刷新
  void _refreshHomePageData() {
    try {
      // 只刷新首页控制器数据，ReviewPlanController会通过Service监听自动更新
      if (Get.isRegistered<HomeController>()) {
        Get.find<HomeController>().refreshData();
      }

      // ReviewPlanController会通过监听ReviewPlanService自动更新，不需要手动刷新
      // if (Get.isRegistered<ReviewPlanController>()) {
      //   Get.find<ReviewPlanController>().refresh();
      // }
    } catch (e) {
      // 静默处理错误，避免影响用户体验
      // 不记录日志，避免在主控制器中引入额外依赖
    }
  }
}
