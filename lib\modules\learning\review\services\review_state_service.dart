import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_model.dart';

/// 复习状态服务
///
/// 管理复习过程中的内存状态，参考LearningStateService的设计
///
/// 核心职责：
/// - 维护5个单词一组的分组逻辑
/// - 跟踪每个单词的答对次数和错误次数
/// - 提供响应式状态供UI监听
/// - 计算复习进度和完成状态
/// - 管理详情页单词ID状态
class ReviewStateService extends GetxService {
  final LogService _log;

  ReviewStateService({required LogService log}) : _log = log;

  // ==================== 响应式状态变量 ====================

  /// 所有待复习单词列表 - 用于：allReviewWords getter、getReviewStats统计、initialize方法
  final _allReviewWords = RxList<WordModel>([]);

  /// 分组后的单词列表（每组5个单词） - 用于：currentGroup计算、_groupWords方法、clear方法
  final _wordGroups = RxList<List<WordModel>>([]);

  /// 当前复习组的索引 - 用于：currentGroup计算、moveToNextGroup、isLastGroup判断、_resetCounters重置
  final _currentGroupIndex = RxInt(0);

  /// 每个单词的答对次数映射 - 用于：getWordCorrectCount、recordCorrectAnswer、getReviewStats统计、_resetCounters重置
  /// 【普通变量】只用于内部计算，无需响应式
  final _wordCorrectCounts = <int, int>{};

  /// 每个单词的错误次数映射 - 用于：getWordErrorCount、recordIncorrectAnswer、getReviewStats统计、_resetCounters重置
  /// 【普通变量】只用于内部计算，无需响应式
  final _wordErrorCounts = <int, int>{};

  /// 已完成复习的单词ID集合（答对3次） - 用于：recordCorrectAnswer判断完成、isCurrentGroupCompleted、isAllCompleted、getNextWordToReview、isWordCompleted、getReviewStats统计
  /// 【响应式变量】被计算属性isCurrentGroupCompleted和isAllCompleted依赖，必须保持响应式
  final _completedWords = RxSet<int>({});

  /// 详情页要显示的单词ID - 用于：setCurrentDetailWordId设置、currentDetailWordIdObs监听
  /// 【响应式变量】被ReviewDetailController的ever监听
  final _currentDetailWordId = RxInt(-1);

  /// 当前测验题目 - 核心状态（可以是 ChoiceQuizModel 或 SpellingQuizModel） - 用于：ReviewQuizController监听、题目生成服务更新
  /// 【响应式变量】被ReviewQuizController的ever监听
  final currentQuiz = Rx<dynamic>(null);

  /// 当前单词 - 用于详情页显示和题目生成 - 用于：ReviewDetailController监听、题目生成
  /// 【响应式变量】被ReviewDetailController的ever监听
  final currentWord = Rx<WordModel?>(null);

  /// 最近答题的单词ID（用于避重选择） - 用于：ReviewQuizGenerationService避重逻辑、recordCorrectAnswer和recordIncorrectAnswer记录
  /// 【普通变量】只用于内部逻辑，无需响应式
  int _lastAnsweredWordId = -1;

  /// 总答对次数（用于进度计算） - 用于：ReviewQuizPage进度显示、recordCorrectAnswer累加、_resetCounters重置
  /// 【响应式变量】被totalCorrectAnswers getter依赖，且该getter在UI中被Obx监听
  final _totalCorrectAnswers = RxInt(0);

  /// 复习开始时间 - 用于：initialize方法设置、reviewDuration计算、getReviewStats返回
  DateTime? _reviewStartTime;

  /// 复习结束时间 - 用于：recordReviewEndTime方法设置、reviewDuration计算、getReviewStats返回
  DateTime? _reviewEndTime;

  // ==================== 只读访问器 ====================

  /// 获取所有待复习单词（不可修改） - 用于：ReviewQuizPage显示总单词数、ReviewExitDialog、ReviewDetailPage
  List<WordModel> get allReviewWords => List.unmodifiable(_allReviewWords);

  /// 获取所有分组（不可修改） - 用于：ReviewQuizPage、ReviewExitDialog、ReviewDetailPage显示总组数
  List<List<WordModel>> get wordGroups => List.unmodifiable(_wordGroups);

  /// 获取复习时长 - 用于：recordReviewEndTime日志输出、getReviewStats返回duration
  Duration? get reviewDuration {
    if (_reviewStartTime != null && _reviewEndTime != null) {
      return _reviewEndTime!.difference(_reviewStartTime!);
    }
    return null;
  }

  /// 获取当前组索引 - 用于：ReviewQuizGenerationService日志、ReviewExitDialog、ReviewDetailPage、ReviewQuizPage显示当前组
  int get currentGroupIndex => _currentGroupIndex.value;

  /// 获取已完成单词集合（不可修改） - 用于：ReviewResultPage、ReviewExitDialog、ReviewDetailPage显示完成数量
  Set<int> get completedWords => Set.unmodifiable(_completedWords);

  // ==================== 响应式访问器（供UI监听） ====================

  // 注意：大部分响应式访问器已删除，因为未被使用
  // 只保留实际被使用的响应式访问器

  // ==================== 详情页状态管理 ====================

  /// 响应式的详情页单词ID - 用于：ReviewDetailController监听单词ID变化
  RxInt get currentDetailWordIdObs => _currentDetailWordId;

  // ==================== 计算属性 ====================

  /// 获取当前复习组的单词列表
  List<WordModel> get currentGroup =>
      _wordGroups.isNotEmpty && _currentGroupIndex.value < _wordGroups.length
          ? _wordGroups[_currentGroupIndex.value]
          : [];

  /// 判断是否为最后一组
  bool get isLastGroup => _currentGroupIndex.value >= _wordGroups.length - 1;

  /// 判断当前组是否已完成（组内所有单词都答对3次） - 用于：ReviewQuizGenerationService判断组完成状态
  bool get isCurrentGroupCompleted {
    if (currentGroup.isEmpty) return false;
    return currentGroup.every((word) => _completedWords.contains(word.id));
  }

  /// 判断是否所有单词都已完成复习 - 用于：ReviewExitDialog显示完成状态
  bool get isAllCompleted => _completedWords.length >= _allReviewWords.length;

  /// 获取最近答题的单词ID - 用于：ReviewQuizGenerationService避重逻辑
  int get lastAnsweredWordId => _lastAnsweredWordId;

  /// 获取总答对次数 - 用于：ReviewQuizPage进度显示
  int get totalCorrectAnswers => _totalCorrectAnswers.value;

  // ==================== 核心业务方法 ====================

  /// 初始化复习状态
  /// [words] 待复习的单词列表
  void initialize(List<WordModel> words) {
    try {
      _log.i('初始化复习状态服务，单词数量: ${words.length}');

      // 记录复习开始时间
      _reviewStartTime = DateTime.now();
      _reviewEndTime = null; // 重置结束时间

      _allReviewWords.assignAll(words);
      _groupWords();
      _resetCounters();

      _log.i('复习状态服务初始化完成，共${_wordGroups.length}组，开始时间: $_reviewStartTime');
    } catch (e) {
      _log.e('初始化复习状态服务失败: $e');
      rethrow;
    }
  }

  /// 记录复习结束时间
  void recordReviewEndTime() {
    _reviewEndTime = DateTime.now();
    final duration = reviewDuration;
    _log.i(
      '记录复习结束时间: $_reviewEndTime，总用时: ${duration?.inMinutes ?? 0}分钟${duration?.inSeconds.remainder(60) ?? 0}秒',
    );
  }

  /// 记录单词答对
  /// [wordId] 单词ID
  void recordCorrectAnswer(int wordId) {
    final currentCount = _wordCorrectCounts[wordId] ?? 0;
    _wordCorrectCounts[wordId] = currentCount + 1;

    // 更新总答对次数（用于进度计算）
    _totalCorrectAnswers.value++;

    // 记录最近答题的单词ID（用于避重选择）
    _lastAnsweredWordId = wordId;

    // 检查是否完成该单词（总共答对3次，不要求连续）
    if (_wordCorrectCounts[wordId]! >= 3) {
      _completedWords.add(wordId);
      _log.i('单词$wordId已完成复习，总答对${_wordCorrectCounts[wordId]}次');
    } else {
      _log.d('单词$wordId答对，当前总答对次数: ${_wordCorrectCounts[wordId]}');
    }
  }

  /// 记录单词答错
  /// [wordId] 单词ID
  void recordIncorrectAnswer(int wordId) {
    // 不重置答对次数，只记录错误次数
    final errorCount = (_wordErrorCounts[wordId] ?? 0) + 1;
    _wordErrorCounts[wordId] = errorCount;

    // 记录最近答题的单词ID（用于避重选择）
    _lastAnsweredWordId = wordId;

    _log.i(
      '单词$wordId答错，累计错误$errorCount次，保持答对次数: ${_wordCorrectCounts[wordId] ?? 0}',
    );
  }

  /// 移动到下一组
  /// 返回是否成功移动
  bool moveToNextGroup() {
    if (isLastGroup) {
      _log.i('已是最后一组，无法进入下一组');
      return false;
    }

    _currentGroupIndex.value++;
    _log.i('成功进入第${_currentGroupIndex.value + 1}组');
    return true;
  }

  /// 获取下一个需要复习的单词
  WordModel? getNextWordToReview() {
    for (final word in currentGroup) {
      if (!_completedWords.contains(word.id)) {
        return word;
      }
    }
    return null;
  }

  /// 获取单词的错误次数（用于质量评分）
  int getWordErrorCount(int wordId) {
    return _wordErrorCounts[wordId] ?? 0;
  }

  /// 获取单词的答对次数
  int getWordCorrectCount(int wordId) {
    return _wordCorrectCounts[wordId] ?? 0;
  }

  /// 检查单词是否已完成
  bool isWordCompleted(int wordId) {
    return _completedWords.contains(wordId);
  }

  /// 设置详情页要显示的单词ID
  void setCurrentDetailWordId(int wordId) {
    _currentDetailWordId.value = wordId;
    _log.i('设置详情页单词ID: $wordId');
  }

  /// 分组处理
  void _groupWords() {
    _wordGroups.clear();
    const groupSize = 5;

    for (int i = 0; i < _allReviewWords.length; i += groupSize) {
      final end =
          (i + groupSize < _allReviewWords.length)
              ? i + groupSize
              : _allReviewWords.length;
      _wordGroups.add(_allReviewWords.sublist(i, end));
    }

    _log.i('单词分组完成: ${_wordGroups.length}组');
  }

  /// 重置计数器
  void _resetCounters() {
    _currentGroupIndex.value = 0;
    _wordCorrectCounts.clear();
    _wordErrorCounts.clear();
    _completedWords.clear();
    _lastAnsweredWordId = -1;
    _totalCorrectAnswers.value = 0;
  }

  /// 清理状态
  void clear() {
    _allReviewWords.clear();
    _wordGroups.clear();
    _resetCounters();
    _log.i('复习状态已清理');
  }

  /// 获取复习统计数据
  Map<String, dynamic> getReviewStats() {
    final totalQuizCount =
        _wordCorrectCounts.values.fold(0, (sum, count) => sum + count) +
        _wordErrorCounts.values.fold(0, (sum, count) => sum + count);
    final correctAnswers = _wordCorrectCounts.values.fold(
      0,
      (sum, count) => sum + count,
    );
    final incorrectAnswers = _wordErrorCounts.values.fold(
      0,
      (sum, count) => sum + count,
    );

    return {
      'totalWords': _allReviewWords.length,
      'completedWords': _completedWords.length,
      'totalQuizCount': totalQuizCount,
      'correctAnswers': correctAnswers,
      'incorrectAnswers': incorrectAnswers,
      'accuracy':
          totalQuizCount > 0 ? (correctAnswers / totalQuizCount * 100) : 0.0,
      'duration': reviewDuration?.inSeconds ?? 0,
      'startTime': _reviewStartTime?.toIso8601String(),
      'endTime': _reviewEndTime?.toIso8601String(),
    };
  }
}
