import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/data/models/user_word_book_model.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/user_word_book_repository.dart';

/// 用户单词本全局状态服务
///
/// 职责：
/// - 管理全局的用户单词本状态
/// - 提供单词本相关的业务操作
/// - 通知状态变化给监听者
///
/// 使用场景：
/// - 首页监听激活单词本状态
/// - 词汇库页面调用激活操作
/// - 其他需要单词本状态的页面
class UserWordBookService extends GetxService {
  final UserWordBookRepository _repository;
  final LogService _log;

  UserWordBookService({
    required UserWordBookRepository repository,
    required LogService log,
  }) : _repository = repository,
       _log = log;

  // ==================== 全局状态 ====================
  /// 当前激活的用户单词本
  final Rx<UserWordBookModel?> activeWordBook = Rx<UserWordBookModel?>(null);

  /// 所有用户单词本列表
  final RxList<UserWordBookModel> allUserWordBooks = <UserWordBookModel>[].obs;

  /// 是否有激活的单词本
  RxBool get hasActiveWordBook => (activeWordBook.value != null).obs;

  /// 加载状态
  final RxBool isLoading = false.obs;

  // ==================== 服务初始化 ====================
  @override
  Future<void> onInit() async {
    super.onInit();
    _log.i('UserWordBookService 初始化');
    await loadInitialData();
  }

  /// 加载初始数据
  Future<void> loadInitialData() async {
    try {
      isLoading.value = true;
      await Future.wait([loadActiveWordBook(), loadAllUserWordBooks()]);
    } catch (e) {
      _log.e('加载初始数据失败', error: e);
    } finally {
      isLoading.value = false;
    }
  }

  // ==================== 核心业务方法 ====================
  /// 加载当前激活的单词本
  Future<void> loadActiveWordBook() async {
    try {
      _log.i('加载激活单词本');
      final wordBook = await _repository.getActiveUserWordBook();
      activeWordBook.value = wordBook;
      _log.i('激活单词本加载完成: ${wordBook?.wordBook.value?.name ?? "无"}');
    } catch (e) {
      _log.e('加载激活单词本失败', error: e);
      activeWordBook.value = null;
    }
  }

  /// 加载所有用户单词本
  Future<void> loadAllUserWordBooks() async {
    try {
      _log.i('加载所有用户单词本');
      final wordBooks = await _repository.getAllUserWordBooks();
      allUserWordBooks.assignAll(wordBooks);
      _log.i('用户单词本加载完成，数量: ${wordBooks.length}');
    } catch (e) {
      _log.e('加载所有用户单词本失败', error: e);
      allUserWordBooks.clear();
    }
  }

  /// 激活指定的用户单词本
  ///
  /// 这是核心方法，会触发全局状态变化，所有监听者会自动更新
  Future<void> activateWordBook(int userWordBookId) async {
    try {
      _log.i('激活用户单词本: $userWordBookId');
      isLoading.value = true;

      // 调用Repository执行激活操作
      await _repository.activateUserWordBook(userWordBookId);

      // 重新加载激活单词本状态
      await loadActiveWordBook();

      // 重新加载所有单词本（更新激活状态）
      await loadAllUserWordBooks();

      _log.i('用户单词本激活成功: $userWordBookId');
    } catch (e) {
      _log.e('激活用户单词本失败', error: e);
      rethrow;
    } finally {
      isLoading.value = false;
    }
  }

  /// 下载并创建用户单词本
  Future<void> downloadAndCreateWordBook(int wordBookId) async {
    try {
      _log.i('下载并创建用户单词本: $wordBookId');
      isLoading.value = true;

      // 调用Repository执行下载操作
      await _repository.downloadAndCreateUserWordBook(wordBookId);

      // 重新加载所有单词本
      await loadAllUserWordBooks();

      _log.i('用户单词本下载创建成功: $wordBookId');
    } catch (e) {
      _log.e('下载创建用户单词本失败', error: e);
      rethrow;
    } finally {
      isLoading.value = false;
    }
  }

  /// 刷新单词本数据
  ///
  /// 供外部调用，重新加载所有单词本相关数据
  Future<void> refreshWordBookData() async {
    _log.i('刷新单词本数据');
    await loadInitialData();
  }

  /// 停用用户单词本
  Future<void> deactivateWordBook(int userWordBookId) async {
    try {
      _log.i('停用用户单词本: $userWordBookId');

      // 调用Repository停用单词本
      await _repository.deactivateUserWordBook(userWordBookId);

      // 刷新数据以更新状态
      await refreshWordBookData();

      _log.i('用户单词本停用成功');
    } catch (e) {
      _log.e('停用用户单词本失败: $e');
      rethrow;
    }
  }

  /// 更新每日学习单词数量
  ///
  /// 🔑 优化：只更新数据库和内存中的字段值，避免触发复习计划重新加载
  Future<void> updateDailyNewWordsCount(
    int userWordBookId,
    int dailyCount,
  ) async {
    try {
      _log.i('更新用户单词本每日学习数量: $userWordBookId -> $dailyCount');

      // 调用Repository更新设置
      await _repository.updateDailyNewWordsCount(userWordBookId, dailyCount);

      // 🔑 关键优化：只更新内存中的字段值，不触发响应式更新
      // 避免触发 activeWordBook 监听器，防止 ReviewPlanService 重新加载
      try {
        final targetBook = allUserWordBooks.firstWhere(
          (book) => book.id == userWordBookId,
        );
        // 直接更新字段值，不调用 .refresh()
        targetBook.dailyNewWordsCount = dailyCount;
      } catch (e) {
        _log.w('未找到ID为 $userWordBookId 的用户单词本');
      }

      // 如果是激活的单词本，也更新激活单词本的字段值
      if (activeWordBook.value?.id == userWordBookId) {
        // 直接更新字段值，不调用 .refresh()
        activeWordBook.value!.dailyNewWordsCount = dailyCount;
      }

      _log.i('用户单词本每日学习数量更新成功');
    } catch (e) {
      _log.e('更新用户单词本每日学习数量失败: $e');
      rethrow;
    }
  }

  // ==================== 便捷方法 ====================
  /// 根据ID获取用户单词本
  UserWordBookModel? getUserWordBookById(int id) {
    try {
      return allUserWordBooks.firstWhere((book) => book.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 检查指定单词本是否已下载
  bool isWordBookDownloaded(int wordBookId) {
    return allUserWordBooks.any((book) => book.wordBookId == wordBookId);
  }

  /// 获取已完成的单词本数量
  int get completedWordBooksCount {
    return allUserWordBooks.where((book) => book.isCompleted).length;
  }

  /// 获取正在学习的单词本数量
  int get learningWordBooksCount {
    return allUserWordBooks.where((book) => !book.isCompleted).length;
  }
}
