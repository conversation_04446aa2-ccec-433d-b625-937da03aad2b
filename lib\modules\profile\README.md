# profile 模块说明文档

## 模块职责

`profile` 模块负责用户个人资料的展示、编辑和管理功能。它是用户管理个人信息、查看学习统计、设置应用偏好的中心区域，提供了用户与应用个人化设置的交互界面。

## 目录结构

```
profile/
├── bindings/
│   └── profile_binding.dart       # 注册ProfileController及其依赖
├── controllers/
│   ├── profile_controller.dart    # 处理个人资料主页业务逻辑
│   └── edit_profile_controller.dart # 处理编辑个人资料业务逻辑
├── views/
│   ├── profile_page.dart          # 个人资料主页面
│   ├── edit_profile_page.dart     # 编辑个人资料页面
│   └── widgets/                   # profile模块特有UI组件
│       ├── achievement_card.dart  # 成就卡片组件
│       └── settings_item.dart     # 设置项组件
└── routes/
    └── profile_routes.dart        # 定义个人资料相关路由
```

## 核心功能

- **个人信息展示**：显示用户头像、昵称、等级、简介等基本信息
- **个人资料编辑**：修改个人基本信息，如头像、昵称、简介等
- **学习统计**：展示学习时长、单词量、连续打卡等统计数据
- **成就系统**：显示用户已获得的成就和徽章
- **应用设置**：提供应用主题、通知、语言等设置选项
- **账号管理**：处理修改密码、注销账号等账号安全操作

## 主要文件说明

### profile_binding.dart

- **职责**：注册个人资料模块所需的依赖项
- **依赖项**：ProfileController、UserRepository、SettingsRepository等
- **使用方式**：在路由定义中通过 `binding: ProfileBinding()` 使用

### profile_controller.dart

- **职责**：管理个人资料页面状态和业务逻辑
- **依赖**：UserRepository、SettingsRepository、AchievementRepository等
- **主要功能**：
  - 加载用户个人信息
  - 获取学习统计数据
  - 加载用户成就列表
  - 处理设置项的导航

### profile_page.dart

- **职责**：构建个人资料页面界面
- **依赖**：ProfileController
- **主要组件**：
  - 用户头像和基本信息区
  - 学习统计卡片
  - 成就展示区
  - 设置项列表
  - 账号安全操作区

### edit_profile_controller.dart

- **职责**：管理编辑个人资料页面的状态和业务逻辑
- **依赖**：UserRepository、StorageService
- **主要功能**：
  - 加载当前用户信息
  - 处理表单验证
  - 上传新头像
  - 提交更新的个人资料

## 数据流

1. `profile_page.dart` 初始化时，加载 `ProfileController`
2. `ProfileController` 在 `onInit()` 中加载用户信息、学习统计和成就数据
3. 数据加载过程中，UI显示加载状态
4. 数据加载完成后，通过 `Obx()` 自动更新UI
5. 用户点击"编辑资料"按钮时，导航至 `edit_profile_page.dart`
6. `edit_profile_page.dart` 加载 `EditProfileController`，用于处理编辑逻辑
7. 用户提交修改后，更新的数据保存到服务器，并返回到个人资料页
8. `ProfileController` 重新加载数据，展示更新后的信息

## 与其他模块的关系

- **auth模块**：依赖auth模块提供的登录状态和用户身份验证
- **home模块**：首页提供个人资料入口，展示用户基本信息
- **learning模块**：获取学习统计数据，展示学习进度和成就
- **settings模块（可选）**：处理更多复杂设置，如通知设置、隐私设置等

## 易混淆点

1. **ProfileController vs AuthService**：
   - `ProfileController`：管理个人资料页面UI相关的状态和逻辑
   - `AuthService`：管理用户认证状态和Token，不处理具体页面逻辑

2. **编辑个人资料与修改密码**：
   - `edit_profile_page.dart`：处理普通个人信息修改（昵称、头像等）
   - 修改密码通常需要额外的验证流程，可能是单独的页面

## 最佳实践

- **渐进式表单加载**：先加载基本信息，后加载需要额外API请求的数据
- **缓存用户头像**：使用缓存管理用户头像，避免频繁加载
- **字段验证**：在客户端进行基本的表单验证，提高用户体验
- **适当的错误处理**：为网络请求失败提供友好的错误提示和重试机制
- **图片优化**：上传头像前进行适当压缩，优化传输性能

## 代码示例

### ProfileController实现

```dart
class ProfileController extends GetxController {
  final UserRepository _userRepository = Get.find<UserRepository>();
  final AchievementRepository _achievementRepository = Get.find<AchievementRepository>();
  
  final RxBool isLoading = true.obs;
  final RxBool hasError = false.obs;
  final RxString errorMessage = ''.obs;
  
  final Rx<User> user = User.empty().obs;
  final Rx<LearningStats> learningStats = LearningStats.empty().obs;
  final RxList<Achievement> achievements = <Achievement>[].obs;
  
  @override
  void onInit() {
    super.onInit();
    loadUserData();
  }
  
  Future<void> loadUserData() async {
    isLoading.value = true;
    hasError.value = false;
    
    try {
      // 并行加载用户数据和统计信息
      final results = await Future.wait([
        _userRepository.getCurrentUser(),
        _userRepository.getLearningStats(),
        _achievementRepository.getUserAchievements(),
      ]);
      
      user.value = results[0] as User;
      learningStats.value = results[1] as LearningStats;
      achievements.value = results[2] as List<Achievement>;
    } catch (e) {
      hasError.value = true;
      errorMessage.value = '加载用户数据失败: $e';
    } finally {
      isLoading.value = false;
    }
  }
  
  void navigateToEditProfile() {
    Get.toNamed(ProfileRoutes.EDIT_PROFILE);
  }
  
  void navigateToSettings() {
    Get.toNamed(AppRoutes.SETTINGS);
  }
  
  void navigateToAchievements() {
    Get.toNamed(ProfileRoutes.ACHIEVEMENTS);
  }
  
  void logout() {
    // 显示确认对话框
    Get.dialog(
      AlertDialog(
        title: Text('确认退出'),
        content: Text('您确定要退出登录吗？'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              Get.find<AuthService>().logout();
              Get.offAllNamed(AuthRoutes.LOGIN);
            },
            child: Text('确认'),
          ),
        ],
      ),
    );
  }
  
  Future<void> refreshData() async {
    await loadUserData();
  }
}
```

### ProfilePage实现

```dart
class ProfilePage extends GetView<ProfileController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('个人资料'),
        actions: [
          IconButton(
            icon: Icon(Icons.settings),
            onPressed: controller.navigateToSettings,
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: controller.refreshData,
        child: Obx(() {
          if (controller.isLoading.value) {
            return Center(child: CircularProgressIndicator());
          }
          
          if (controller.hasError.value) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(controller.errorMessage.value),
                  ElevatedButton(
                    onPressed: controller.loadUserData,
                    child: Text('重试'),
                  ),
                ],
              ),
            );
          }
          
          return ListView(
            padding: EdgeInsets.all(16),
            children: [
              // 用户基本信息卡片
              Card(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    children: [
                      // 头像
                      CircleAvatar(
                        radius: 50,
                        backgroundImage: NetworkImage(controller.user.value.avatarUrl),
                      ),
                      SizedBox(height: 16),
                      
                      // 用户名
                      Text(
                        controller.user.value.username,
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      SizedBox(height: 8),
                      
                      // 等级信息
                      Text(
                        '等级 ${controller.user.value.level} · ${controller.user.value.points} 积分',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      SizedBox(height: 16),
                      
                      // 编辑按钮
                      ElevatedButton(
                        onPressed: controller.navigateToEditProfile,
                        child: Text('编辑资料'),
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 24),
              
              // 学习统计
              Text('学习统计', style: Theme.of(context).textTheme.titleMedium),
              SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildStatItem(
                        icon: Icons.calendar_today,
                        value: '${controller.learningStats.value.streakDays}',
                        label: '连续打卡',
                      ),
                      _buildStatItem(
                        icon: Icons.book,
                        value: '${controller.learningStats.value.learnedWords}',
                        label: '已学单词',
                      ),
                      _buildStatItem(
                        icon: Icons.access_time,
                        value: '${controller.learningStats.value.totalMinutes}',
                        label: '学习分钟',
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 24),
              
              // 成就展示
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('我的成就', style: Theme.of(context).textTheme.titleMedium),
                  TextButton(
                    onPressed: controller.navigateToAchievements,
                    child: Text('查看全部'),
                  ),
                ],
              ),
              SizedBox(height: 16),
              SizedBox(
                height: 120,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: controller.achievements.length.clamp(0, 5),
                  itemBuilder: (context, index) {
                    final achievement = controller.achievements[index];
                    return AchievementCard(achievement: achievement);
                  },
                ),
              ),
              SizedBox(height: 24),
              
              // 设置项
              Text('设置', style: Theme.of(context).textTheme.titleMedium),
              SizedBox(height: 16),
              SettingsItem(
                icon: Icons.lock,
                title: '修改密码',
                onTap: () => Get.toNamed(ProfileRoutes.CHANGE_PASSWORD),
              ),
              SettingsItem(
                icon: Icons.language,
                title: '语言设置',
                onTap: () => Get.toNamed(AppRoutes.LANGUAGE_SETTINGS),
              ),
              SettingsItem(
                icon: Icons.help,
                title: '帮助与反馈',
                onTap: () => Get.toNamed(AppRoutes.HELP),
              ),
              SettingsItem(
                icon: Icons.info,
                title: '关于我们',
                onTap: () => Get.toNamed(AppRoutes.ABOUT),
              ),
              SizedBox(height: 24),
              
              // 退出登录按钮
              ElevatedButton(
                onPressed: controller.logout,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                ),
                child: Text('退出登录'),
              ),
            ],
          );
        }),
      ),
    );
  }
  
  Widget _buildStatItem({required IconData icon, required String value, required String label}) {
    return Column(
      children: [
        Icon(icon, size: 30),
        SizedBox(height: 8),
        Text(value, style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
        Text(label),
      ],
    );
  }
}
```

### EditProfileController实现

```dart
class EditProfileController extends GetxController {
  final UserRepository _userRepository = Get.find<UserRepository>();
  final StorageService _storageService = Get.find<StorageService>();
  
  final RxBool isLoading = false.obs;
  final RxBool isSaving = false.obs;
  final RxBool hasError = false.obs;
  final RxString errorMessage = ''.obs;
  
  // 表单控制器
  late TextEditingController usernameController;
  late TextEditingController bioController;
  
  // 当前用户数据
  final Rx<User> user = User.empty().obs;
  
  // 新头像文件
  final Rxn<File> newAvatarFile = Rxn<File>();
  
  @override
  void onInit() {
    super.onInit();
    usernameController = TextEditingController();
    bioController = TextEditingController();
    loadUserData();
  }
  
  @override
  void onClose() {
    usernameController.dispose();
    bioController.dispose();
    super.onClose();
  }
  
  Future<void> loadUserData() async {
    isLoading.value = true;
    hasError.value = false;
    
    try {
      final userData = await _userRepository.getCurrentUser();
      user.value = userData;
      
      // 填充表单
      usernameController.text = userData.username;
      bioController.text = userData.bio ?? '';
    } catch (e) {
      hasError.value = true;
      errorMessage.value = '加载用户数据失败: $e';
    } finally {
      isLoading.value = false;
    }
  }
  
  Future<void> pickImage() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(source: ImageSource.gallery);
      
      if (image != null) {
        newAvatarFile.value = File(image.path);
      }
    } catch (e) {
      Get.snackbar('错误', '选择图片失败: $e');
    }
  }
  
  Future<void> saveProfile() async {
    // 表单验证
    if (usernameController.text.trim().isEmpty) {
      Get.snackbar('错误', '用户名不能为空');
      return;
    }
    
    isSaving.value = true;
    
    try {
      // 如果有新头像，先上传
      String? newAvatarUrl;
      if (newAvatarFile.value != null) {
        newAvatarUrl = await _userRepository.uploadAvatar(newAvatarFile.value!);
      }
      
      // 更新用户资料
      final updatedUser = user.value.copyWith(
        username: usernameController.text.trim(),
        bio: bioController.text.trim(),
        avatarUrl: newAvatarUrl ?? user.value.avatarUrl,
      );
      
      await _userRepository.updateUserProfile(updatedUser);
      
      Get.snackbar('成功', '个人资料已更新');
      Get.back(); // 返回个人资料页
    } catch (e) {
      Get.snackbar('错误', '保存资料失败: $e');
    } finally {
      isSaving.value = false;
    }
  }
}
```

## 相关联文件

- `lib/app/services/auth_service.dart`：验证用户登录状态，提供登出功能
- `lib/data/repositories/user_repository.dart`：获取和更新用户数据
- `lib/data/repositories/achievement_repository.dart`：获取用户成就数据
- `lib/data/models/user_model.dart`：用户数据模型
- `lib/data/models/achievement_model.dart`：成就数据模型
- `lib/app/global/widgets/avatar_widget.dart`：通用头像组件（如果存在）

## 常见问题与解决方案

- **头像上传失败**：
  - 检查文件大小，可能需要压缩后再上传
  - 验证网络连接和服务器状态
  - 确保用户已授权应用访问相册权限

- **个人资料无法保存**：
  - 验证表单输入是否符合服务器的字段限制
  - 检查网络连接状态
  - 实现重试机制和错误提示

- **学习统计数据不同步**：
  - 实现数据刷新机制
  - 添加上次更新时间显示
  - 与learning模块保持数据一致性 