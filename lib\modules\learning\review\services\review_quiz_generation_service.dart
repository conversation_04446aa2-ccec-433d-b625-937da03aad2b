import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/quiz/quiz_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/word_repository.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/services/review_state_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/services/review_navigation_service.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_model.dart';

/// 复习测验题生成服务
///
/// 负责生成和管理复习测验题，实现复习特有的题目选择逻辑和流程控制
/// 职责：
/// - 根据复习逻辑生成测验题
/// - 集中复习完成判断
/// - 负责结果页面导航
/// - 实现5个单词一组，每个单词答对3次的复习逻辑
class ReviewQuizGenerationService extends GetxService {
  // ==================== 依赖注入 ====================
  final QuizRepository _quizRepository;
  final WordRepository _wordRepository;
  final ReviewStateService _stateService;
  final ReviewNavigationService _navigationService;
  final LogService _log;

  ReviewQuizGenerationService({
    required QuizRepository quizRepository,
    required WordRepository wordRepository,
    required ReviewStateService stateService,
    required ReviewNavigationService navigationService,
    required LogService log,
  }) : _quizRepository = quizRepository,
       _wordRepository = wordRepository,
       _stateService = stateService,
       _navigationService = navigationService,
       _log = log;

  // ==================== 公开方法 ====================

  /// 生成第一题
  Future<void> generateFirstQuiz() async {
    try {
      _log.i('生成第一道复习测验题');

      final firstWord = _selectFirstWord();
      if (firstWord != null) {
        await _generateQuizForWord(firstWord);
      } else {
        // 没有单词需要复习，直接导航到结果页面
        _log.i('没有单词需要复习，导航到结果页面');
        await _navigationService.navigateToResult();
      }
    } catch (e) {
      _handleQuizGenerationError('生成第一道测验题', e);
    }
  }

  /// 生成下一题
  Future<void> generateNextQuiz() async {
    try {
      _log.i('生成下一道复习测验题');

      final nextWord = _selectNextWord();
      _log.i('选择的下一个单词: ${nextWord?.spelling ?? "null"}');

      if (nextWord != null) {
        // 还有单词需要复习，生成题目
        await _generateQuizForWord(nextWord);
      } else {
        // 复习完成，直接导航到结果页面
        _log.i('所有单词复习完成，导航到结果页面');
        await _navigationService.navigateToResult();
      }
    } catch (e) {
      _handleQuizGenerationError('生成下一道测验题', e);
    }
  }

  /// 为指定单词生成题目（详情页调用）
  Future<void> generateQuizForWord(WordModel word) async {
    try {
      _log.i('为指定单词 ${word.spelling} 生成测验题');
      await _generateQuizForWord(word);
    } catch (e) {
      _handleQuizGenerationError('为指定单词生成测验题', e);
    }
  }

  // ==================== 私有方法 ====================

  /// 内部方法：为单词生成测验题并更新状态
  Future<void> _generateQuizForWord(WordModel word) async {
    // 获取所有单词（用于生成干扰项）
    final allWords = await _wordRepository.getAllWords();
    if (allWords.isEmpty) {
      _log.w('获取所有单词失败，无法生成干扰项');
      _stateService.currentQuiz.value = null;
      _stateService.currentWord.value = null;
      return;
    }

    // 生成一道测验题
    final quiz = await _quizRepository.generateRandomQuizForWord(
      word,
      allWords,
    );

    if (quiz != null) {
      _stateService.currentQuiz.value = quiz;
      _stateService.currentWord.value = word;
      _log.i('成功为单词 ${word.spelling} 生成测验题');
    } else {
      _log.w('为单词 ${word.spelling} 生成测验题失败');
      _stateService.currentQuiz.value = null;
      _stateService.currentWord.value = null;
    }
  }

  /// 选择第一个单词
  WordModel? _selectFirstWord() {
    try {
      final currentGroup = _stateService.currentGroup;
      if (currentGroup.isEmpty) {
        _log.w('当前组没有单词');
        return null;
      }

      // 随机选择第一个单词
      currentGroup.shuffle();
      final selectedWord = currentGroup.first;
      _log.i('选择第一个单词: ${selectedWord.spelling} (ID: ${selectedWord.id})');
      return selectedWord;
    } catch (e) {
      _log.e('选择第一个单词失败: $e');
      return null;
    }
  }

  /// 选择下一个单词
  WordModel? _selectNextWord() {
    try {
      _log.i('开始选择下一个单词');
      _log.i('当前组索引: ${_stateService.currentGroupIndex}');
      _log.i('当前组是否完成: ${_stateService.isCurrentGroupCompleted}');

      // 检查当前组是否完成
      if (!_stateService.isCurrentGroupCompleted) {
        // 当前组未完成，选择未完成的单词
        final currentGroup = _stateService.currentGroup;
        _log.i('当前组单词数量: ${currentGroup.length}');

        final uncompletedWords =
            currentGroup.where((word) {
              final correctCount = _stateService.getWordCorrectCount(word.id);
              _log.d(
                '单词 ${word.spelling} (ID: ${word.id}) 答对次数: $correctCount',
              );
              return correctCount < 3;
            }).toList();

        _log.i('未完成单词数量: ${uncompletedWords.length}');

        if (uncompletedWords.isNotEmpty) {
          // 优先选择非最近答题的单词（避重机制）
          final lastAnsweredWordId = _stateService.lastAnsweredWordId;
          final nonRecentWords =
              uncompletedWords
                  .where((word) => word.id != lastAnsweredWordId)
                  .toList();

          WordModel selectedWord;
          if (nonRecentWords.isNotEmpty) {
            // 有非最近答题的单词，从中随机选择
            nonRecentWords.shuffle();
            selectedWord = nonRecentWords.first;
            _log.i('避重选择单词: ${selectedWord.spelling} (ID: ${selectedWord.id})');
          } else {
            // 特殊情况：只剩最近答题的单词，直接选择
            uncompletedWords.shuffle();
            selectedWord = uncompletedWords.first;
            _log.i(
              '特殊情况选择单词: ${selectedWord.spelling} (ID: ${selectedWord.id})',
            );
          }

          _log.i(
            '当前组未完成，选择单词: ${selectedWord.spelling} (ID: ${selectedWord.id})',
          );
          return selectedWord;
        }
      }

      // 当前组已完成，尝试进入下一组
      if (!_stateService.isLastGroup) {
        _stateService.moveToNextGroup();
        _log.i('进入下一组: ${_stateService.currentGroupIndex + 1}');

        // 选择新组中的第一个单词
        final nextGroupWords = _stateService.currentGroup;
        if (nextGroupWords.isNotEmpty) {
          nextGroupWords.shuffle();
          final selectedWord = nextGroupWords.first;
          _log.i(
            '新组开始，选择单词: ${selectedWord.spelling} (ID: ${selectedWord.id})',
          );
          return selectedWord;
        }
      }

      // 所有组都完成了
      _log.i('所有复习组都已完成');
      return null;
    } catch (e) {
      _log.e('选择下一个复习单词失败: $e');
      return null;
    }
  }

  /// 统一处理题目生成错误
  void _handleQuizGenerationError(String operation, dynamic error) {
    _log.e('$operation失败: $error');
    Get.snackbar('错误', '生成测验题失败，请重试');
  }
}
