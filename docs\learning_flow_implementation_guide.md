# 单词学习流程实现指南

## 1. 项目概述

本文档提供Flutter单词学习应用中学习流程模块的详细实现步骤。学习流程包含回忆、详情、测验三个微观阶段，以及学习、最终测验和结果展示三个宏观阶段。同时实现会话中断与恢复机制，确保用户体验连贯。

## 2. 文件结构与职责

### 2.1 服务层文件

**位置：modules/learning/services/**

1. **learning_state_service.dart**
   - 职责：管理内存中的学习状态
   - 主要方法：
     - `updateWordRecognition(int wordId, bool isRecognized)`
     - `incrementErrorCount(int wordId)`
     - `incrementCompletedQuizzes(int wordId)`
     - `getWordLearningState(int wordId)`
     - `isLastWord()`
     - `getAllWordStates()`

2. **learning_persistence_service.dart**
   - 职责：处理状态保存和恢复
   - 主要方法：
     - `createNewSession(int wordBookId, List<int> wordIds)`
     - `saveCurrentState(bool enteringFinalQuiz = false)`
     - `restoreSession(LearningSession session)`

3. **learning_navigation_service.dart**
   - 职责：处理页面导航
   - 主要方法：
     - `navigateToRecall(int wordIndex)`
     - `navigateToDetail(int wordIndex)`
     - `navigateToQuiz(int wordIndex)`
     - `navigateToFinalQuiz()`
     - `navigateToResult()`

4. **quiz_generation_service.dart**
   - 职责：生成测验题
   - 主要方法：
     - `generateQuizzesForWord(int wordId, int count)`
     - `generateFinalQuizzes(Map<int, WordLearningState> wordStates)`

### 2.2 模型文件

**位置：modules/learning/models/**

1. **word_learning_state.dart**
   - 职责：定义单词学习状态模型
   - 主要属性：
     - `bool isRecognized`
     - `int errorCount`
     - `int completedQuizzes`

### 2.3 控制器层

1. **modules/learning/controllers/learning_controller.dart**
   - 职责：协调整体学习流程
   - 主要方法：
     - `startNewSession(int wordBookId)`
     - `completeSession()`
     - `updateWordReviewItem(int wordId)`

2. **modules/learning/recall/controllers/recall_controller.dart**
   - 职责：处理回忆阶段逻辑
   - 主要方法：
     - `markAsRecognized()`
     - `markAsUnrecognized()`
     - `navigateToDetail()`

3. **modules/learning/detail/controllers/detail_controller.dart**
   - 职责：处理详情阶段逻辑
   - 主要方法：
     - `loadWordDetails(int wordId)`
     - `startQuiz()`

4. **modules/learning/quiz/controllers/quiz_controller.dart**
   - 职责：处理测验阶段逻辑
   - 主要方法：
     - `loadQuizzes()`
     - `submitAnswer(String answer)`
     - `handleCorrectAnswer()`
     - `handleWrongAnswer()`
     - `checkWordCompletion()`
     - `handleWordLearningQuality()`

### 2.4 视图层

1. **modules/learning/recall/views/recall_page.dart**
   - 职责：实现回忆阶段UI

## 3. 实现步骤

### 3.1 创建基础模型

1. **创建WordLearningState模型**
   - 定义单词学习状态属性
   - 实现JSON序列化和反序列化方法

### 3.2 实现服务层

1. **实现learning_state_service.dart**
   - 创建内存中维护的单词学习状态Map
   - 实现状态操作方法

2. **实现learning_persistence_service.dart**
   - 与LearningSessionRepository交互
   - 实现会话状态的序列化与反序列化

3. **实现learning_navigation_service.dart**
   - 使用Get.to系列方法实现页面导航
   - 处理不同阶段的页面跳转逻辑

4. **实现quiz_generation_service.dart**
   - 与QuizRepository交互生成测验题
   - 实现最终测验题的生成逻辑

### 3.3 实现控制器层

1. **实现learning_controller.dart**
   - 注入所有服务依赖
   - 实现学习流程协调逻辑

2. **实现recall_controller.dart**
   - 处理用户对单词认知状态的标记
   - 通过learning_state_service更新状态

3. **实现detail_controller.dart**
   - 加载单词详细信息
   - 处理用户进入测验的交互

4. **实现quiz_controller.dart**
   - 处理答题逻辑
   - 实现状态保存触发点
   - 处理学习质量记录

### 3.4 实现视图层

1. **实现recall_page.dart**
   - 显示单词拼写，隐藏释义
   - 提供"认识"与"不认识"按钮

## 3. 详细实现步骤

### 3.1 准备工作

1. **检查必要的依赖**
   - 确保Isar数据库依赖已配置
   - 验证GetX状态管理包已正确安装
   - 确认项目结构符合规范

2. **更新数据层**
   - 确认`data/models/learning_session.dart`已按照设计规范实现
   - 验证`data/repositories/learning_session_repository.dart`提供了必要的CRUD操作方法
   - 运行`flutter pub run build_runner build`生成必要的Isar模型代码

### 3.2 创建基础模型

1. **创建WordLearningState模型**
   - 在`modules/learning/models/`目录创建`word_learning_state.dart`
   - 实现以下属性：
     * `bool isRecognized`（用户是否认识此单词）
     * `int errorCount`（错误次数累计）
     * `int completedQuizzes`（已完成测验数量）
   - 添加`toJson()`和`fromJson()`方法，用于序列化和反序列化
   - 添加构造函数和命名构造函数`WordLearningState.initial()`

### 3.3 实现服务层

1. **实现learning_state_service.dart**
   - 创建`modules/learning/services/learning_state_service.dart`
   - 实现以下私有属性：
     * `Map<int, WordLearningState> _wordStates`（单词ID到学习状态的映射）
     * `List<int> _wordIds`（学习单词ID列表）
     * `int _currentWordIndex`（当前学习单词索引）
   - 实现以下公共方法：
     * `initialize(List<int> wordIds)`：初始化服务
     * `updateWordRecognition(int wordId, bool isRecognized)`
     * `incrementErrorCount(int wordId)`
     * `incrementCompletedQuizzes(int wordId)`
     * `getWordLearningState(int wordId)`
     * `isLastWord()`：检查是否是最后一个单词
     * `getCurrentWordId()`：获取当前学习单词ID
     * `moveToNextWord()`：移动到下一个单词
     * `getAllWordStates()`：获取所有单词状态
   - 注册为GetX服务:`Get.lazyPut<LearningStateService>(() => LearningStateService());`

2. **实现learning_persistence_service.dart**
   - 创建`modules/learning/services/learning_persistence_service.dart`
   - 注入依赖：
     * `final LearningSessionRepository _repository`
     * `final LearningStateService _stateService`
   - 实现以下方法：
     * `createNewSession(int wordBookId, List<int> wordIds)`：创建并保存新会话
     * `saveCurrentState(bool enteringFinalQuiz = false)`：保存当前状态
     * `restoreSession(LearningSession session)`：恢复会话状态
   - 注册为GetX服务

3. **实现learning_navigation_service.dart**
   - 创建`modules/learning/services/learning_navigation_service.dart`
   - 注入依赖：`final LearningStateService _stateService`
   - 实现页面导航方法：
     * `navigateToRecall(int wordIndex)`：使用`Get.to`导航到回忆页面
     * `navigateToDetail(int wordIndex)`：导航到详情页面
     * `navigateToQuiz(int wordIndex)`：导航到测验页面
     * `navigateToFinalQuiz()`：导航到最终测验页面
     * `navigateToResult()`：导航到结果页面
   - 注册为GetX服务

4. **实现quiz_generation_service.dart**
   - 创建`modules/learning/services/quiz_generation_service.dart`
   - 注入依赖：`final QuizRepository _quizRepository`
   - 实现以下方法：
     * `generateQuizzesForWord(int wordId, int count)`：生成单个单词的测验题
     * `generateFinalQuizzes(Map<int, WordLearningState> wordStates)`：生成最终测验题
     * `_ensureNoRepeatQuizzes(List<Quiz> existingQuizzes, List<Quiz> newQuizzes)`：确保无重复题目
   - 注册为GetX服务

### 3.4 实现控制器层

1. **实现learning_controller.dart**
   - 创建`modules/learning/controllers/learning_controller.dart`
   - 继承`GetxController`
   - 注入所有服务依赖：
     * `final LearningStateService _stateService`
     * `final LearningPersistenceService _persistenceService`
     * `final LearningNavigationService _navigationService`
     * `final QuizGenerationService _quizGenerationService`
     * `final ReviewItemRepository _reviewItemRepository`
   - 实现核心方法：
     * `startNewSession(int wordBookId)`：从单词本ID开始新会话
     * `completeSession()`：完成整个学习会话
     * `updateWordReviewItem(int wordId)`：更新单词复习项
   - 注册为GetX控制器：`Get.lazyPut(() => LearningController(...));`

2. **实现recall_controller.dart**
   - 创建`modules/learning/recall/controllers/recall_controller.dart`
   - 继承`GetxController`
   - 注入依赖：
     * `final LearningStateService _stateService`
     * `final LearningNavigationService _navigationService`
     * `final WordRepository _wordRepository`
     * `final AudioService _audioService`
   - 实现可观察变量：
     * `Rx<WordModel?> currentWord = Rx<WordModel?>(null)`
   - 实现核心方法：
     * `onInit()`：加载当前单词
     * `loadCurrentWord()`：从仓库加载单词数据
     * `playAudio()`：播放单词音频
     * `markAsRecognized()`：标记为认识并导航到详情
     * `markAsUnrecognized()`：标记为不认识并导航到详情
   - 注册为GetX控制器

3. **实现detail_controller.dart**
   - 创建`modules/learning/detail/controllers/detail_controller.dart`
   - 继承`GetxController`
   - 注入依赖：
     * `final LearningNavigationService _navigationService`
     * `final WordRepository _wordRepository`
     * `final AudioService _audioService`
   - 实现可观察变量：
     * `Rx<WordModel?> wordDetail = Rx<WordModel?>(null)`
   - 实现核心方法：
     * `onInit()`：加载单词详情
     * `loadWordDetails(int wordId)`：加载详细信息
     * `playAudio()`：播放单词音频
     * `startQuiz()`：开始测验
   - 注册为GetX控制器

4. **实现quiz_controller.dart**
   - 创建`modules/learning/quiz/controllers/quiz_controller.dart`
   - 继承`GetxController`
   - 注入依赖：
     * `final LearningStateService _stateService`
     * `final LearningPersistenceService _persistenceService`
     * `final LearningNavigationService _navigationService`
     * `final QuizGenerationService _quizGenerationService`
     * `final LearningController _learningController`
     * `final ReviewItemRepository _reviewItemRepository`
   - 实现可观察变量：
     * `RxList<Quiz> quizzes = RxList<Quiz>([])`
     * `RxInt currentQuizIndex = 0.obs`
     * `RxBool isAnswerSubmitted = false.obs`
     * `RxBool isAnswerCorrect = false.obs`
   - 实现核心方法：
     * `onInit()`：加载测验题
     * `loadQuizzes()`：加载或生成测验题
     * `submitAnswer(String answer)`：提交答案
     * `handleCorrectAnswer()`：处理正确答案
     * `handleWrongAnswer()`：处理错误答案
     * `checkWordCompletion()`：检查单词是否完成所有测验
     * `handleWordLearningQuality()`：处理单词学习质量评分
     * `navigateNext()`：导航到下一步
   - 注册为GetX控制器

### 3.5 实现视图层

1. **实现recall_page.dart**
   - 创建`modules/learning/recall/views/recall_page.dart`
   - 继承`GetView<RecallController>`
   - 实现UI组件：
     * 单词拼写显示（不显示释义）
     * 单词音频播放按钮
     * "认识"和"不认识"按钮
   - 绑定按钮点击事件到控制器方法

2. **检查detail_page.dart实现**
   - 确保`modules/learning/detail/views/detail_page.dart`已正确实现
   - 验证以下UI组件：
     * 单词完整信息显示（拼写、音标、释义等）
     * 音频播放控件
     * "开始测验"按钮
   - 绑定控制器方法

3. **检查quiz_page.dart实现**
   - 确保`modules/learning/quiz/views/quiz_page.dart`已正确实现
   - 验证以下UI组件：
     * 测验题显示
     * 选项或输入框
     * 反馈显示区域
     * 进度指示器
   - 绑定控制器方法

### 3.6 配置路由和绑定

1. **更新learning_routes.dart**
   - 在`modules/learning/routes/learning_routes.dart`中添加新页面路由
   - 确保所有页面都有唯一的路由名称

2. **更新learning_binding.dart**
   - 在`modules/learning/bindings/learning_binding.dart`中注入所有依赖
   - 确保按正确顺序注入以避免依赖问题

3. **更新app_pages.dart**
   - 在`app/routes/app_pages.dart`中集成学习模块路由
   - 添加必要的中间件（如果需要）

### 3.7 集成和测试

1. **单元测试**
   - 为每个服务编写单元测试
   - 测试状态转换和计算逻辑
   - 验证序列化和反序列化功能

2. **集成模块到应用**
   - 在主页中添加学习入口
   - 验证导航流程
   - 测试会话恢复功能

3. **全面测试**
   - 测试完整学习流程
   - 验证状态保存和恢复
   - 测试边缘情况处理
   - 验证与其他模块的集成 