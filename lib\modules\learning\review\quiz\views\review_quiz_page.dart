import 'package:flutter/material.dart';
import 'package:flutter_study_flow_by_myself/data/models/quiz/choice_quiz_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/quiz/spelling_quiz_model.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/quiz/widgets/review_choice_quiz_widget.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/quiz/widgets/review_spelling_quiz_widget.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/quiz/controllers/review_quiz_controller.dart';

import 'package:flutter_study_flow_by_myself/modules/learning/review/services/review_state_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/widgets/review_exit_dialog.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/widgets/review_progress_bar.dart';
import 'package:get/get.dart';

/// 复习测验页面
///
/// 根据测验类型动态展示不同的测验组件，参考QuizPage的设计
/// 支持选择题和拼写题两种题型
class ReviewQuizPage extends GetView<ReviewQuizController> {
  const ReviewQuizPage({super.key});

  @override
  Widget build(BuildContext context) {
    // 获取状态服务
    final stateService = Get.find<ReviewStateService>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('单词复习'),
        elevation: 0,
        // 🔑 自定义返回按钮
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => ReviewExitDialog.show(context: context),
        ),
      ),
      body: Column(
        children: [
          // 顶部进度条
          Obx(() {
            return ReviewProgressBar(
              completedWords: stateService.totalCorrectAnswers,
              totalWords: stateService.allReviewWords.length * 3, // 每个单词需要答对3次
              currentGroupIndex: stateService.currentGroupIndex,
              totalGroups: stateService.wordGroups.length,
              currentGroupCompleted: stateService.currentGroup.fold<int>(0, (
                sum,
                word,
              ) {
                return sum + stateService.getWordCorrectCount(word.id);
              }),
              currentGroupTotal:
                  stateService.currentGroup.length * 3, // 每个单词需要答对3次
            );
          }),

          // 主要内容区域
          Expanded(
            child: Obx(() {
              // 加载状态
              if (controller.isLoading.value) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text('正在加载题目...'),
                    ],
                  ),
                );
              }

              // 获取当前题目
              final currentQuiz = stateService.currentQuiz.value;
              if (currentQuiz == null) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.quiz, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text(
                        '没有可用的题目',
                        style: TextStyle(fontSize: 18, color: Colors.grey),
                      ),
                    ],
                  ),
                );
              }

              // 根据题目类型显示不同的组件
              return Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    // 题目区域
                    Expanded(child: _buildQuizWidget(currentQuiz)),

                    // 底部操作按钮
                    _buildActionButton(),
                  ],
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  /// 根据题目类型构建对应的组件
  Widget _buildQuizWidget(dynamic quiz) {
    if (quiz is ChoiceQuizModel) {
      return ReviewChoiceQuizWidget(
        key: ValueKey('review_choice_${quiz.wordId}_${quiz.hashCode}'),
        quiz: quiz,
        controller: controller,
      );
    } else if (quiz is SpellingQuizModel) {
      return ReviewSpellingQuizWidget(
        key: ValueKey('review_spelling_${quiz.wordId}_${quiz.hashCode}'),
        quiz: quiz,
        controller: controller,
      );
    } else {
      return const Center(
        child: Text(
          '不支持的题目类型',
          style: TextStyle(fontSize: 18, color: Colors.red),
        ),
      );
    }
  }

  /// 构建底部操作按钮
  Widget _buildActionButton() {
    return Obx(() {
      final buttonText = controller.actionButtonText;

      // 如果没有按钮文本，不显示按钮
      if (buttonText.isEmpty) {
        return const SizedBox.shrink();
      }

      return Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: ElevatedButton(
          onPressed: controller.onActionButtonPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: _getButtonColor(buttonText),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 2,
          ),
          child: Text(
            buttonText,
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
          ),
        ),
      );
    });
  }

  /// 根据按钮文本获取对应的颜色
  Color _getButtonColor(String buttonText) {
    switch (buttonText) {
      case '下一题':
        return Colors.green;
      case '查看详情':
        return Colors.orange;
      default:
        return Colors.blue;
    }
  }
}
