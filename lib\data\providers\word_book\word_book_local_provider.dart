import 'package:isar/isar.dart';
import 'package:flutter_study_flow_by_myself/app/services/isar_service.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/app/exceptions/app_exception.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_book_model.dart';

/// WordBookLocalProvider 负责与本地数据库交互，管理 WordBookModel 的 CRUD 操作
///
/// 主要功能：
/// - 获取所有单词本
/// - 获取特定单词本
/// - 保存单词本
/// - 删除单词本
/// - 建立单词与单词本的关联关系
class WordBookLocalProvider {
  final IsarService _isarService;
  final LogService _log;

  WordBookLocalProvider({
    required IsarService isarService,
    required LogService log,
  }) : _isarService = isarService,
       _log = log;

  /// 获取所有单词本
  Future<List<WordBookModel>> getAllWordBooks() async {
    try {
      final wordBooks = await _isarService.getAll<WordBookModel>();
      return wordBooks;
    } catch (e) {
      _log.e('获取所有单词本失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：获取所有单词本失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 根据ID获取单个单词本
  /// [wordBookId] 单词本的 Isar ID
  /// 返回对应的 WordBookModel 对象，如果不存在则返回 null
  Future<WordBookModel?> getWordBookById(int wordBookId) async {
    try {
      return await _isarService.get<WordBookModel>(wordBookId);
    } catch (e) {
      _log.e('根据ID获取单词本失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：根据ID获取单词本失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 根据后端ID获取单词本
  /// [backendId] 单词本的后端ID
  /// 返回对应的 WordBookModel 对象，如果不存在则返回 null
  Future<WordBookModel?> getWordBookByBackendId(int backendId) async {
    try {
      // 对于有索引的可空字段，使用 where() 查询
      // 回到工作的方案
      // 由于 Isar 查询 API 问题，使用内存筛选方案
      final wordBook =
          await _isarService.isar.wordBookModels
              .filter()
              .wordBookIdEqualTo(backendId)
              .findFirst();

      return wordBook;
    } catch (e) {
      _log.e('根据后端ID获取单词本失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：根据后端ID获取单词本失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 保存单个单词本到本地数据库
  /// [wordBook] 要保存的 WordBookModel 对象
  /// 返回保存成功后单词本的 Isar ID
  Future<int> saveWordBook(WordBookModel wordBook) async {
    try {
      return await _isarService.put<WordBookModel>(wordBook);
    } catch (e) {
      _log.e('保存单词本失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：保存单词本失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 批量保存单词本到本地数据库
  /// [wordBooks] 要保存的 WordBookModel 对象列表
  /// 返回保存成功后单词本的 Isar ID 列表
  Future<List<int>> saveAllWordBooks(List<WordBookModel> wordBooks) async {
    try {
      return await _isarService.putAll<WordBookModel>(wordBooks);
    } catch (e) {
      _log.e('批量保存单词本失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：批量保存单词本失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 根据 ID 删除单个单词本
  /// [wordBookId] 要删除单词本的 Isar ID
  /// 返回 true 表示删除成功，false 表示删除失败或单词本不存在
  Future<bool> deleteWordBookById(int wordBookId) async {
    try {
      return await _isarService.delete<WordBookModel>(wordBookId);
    } catch (e) {
      _log.e('删除单词本失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：删除单词本失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 批量删除单词本
  /// [wordBookIds] 要删除单词本的 Isar ID 列表
  /// 返回成功删除的数量
  Future<int> deleteWordBooksByIds(List<int> wordBookIds) async {
    try {
      return await _isarService.deleteByIds<WordBookModel>(wordBookIds);
    } catch (e) {
      _log.e('批量删除单词本失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：批量删除单词本失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 清空所有单词本数据
  Future<void> clearAllWordBooks() async {
    try {
      await _isarService.clearCollection<WordBookModel>();
    } catch (e) {
      _log.e('清空所有单词本失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：清空所有单词本失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 查询单词本数量
  Future<int> countWordBooks() async {
    try {
      return await _isarService.count<WordBookModel>();
    } catch (e) {
      _log.e('查询单词本数量失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：查询单词本数量失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 建立单词与单词本的关联关系
  /// [words] 单词列表
  /// [wordBook] 单词本
  Future<void> associateWordsWithBook(
    List<WordModel> words,
    WordBookModel wordBook,
  ) async {
    try {
      await _isarService.isar.writeTxn(() async {
        // 建立关联关系
        for (final word in words) {
          word.wordBooks.add(wordBook);
          await word.wordBooks.save();
        }

        // 更新单词本的单词数量
        wordBook.wordCount = words.length;
        await _isarService.put<WordBookModel>(wordBook);
      });
    } catch (e) {
      _log.e('建立单词与单词本关联失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：建立单词与单词本关联失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 获取单词本中的所有单词
  /// [wordBookId] 单词本的 Isar ID
  Future<List<WordModel>> getWordsFromWordBook(int wordBookId) async {
    try {
      _log.i('开始获取单词本($wordBookId)中的单词');

      // 先获取单词本
      final wordBook = await getWordBookById(wordBookId);
      if (wordBook == null) {
        _log.w('单词本($wordBookId)不存在');
        throw AppException(AppExceptionType.database, '单词本不存在');
      }

      _log.i(
        '找到单词本: ${wordBook.name}, ID: ${wordBook.id}, 远程ID: ${wordBook.wordBookId}',
      );

      // 加载单词本关联的所有单词
      await wordBook.words.load();
      final wordsList = wordBook.words.toList();
      _log.i('单词本($wordBookId)中加载到${wordsList.length}个单词');

      // 打印前几个单词的信息（如果有）
      if (wordsList.isNotEmpty) {
        final sampleSize = wordsList.length > 3 ? 3 : wordsList.length;
        for (var i = 0; i < sampleSize; i++) {
          _log.i(
            '单词示例 $i: ${wordsList[i].spelling} - ${wordsList[i].definition}',
          );
        }
      }

      return wordsList;
    } catch (e) {
      _log.e('获取单词本中的单词失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：获取单词本中的单词失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 获取单词本中的单词数量
  /// [wordBookId] 单词本的 Isar ID
  Future<int> getWordCountInWordBook(int wordBookId) async {
    try {
      // 先获取单词本
      final wordBook = await getWordBookById(wordBookId);
      if (wordBook == null) {
        throw AppException(AppExceptionType.database, '单词本不存在');
      }

      // 计算关联的单词数量
      return await wordBook.words.count();
    } catch (e) {
      _log.e('获取单词本中的单词数量失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：获取单词本中的单词数量失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 更新单词本信息
  /// [wordBook] 要更新的单词本
  Future<int> updateWordBook(WordBookModel wordBook) async {
    try {
      return await saveWordBook(wordBook);
    } catch (e) {
      _log.e('更新单词本信息失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：更新单词本信息失败',
        type: AppExceptionType.database,
      );
    }
  }
}
