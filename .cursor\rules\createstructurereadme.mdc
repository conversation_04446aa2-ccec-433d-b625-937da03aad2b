---
description: 
globs: 
alwaysApply: false
---
我需要你为Flutter项目中的lib目录下的每个子文件夹创建详细的README.md说明文档。这个项目使用GetX框架结合MVVM架构模式。请确保每个文档包含以下内容：

1. 目录职责：用简洁明了的语言描述该目录的主要职责和设计目的。
2. 文件结构：列出该目录下的主要文件和子目录结构，并简要说明每个文件的用途。
3. 核心功能：详细列举该模块提供的主要功能点。
4. 主要文件说明：对每个关键文件进行详细解释，包括：
   - 其依赖关系
   - 主要方法和属性
   - 实现的核心功能
5. 运行流程：描述该模块的典型工作流程或数据流向。
6. 状态管理：对于包含控制器的模块，说明如何管理状态（使用GetX的.obs、Obx等）。
7. 与其他模块的关系：清晰解释该模块如何与应用中的其他模块交互，包括依赖关系和通信方式。
8. 常见混淆点：指出开发者容易混淆的概念和关系，并提供澄清。
9. 最佳实践：提供该模块相关的开发建议和最佳实践，包括代码组织、命名约定等。
10. 代码示例：包含实际的代码示例，展示该模块的典型用法。
11. 相关文件：列出与该模块功能相关但位于其他目录的文件。
12. 常见问题与解决方案：列出使用该模块时可能遇到的问题及其对应的解决方法。

请为以下文件夹创建单独的README.md文档：
1. lib/modules/home
2. lib/modules/profile
3. lib/modules/settings
4. lib/data/repositories
5. lib/data/providers
6. lib/data/models
7. lib/app/services

请确保文档风格一致，内容全面且具体，能够帮助新开发者快速理解每个目录的用途和工作方式。如果某些目录有特殊的设计考量或易混淆点，请特别强调。

对于Flutter和GetX的特定概念，请提供简短的解释，不要假设读者完全熟悉这些技术。

文档应当既适合新手理解，也能为有经验的开发者提供有价值的参考。