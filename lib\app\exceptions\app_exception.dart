import 'package:dio/dio.dart';

/// 应用异常类型枚举
enum AppExceptionType {
  network, // 网络相关异常
  database, // 数据库相关异常
  unknown, // 未知异常
  // 可以根据需要添加更多类型，例如：
  // auth, // 认证授权异常
  // validation, // 数据校验异常
  // business, // 业务逻辑异常
}

/// 自定义应用异常类，用于统一管理和处理各种错误
/// 这样业务层只需处理 AppException，简化异常处理逻辑
class AppException implements Exception {
  /// 错误类型
  final AppExceptionType type;

  /// 错误码，可选（如后端返回的 code 或 HTTP 状态码）
  final int? code;

  /// 错误信息，用于展示给用户或日志记录
  final String message;

  /// 原始异常，用于内部日志记录或调试
  final dynamic originalException;

  AppException(this.type, this.message, {this.code, this.originalException});

  /// 工厂方法：根据 DioError 创建 AppException
  factory AppException.fromDioError(DioException error) {
    AppExceptionType type = AppExceptionType.unknown;
    String message = "未知错误，请稍后重试";
    int? code = error.response?.statusCode;

    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        type = AppExceptionType.network;
        message = "连接超时，请检查网络";
        break;
      case DioExceptionType.sendTimeout:
        type = AppExceptionType.network;
        message = "请求超时，请重试";
        break;
      case DioExceptionType.receiveTimeout:
        type = AppExceptionType.network;
        message = "响应超时，请重试";
        break;
      case DioExceptionType.badResponse:
        type = AppExceptionType.network;
        message = "服务器异常: ${error.response?.statusCode?.toString() ?? "未知"}";
        break;
      case DioExceptionType.cancel:
        type = AppExceptionType.network;
        message = "请求已取消";
        break;
      case DioExceptionType.unknown:
      default:
        type = AppExceptionType.unknown;
        message = "未知错误，请稍后重试";
        break;
    }
    return AppException(type, message, code: code, originalException: error);
  }

  /// 工厂方法：从任何其他异常创建 AppException
  factory AppException.fromException(
    dynamic error, {
    String? defaultMessage,
    AppExceptionType type = AppExceptionType.unknown,
  }) {
    String message = defaultMessage ?? "发生了未知错误";
    if (error is AppException) {
      // 如果已经是 AppException，则直接返回
      return error;
    } else if (error is DioException) {
      return AppException.fromDioError(error);
    } else {
      // 对于其他类型的异常，可以提取其 toString() 作为消息
      message = defaultMessage ?? error.toString();
    }
    return AppException(type, message, originalException: error);
  }

  @override
  String toString() =>
      'AppException(type: $type, code: $code, message: $message, originalException: $originalException)';
}
