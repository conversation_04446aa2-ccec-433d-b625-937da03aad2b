import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/app/routes/app_pages.dart';
import 'package:flutter_study_flow_by_myself/data/models/user_word_book_model.dart';

/// 单词本信息对话框
class WordBookInfoDialog {
  /// 显示单词本信息对话框
  static void show(BuildContext context, UserWordBookModel wordBook, int progressPercentage) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('单词本信息'),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '名称: ${wordBook.wordBook.value?.name ?? "未知"}',
              ),
              Text('学习进度: $progressPercentage%'),
              Text(
                '已学单词: ${wordBook.totalLearnedWords}/${wordBook.totalWords}',
              ),
              Text('每日新词: ${wordBook.dailyNewWordsCount}'),
              Text(
                '最后学习: ${wordBook.lastLearningDate?.toString() ?? "未学习"}',
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  Get.toNamed(
                    AppRoutes.WORD_BOOK_DETAIL,
                    arguments: {'userWordBookId': wordBook.id},
                  );
                },
                child: const Text('查看详情'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}
