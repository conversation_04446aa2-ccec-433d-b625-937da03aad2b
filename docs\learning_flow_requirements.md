# 单词学习流程需求文档

## 1. 总体学习流程

单词学习应用的核心流程分为三个主要阶段：学习阶段、最终测验阶段和结果展示阶段。整个流程设计遵循科学的记忆方法，确保用户能高效地学习和记忆单词。

## 2. 宏观学习阶段

### 2.1 学习阶段
- 用户在首页选择单词本并点击"开始新的学习会话"按钮
- 每个单词依次经历回忆、详情、测验三个页面 测验对了 算该单词已学习
- 所有单词已学习之后进入最终测验
- 系统记录每个单词的学习情况，包括用户是否认识、错误次数等

### 2.2 最终测验阶段
- 当用户完成所有单词的学习后，系统自动进入最终测验阶段
- 系统从每个单词的测验题列表中选取除第一个以外的所有测验题
- 系统将这些测验题随机打乱顺序，添加到当前阶段测验题列表中
- 对本次学习的所有单词进行综合测验
- 答错的单词会立即返回详情页面重新学习，然后再次测验
- 系统详细记录每个单词的答题情况和错误次数

### 2.3 结果展示阶段
- 当所有单词都通过最终测验后，系统进入结果展示阶段
- 系统跳转到结果页面
- 展示详细的学习统计数据
- 提供本次学习的完整总结

## 3. 微观学习阶段

### 3.1 回忆阶段
- 系统显示单词拼写，隐藏释义和其他信息
- 自动播放单词发音
- 用户判断是否认识该单词，选择"认识"或"不认识"
- 系统记录用户的认知状态到wordRecognitionMap
- 系统导航到详情页面

### 3.2 详情阶段
- 系统展示单词的完整信息（拼写、音标、释义、例句等）
- 自动播放单词发音
- 用户学习完成后点击"开始测验"按钮
- 系统导航到测验页面

### 3.3 测验阶段
- 系统提供选择题测试用户对单词的掌握程度
- 答对：
  * 系统记录正确答题
  * 如果是学习阶段，进入下一个单词的回忆阶段
  * 如果是最终测验阶段，增加该单词的已完成测验数量
- 答错：
  * 系统记录错误次数到wordErrorCountMap
  * 返回当前单词的详情阶段重新学习

## 4. 状态管理需求

### 4.1 状态类型


### 4.2 状态保存内容
- 当前单词索引和测验题索引
- 用户对单词的认知状态(wordRecognitionMap)
- 单词错误次数记录(wordErrorCountMap)
- 单词测验完成情况(wordCompletedQuizzes)
- 最后学习时间(lastStudyTime)

## 5. 间隔重复学习算法

### 5.1 学习质量评分计算
- 系统在最终测验阶段，当某个单词完成所有测验题时，即达到3次，调用_recordLearningQuality方法当前单词的计算学习质量
- 具体计算方法：
  * 认识且全部答对：5分
  * 认识但错了1题：4分
  * 认识但错了2题：3分
  * 认识但错了3题或更多：2分
  * 不认识但全部答对：4分
  * 不认识且错了1题：3分
  * 不认识且错了2题：2分
  * 不认识且错了3题或更多：1分

### 5.2 下次复习时间计算
- 当单词在最终测验阶段完成所有测验题（达到3次）时，系统立即调用_reviewItemRepository.processLearningResult方法处理学习结果
- 在processLearningResult方法中使用SM-2间隔重复算法计算下次复习间隔：
  * 评分低于3分：安排明天复习（1天）
  * 首次成功(repetitionCount==0)：1天后复习
  * 第二次成功(repetitionCount==1)：6天后复习
  * 其后每次间隔 = 上次间隔 * 难易度因子

## 6. 学习中断与恢复机制

### 6.1 自动保存
- 系统在以下时刻自动保存学习进度：
  * 完成测验题(onQuizAnswered)

### 6.2 恢复机制
- 用户重新进入应用时，系统检查是否有未完成的学习会话
- 如有，系统恢复会话状态，包括：
  * 单词列表、当前单词索引、当前测验索引
  * 单词认知状态、错误次数、测验完成情况

## 7. 导航策略

### 7.1 页面切换逻辑
- 系统不会自动根据状态变化切换页面，而是在状态变化后手动调用导航方法
- 在回忆页面，用户选择"认识"或"不认识"后，系统手动导航到详情页面
- 在详情页面，用户点击"开始测验"后，系统手动导航到测验页面
- 在测验页面，用户答题后，系统根据答题结果决定导航目标（如果答错 直接进入对应单词的详情页 如果答对且是学习阶段直接进入下一个单词的回忆页面 如果答对且是最终测验阶段 直接进入下一个单词的测验页面） 