import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/data/models/quiz/choice_quiz_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/quiz/spelling_quiz_model.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/quiz/widgets/choice_quiz_widget_stateless.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/quiz/widgets/spelling_quiz_widget.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/quiz/controllers/quiz_controller.dart';

/// 测验组件工厂
///
/// 根据不同的测验类型返回对应的UI组件
class QuizWidgetFactory {
  /// 构建测验组件
  ///
  /// [quiz] - 测验模型，可以是ChoiceQuizModel或SpellingQuizModel
  /// [onAnswered] - 答题回调函数，拼写题参数为(userInput, isCorrect)，选择题参数为(isCorrect)
  /// [onOptionSelected] - 选择题选项回调函数，参数为选择的选项索引
  ///
  /// 返回对应类型的测验组件
  static Widget buildQuizWidget({
    required dynamic quiz,
    Function(bool isCorrect)? onAnswered,
    Function(int selectedIndex)? onOptionSelected,
  }) {
    if (quiz is ChoiceQuizModel) {
      return ChoiceQuizWidgetStateless(
        quiz: quiz,
        controller: Get.find<QuizController>(),
      );
    } else if (quiz is SpellingQuizModel) {
      // 适配器：将新的回调签名适配到旧的接口
      return SpellingQuizWidget(
        quiz: quiz,
        onAnswered: (userInput, isCorrect) {
          // 调用旧的回调接口，只传递isCorrect
          onAnswered?.call(isCorrect);
        },
      );
    }

    // 未知类型，返回错误提示
    return const Center(
      child: Text(
        '不支持的测验类型',
        style: TextStyle(color: Colors.red, fontSize: 18),
      ),
    );
  }
}
