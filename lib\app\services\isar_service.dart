// 你的思考非常细致，这确实是架构设计中常见的疑问。下面详细分析：

// ---

// ## 1. **IsarService 和 StorageService 的定位**

// - **IsarService**：专门负责结构化数据的本地数据库存储（如表、对象、关系等），适合复杂数据、批量操作、查询等。
// - **StorageService**（基于 SharedPreferences）：适合存储**简单的键值对**，如用户设置、token、偏好、布尔开关等。

// 它们虽然都属于"本地存储"，但**用途和适用场景不同**。

// ---

// ## 2. **命名是否冲突？**

// - 在实际项目中，`StorageService` 通常指"轻量级的键值对存储"，而像 Isar、Hive 这类数据库服务会单独命名为 `IsarService`、`HiveService` 等。
// - 只要你在项目中**明确区分用途**，命名不会造成实际冲突。

// ### 业界常见做法
// - `StorageService`/`LocalStorageService`/`PreferencesService`：用于封装 SharedPreferences、NSUserDefaults、MMKV 等轻量存储。
// - `IsarService`/`DatabaseService`/`SqliteService`：用于封装结构化数据库。

// ---

// ## 3. **如何避免歧义？**

// - 如果你担心歧义，可以用更具体的名字，比如：
//   - `PreferencesService` 或 `PrefsService`（专指用户偏好/设置）
//   - `KeyValueStorageService`（强调键值对存储）
// - 但如果你的 `StorageService` 只负责 SharedPreferences 相关内容，且文档/注释清楚，直接用 `StorageService` 也没问题。

// ---

// ## 4. **推荐方案**

// - **IsarService**：负责结构化数据（如单词、题库等）。
// - **StorageService**（或 `PreferencesService`）：负责简单键值对（如主题、token、登录状态等）。

// 这样分工明确，命名不会冲突，且便于后期维护和扩展。

// ---

// ## 5. **总结**

// - 命名不会冲突，只要职责清晰。
// - 推荐继续用 `IsarService` 和 `StorageService`，如需更细致可用 `PreferencesService`。
// - 路径都放在 `lib/app/services/` 下即可。

// ---

// 如需帮你生成 `StorageService`/`PreferencesService` 的标准代码模板，欢迎继续提问！
import 'package:flutter_study_flow_by_myself/data/models/quiz/choice_quiz_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/quiz/spelling_quiz_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/user_word_book_model.dart';
import 'package:isar/isar.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/learning_session.dart';
import 'package:flutter/foundation.dart'; // 导入 kDebugMode
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart'; // 导入 LogService
import 'package:flutter_study_flow_by_myself/data/models/review_item_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_book_model.dart';
import 'package:get/get.dart';
// import 'package:flutter_study_flow_by_myself/data/models/user_model.dart'; // 暂时注释掉 UserModel 的导入，因为用户暂时不需要
// 重要的提示：
// 在 Isar.open() 中引用 Schema (例如 WordModelSchema) 时，
// 你需要确保对应的模型文件 (例如 word_model.dart) 已经通过 'part' 指令引用了
// 自动生成的 .g.dart 文件，并且你已经运行了代码生成器。
// 这里不需要直接导入 .g.dart 文件，导入模型文件本身即可。
// Isar 会在编译时通过 'part' 机制找到 Schema 定义。

/// Isar数据库服务类
/// 采用单例模式，确保全局只有一个Isar数据库实例
/// 职责：提供通用的、底层的数据库操作（如打开/关闭数据库，泛型的增删改查）
/// 不应包含与具体业务逻辑（如获取特定单词列表）相关的查询
class IsarService extends GetxService {
  late Isar _isar; // Isar 数据库实例
  final LogService _logService; // 获取 LogService 实例

  IsarService({required LogService logService}) : _logService = logService;

  /// 获取 Isar 数据库实例的引用
  /// 这个方法在 IsarService 内部使用，或供 LocalProvider 调用来构建复杂查询
  Isar get isar => _isar;

  /// 异步初始化方法，用于打开或创建 Isar 数据库
  /// 这个方法应在应用程序启动时被调用 (例如在 main.dart 中)
  Future<IsarService> init() async {
    // 检查数据库是否已经打开，避免重复初始化
    // Isar.instanceNames.isNotEmpty 是 Isar 官方推荐的判断方式，表示当前进程中是否已经有打开的 Isar 实例。
    if (Isar.instanceNames.isNotEmpty) {
      return this;
    }
    // 获取应用文档目录，用于存放数据库文件
    final dir = await getApplicationDocumentsDirectory();
    try {
      _isar = await Isar.open(
        // 注册所有需要存储在Isar中的模型Schema
        // 注意：这里直接引用模型类名后跟 Schema，而不是导入 .g.dart 文件
        [
          WordModelSchema,
          ChoiceQuizModelSchema,
          SpellingQuizModelSchema,
          LearningSessionSchema,
          ReviewItemModelSchema,
          WordBookModelSchema,
          UserWordBookModelSchema,
          // UserModelSchema, // 暂时注释掉 UserModelSchema 的注册
        ],
        directory: dir.path, // 数据库文件存放的目录
        inspector: kDebugMode, // 根据 kDebugMode 决定是否开启 Inspector
      );
      return this;
    } catch (e) {
      // 捕获数据库打开过程中的任何异常
      // 在这里可以添加错误日志记录，例如使用 LogService.e()
      _logService.e('Isar数据库初始化失败: \$e', error: e); // 使用 LogService 记录错误
      rethrow; // 重新抛出异常，让上层知道初始化失败
    }
  }

  /// 关闭 Isar 数据库
  /// 在应用程序生命周期结束时调用，释放资源
  Future<void> close() async {
    // 修复：_isar 可能未被初始化，需要进行空检查
    if (_isar.isOpen) {
      await _isar.close();
    }
  }

  // --- 通用数据库操作方法 ---
  /// 保存单个对象到数据库
  /// [T] 是你的数据模型类型 (例如 WordModel, ChoiceQuizModel)
  /// [object] 要保存的对象
  /// 返回保存对象的 Id
  Future<Id> put<T>(T object) async {
    // writeTxn 用于在写入事务中执行操作，确保数据一致性
    return await _isar.writeTxn(() async {
      // 修复：ensureInitialized 应该在 Isar.open() 之后使用，或者在应用程序启动时全局调用
      // 这里移除 Isar.collection<T>().ensureInitialized();
      return await _isar.collection<T>().put(object);
    });
  }

  /// 批量保存多个对象到数据库
  /// [objects] 要保存的对象列表
  /// 返回保存对象的 Id 列表
  Future<List<Id>> putAll<T>(List<T> objects) async {
    return await _isar.writeTxn(() async {
      // 这里移除 Isar.collection<T>().ensureInitialized();
      return await _isar.collection<T>().putAll(objects);
    });
  }

  /// 根据 Id 从数据库获取单个对象
  /// [id] 对象的唯一标识符
  /// 返回对应的对象，如果不存在则返回 null
  Future<T?> get<T>(Id id) async {
    // 这里移除 Isar.collection<T>().ensureInitialized();
    return await _isar.collection<T>().get(id);
  }

  /// 根据 Id 列表从数据库批量获取对象
  /// [ids] 对象的唯一标识符列表
  /// 返回对应的对象列表，对于不存在的ID，列表中对应位置为 null
  Future<List<T?>> getByIds<T>(List<Id> ids) async {
    return await _isar.collection<T>().getAll(ids);
  }

  /// 获取某个集合（Collection）中的所有对象
  /// 返回该集合中所有对象的列表
  Future<List<T>> getAll<T>() async {
    // 这里移除 Isar.collection<T>().ensureInitialized();
    return await _isar.collection<T>().where().findAll();
  }

  /// 根据 Id 删除单个对象
  /// [id] 要删除对象的唯一标识符
  /// 返回 true 表示删除成功，false 表示删除失败或对象不存在
  Future<bool> delete<T>(Id id) async {
    return await _isar.writeTxn(() async {
      // 这里移除 Isar.collection<T>().ensureInitialized();
      return await _isar.collection<T>().delete(id);
    });
  }

  /// 批量删除多个对象
  /// [ids] 要删除对象的 Id 列表
  /// 返回成功删除的数量
  Future<int> deleteByIds<T>(List<Id> ids) async {
    return await _isar.writeTxn(() async {
      // 这里移除 Isar.collection<T>().ensureInitialized();
      return await _isar.collection<T>().deleteAll(ids);
    });
  }

  /// 清空某个集合（Collection）的所有数据
  // ignore: unintended_html_in_doc_comment
  /// 修复：Isar 的 clear() 方法返回 void，因此这里返回 Future<void>
  /// 如果需要返回数量，应该在 clear 之前查询 count()
  Future<void> clearCollection<T>() async {
    return await _isar.writeTxn(() async {
      await _isar.collection<T>().clear(); // 确保返回了 int 类型
    });
  }

  /// 查询某个集合中的对象数量
  Future<int> count<T>() async {
    // 这里移除 Isar.collection<T>().ensureInitialized();
    return await _isar.collection<T>().count();
  }
}
