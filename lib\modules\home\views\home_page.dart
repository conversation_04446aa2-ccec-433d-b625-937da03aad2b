import '../controllers/home_controller.dart';
import '../controllers/review_plan_controller.dart';
import '../widgets/word_book_info_dialog.dart';
import 'review_plan_page.dart';
import 'package:flutter_study_flow_by_myself/app/config/review_config.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';

class HomePage extends GetView<HomeController> {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    // 注释掉这段代码，避免重复加载问题
    // 原因：
    // 1. HomeController.onInit()已经调用了loadDueReviewCount()加载数据
    // 2. 这段代码会在页面渲染时再次调用相同的方法
    // 3. 由于异步操作的时序问题，两次调用几乎同时发生
    // 4. 这导致了重复的日志输出和重复的数据库查询
    /*
    if (!controller.hasLoadedInitialData.value &&
        !controller.isLoadingReviewCount.value) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        controller.loadDueReviewCount();
      });
    }
    */

    return Scaffold(
      appBar: AppBar(
        title: const Text('背单词App'),
        centerTitle: true,
        actions: [
          // 测试模式开关
          Obx(() {
            final reviewConfig = Get.find<ReviewConfig>();
            return IconButton(
              icon: Icon(
                reviewConfig.isTestMode ? Icons.speed : Icons.schedule,
                color: reviewConfig.isTestMode ? Colors.orange : null,
              ),
              tooltip: reviewConfig.isTestMode ? '测试模式已开启' : '开启测试模式',
              onPressed: () => reviewConfig.toggleTestMode(),
            );
          }),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // 添加到HomePage的顶部区域，显示当前激活单词本信息
              _buildActiveWordBookCard(context),
              // 复习卡片
              _buildReviewCard(),
              const SizedBox(height: 32),

              // 标题区
              const Text(
                '每日学习单词数',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 24),

              // 滑块选择器
              Obx(
                () => Column(
                  children: [
                    Slider(
                      value: controller.dailyWordCount.value.toDouble(),
                      min: 2,
                      max: 50,
                      divisions: 45,
                      label: controller.dailyWordCount.value.toString(),
                      onChanged:
                          (value) => controller.onCountChanged(value.round()),
                    ),
                    Text(
                      '${controller.dailyWordCount.value} 个单词/天',
                      style: const TextStyle(fontSize: 18),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 40),

              // 根据状态显示不同的按钮
              Obx(() {
                if (controller.isLoadingNewWords.value) {
                  // 显示加载指示器
                  return const CircularProgressIndicator();
                }

                switch (controller.currentButtonState) {
                  case HomeButtonState.completedAndActive:
                    return _buildCompletedAndActiveButtons();
                  case HomeButtonState.completedInactive:
                    return _buildCompletedInactiveButtons();
                  case HomeButtonState.continueAndNew:
                    return _buildContinueAndNewButtons();
                  case HomeButtonState.startLearning:
                    return _buildStartLearningButton();
                  case HomeButtonState.noWordBook:
                    return _buildSelectWordBookButton();
                }
              }),

              // 添加查看复习计划按钮
              const SizedBox(height: 24),
              OutlinedButton.icon(
                onPressed: () => _navigateToReviewPlan(context),
                icon: const Icon(Icons.schedule),
                label: const Text('查看所有单词复习计划'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
              // 添加底部空间，确保底部有足够的滚动空间
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建复习卡片
  Widget _buildReviewCard() {
    return Obx(() {
      // 加载中状态
      if (controller.isLoadingReview.value) {
        return Card(
          elevation: 3,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: const Padding(
            padding: EdgeInsets.all(20.0),
            child: Center(child: CircularProgressIndicator()),
          ),
        );
      }

      return Card(
        elevation: 3,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              Row(
                children: [
                  const Icon(Icons.replay, color: Colors.blue),
                  const SizedBox(width: 8),
                  const Text(
                    '单词复习',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                  const Spacer(),
                  // 待复习数量
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color:
                          controller.dueReviewCount > 0
                              ? Colors.red[100]
                              : Colors.grey[200],
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '${controller.dueReviewCount}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color:
                            controller.dueReviewCount > 0
                                ? Colors.red
                                : Colors.grey[700],
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // 描述
              Text(
                controller.dueReviewCount > 0
                    ? '你有 ${controller.dueReviewCount} 个单词需要复习'
                    : '当前没有需要复习的单词',
                style: TextStyle(fontSize: 16, color: Colors.grey[700]),
              ),
              const SizedBox(height: 16),

              // 按钮
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed:
                      controller.dueReviewCount > 0
                          ? controller.onUserClickStartReview
                          : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('开始复习', style: TextStyle(fontSize: 16)),
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  // 添加到HomePage的顶部区域，显示当前激活单词本信息
  Widget _buildActiveWordBookCard(BuildContext context) {
    final controller = Get.find<HomeController>();
    return Obx(() {
      if (!controller.hasActiveWordBook) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Text('没有激活的单词本', style: TextStyle(fontSize: 18)),
                SizedBox(height: 8),
                ElevatedButton(
                  onPressed: controller.onGoToVocabulary,
                  child: Text('选择单词本'),
                ),
              ],
            ),
          ),
        );
      }

      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      controller.activeWordBook?.wordBook.value?.name ??
                          '未知单词本',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  IconButton(
                    icon: Icon(Icons.info_outline),
                    onPressed: () => _showWordBookInfoDialog(context),
                  ),
                ],
              ),
              SizedBox(height: 8),
              LinearProgressIndicator(
                value: controller.progressPercentage / 100,
                backgroundColor: Colors.grey[200],
              ),
              SizedBox(height: 4),
              Text('学习进度: ${controller.progressPercentage}%'),
            ],
          ),
        ),
      );
    });
  }

  /// 显示单词本信息对话框
  void _showWordBookInfoDialog(BuildContext context) {
    if (!controller.hasActiveWordBook) {
      return;
    }

    WordBookInfoDialog.show(
      context,
      controller.activeWordBook!,
      controller.progressPercentage,
    );
  }

  /// 导航到复习计划详情页面
  void _navigateToReviewPlan(BuildContext context) {
    if (!controller.hasActiveWordBook) {
      Get.snackbar('提示', '请先激活一个单词本', snackPosition: SnackPosition.BOTTOM);
      return;
    }

    // 注入ReviewPlanController并导航
    Get.put(ReviewPlanController());
    Get.to(() => const ReviewPlanPage());
  }

  /// 构建单词本已完成且激活状态的按钮（可复习）
  Widget _buildCompletedAndActiveButtons() {
    return Column(
      children: [
        // 完成状态提示
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.green.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.green.shade200),
          ),
          child: Column(
            children: [
              const Icon(Icons.check_circle, color: Colors.green, size: 48),
              const SizedBox(height: 8),
              const Text(
                '🎉 单词本已完成！',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
              const SizedBox(height: 4),
              Obx(() {
                final wordBookName =
                    controller.activeWordBook?.wordBook.value?.name ?? '单词本';
                return Text(
                  '「$wordBookName」',
                  style: const TextStyle(fontSize: 16, color: Colors.black87),
                );
              }),
            ],
          ),
        ),
        const SizedBox(height: 16),
        // 复习按钮
        SizedBox(
          width: double.infinity,
          height: 48,
          child: ElevatedButton(
            onPressed: controller.onUserClickStartNewLearning,
            style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
            child: const Text('开始复习', style: TextStyle(fontSize: 18)),
          ),
        ),
        const SizedBox(height: 12),
        // 选择新单词本按钮
        SizedBox(
          width: double.infinity,
          height: 48,
          child: ElevatedButton(
            onPressed: controller.onGoToVocabulary,
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('选择新单词本', style: TextStyle(fontSize: 18)),
          ),
        ),
      ],
    );
  }

  /// 构建单词本已完成但未激活状态的按钮
  Widget _buildCompletedInactiveButtons() {
    return Column(
      children: [
        // 完成状态提示
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.green.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.green.shade200),
          ),
          child: Column(
            children: [
              const Icon(Icons.check_circle, color: Colors.green, size: 48),
              const SizedBox(height: 8),
              const Text(
                '🎉 恭喜完成单词本！',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
              const SizedBox(height: 4),
              Obx(() {
                final wordBookName =
                    controller.activeWordBook?.wordBook.value?.name ?? '单词本';
                return Text(
                  '「$wordBookName」',
                  style: const TextStyle(fontSize: 16, color: Colors.black87),
                );
              }),
            ],
          ),
        ),
        const SizedBox(height: 16),
        // 选择新单词本按钮
        SizedBox(
          width: double.infinity,
          height: 48,
          child: ElevatedButton(
            onPressed: controller.onGoToVocabulary,
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('选择新单词本', style: TextStyle(fontSize: 18)),
          ),
        ),
      ],
    );
  }

  /// 构建继续学习和开始新学习按钮
  Widget _buildContinueAndNewButtons() {
    return Column(
      children: [
        const Text(
          '检测到未完成的学习会话',
          style: TextStyle(
            fontSize: 16,
            color: Colors.blue,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          height: 48,
          child: ElevatedButton(
            onPressed: controller.onUserClickContinueLearning,
            style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
            child: const Text('继续上次学习', style: TextStyle(fontSize: 18)),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          height: 48,
          child: ElevatedButton(
            onPressed: controller.onUserClickStartNewLearning,
            child: const Text('开始新的学习', style: TextStyle(fontSize: 18)),
          ),
        ),
      ],
    );
  }

  /// 构建开始学习按钮
  Widget _buildStartLearningButton() {
    return SizedBox(
      width: double.infinity,
      height: 48,
      child: ElevatedButton(
        onPressed: controller.onUserClickStartNewLearning,
        child: const Text('开始学习', style: TextStyle(fontSize: 18)),
      ),
    );
  }

  /// 构建选择单词本按钮
  Widget _buildSelectWordBookButton() {
    return Column(
      children: [
        const Text(
          '请先选择一个单词本',
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          height: 48,
          child: ElevatedButton(
            onPressed: controller.onGoToVocabulary,
            child: const Text('选择单词本', style: TextStyle(fontSize: 18)),
          ),
        ),
      ],
    );
  }
}
