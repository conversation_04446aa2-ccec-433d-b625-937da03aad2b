import 'package:flutter_study_flow_by_myself/data/models/word_model.dart';
import 'package:flutter_study_flow_by_myself/data/providers/word/word_local_provider.dart';
import 'package:flutter_study_flow_by_myself/data/providers/word/word_remote_provider.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/app/exceptions/app_exception.dart';
import 'package:flutter_study_flow_by_myself/data/providers/review_item/review_item_local_provider.dart';

/// WordRepository 负责协调本地和远程 Provider，提供单词相关的业务数据接口
/// 典型职责：
/// - 优先本地获取，必要时远程拉取并缓存
/// - 业务聚合、数据整合
/// - 统一异常和日志处理
///
/// 单词本功能扩展：
/// - 支持从特定单词本获取单词
/// - 支持基于单词本的学习进度查询
/// - 删除独立于单词本的旧方法
class WordRepository {
  final WordLocalProvider _localProvider;
  final WordRemoteProvider _remoteProvider;
  final ReviewItemLocalProvider? _reviewItemProvider; // 用于获取学习状态
  final LogService _log;

  WordRepository({
    required WordLocalProvider localProvider,
    required WordRemoteProvider remoteProvider,
    required LogService log,
    ReviewItemLocalProvider? reviewItemProvider,
  }) : _localProvider = localProvider,
       _remoteProvider = remoteProvider,
       _log = log,
       _reviewItemProvider = reviewItemProvider;

  /// 获取所有单词
  /// [forceRefresh] 为 true 时强制从远程拉取并覆盖本地缓存
  Future<List<WordModel>> getAllWords({bool forceRefresh = false}) async {
    try {
      if (!forceRefresh) {
        // 优先尝试本地获取
        final localWords = await _localProvider.getAllWords();
        if (localWords.isNotEmpty) {
          return localWords;
        }
      }
      // 本地无数据或强制刷新，拉取远程
      final remoteWords = await _remoteProvider.fetchAllWords();
      await _localProvider.saveAllWords(remoteWords);
      return remoteWords;
    } on AppException catch (e) {
      _log.e('获取所有单词失败: ${e.message}', error: e);
      rethrow;
    } catch (e) {
      _log.e('获取所有单词失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '获取所有单词失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 根据ID获取单个单词
  Future<WordModel?> getWordById(int wordId) async {
    try {
      // 先查本地
      final localWord = await _localProvider.getWordById(wordId);
      if (localWord != null) {
        return localWord;
      }
      // 本地没有，查远程
      final remoteWord = await _remoteProvider.fetchWordById(wordId.toString());
      await _localProvider.saveWord(remoteWord);
      return remoteWord;
    } on AppException catch (e) {
      _log.e('根据ID获取单词失败: ${e.message}', error: e);
      rethrow;
    } catch (e) {
      _log.e('根据ID获取单词失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '根据ID获取单词失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 根据ID列表批量获取单词
  /// 主要用于恢复学习会话时加载所需单词
  Future<List<WordModel>> getWordsByIds(List<int> wordIds) async {
    try {
      // 直接从本地 Provider 获取
      // 未来可扩展：如果本地部分缺失，可根据ID从远程拉取
      return await _localProvider.getWordsByIds(wordIds);
    } on AppException catch (e) {
      _log.e('根据ID列表获取单词失败: ${e.message}', error: e);
      rethrow;
    } catch (e) {
      _log.e('根据ID列表获取单词失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '根据ID列表获取单词失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 获取指定数量的新单词用于开始一个学习会話
  /// [count] 需要获取的单词数量
  ///
  /// @deprecated 使用 getNewWordsFromWordBook 替代
  /// 该方法不再依赖单词本，将在未来版本中移除
  Future<List<WordModel>> getNewWords(int count) async {
    try {
      // 调用本地 Provider 的方法获取新词
      // 这里的业务逻辑可以很复杂，例如排除已学习的、按特定顺序等
      // 目前 Provider 中的实现是占位符，我们直接调用即可
      final words = await _localProvider.getNewWordsForLearning(limit: count);

      // 如果本地新词不足，可以从远程拉取补充
      if (words.length < count) {
        _log.w('本地新词不足 (${words.length}/$count)，未来可实现从远程拉取补充');
        // final remoteWords = await _remoteProvider.fetchNewWords(count - words.length);
        // await _localProvider.saveAllWords(remoteWords);
        // words.addAll(remoteWords);
      }

      return words;
    } on AppException catch (e) {
      _log.e('获取新单词失败: ${e.message}', error: e);
      rethrow;
    } catch (e) {
      _log.e('获取新单词失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '获取新单词失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 从特定单词本获取指定数量的新单词
  ///
  /// [wordBookId] 单词本ID
  /// [limit] 需要获取的单词数量
  /// [excludeIds] 可选参数，需要排除的单词ID列表
  ///
  /// 返回从指定单词本中获取的未学习单词，数量不超过limit
  Future<List<WordModel>> getNewWordsFromWordBook(
    int wordBookId,
    int limit, {
    List<int>? excludeIds,
  }) async {
    try {
      // 如果没有ReviewItemProvider，无法获取学习状态，直接返回空列表
      if (_reviewItemProvider == null) {
        _log.e('ReviewItemProvider未注入，无法获取新单词');
        throw AppException(
          AppExceptionType.unknown,
          'ReviewItemProvider未注入，无法获取新单词',
        );
      }

      // 1. 直接从ReviewItemProvider获取标记为新词的单词ID列表
      final newWordIds = await _reviewItemProvider.getNewWordIdsByUserWordBook(
        wordBookId,
      );

      if (newWordIds.isEmpty) {
        _log.w('单词本($wordBookId)中没有标记为新词的单词');
        return [];
      }

      _log.i('单词本($wordBookId)中找到${newWordIds.length}个标记为新词的单词');

      // 2. 排除指定ID
      List<int> filteredIds = newWordIds;
      if (excludeIds != null && excludeIds.isNotEmpty) {
        filteredIds =
            newWordIds.where((id) => !excludeIds.contains(id)).toList();
        _log.i('排除${excludeIds.length}个指定ID后，剩余${filteredIds.length}个单词');
      }

      // 3. 随机打乱顺序，以获得不同的学习体验
      filteredIds.shuffle();

      // 4. 限制数量
      final limitedIds =
          filteredIds.length > limit
              ? filteredIds.sublist(0, limit)
              : filteredIds;

      // 5. 获取对应的单词对象
      final newWords = await _localProvider.getWordsByIds(limitedIds);
      _log.i('从单词本($wordBookId)获取了${newWords.length}个新单词');

      return newWords;
    } on AppException catch (e) {
      _log.e('从单词本获取新单词失败: ${e.message}', error: e);
      rethrow;
    } catch (e) {
      _log.e('从单词本获取新单词失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '从单词本获取新单词失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 获取指定单词本中已学习的单词数量
  ///
  /// [userWordBookId] 用户单词本ID
  /// 返回该单词本中已学习的单词数量
  Future<int> getLearnedWordCountByWordBookId(int userWordBookId) async {
    try {
      if (_reviewItemProvider == null) {
        throw AppException(
          AppExceptionType.unknown,
          'ReviewItemProvider未注入，无法获取学习进度',
        );
      }

      return await _reviewItemProvider.getLearnedWordsCountByUserWordBook(
        userWordBookId,
      );
    } on AppException catch (e) {
      _log.e('获取单词本已学习单词数量失败: ${e.message}', error: e);
      rethrow;
    } catch (e) {
      _log.e('获取单词本已学习单词数量失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '获取单词本已学习单词数量失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 获取指定单词本中的所有单词
  ///
  /// [wordBookId] 单词本ID
  /// 返回该单词本中的所有单词列表
  Future<List<WordModel>> getWordsByWordBookId(int wordBookId) async {
    try {
      return await _localProvider.getWordsByWordBookId(wordBookId);
    } on AppException catch (e) {
      _log.e('获取单词本中的单词失败: ${e.message}', error: e);
      rethrow;
    } catch (e) {
      _log.e('获取单词本中的单词失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '获取单词本中的单词失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 保存单个单词到本地
  Future<int> saveWord(WordModel word) async {
    try {
      return await _localProvider.saveWord(word);
    } on AppException catch (e) {
      _log.e('保存单词失败: ${e.message}', error: e);
      rethrow;
    } catch (e) {
      _log.e('保存单词失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '保存单词失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 批量保存单词到本地
  Future<List<int>> saveAllWords(List<WordModel> words) async {
    try {
      return await _localProvider.saveAllWords(words);
    } on AppException catch (e) {
      _log.e('批量保存单词失败: ${e.message}', error: e);
      rethrow;
    } catch (e) {
      _log.e('批量保存单词失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '批量保存单词失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 根据ID删除单个单词
  Future<bool> deleteWordById(int wordId) async {
    try {
      return await _localProvider.deleteWordById(wordId);
    } on AppException catch (e) {
      _log.e('删除单词失败: ${e.message}', error: e);
      rethrow;
    } catch (e) {
      _log.e('删除单词失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '删除单词失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 批量删除单词
  Future<int> deleteWordsByIds(List<int> wordIds) async {
    try {
      return await _localProvider.deleteWordsByIds(wordIds);
    } on AppException catch (e) {
      _log.e('批量删除单词失败: ${e.message}', error: e);
      rethrow;
    } catch (e) {
      _log.e('批量删除单词失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '批量删除单词失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 清空所有单词
  Future<void> clearAllWords() async {
    try {
      await _localProvider.clearAllWords();
    } on AppException catch (e) {
      _log.e('清空所有单词失败: ${e.message}', error: e);
      rethrow;
    } catch (e) {
      _log.e('清空所有单词失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '清空所有单词失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 查询本地单词数量
  Future<int> countWords() async {
    try {
      return await _localProvider.countWords();
    } on AppException catch (e) {
      _log.e('查询单词数量失败: ${e.message}', error: e);
      rethrow;
    } catch (e) {
      _log.e('查询单词数量失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '查询单词数量失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 强制同步远程单词到本地（可选扩展）
  Future<void> syncRemoteWords() async {
    try {
      final remoteWords = await _remoteProvider.fetchAllWords();
      await _localProvider.saveAllWords(remoteWords);
    } on AppException catch (e) {
      _log.e('同步远程单词失败: ${e.message}', error: e);
      rethrow;
    } catch (e) {
      _log.e('同步远程单词失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '同步远程单词失败',
        type: AppExceptionType.unknown,
      );
    }
  }
}
