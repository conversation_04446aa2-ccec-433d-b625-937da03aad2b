import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/data/models/user_word_book_model.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/user_word_book_repository.dart';

/// 单词本完成庆祝页面控制器
///
/// 职责：
/// - 加载已完成的单词本数据
/// - 提供单词本完成信息的访问接口
/// - 管理页面加载状态
class WordBookCompletionController extends GetxController {
  // ==================== 依赖注入 ====================
  /// 日志服务 - 用于记录控制器运行日志
  ///
  /// 调用位置：
  /// - _loadCompletedWordBookData() 方法中记录加载成功/失败日志
  final LogService _log;

  /// 用户单词本仓库 - 用于获取用户单词本数据
  ///
  /// 调用位置：
  /// - _loadCompletedWordBookData() 方法中调用 getActiveUserWordBook()
  final UserWordBookRepository _userWordBookRepository;

  /// 构造函数 - 依赖注入
  ///
  /// 通过构造函数注入所需的服务和仓库实例，提高代码的可测试性和解耦性
  ///
  /// 参数：
  /// - [log] 日志服务实例，用于记录运行日志
  /// - [userWordBookRepository] 用户单词本仓库实例，用于数据访问
  ///
  /// 注册位置：lib/modules/learning/bindings/learning_binding.dart
  WordBookCompletionController({
    required LogService log,
    required UserWordBookRepository userWordBookRepository,
  }) : _log = log,
       _userWordBookRepository = userWordBookRepository;

  // ==================== 响应式状态变量 ====================
  /// 完成的单词本信息
  ///
  /// 存储已完成的用户单词本数据，用于在UI中显示相关信息
  ///
  /// UI 使用位置：
  /// - word_book_completion_page.dart 中通过各个 getter 方法间接访问
  /// - 所有数据访问接口方法都依赖此变量
  ///
  /// 响应式监听：通过 Obx 组件监听变化，自动更新UI显示
  final Rx<UserWordBookModel?> completedWordBook = Rx<UserWordBookModel?>(null);

  /// 是否正在加载数据的状态标识
  ///
  /// 用于控制UI的加载状态显示，提供用户友好的加载反馈
  ///
  /// UI 使用位置：
  /// - word_book_completion_page.dart:54 - 控制单词本名称区域的加载显示
  /// - word_book_completion_page.dart:138 - 控制统计卡片区域的加载显示
  ///
  /// 响应式监听：通过 Obx 组件监听，在加载时显示加载指示器
  final RxBool isLoading = true.obs;

  // ==================== 生命周期方法 ====================
  /// 控制器初始化方法
  ///
  /// 在控制器创建时自动调用，负责初始化控制器状态和加载必要数据
  ///
  /// 调用时机：
  /// - 当用户导航到单词本完成页面时，GetX 自动调用此方法
  /// - 通过 LearningNavigationService.navigateToWordBookCompletion() 导航触发
  ///
  /// 执行流程：
  /// 1. 调用父类的 onInit() 方法
  /// 2. 启动数据加载流程 _loadCompletedWordBookData()
  @override
  void onInit() {
    super.onInit();
    _loadCompletedWordBookData();
  }

  // ==================== 数据加载方法 ====================
  /// 加载已完成的单词本数据
  ///
  /// 从数据库获取当前激活且已完成的单词本信息，用于在完成庆祝页面显示相关统计数据。
  /// 此方法是控制器的核心数据加载逻辑，确保页面能够正确显示完成信息。
  ///
  /// 调用位置：
  /// - onInit() 方法中自动调用
  ///
  /// 业务逻辑：
  /// 1. 设置加载状态为 true，触发UI显示加载指示器
  /// 2. 通过 UserWordBookRepository 获取当前激活的单词本
  /// 3. 验证单词本是否存在且已完成
  /// 4. 更新 completedWordBook 响应式变量，触发UI更新
  /// 5. 记录操作日志（成功/警告/错误）
  /// 6. 无论成功失败都会重置加载状态
  ///
  /// 异常处理：
  /// - 捕获所有异常并记录错误日志
  /// - 不会抛出异常，确保页面不会崩溃
  /// - 在 finally 块中确保加载状态被正确重置
  ///
  /// 返回值：无返回值（Future&lt;void&gt;）
  Future<void> _loadCompletedWordBookData() async {
    try {
      isLoading.value = true;

      // 获取当前激活的单词本（应该是刚完成的）
      final wordBook = await _userWordBookRepository.getActiveUserWordBook();

      if (wordBook != null && wordBook.isCompleted) {
        completedWordBook.value = wordBook;
        _log.i('加载已完成单词本数据成功: ${wordBook.wordBook.value?.name}');
      } else {
        _log.w('未找到已完成的单词本');
      }
    } catch (e) {
      _log.e('加载已完成单词本数据失败: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // ==================== 数据访问接口 ====================
  // 以下 getter 方法为 UI 层提供格式化后的数据访问接口
  // 所有方法都依赖 completedWordBook 响应式变量，当数据变化时会自动触发UI更新

  /// 获取单词本名称
  ///
  /// 返回已完成单词本的名称，如果数据未加载则返回默认值 '单词本'
  ///
  /// UI 调用位置：
  /// - word_book_completion_page.dart:67 - 在页面标题区域显示单词本名称
  ///
  /// 数据来源：completedWordBook.value?.wordBook.value?.name
  /// 默认值：'单词本'
  /// 返回值：String - 单词本名称或默认值
  String get wordBookName {
    return completedWordBook.value?.wordBook.value?.name ?? '单词本';
  }

  /// 获取总单词数
  ///
  /// 返回单词本包含的总单词数量，用于统计显示
  ///
  /// UI 调用位置：
  /// - word_book_completion_page.dart:146 - 在统计卡片中显示 "学习单词: X 个"
  ///
  /// 数据来源：completedWordBook.value?.totalWords
  /// 默认值：0
  /// 返回值：int - 单词本总单词数
  int get totalWords {
    return completedWordBook.value?.totalWords ?? 0;
  }

  /// 获取已学单词数
  ///
  /// 返回用户已学习的单词数量，目前在代码中定义但未在UI中直接使用
  ///
  /// UI 调用位置：
  /// - 当前未在UI中直接使用，但可用于未来的详细统计显示
  ///
  /// 数据来源：completedWordBook.value?.totalLearnedWords
  /// 默认值：0
  /// 返回值：int - 已学习的单词数量
  ///
  /// 注意：此方法当前未被UI使用，可考虑在未来版本中添加更详细的统计信息
  int get learnedWords {
    return completedWordBook.value?.totalLearnedWords ?? 0;
  }

  /// 获取完成率
  ///
  /// 返回学习完成率，范围为 0.0 到 1.0，在UI中转换为百分比显示
  ///
  /// UI 调用位置：
  /// - word_book_completion_page.dart:150 - 在统计卡片中显示 "完成率: X%"
  /// - 通过 (controller.completionRate * 100).toInt() 转换为百分比
  ///
  /// 数据来源：completedWordBook.value?.completionRate
  /// 计算逻辑：在 UserWordBookModel 中通过 totalLearnedWords / totalWords 计算
  /// 默认值：0.0
  /// 返回值：double - 完成率（0.0 - 1.0）
  double get completionRate {
    return completedWordBook.value?.completionRate ?? 0.0;
  }

  // ==================== 时间格式化方法 ====================
  /// 获取完成时间的相对时间描述
  ///
  /// 将单词本完成时间转换为用户友好的相对时间格式，提供直观的时间感知
  ///
  /// UI 调用位置：
  /// - word_book_completion_page.dart:144 - 在统计卡片中显示 "完成时间: X"
  ///
  /// 格式化规则：
  /// - 小于1分钟：显示 "刚刚"
  /// - 小于1小时：显示 "X分钟前"
  /// - 小于1天：显示 "X小时前"
  /// - 1天及以上：显示 "X天前"
  ///
  /// 数据来源：completedWordBook.value?.completedAt
  /// 默认值：'刚刚'（当完成时间为 null 时）
  /// 返回值：String - 格式化后的相对时间描述
  ///
  /// 实现逻辑：
  /// 1. 获取单词本完成时间 completedAt
  /// 2. 如果时间为空，返回默认值 '刚刚'
  /// 3. 调用 _formatRelativeTime() 进行具体的时间格式化
  String get completedTime {
    final completedAt = completedWordBook.value?.completedAt;
    if (completedAt == null) return '刚刚';

    return _formatRelativeTime(completedAt);
  }

  /// 格式化相对时间的核心算法
  ///
  /// 将给定的时间转换为相对于当前时间的用户友好描述。
  /// 此方法封装了复杂的时间计算逻辑，提高代码的模块化和可测试性。
  ///
  /// 调用位置：
  /// - completedTime getter 方法中调用
  ///
  /// 参数：
  /// - [dateTime] 要格式化的目标时间，通常是单词本的完成时间
  ///
  /// 算法逻辑：
  /// 1. 计算目标时间与当前时间的差值 (DateTime.now().difference(dateTime))
  /// 2. 根据时间差的大小选择合适的显示格式：
  ///    - difference.inMinutes &lt; 1: 返回 "刚刚"
  ///    - difference.inHours &lt; 1: 返回 "X分钟前"
  ///    - difference.inDays &lt; 1: 返回 "X小时前"
  ///    - 其他情况: 返回 "X天前"
  ///
  /// 返回值：String - 格式化后的相对时间字符串
  ///
  /// 注意事项：
  /// - 此方法假设传入的时间不会是未来时间
  /// - 时间计算基于系统当前时间，可能受时区影响
  /// - 未来可考虑提取到独立的工具类中以提高复用性
  String _formatRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}小时前';
    } else {
      return '${difference.inDays}天前';
    }
  }
}
