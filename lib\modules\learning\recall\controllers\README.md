# Recall Controllers 目录说明文档

## 目录职责

`recall/controllers` 目录负责实现学习模块中单词回忆环节的业务逻辑控制，管理单词数据的加载、展示顺序、记忆评分处理和学习进度追踪等功能。该目录是MVVM架构中的ViewModel层，连接单词数据模型和回忆视图。

## 文件结构

```
recall/controllers/
└── recall_controller.dart      # 单词回忆控制器，处理单词学习和复习逻辑
```

## 核心功能

- **单词数据管理**：加载和管理待学习或复习的单词列表
- **卡片状态控制**：管理单词卡片的翻转状态
- **记忆评分处理**：处理用户的记忆评分输入
- **发音播放控制**：控制单词发音的播放
- **学习进度跟踪**：跟踪和更新学习进度
- **导航控制**：管理详情查看和单词跳过等导航行为

## 主要文件说明

### recall_controller.dart

- **职责**：单词回忆页面的控制器
- **依赖关系**：
  - `LearningController`：获取当前学习会话状态
  - `WordRepository`：获取单词数据
  - `AudioService`：处理单词发音播放
  - `SpacedRepetitionService`：处理间隔重复算法
- **主要方法和属性**：
  - **状态变量**：
    - `currentWord.obs`：当前展示的单词对象
    - `isCardFlipped.obs`：卡片是否翻转（显示释义）
    - `isPlaying.obs`：发音播放状态
    - `currentIndex.obs`：当前单词索引
    - `totalWords`：总单词数量
    - `isNewWordsMode`：是否为新词学习模式
  - **方法**：
    - `loadWords()`：加载单词数据
    - `flipCard()`：翻转单词卡片
    - `playPronunciation()`：播放单词发音
    - `rateWord()`：处理记忆评分
    - `nextWord()`：切换到下一个单词
    - `skipWord()`：跳过当前单词
    - `viewWordDetails()`：查看单词详情

## 运行流程

1. **初始化阶段**：
   - `RecallController` 在用户进入回忆页面时初始化
   - 从 `LearningController` 获取当前学习模式（新词学习或复习）
   - 根据模式加载相应的单词列表

2. **学习交互阶段**：
   - 展示当前单词，等待用户交互
   - 响应用户的卡片翻转、发音播放等请求
   - 处理用户的记忆评分输入
   - 根据评分更新单词的学习状态
   - 加载下一个单词，更新学习进度

3. **完成阶段**：
   - 所有单词学习完成后，通知 `LearningController`
   - 根据当前模式进入下一学习阶段（如从新词学习进入复习）

## 状态管理

`RecallController` 使用GetX的响应式状态管理，主要包括：

- **响应式变量**：关键状态声明为.obs类型，用于自动触发UI更新
- **依赖注入**：通过Get.find()获取其他控制器和服务
- **异步处理**：使用Future和异步方法处理数据加载和音频播放
- **学习状态追踪**：记录用户的学习进度和记忆评分

## 与其他模块的关系

- **learning/controllers**：从`LearningController`获取学习会话状态，通知学习进度
- **recall/views**：为回忆视图提供数据和行为
- **detail模块**：提供导航到单词详情的功能
- **data/repositories**：从`WordRepository`获取单词数据
- **app/services**：使用`AudioService`播放单词发音，使用`SpacedRepetitionService`处理记忆算法

## 常见混淆点

1. **回忆控制器vs学习控制器**：
   - `RecallController` 专注于单词回忆页面的具体交互逻辑
   - `LearningController` 管理整体学习流程和会话状态

2. **新词模式vs复习模式**：
   - 同一个控制器处理两种模式，但加载不同的单词集
   - 评分处理逻辑在两种模式下可能有所不同

3. **记忆算法实现**：
   - 控制器负责收集用户评分
   - 具体的间隔重复算法由专门的服务实现

## 最佳实践

- **错误处理**：妥善处理数据加载和音频播放失败情况
- **性能优化**：预加载下一个单词的数据，避免切换时的延迟
- **用户体验**：提供流畅的交互反馈和状态更新
- **数据持久化**：确保学习进度和记忆评分被正确保存
- **模块化设计**：将复杂逻辑拆分为独立方法，保持单一职责

## 代码示例

```dart
class RecallController extends GetxController {
  // 依赖项
  final LearningController learningController = Get.find<LearningController>();
  final AudioService audioService = Get.find<AudioService>();
  final SpacedRepetitionService spacedRepetitionService = Get.find<SpacedRepetitionService>();
  
  // 状态变量
  final Rx<Word?> currentWord = Rx<Word?>(null);
  final RxBool isCardFlipped = false.obs;
  final RxBool isPlaying = false.obs;
  final RxInt currentIndex = 0.obs;
  final RxBool isLoading = true.obs;
  final RxBool hasError = false.obs;
  final RxString errorMessage = ''.obs;
  
  // 单词列表
  late List<Word> _words;
  
  // 计算属性
  bool get isNewWordsMode => learningController.currentStage.value == LearningStage.NEW_WORDS;
  int get totalWords => _words.length;
  double get progress => totalWords > 0 ? (currentIndex.value + 1) / totalWords : 0;
  
  @override
  void onInit() {
    super.onInit();
    loadWords();
  }
  
  @override
  void onClose() {
    // 停止任何正在播放的音频
    if (isPlaying.value) {
      audioService.stop();
    }
    super.onClose();
  }
  
  // 加载单词数据
  Future<void> loadWords() async {
    isLoading.value = true;
    hasError.value = false;
    
    try {
      final session = learningController.session.value;
      
      // 根据当前学习阶段加载相应的单词列表
      if (isNewWordsMode) {
        _words = session.newWords.where((word) => 
          !session.learnedNewWords.contains(word)
        ).toList();
      } else {
        _words = session.reviewWords.where((word) => 
          !session.learnedReviewWords.contains(word)
        ).toList();
      }
      
      // 如果没有单词需要学习，报告错误
      if (_words.isEmpty) {
        hasError.value = true;
        errorMessage.value = '没有${isNewWordsMode ? '新词' : '复习词'}需要学习';
        return;
      }
      
      // 重置状态
      currentIndex.value = 0;
      isCardFlipped.value = false;
      
      // 设置当前单词
      currentWord.value = _words[0];
      
      // 预加载发音
      _preloadPronunciation(currentWord.value!);
      
    } catch (e) {
      hasError.value = true;
      errorMessage.value = '加载单词失败: $e';
      Get.log('RecallController加载失败: $e');
    } finally {
      isLoading.value = false;
    }
  }
  
  // 重试加载
  void retryLoading() {
    loadWords();
  }
  
  // 翻转卡片
  void flipCard() {
    isCardFlipped.value = !isCardFlipped.value;
    
    // 翻转到释义面时自动播放发音
    if (isCardFlipped.value) {
      playPronunciation();
    }
  }
  
  // 播放单词发音
  Future<void> playPronunciation() async {
    if (isPlaying.value || currentWord.value == null) return;
    
    try {
      isPlaying.value = true;
      
      // 获取发音URL
      final pronunciationUrl = currentWord.value!.pronunciationUrl;
      
      if (pronunciationUrl != null && pronunciationUrl.isNotEmpty) {
        // 在线发音
        await audioService.playFromUrl(pronunciationUrl);
      } else {
        // 离线TTS发音
        await audioService.speakText(currentWord.value!.word);
      }
      
    } catch (e) {
      Get.log('播放发音失败: $e');
      Get.snackbar('播放失败', '无法播放单词发音');
    } finally {
      isPlaying.value = false;
    }
  }
  
  // 预加载发音
  Future<void> _preloadPronunciation(Word word) async {
    try {
      final pronunciationUrl = word.pronunciationUrl;
      if (pronunciationUrl != null && pronunciationUrl.isNotEmpty) {
        audioService.preloadAudio(pronunciationUrl);
      }
    } catch (e) {
      // 预加载失败不影响主流程
      Get.log('预加载发音失败: $e');
    }
  }
  
  // 处理记忆评分
  void rateWord(int rating) {
    if (currentWord.value == null) return;
    
    // 记录当前单词的学习状态
    final word = currentWord.value!;
    
    // 根据当前模式更新学习会话
    if (isNewWordsMode) {
      learningController.recordNewWordLearned(word, rating);
    } else {
      learningController.recordReviewWordLearned(word, rating);
    }
    
    // 使用间隔重复服务更新单词的复习计划
    spacedRepetitionService.updateWordSchedule(word.id, rating);
    
    // 切换到下一个单词
    nextWord();
  }
  
  // 切换到下一个单词
  void nextWord() {
    // 检查是否还有下一个单词
    if (currentIndex.value < _words.length - 1) {
      // 重置卡片状态
      isCardFlipped.value = false;
      
      // 更新索引和当前单词
      currentIndex.value++;
      currentWord.value = _words[currentIndex.value];
      
      // 预加载下一个单词的发音
      _preloadPronunciation(currentWord.value!);
    } else {
      // 所有单词学习完成，通知学习控制器
      _completeCurrentStage();
    }
  }
  
  // 跳过当前单词
  void skipWord() {
    if (currentWord.value == null) return;
    
    // 将当前单词移到列表末尾
    final skippedWord = currentWord.value!;
    _words.removeAt(currentIndex.value);
    _words.add(skippedWord);
    
    // 如果跳过后没有其他单词，重新加载当前单词
    if (_words.length == 1) {
      // 重置卡片状态
      isCardFlipped.value = false;
      return;
    }
    
    // 如果跳过的是最后一个单词，索引需要减1
    if (currentIndex.value >= _words.length) {
      currentIndex.value = _words.length - 1;
    }
    
    // 更新当前单词
    currentWord.value = _words[currentIndex.value];
    
    // 重置卡片状态
    isCardFlipped.value = false;
    
    // 预加载发音
    _preloadPronunciation(currentWord.value!);
  }
  
  // 查看单词详情
  void viewWordDetails() {
    if (currentWord.value == null) return;
    
    // 导航到单词详情页
    Get.toNamed(
      '/learning/detail',
      arguments: {'wordId': currentWord.value!.id},
    );
  }
  
  // 完成当前学习阶段
  void _completeCurrentStage() {
    // 通知学习控制器当前阶段已完成
    if (isNewWordsMode) {
      // 新词学习完成，检查是否有复习词
      if (learningController.session.value.reviewWords.isNotEmpty) {
        // 进入复习阶段
        learningController.currentStage.value = LearningStage.REVIEW;
      } else {
        // 没有复习词，直接进入测验或结果阶段
        learningController.currentStage.value = LearningStage.QUIZ;
      }
    } else {
      // 复习完成，进入测验阶段
      learningController.currentStage.value = LearningStage.QUIZ;
    }
  }
}
```

## 相关文件

- **lib/modules/learning/recall/views/recall_page.dart**: 单词回忆页面UI实现
- **lib/modules/learning/controllers/learning_controller.dart**: 整体学习流程控制
- **lib/modules/learning/detail/controllers/detail_controller.dart**: 单词详情控制器
- **lib/data/models/word.dart**: 单词数据模型
- **lib/app/services/audio_service.dart**: 音频播放服务
- **lib/app/services/spaced_repetition_service.dart**: 间隔重复算法服务

## 常见问题与解决方案

1. **问题**：单词列表加载缓慢  
   **解决方案**：实现分批加载和预加载机制，优化数据获取逻辑

2. **问题**：记忆评分不准确  
   **解决方案**：优化间隔重复算法，考虑用户学习历史和表现，提供更精准的复习安排

3. **问题**：音频播放失败  
   **解决方案**：实现多音源备选方案，提供在线和离线发音选项，妥善处理权限问题

4. **问题**：学习进度丢失  
   **解决方案**：实现定期自动保存机制，确保学习进度不会因应用崩溃或关闭而丢失 