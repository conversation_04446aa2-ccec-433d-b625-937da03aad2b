import 'package:logger/logger.dart';
import 'package:flutter/foundation.dart'; // 导入 kDebugMode
import 'package:get/get.dart';
// Level.debug 和 Level.warning 是什么？
// 来源与作用：Level 是 logger 包（package:logger/logger.dart）中定义的一个枚举，用于表示日志的严重程度或级别。logger 包支持多种日志级别，以便开发者可以根据需求过滤和管理日志输出。
// 常见级别（从最不严重到最严重）：
// Level.verbose：非常详细的日志，通常用于追踪所有可能的细节。
// Level.debug：调试信息，用于开发过程中定位问题和理解程序流程。
// Level.info：普通信息，用于记录应用程序的正常运行状态。
// Level.warning：警告信息，表示可能存在问题但应用程序仍能继续运行的情况。
// Level.error：错误信息，表示程序执行过程中发生了错误，可能影响部分功能。
// Level.fatal：致命错误，表示发生了严重错误，可能导致应用程序崩溃或无法继续运行。
// 控制输出：当你设置 Logger 的 level 属性时，logger 会只输出那些级别等于或高于指定级别的日志。例如：
// 设置 level: Level.debug：会输出 debug, info, warning, error, fatal 级别的日志。
// 设置 level: Level.warning：只会输出 warning, error, fatal 级别的日志。

/// 日志服务类，封装 Logger 包提供统一的日志打印接口
///
/// 继承GetxService的原因：
/// 1. 作为全局基础设施，需要在整个应用生命周期中存在
/// 2. 便于通过Get.find()在应用任何位置访问
/// 3. 可以利用GetxService的生命周期钩子管理Logger实例
class LogService extends GetxService {
  // Logger 实例
  late final Logger _logger;

  /// 初始化Logger
  @override
  void onInit() {
    super.onInit();
    _logger = Logger(
      // 根据 kDebugMode 设置日志级别
      level: kDebugMode ? Level.debug : Level.warning,
      printer: PrettyPrinter(
        methodCount: 2, // 显示 2 层调用栈
        errorMethodCount: 8, // 错误时显示 8 层调用栈
        lineLength: 120, // 每行最大长度
        colors: true, // 启用颜色打印
        printEmojis: false, // 打印 emoji
        dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart, // 打印时间
      ),
    );
  }

  /// 打印详细调试信息
  void d(dynamic message, {dynamic error, StackTrace? stackTrace}) {
    _logger.d(message, error: error, stackTrace: stackTrace);
  }

  /// 打印普通信息
  void i(dynamic message, {dynamic error, StackTrace? stackTrace}) {
    _logger.i(message, error: error, stackTrace: stackTrace);
  }

  /// 打印警告信息
  void w(dynamic message, {dynamic error, StackTrace? stackTrace}) {
    _logger.w(message, error: error, stackTrace: stackTrace);
  }

  /// 打印错误信息
  void e(dynamic message, {dynamic error, StackTrace? stackTrace}) {
    _logger.e(message, error: error, stackTrace: stackTrace);
  }

  /// 打印关键信息（严重错误）
  void f(dynamic message, {dynamic error, StackTrace? stackTrace}) {
    _logger.f(message, error: error, stackTrace: stackTrace);
  }
}
