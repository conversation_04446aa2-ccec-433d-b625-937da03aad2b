import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';

/// 音频服务类，负责处理单词发音播放
///
/// 继承GetxService的原因：
/// 1. 管理AudioPlayer资源的生命周期
/// 2. 需要在应用关闭时正确释放音频资源
/// 3. 作为全局服务可被多个页面共享使用
/// 4. 便于通过Get.find()在应用任何位置访问
class AudioService extends GetxService {
  final AudioPlayer _audioPlayer;
  final LogService _logService;
  AudioService({required LogService logService})
    : _logService = logService,
      _audioPlayer = AudioPlayer();

  // 播放单词音频
  Future<void> playWordAudio(String word, {String? url}) async {
    try {
      // 检查URL是否为示例URL（example.com），如果是则忽略
      if (url != null && url.isNotEmpty && !url.contains('example.com')) {
        _logService.i('正在播放单词音频: $word, URL: $url');
        try {
          // 尝试使用提供的URL播放
          await _audioPlayer.setUrl(url);
          await _audioPlayer.play();
          return; // 播放成功，直接返回
        } catch (e) {
          // URL加载失败，记录错误，但继续尝试使用有道词典API
          _logService.w('使用提供的URL播放失败，尝试使用有道词典API: $e');
        }
      }

      // 处理多词组合：只播放第一个单词
      String audioWord = word;
      if (word.contains(' ')) {
        audioWord = word.split(' ').first;
        _logService.i('多词组合 "$word"，只播放第一个单词: $audioWord');
      }

      // 使用有道词典API作为默认或备选方案
      final youdaoUrl =
          'https://dict.youdao.com/dictvoice?audio=$audioWord&type=2';
      _logService.i('使用有道词典API播放单词音频: $audioWord, URL: $youdaoUrl');
      await _audioPlayer.setUrl(youdaoUrl);
      await _audioPlayer.play();
    } catch (e) {
      _logService.e('音频($word)播放失败:', error: e);
      // 显示简短的提示，不打断用户体验
      Get.snackbar(
        '提示',
        '单词发音播放失败，请检查网络连接',
        snackPosition: SnackPosition.BOTTOM,
        duration: Duration(seconds: 2),
      );
    }
  }

  // 释放资源
  @override
  void onClose() {
    _audioPlayer.dispose();
    super.onClose();
  }
}
