# recall 子模块说明文档

## 子模块职责

`recall` 子模块是 `learning` 模块的核心子模块，负责实现单词记忆和回忆的主要功能。它提供了单词卡片的展示、翻转、评分和导航功能，支持新词学习和复习两种场景，是用户与单词直接交互的主要界面。

## 目录结构

```
recall/
├── controllers/
│   └── recall_controller.dart       # 回忆页面的局部控制器，管理卡片、翻页、发音等
└── views/
    └── recall_page.dart             # 回忆页面UI
```

## 核心功能

- **单词卡片展示**：以卡片形式呈现单词，支持正面（单词）和背面（释义、例句）
- **卡片翻转**：用户可以翻转卡片查看单词的详细信息
- **记忆评分**：用户可以对记忆效果进行自评，影响后续复习安排
- **语音发音**：提供单词发音功能，辅助记忆
- **导航控制**：支持前后翻页、跳过单词、查看详情等操作
- **学习进度展示**：显示当前学习进度和剩余单词数量

## 主要文件说明

### recall_controller.dart

- **职责**：管理单词回忆页面的局部逻辑和状态
- **依赖**：LearningController（从主模块获取单词数据和进度跟踪）、AudioService（播放单词发音）
- **主要功能**：
  - 管理当前显示的单词卡片
  - 处理卡片翻转逻辑
  - 处理用户的记忆评分操作
  - 管理前后导航
  - 触发单词发音播放
  - 更新学习进度到主控制器

### recall_page.dart

- **职责**：构建单词回忆页面的UI
- **依赖**：RecallController
- **主要组件**：
  - 单词卡片组件（显示单词、音标、释义、例句）
  - 进度指示器
  - 翻转按钮
  - 记忆评分按钮（"记得"、"模糊"、"不记得"等）
  - 导航按钮（上一个、下一个）
  - 发音按钮
  - 查看详情按钮

## 运行流程

1. 用户进入学习流程，`LearningController` 初始化学习会话，加载单词数据
2. 根据当前学习阶段（新词或复习），用户被导航到 `recall_page.dart`
3. `RecallController` 从 `LearningController` 获取当前阶段的单词列表
4. 页面显示第一个单词的卡片，默认只显示单词（正面）
5. 用户点击卡片或翻转按钮，卡片翻转显示释义和例句（背面）
6. 用户可以点击发音按钮听取单词读音
7. 用户通过记忆评分按钮对当前单词的记忆情况进行评分
8. `RecallController` 将评分结果传递给 `LearningController`，更新学习进度
9. 显示下一个单词，重复步骤5-8
10. 当所有单词学习完成后，`RecallController` 通知 `LearningController` 进入下一个学习阶段

## 状态管理

`RecallController` 管理以下局部状态：

- `currentIndex`：当前显示的单词索引
- `isShowingMeaning`：是否显示单词释义（卡片是否翻转）
- `isPlayingAudio`：是否正在播放单词音频

这些状态使用 GetX 的 `.obs` 包装，实现响应式UI更新。

## 与其他模块的关系

- **learning模块主控制器**：RecallController依赖LearningController获取单词数据和更新学习进度
- **detail子模块**：当用户点击"查看详情"时，导航到detail子模块查看单词的更多信息
- **app/services/audio_service.dart**：使用音频服务播放单词读音

## 易混淆点

1. **RecallController vs LearningController**：
   - `RecallController`：只管理单词卡片页面的局部UI状态和交互
   - `LearningController`：管理整个学习会话，包括会话状态、进度跟踪和阶段转换

2. **新词学习 vs 复习**：
   - 同一个 `RecallController` 和 `RecallPage` 用于两种不同场景
   - 区别在于显示的单词列表来源不同（新词或复习词）
   - 通过 `LearningController.currentStage` 区分当前处于哪个阶段

## 最佳实践

- **卡片动画**：为卡片翻转添加平滑动画，提升用户体验
- **手势支持**：实现左右滑动手势进行翻页，上下滑动手势进行卡片翻转
- **预加载**：预加载下一个单词的音频，减少等待时间
- **离线支持**：下载并缓存单词音频，支持离线学习
- **学习算法**：根据用户评分调整单词在复习列表中的排序和出现频率

## 代码示例

### RecallController实现

```dart
class RecallController extends GetxController {
  final LearningController _learningController = Get.find<LearningController>();
  final AudioService _audioService = Get.find<AudioService>();
  
  // 局部状态
  final RxInt currentIndex = 0.obs;
  final RxBool isShowingMeaning = false.obs;
  final RxBool isPlayingAudio = false.obs;
  
  // 计算属性
  List<Word> get wordsList {
    // 根据当前学习阶段返回不同的单词列表
    final stage = _learningController.currentStage.value;
    if (stage == LearningStage.NEW_WORDS) {
      return _learningController.session.value.newWords;
    } else {
      return _learningController.session.value.reviewWords;
    }
  }
  
  Word? get currentWord {
    if (wordsList.isEmpty || currentIndex.value >= wordsList.length) {
      return null;
    }
    return wordsList[currentIndex.value];
  }
  
  bool get isNewWordsStage => 
      _learningController.currentStage.value == LearningStage.NEW_WORDS;
  
  double get progress => 
      wordsList.isEmpty ? 0 : (currentIndex.value + 1) / wordsList.length;
  
  String get stageTitle => 
      isNewWordsStage ? '新词学习' : '单词复习';
  
  // 方法
  @override
  void onInit() {
    super.onInit();
    // 自动播放第一个单词的读音
    if (currentWord != null) {
      playWordPronunciation();
    }
  }
  
  void toggleMeaning() {
    isShowingMeaning.value = !isShowingMeaning.value;
    
    // 如果是首次显示释义，自动播放发音
    if (isShowingMeaning.value && !isPlayingAudio.value) {
      playWordPronunciation();
    }
  }
  
  Future<void> playWordPronunciation() async {
    if (currentWord == null || isPlayingAudio.value) return;
    
    isPlayingAudio.value = true;
    try {
      await _audioService.playAudio(currentWord!.audioUrl);
    } catch (e) {
      Get.snackbar('提示', '单词发音播放失败');
    } finally {
      isPlayingAudio.value = false;
    }
  }
  
  void nextWord() {
    if (currentIndex.value < wordsList.length - 1) {
      // 还有下一个单词
      currentIndex.value++;
      isShowingMeaning.value = false;
      playWordPronunciation();
    } else {
      // 所有单词都已显示
      _finishRecallStage();
    }
  }
  
  void previousWord() {
    if (currentIndex.value > 0) {
      currentIndex.value--;
      isShowingMeaning.value = false;
      playWordPronunciation();
    }
  }
  
  void recordMemoryScore(int score) {
    if (currentWord == null) return;
    
    // 记录学习结果
    if (isNewWordsStage) {
      _learningController.recordNewWordLearned(currentWord!, score);
    } else {
      _learningController.recordReviewWordLearned(currentWord!, score);
    }
    
    // 进入下一个单词
    nextWord();
  }
  
  void viewWordDetails() {
    if (currentWord == null) return;
    Get.toNamed(LearningRoutes.DETAIL, arguments: currentWord);
  }
  
  void _finishRecallStage() {
    // 当前阶段完成，让主控制器决定下一步
    final stage = _learningController.currentStage.value;
    if (stage == LearningStage.NEW_WORDS) {
      if (_learningController.session.value.reviewWords.isNotEmpty) {
        // 有复习词，进入复习阶段
        _learningController.navigateTo(LearningStage.REVIEW);
      } else {
        // 没有复习词，直接进入测验
        _learningController.navigateTo(LearningStage.QUIZ);
      }
    } else {
      // 复习阶段完成，进入测验
      _learningController.navigateTo(LearningStage.QUIZ);
    }
  }
}
```

### RecallPage实现

```dart
class RecallPage extends GetView<RecallController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(() => Text(controller.stageTitle)),
        actions: [
          IconButton(
            icon: Icon(Icons.help_outline),
            onPressed: () => Get.dialog(const HelpDialog()),
          ),
        ],
      ),
      body: SafeArea(
        child: Obx(() {
          final currentWord = controller.currentWord;
          if (currentWord == null) {
            return const Center(child: Text('没有单词可以学习'));
          }
          
          return Column(
            children: [
              // 进度条
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: LinearProgressIndicator(
                  value: controller.progress,
                  backgroundColor: Colors.grey[200],
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '${controller.currentIndex.value + 1}/${controller.wordsList.length}',
                      style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                    ),
                    if (controller.isNewWordsStage)
                      Text(
                        '新词学习',
                        style: TextStyle(fontSize: 14, color: Colors.blue),
                      )
                    else
                      Text(
                        '单词复习',
                        style: TextStyle(fontSize: 14, color: Colors.green),
                      ),
                  ],
                ),
              ),
              
              // 单词卡片
              Expanded(
                child: GestureDetector(
                  onTap: controller.toggleMeaning,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Card(
                      elevation: 4,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16.0),
                      ),
                      child: AnimatedFlipCard(
                        isFlipped: controller.isShowingMeaning.value,
                        frontWidget: _buildWordFront(context, currentWord),
                        backWidget: _buildWordBack(context, currentWord),
                      ),
                    ),
                  ),
                ),
              ),
              
              // 控制按钮区域
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    // 记忆评分按钮
                    if (controller.isShowingMeaning.value) ...[
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildRatingButton(
                            label: '不认识',
                            color: Colors.red,
                            score: 1,
                            icon: Icons.sentiment_very_dissatisfied,
                          ),
                          _buildRatingButton(
                            label: '模糊',
                            color: Colors.orange,
                            score: 3,
                            icon: Icons.sentiment_neutral,
                          ),
                          _buildRatingButton(
                            label: '认识',
                            color: Colors.green,
                            score: 5,
                            icon: Icons.sentiment_very_satisfied,
                          ),
                        ],
                      ),
                      SizedBox(height: 16),
                    ],
                    
                    // 控制按钮
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        // 上一个
                        IconButton(
                          icon: Icon(Icons.arrow_back),
                          onPressed: controller.currentIndex.value > 0
                              ? controller.previousWord
                              : null,
                          color: Colors.blue,
                        ),
                        
                        // 发音
                        IconButton(
                          icon: Icon(
                            controller.isPlayingAudio.value
                                ? Icons.volume_up
                                : Icons.volume_up_outlined,
                          ),
                          onPressed: controller.isPlayingAudio.value
                              ? null
                              : controller.playWordPronunciation,
                          color: Colors.purple,
                        ),
                        
                        // 翻转
                        IconButton(
                          icon: Icon(Icons.flip),
                          onPressed: controller.toggleMeaning,
                          color: Colors.amber,
                        ),
                        
                        // 详情
                        IconButton(
                          icon: Icon(Icons.info_outline),
                          onPressed: controller.viewWordDetails,
                          color: Colors.teal,
                        ),
                        
                        // 下一个
                        IconButton(
                          icon: Icon(Icons.arrow_forward),
                          onPressed: controller.isShowingMeaning.value
                              ? () => controller.recordMemoryScore(3) // 默认"模糊"评分
                              : null,
                          color: Colors.blue,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          );
        }),
      ),
    );
  }
  
  Widget _buildWordFront(BuildContext context, Word word) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            word.text,
            style: TextStyle(fontSize: 32, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16),
          Text(
            word.phonetic,
            style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 32),
          Text(
            '点击卡片查看释义',
            style: TextStyle(fontSize: 14, color: Colors.grey),
          ),
        ],
      ),
    );
  }
  
  Widget _buildWordBack(BuildContext context, Word word) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: Text(
              word.text,
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ),
          Center(
            child: Text(
              word.phonetic,
              style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(height: 24),
          Text(
            '释义:',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            word.definition,
            style: TextStyle(fontSize: 16),
          ),
          SizedBox(height: 16),
          Text(
            '例句:',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            word.example,
            style: TextStyle(fontSize: 16, fontStyle: FontStyle.italic),
          ),
        ],
      ),
    );
  }
  
  Widget _buildRatingButton({
    required String label,
    required Color color,
    required int score,
    required IconData icon,
  }) {
    return ElevatedButton.icon(
      icon: Icon(icon, color: Colors.white),
      label: Text(label, style: TextStyle(color: Colors.white)),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
      onPressed: () => controller.recordMemoryScore(score),
    );
  }
}

// 自定义翻转卡片动画组件
class AnimatedFlipCard extends StatelessWidget {
  final bool isFlipped;
  final Widget frontWidget;
  final Widget backWidget;
  
  const AnimatedFlipCard({
    Key? key,
    required this.isFlipped,
    required this.frontWidget,
    required this.backWidget,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return AnimatedSwitcher(
      duration: Duration(milliseconds: 400),
      transitionBuilder: (Widget child, Animation<double> animation) {
        final rotateAnimation = Tween(begin: pi, end: 0.0).animate(animation);
        return AnimatedBuilder(
          animation: rotateAnimation,
          child: child,
          builder: (context, widget) {
            final isUnder = (ValueKey(isFlipped) != child.key);
            var tilt = ((animation.value - 0.5).abs() - 0.5) * 0.003;
            tilt *= isUnder ? -1.0 : 1.0;
            final value = isUnder ? min(rotateAnimation.value, pi / 2) : rotateAnimation.value;
            return Transform(
              transform: Matrix4.rotationY(value)..setEntry(3, 0, tilt),
              alignment: Alignment.center,
              child: widget,
            );
          },
        );
      },
      switchInCurve: Curves.easeInBack,
      switchOutCurve: Curves.easeInBack.flipped,
      child: isFlipped
          ? Container(key: const ValueKey(true), child: backWidget)
          : Container(key: const ValueKey(false), child: frontWidget),
    );
  }
}
```

## 相关联文件

- `lib/modules/learning/controllers/learning_controller.dart`：提供单词数据和学习会话管理
- `lib/modules/learning/models/learning_session.dart`：定义学习会话数据模型
- `lib/modules/learning/widgets/word_card.dart`：如果存在，可能提供通用的单词卡片组件
- `lib/app/services/audio_service.dart`：提供音频播放服务
- `lib/data/models/word_model.dart`：单词数据模型

## 常见问题与解决方案

- **卡片翻转动画卡顿**：
  - 优化动画实现，减少不必要的重建
  - 使用更轻量级的动画方案
  - 在低端设备上降低动画复杂度

- **音频播放延迟**：
  - 实现预加载机制，提前缓存下几个单词的音频
  - 使用缓存管理，避免重复下载相同音频
  - 在播放前显示加载状态

- **学习算法优化**：
  - 实现更科学的间隔重复算法，而不是简单的顺序展示
  - 根据用户的记忆评分动态调整单词出现频率
  - 分析用户学习模式，优化学习路径 