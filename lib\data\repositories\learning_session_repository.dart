import 'package:flutter_study_flow_by_myself/app/exceptions/app_exception.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/data/models/learning_session.dart';
import 'package:flutter_study_flow_by_myself/data/providers/learning_session/learning_session_local_provider.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';

/// 学习会话仓库
///
/// 负责处理学习会话的业务逻辑，包括创建、获取、更新和完成会话。
class LearningSessionRepository {
  final LearningSessionLocalProvider _provider;
  final LogService _log;

  LearningSessionRepository({
    required LearningSessionLocalProvider provider,
    required LogService log,
  }) : _provider = provider,
       _log = log;

  /// 创建新的学习会话
  ///
  /// [userWordBookId] 单词本ID
  /// [wordIds] 学习单词ID列表
  /// 返回创建的学习会话
  Future<LearningSession> createSession(
    int userWordBookId,
    List<int> wordIds,
  ) async {
    try {
      _log.i('创建学习会话: 单词本ID=$userWordBookId, 单词数量=${wordIds.length}');

      // 创建会话对象
      final session = LearningSession(
        sessionId: const Uuid().v4(),
        userWordBookId: userWordBookId,
        wordIds: wordIds,
        startTime: DateTime.now(),
        lastStudyTime: DateTime.now(),
        currentWordIndex: 0,
        isInFinalQuiz: false,
        wordLearningStateJson: '{}',
      );

      // 保存到数据库
      await _provider.saveSession(session);

      // 返回创建的会话
      _log.i('成功创建学习会话: ${session.sessionId}');
      return session;
    } catch (e) {
      _log.e('创建学习会话失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '创建学习会话失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 获取指定ID的学习会话
  ///
  /// [sessionId] 会话ID（字符串类型的唯一标识符）
  /// 返回学习会话，如果不存在则返回null
  Future<LearningSession?> getSessionById(String sessionId) async {
    try {
      _log.i('获取学习会话: $sessionId');

      // 查找匹配sessionId的会话
      final sessions = await _provider.getAllSessions();
      final session = sessions.firstWhereOrNull(
        (s) => s.sessionId == sessionId,
      );

      if (session == null) {
        _log.w('找不到学习会话: $sessionId');
      } else {
        _log.i('成功获取学习会话: $sessionId');
      }
      return session;
    } catch (e) {
      _log.e('获取学习会话失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '获取学习会话失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 获取最新的未完成会话
  ///
  /// [userWordBookId] 单词本ID
  /// 返回最新的未完成会话，如果不存在则返回null
  Future<LearningSession?> getLatestUnfinishedSession(
    int userWordBookId,
  ) async {
    try {
      _log.i('获取最新未完成会话: 单词本ID=$userWordBookId');
      final session = await _provider.getLatestUnfinishedSession(
        userWordBookId,
      );
      if (session == null) {
        _log.i('没有找到未完成的会话');
      } else {
        _log.i('成功获取最新未完成会话: ${session.sessionId}');
      }
      return session;
    } catch (e) {
      _log.e('获取最新未完成会话失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '获取最新未完成会话失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 更新学习会话
  ///
  /// [session] 要更新的会话
  /// 返回更新后的会话
  Future<LearningSession> updateSession(LearningSession session) async {
    try {
      _log.i('更新学习会话: ${session.sessionId}');
      // 更新最后学习时间
      session = session.copyWith(lastStudyTime: DateTime.now());

      // 保存到数据库
      await _provider.saveSession(session);

      _log.i('成功更新学习会话: ${session.sessionId}');
      return session;
    } catch (e) {
      _log.e('更新学习会话失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '更新学习会话失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 完成学习会话
  ///
  /// [session] 要完成的会话
  /// 返回更新后的会话
  Future<LearningSession> completeSession(LearningSession session) async {
    try {
      _log.i('完成学习会话: ${session.sessionId}');

      // 更新结束时间和最后学习时间
      final now = DateTime.now();
      session = session.copyWith(endTime: now, lastStudyTime: now);

      // 保存到数据库
      await _provider.saveSession(session);

      _log.i('成功完成学习会话: ${session.sessionId}');
      return session;
    } catch (e) {
      _log.e('完成学习会话失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '完成学习会话失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 获取指定单词本的未完成会话（排除指定会话）
  ///
  /// [wordBookId] 单词本ID
  /// [excludeSessionId] 要排除的会话ID
  /// 返回未完成的会话列表
  Future<List<LearningSession>> getUnfinishedSessionsByWordBook(
    int wordBookId, {
    int? excludeSessionId,
  }) async {
    try {
      _log.i('查询单词本 $wordBookId 的未完成会话，排除会话: $excludeSessionId');

      final sessions = await _provider.getUnfinishedSessionsByWordBook(
        wordBookId,
        excludeSessionId: excludeSessionId,
      );

      _log.i('找到 ${sessions.length} 个未完成会话');
      return sessions;
    } catch (e) {
      _log.e('查询未完成会话失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '查询未完成会话失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 获取超过指定时间的未完成会话
  ///
  /// [cutoffTime] 截止时间
  /// 返回过期的未完成会话列表
  Future<List<LearningSession>> getExpiredUnfinishedSessions(
    DateTime cutoffTime,
  ) async {
    try {
      _log.i('查询超过 $cutoffTime 的未完成会话');

      final sessions = await _provider.getExpiredUnfinishedSessions(cutoffTime);

      _log.i('找到 ${sessions.length} 个过期未完成会话');
      return sessions;
    } catch (e) {
      _log.e('查询过期未完成会话失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '查询过期未完成会话失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 批量标记会话为已完成
  ///
  /// [sessionIds] 要标记的会话ID列表
  /// 返回更新的会话数量
  Future<int> batchMarkSessionsAsCompleted(List<int> sessionIds) async {
    try {
      if (sessionIds.isEmpty) {
        _log.i('没有需要标记的会话');
        return 0;
      }

      _log.i('批量标记 ${sessionIds.length} 个会话为已完成');

      final updatedCount = await _provider.batchMarkSessionsAsCompleted(
        sessionIds,
      );

      _log.i('成功标记 $updatedCount 个会话为已完成');
      return updatedCount;
    } catch (e) {
      _log.e('批量标记会话完成失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '批量标记会话完成失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 智能清理会话（同单词本 + 过期会话）
  ///
  /// [currentSessionId] 当前会话ID
  /// [wordBookId] 单词本ID
  /// [expiredHours] 过期小时数，默认24小时
  /// 返回清理统计信息
  Future<Map<String, int>> performSmartCleanup({
    int? currentSessionId,
    int? wordBookId,
    int expiredHours = 24,
  }) async {
    int sameBookCleaned = 0;
    int expiredCleaned = 0;

    try {
      _log.i(
        '开始执行智能清理: 当前会话=$currentSessionId, 单词本=$wordBookId, 过期时间=$expiredHours小时',
      );

      // 1. 清理同单词本的其他未完成会话
      if (currentSessionId != null && wordBookId != null) {
        final sameBookSessions = await getUnfinishedSessionsByWordBook(
          wordBookId,
          excludeSessionId: currentSessionId,
        );
        if (sameBookSessions.isNotEmpty) {
          final sessionIds = sameBookSessions.map((s) => s.id).toList();
          sameBookCleaned = await batchMarkSessionsAsCompleted(sessionIds);
          _log.i('清理同单词本未完成会话: $sameBookCleaned 个');
        }
      }

      // 2. 清理过期会话
      final cutoffTime = DateTime.now().subtract(Duration(hours: expiredHours));
      final expiredSessions = await getExpiredUnfinishedSessions(cutoffTime);
      if (expiredSessions.isNotEmpty) {
        final sessionIds = expiredSessions.map((s) => s.id).toList();
        expiredCleaned = await batchMarkSessionsAsCompleted(sessionIds);
        _log.i('清理过期未完成会话: $expiredCleaned 个');
      }

      final result = {
        'sameBookCleaned': sameBookCleaned,
        'expiredCleaned': expiredCleaned,
      };

      _log.i('智能清理完成: 同单词本$sameBookCleaned个, 过期$expiredCleaned个');
      return result;
    } catch (e) {
      _log.e('智能清理会话失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '智能清理会话失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 仅清理过期会话（用于启动时）
  ///
  /// [hours] 过期小时数，默认24小时
  /// 返回清理的会话数量
  Future<int> cleanupExpiredSessions({int hours = 24}) async {
    try {
      _log.i('开始清理超过 $hours 小时的过期会话');

      final cutoffTime = DateTime.now().subtract(Duration(hours: hours));
      final expiredSessions = await getExpiredUnfinishedSessions(cutoffTime);

      if (expiredSessions.isEmpty) {
        _log.i('没有找到需要清理的过期会话');
        return 0;
      }

      final sessionIds = expiredSessions.map((s) => s.id).toList();
      final cleanedCount = await batchMarkSessionsAsCompleted(sessionIds);

      _log.i('成功清理 $cleanedCount 个过期会话（超过 $hours 小时）');
      return cleanedCount;
    } catch (e) {
      _log.e('清理过期会话失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '清理过期会话失败',
        type: AppExceptionType.database,
      );
    }
  }
}
