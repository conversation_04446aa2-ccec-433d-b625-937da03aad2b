# Quiz 子模块说明文档

## 子模块职责

`quiz` 子模块是 `learning` 模块的重要组成部分，负责实现单词学习过程中的测验环节。该子模块通过提供各种类型的测验题目来评估用户对所学单词的掌握程度，包括选择题、拼写题等多种测验形式。测验结果将用于计算用户的记忆得分，并决定后续的学习路径。

## 目录结构

```
quiz/
├── controllers/
│   └── quiz_controller.dart      # 测验页面控制器，管理测验题目和用户作答
└── views/
    └── quiz_page.dart            # 测验页面UI，展示测验题目和选项
```

## 核心功能

1. **测验题目生成**：根据用户正在学习的单词生成不同类型的测验题目
2. **用户作答交互**：接收并处理用户的答案选择
3. **计时功能**：为每个问题或整个测验提供计时
4. **进度跟踪**：显示当前测验的完成进度
5. **成绩统计**：计算用户的答题正确率和得分
6. **自适应测试**（可选）：根据用户的表现调整题目难度

## 主要文件说明

### `quiz_controller.dart`

`QuizController` 负责管理测验页面的状态和逻辑，包括：

- **依赖**：
  - `LearningController`：获取当前学习单词列表和管理学习状态
  - `WordRepository`：获取单词的详细信息，用于生成测验选项
  
- **主要功能**：
  - 生成测验题目
  - 验证用户答案
  - 管理测验进度
  - 计算测验得分
  - 控制测验流程

### `quiz_page.dart`

`QuizPage` 构建测验的用户界面，包括：

- **依赖**：
  - `QuizController`：获取测验状态和管理用户交互
  
- **主要功能**：
  - 显示测验题目和选项
  - 提供用户交互界面
  - 展示答题反馈
  - 显示测验进度
  - 呈现计时器

## 运行流程

1. 用户完成单词学习（recall）阶段后进入测验环节
2. `LearningController` 触发测验开始，导航到 `QuizPage`
3. `QuizController` 初始化并生成测验题目
4. 用户逐题作答，每题完成后自动进入下一题
5. 用户完成所有题目后，`QuizController` 计算测验结果
6. 测验结果传递给 `LearningController`，用户进入结果页面
7. 根据测验结果决定是否需要复习错题或进入下一组单词

## 状态管理

`QuizController` 管理以下本地状态：

- **题目列表**：当前测验的所有题目
- **当前题目索引**：用户当前正在回答的题目
- **已选答案**：用户已选择的答案
- **加载状态**：测验加载状态
- **计时器状态**：测验或题目计时器

## 与其他模块的关系

- **与 `LearningController` 的关系**：
  - 从 `LearningController` 获取需要测验的单词列表
  - 向 `LearningController` 报告测验结果
  
- **与 `ResultModule` 的关系**：
  - 测验完成后将结果传递给 `ResultModule` 展示
  
- **与 `WordRepository` 的关系**：
  - 获取单词详细信息用于生成测验选项
  
- **与 `RecallModule` 的关系**：
  - 在用户完成单词记忆卡片学习后接入测验流程

## 常见混淆点

1. **`quiz` 模块与 `result` 模块的区别**：
   - `quiz` 模块负责测验过程和答题交互
   - `result` 模块负责展示测验结果和学习统计
   
2. **`quiz` 模块与 `recall` 模块的区别**：
   - `recall` 模块专注于展示单词卡片和记忆过程
   - `quiz` 模块专注于测验和评估用户记忆效果

## 最佳实践

1. **测验设计**：
   - 题目类型多样化（选择题、填空题、听力题等）
   - 确保题目分布均衡，覆盖所有学习的单词
   - 使用智能干扰项，选择与正确答案相似的词作为错误选项
   
2. **用户体验**：
   - 提供即时反馈，告知用户答案是否正确
   - 设计清晰的进度指示器
   - 考虑可访问性，支持不同用户需求
   
3. **算法优化**：
   - 实现间隔重复算法，根据用户表现调整复习频率
   - 使用用户历史数据个性化测验难度
   - 分析测验数据优化后续学习

## 代码示例

### `QuizController` 实现示例

```dart
import 'package:get/get.dart';
import '../../../data/repositories/word_repository.dart';
import '../../learning_controller.dart';
import '../../../models/quiz_question.dart';

class QuizController extends GetxController {
  final LearningController learningController = Get.find<LearningController>();
  final WordRepository wordRepository = Get.find<WordRepository>();
  
  final RxList<QuizQuestion> questions = <QuizQuestion>[].obs;
  final RxInt currentIndex = 0.obs;
  final RxMap<int, String> userAnswers = <int, String>{}.obs;
  final RxBool isLoading = true.obs;
  
  @override
  void onInit() {
    super.onInit();
    generateQuestions();
  }
  
  void generateQuestions() {
    isLoading.value = true;
    
    // 获取当前学习的单词
    final currentWords = learningController.currentWords;
    
    // 为每个单词生成测验题目
    for (var word in currentWords) {
      // 生成多种类型的题目
      questions.add(generateMultipleChoiceQuestion(word));
      // 可以添加其他类型的题目
    }
    
    isLoading.value = false;
  }
  
  QuizQuestion generateMultipleChoiceQuestion(Word word) {
    // 生成一个选择题，包括正确选项和干扰项
    // 实际实现会更复杂，包括从词库中选择合适的干扰项
    return QuizQuestion(
      questionText: '${word.meaning}的英文单词是？',
      options: generateOptions(word),
      correctAnswer: word.text,
      wordId: word.id,
    );
  }
  
  List<String> generateOptions(Word correctWord) {
    // 生成选项列表，包括正确答案和干扰项
    List<String> options = [correctWord.text];
    
    // 从词库中选择3个干扰项
    // 实际实现需要考虑相似词、同义词等因素
    List<Word> distractors = wordRepository.getSimilarWords(correctWord, 3);
    options.addAll(distractors.map((w) => w.text));
    
    // 打乱选项顺序
    options.shuffle();
    return options;
  }
  
  void submitAnswer(String answer) {
    // 记录用户答案
    userAnswers[currentIndex.value] = answer;
    
    // 延迟后移动到下一题
    Future.delayed(Duration(milliseconds: 500), () {
      if (currentIndex.value < questions.length - 1) {
        currentIndex.value++;
      } else {
        finishQuiz();
      }
    });
  }
  
  void finishQuiz() {
    // 计算得分
    int correctCount = 0;
    userAnswers.forEach((index, answer) {
      if (answer == questions[index].correctAnswer) {
        correctCount++;
      }
    });
    
    double score = correctCount / questions.length * 100;
    
    // 将结果传递给学习控制器
    learningController.completeQuiz(score, userAnswers.map((index, answer) {
      return MapEntry(
        questions[index].wordId,
        answer == questions[index].correctAnswer
      );
    }));
    
    // 导航到结果页面
    Get.toNamed('/learning/result');
  }
}
```

### `QuizPage` 实现示例

```dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/quiz_controller.dart';

class QuizPage extends GetView<QuizController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('单词测验')),
      body: Obx(() {
        if (controller.isLoading.value) {
          return Center(child: CircularProgressIndicator());
        }
        
        if (controller.questions.isEmpty) {
          return Center(child: Text('没有可用的测验题目'));
        }
        
        final currentQuestion = controller.questions[controller.currentIndex.value];
        
        return Padding(
          padding: EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // 进度指示器
              LinearProgressIndicator(
                value: (controller.currentIndex.value + 1) / controller.questions.length,
              ),
              SizedBox(height: 16),
              
              // 题目文本
              Text(
                currentQuestion.questionText,
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 24),
              
              // 选项列表
              ...currentQuestion.options.map((option) {
                final isSelected = controller.userAnswers[controller.currentIndex.value] == option;
                
                return Padding(
                  padding: EdgeInsets.only(bottom: 12),
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      primary: isSelected ? Colors.blue : Colors.white,
                      onPrimary: isSelected ? Colors.white : Colors.black,
                      padding: EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: Text(option, style: TextStyle(fontSize: 16)),
                    onPressed: () => controller.submitAnswer(option),
                  ),
                );
              }).toList(),
            ],
          ),
        );
      }),
    );
  }
}
```

## 相关文件

- `lib/modules/learning/learning_controller.dart`：主控制器，管理整个学习流程
- `lib/modules/learning/result/controllers/result_controller.dart`：结果页面控制器
- `lib/data/repositories/word_repository.dart`：单词数据仓库
- `lib/data/models/quiz_question.dart`：测验题目数据模型
- `lib/data/models/quiz_result.dart`：测验结果数据模型

## 常见问题与解决方案

1. **测验题目质量问题**：
   - **症状**：题目干扰项过于明显，无法有效测试用户记忆
   - **解决方案**：改进干扰项选择算法，选择与正确答案在语义或拼写上相似的单词

2. **UI响应性问题**：
   - **症状**：在用户提交答案后切换到下一题时出现延迟
   - **解决方案**：优化状态管理，使用预加载技术提前准备下一题内容

3. **测验结果准确性问题**：
   - **症状**：测验结果不能准确反映用户的实际掌握程度
   - **解决方案**：引入更复杂的评分机制，考虑回答时间、错误类型等因素 