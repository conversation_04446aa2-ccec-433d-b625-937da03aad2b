# routes 目录说明文档

## 目录职责

`routes` 目录负责定义 `auth` 模块中所有页面的路由配置，包括路径常量和路由页面定义。它为整个认证模块提供统一的导航管理，确保模块内部的页面可以被正确访问，同时也允许其他模块导航到认证模块的相关页面。

## 文件结构

```
routes/
└── auth_routes.dart     # 认证模块的路由定义
```

## 核心文件说明

### `auth_routes.dart`

`auth_routes.dart` 是认证模块的路由管理文件，负责：

- **定义路由路径常量**：为每个认证相关页面定义唯一的路由路径字符串
- **创建 GetPage 对象**：将路由路径与实际页面组件及其依赖绑定关联
- **提供导出的路由列表**：供全局路由表（`app_pages.dart`）使用

## 实现详情

### 路由路径常量定义

```dart
abstract class AuthRoutes {
  // 主入口路由
  static const String main = '/auth';
  
  // 子页面路由
  static const String login = '/auth/login';
  static const String register = '/auth/register';
  static const String forgotPassword = '/auth/forgot-password';
  static const String resetPassword = '/auth/reset-password';
  static const String verification = '/auth/verification';
}
```

### GetPage 定义

```dart
class AuthPages {
  static final routes = [
    GetPage(
      name: AuthRoutes.main,
      page: () => AuthPage(),
      binding: AuthBinding(),
      children: [
        GetPage(
          name: '/login',
          page: () => LoginPage(),
        ),
        GetPage(
          name: '/register',
          page: () => RegisterPage(),
        ),
        GetPage(
          name: '/forgot-password',
          page: () => ForgotPasswordPage(),
        ),
        GetPage(
          name: '/reset-password',
          page: () => ResetPasswordPage(),
        ),
        GetPage(
          name: '/verification',
          page: () => VerificationPage(),
        ),
      ],
    ),
  ];
}
```

## 完整文件示例

```dart
import 'package:get/get.dart';
import '../bindings/auth_binding.dart';
import '../views/auth_page.dart';
import '../views/login_page.dart';
import '../views/register_page.dart';
import '../views/forgot_password_page.dart';
import '../views/reset_password_page.dart';
import '../views/verification_page.dart';

// 路由路径常量
abstract class AuthRoutes {
  // 主入口路由
  static const String main = '/auth';
  
  // 子页面路由（使用相对路径）
  static const String login = '/auth/login';
  static const String register = '/auth/register';
  static const String forgotPassword = '/auth/forgot-password';
  static const String resetPassword = '/auth/reset-password';
  static const String verification = '/auth/verification';
}

// 路由页面定义
class AuthPages {
  static final routes = [
    GetPage(
      name: AuthRoutes.main,
      page: () => AuthPage(),
      binding: AuthBinding(),
      children: [
        GetPage(
          name: '/login',
          page: () => LoginPage(),
          transition: Transition.fadeIn,
        ),
        GetPage(
          name: '/register',
          page: () => RegisterPage(),
          transition: Transition.rightToLeft,
        ),
        GetPage(
          name: '/forgot-password',
          page: () => ForgotPasswordPage(),
          transition: Transition.rightToLeft,
        ),
        GetPage(
          name: '/reset-password',
          page: () => ResetPasswordPage(),
          transition: Transition.rightToLeft,
        ),
        GetPage(
          name: '/verification',
          page: () => VerificationPage(),
          transition: Transition.rightToLeft,
          middlewares: [
            // 示例：验证中间件，确保只有在重置密码流程中才能访问验证页面
            VerificationMiddleware(),
          ],
        ),
      ],
    ),
  ];
}

// 示例：验证中间件
class VerificationMiddleware extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    final authController = Get.find<AuthController>();
    // 只有在重置密码流程中才能访问验证页面
    if (!authController.isInResetProcess) {
      return RouteSettings(name: AuthRoutes.login);
    }
    return null;
  }
}
```

## 使用方式

### 在主路由表中使用

在全局路由表（`lib/app/routes/app_pages.dart`）中导入和使用认证模块的路由：

```dart
import '../../modules/auth/routes/auth_routes.dart';

class AppPages {
  static final routes = [
    ...HomePages.routes,
    ...AuthPages.routes,  // 导入认证模块的路由
    ...LearningPages.routes,
    // 其他模块路由...
  ];
}
```

### 在模块内部导航

在认证模块的控制器中使用定义的路由进行导航：

```dart
class AuthController extends GetxController {
  // 导航到登录页面
  void navigateToLogin() {
    Get.toNamed(AuthRoutes.login);
  }
  
  // 导航到注册页面
  void navigateToRegister() {
    Get.toNamed(AuthRoutes.register);
  }
  
  // 导航到忘记密码页面
  void navigateToForgotPassword() {
    Get.toNamed(AuthRoutes.forgotPassword);
  }
  
  // 开始密码重置流程
  void startPasswordReset(String email) {
    // 设置状态标记正在进行重置密码流程
    isInResetProcess = true;
    // 保存邮箱信息
    this.email = email;
    // 导航到验证页面
    Get.toNamed(AuthRoutes.verification);
  }
}
```

### 从其他模块导航到认证模块

在其他模块中使用认证模块的路由：

```dart
class ProfileController extends GetxController {
  // 退出登录
  void logout() {
    // 清除用户数据
    _clearUserData();
    // 导航到登录页面
    Get.offAllNamed(AuthRoutes.login);
  }
}
```

## 路由设计考量

1. **嵌套路由结构**：
   - 主路由（`/auth`）作为模块入口
   - 子路由作为模块内部页面，定义为主路由的子项
   - 这种结构使得路由层次更清晰，也便于管理模块内部的导航

2. **转场动画**：
   - 为不同类型的页面设置适合的转场动画
   - 例如，登录页使用淡入动画，其他页面使用右滑入场动画

3. **路由中间件**：
   - 使用中间件进行路由拦截和重定向
   - 例如，确保只有在特定流程中才能访问某些页面

4. **路由命名规范**：
   - 模块路径使用模块名（如 `/auth`）
   - 子页面路径附加在模块路径后（如 `/auth/login`）
   - 使用连字符（如 `forgot-password`）而非驼峰命名

## 与其他目录的关系

- **bindings目录**：路由定义中关联相应的绑定（如 `AuthBinding`）
- **views目录**：路由指向对应的页面组件
- **controller目录**：控制器通过路由导航来切换页面
- **全局routes目录**：模块路由被导入到全局路由表中

## 最佳实践

1. **集中管理**：
   - 所有路由相关的定义都集中在 `auth_routes.dart` 中
   - 避免在多个文件中定义路由，以免造成混乱和冲突

2. **常量使用**：
   - 使用常量定义路由路径，而不是硬编码字符串
   - 这样可以避免拼写错误，也便于重构

3. **安全考虑**：
   - 使用中间件保护需要认证的路由
   - 防止未授权访问敏感页面

4. **参数传递**：
   - 使用路由参数传递简单数据
   - 使用控制器状态传递复杂对象或敏感数据

## 常见问题与解决方案

1. **认证状态检查**：
   - 问题：未登录用户尝试访问需要认证的页面
   - 解决：使用全局中间件或路由守卫进行认证状态检查

2. **参数传递**：
   - 问题：在重置密码流程中需要在多个页面间传递信息
   - 解决：在控制器中保存状态，或使用Get.arguments传递数据

3. **返回导航**：
   - 问题：完成注册后需要返回到登录页面
   - 解决：使用Get.offNamed()替换当前路由，而不是添加新路由

4. **深度链接**：
   - 问题：从邮件链接直接进入重置密码页面
   - 解决：支持路由参数解析，处理外部深度链接 