import 'package:flutter/material.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/routes/learning_routes.dart';
import 'package:flutter_study_flow_by_myself/modules/vocabulary/controllers/word_book_detail_controller.dart';
import 'package:get/get.dart';

class WordBookDetailPage extends GetView<WordBookDetailController> {
  const WordBookDetailPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(
          () => Text(
            controller.userWordBook.value?.wordBook.value?.name ?? '词书详情',
          ),
        ),
        actions: [
          // 在AppBar的右侧添加激活按钮
          // 只有当词书未激活时才显示此按钮
          Obx(() {
            if (controller.userWordBook.value != null &&
                !controller.userWordBook.value!.isActive) {
              return IconButton(
                icon: const Icon(Icons.check_circle_outline),
                tooltip: '激活词书',
                onPressed: controller.activateWordBook,
              );
            }
            return const SizedBox.shrink(); // 词书已激活时不显示按钮
          }),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        if (controller.userWordBook.value == null) {
          return const Center(child: Text('未找到词书信息'));
        }

        return RefreshIndicator(
          onRefresh: controller.loadWordBookDetails,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(context),
                _buildProgressSection(context),
                _buildDailySettingsSection(context),
                _buildWordPreviewSection(context),
                const SizedBox(height: 80),
              ],
            ),
          ),
        );
      }),
      // 浮动按钮：只有当词书已激活时才显示"开始学习"按钮
      floatingActionButton: Obx(() {
        if (controller.userWordBook.value?.isActive == true) {
          return FloatingActionButton.extended(
            onPressed: () => _startLearning(context),
            icon: const Icon(Icons.school),
            label: const Text('开始学习'),
          );
        }
        return const SizedBox.shrink(); // 词书未激活时不显示按钮
      }),
    );
  }

  // 构建词书详情页面的头部信息卡片
  Widget _buildHeader(BuildContext context) {
    final wordBook = controller.userWordBook.value!;
    final bookInfo = wordBook.wordBook.value;

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    bookInfo?.name ?? '未知词书',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                ),
                // 状态指示标签：显示词书的激活状态
                Chip(
                  label: Text(wordBook.isActive ? '已激活' : '未激活'),
                  backgroundColor:
                      wordBook.isActive
                          ? Colors.green.shade100
                          : Colors.grey.shade300,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              bookInfo?.description ?? '无描述信息',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 8),
            Text(
              '总计 ${bookInfo?.wordCount ?? 0} 个单词',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            const SizedBox(height: 16),
            // 根据激活状态显示不同的操作按钮
            if (wordBook.isActive) ...[
              // 已激活：显示停用按钮
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: controller.deactivateWordBook,
                  icon: const Icon(Icons.cancel),
                  label: const Text('停用此词书'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ] else ...[
              // 未激活：显示激活按钮
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: controller.activateWordBook,
                  icon: const Icon(Icons.check_circle_outline),
                  label: const Text('激活此词书'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildProgressSection(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('学习进度', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: controller.progressPercentage.value / 100,
              minHeight: 10,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Obx(() {
                  final stats = controller.learningStats;
                  final learnedCount = stats['learnedCount'] as int? ?? 0;
                  final totalCount = stats['totalCount'] as int? ?? 0;
                  return Text('已学习: $learnedCount / $totalCount');
                }),
                Obx(() => Text('${controller.progressPercentage.value}%')),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDailySettingsSection(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('每日学习设置', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('每日新词数量:'),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 快速选择按钮
                    Wrap(
                      spacing: 4,
                      children:
                          [1, 2, 5, 10, 15, 20].map((count) {
                            return Obx(() {
                              final isSelected =
                                  controller.dailyWordCount.value == count;
                              return GestureDetector(
                                onTap:
                                    () =>
                                        controller.setDailyNewWordsCount(count),
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color:
                                        isSelected
                                            ? Colors.blue
                                            : Colors.grey[200],
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Text(
                                    '$count',
                                    style: TextStyle(
                                      color:
                                          isSelected
                                              ? Colors.white
                                              : Colors.black,
                                      fontSize: 12,
                                    ),
                                  ),
                                ),
                              );
                            });
                          }).toList(),
                    ),
                    const SizedBox(width: 8),
                    // 自定义输入
                    SizedBox(
                      width: 60,
                      child: Obx(
                        () => TextFormField(
                          initialValue:
                              controller.dailyWordCount.value.toString(),
                          keyboardType: TextInputType.number,
                          textAlign: TextAlign.center,
                          style: const TextStyle(fontSize: 14),
                          decoration: const InputDecoration(
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            border: OutlineInputBorder(),
                            isDense: true,
                          ),
                          onFieldSubmitted: (value) {
                            final count = int.tryParse(value);
                            if (count != null && count > 0 && count <= 100) {
                              controller.setDailyNewWordsCount(count);
                            } else {
                              Get.snackbar('错误', '请输入1-100之间的数字');
                            }
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWordPreviewSection(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('单词预览', style: Theme.of(context).textTheme.titleMedium),
                Obx(() {
                  if (controller.isLoadingWords.value) {
                    return const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    );
                  }
                  return IconButton(
                    icon: const Icon(Icons.refresh),
                    onPressed: () => controller.loadWordPreview(),
                    iconSize: 20,
                  );
                }),
              ],
            ),
            const SizedBox(height: 16),
            Obx(() {
              if (controller.words.isEmpty) {
                return const Center(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Text('暂无单词预览'),
                  ),
                );
              }

              return ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: controller.words.length.clamp(0, 10),
                itemBuilder: (context, index) {
                  final word = controller.words[index];
                  return ListTile(
                    title: Text(word.spelling),
                    subtitle: Text(
                      word.definition,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  );
                },
              );
            }),
          ],
        ),
      ),
    );
  }

  void _startLearning(BuildContext context) {
    if (controller.userWordBook.value == null) {
      Get.snackbar('错误', '无法开始学习，词书信息不完整', snackPosition: SnackPosition.BOTTOM);
      return;
    }

    // 跳转到学习流程，传递词书ID和每日词数
    Get.toNamed(
      LearningRoutes.RECALL,
      arguments: {
        'userWordBookId': controller.userWordBook.value!.id,
        'wordCount': controller.dailyWordCount.value,
        'isNewSession': true,
      },
    );
  }
}
