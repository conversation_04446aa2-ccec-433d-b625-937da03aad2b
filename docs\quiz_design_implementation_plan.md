# 测验题实现设计方案

## 项目背景

针对程序员的背单词App中测验功能的设计方案，目标是在测验环节中**兼顾自动生成效率、题型多样性、认知深度与开发成本控制**。本设计基于**用户认知层次模型（从记忆→理解→应用→分析）**，提供了系统化的测验题型框架。

## 测验题型设计表格

### ✅ 程序员背单词 App：测验题型设计表格（增强版）

| 题目名称             | 难度（1-5） | 适合方式         | 理由                        | 实现思路                                                         |
| ---------------- | ------- | ------------ | ------------------------- | ------------------------------------------------------------ |
| **听音选义**         | ⭐⭐      | 自动生成         | 锻炼听力与词义联想，适合被动记忆阶段        | 播放 `audioUrl`，展示 4 个词义选项（含正确释义 + 干扰项），干扰项从同类词中随机抽取。          |
| **听音拼写**         | ⭐⭐      | 自动生成         | 锻炼听音辨词与拼写记忆               | 播放发音音频，让用户拼写单词，后台比对 `spelling` 字段；支持拼写自动检查。                  |
| **看英选中（选择中文义项）** | ⭐       | 自动生成         | 直观简单，适合记忆初期，常用于快速检测       | 展示单词本体 + 4 个中文释义（包含正确释义 + 干扰项）；干扰项从词性或领域相近词义中抽取。             |
| **看中选英（选择英文单词）** | ⭐⭐      | 自动生成         | 倒置认知路径，锻炼中→英回忆能力          | 展示中文释义，4 个英文单词中选择正确项；干扰项根据同词性或同词根构造。                         |
| **例句填空（词义填空）**   | ⭐⭐      | 自动生成         | 锻炼语境理解和记忆迁移               | 使用 `example` 中的句子，将目标单词替换为空；用户需填写或选择正确单词。                    |
| **拼写题（纯拼写）**     | ⭐       | 自动生成         | 简单直接，记忆强化                 | 展示释义或中文提示，让用户完整拼写单词；支持拼写提示或词根提示。                             |
| **概念理解题**        | ⭐⭐⭐⭐    | LLM生成 + 人工审核 | 程序员词汇常含抽象概念，机器难以准确生成      | 用语言模型生成解释性题干（如：解释"dependency injection"），用户判断/选择最佳解释。建议人工筛选。 |
| **代码片段分析题**      | ⭐⭐⭐⭐⭐   | 手动设计         | 结合真实语境，提升应用能力，认知负荷高       | 手动构建含目标词汇的代码片段，提出基于语义的选择题、填空题、判断题等，要求用户理解该单词在语义中的功能或含义。      |
| **词汇比较题（辨析）**    | ⭐⭐⭐⭐    | LLM生成 + 人工审核 | 适合区分易混词（如 async vs await） | 利用模型生成两个词的对比解释、场景判断题；如"哪个词适合用于以下语境？"等。                       |
| **多义词义匹配题**      | ⭐⭐⭐     | 自动生成 + LLM辅助 | 多义词常见，考查词义广度              | 从 WordModel 的 `definition` 中提取多个义项，匹配不同例句，选择哪一义项适合该例句。       |
| **语义排序题**        | ⭐⭐⭐     | 半自动生成        | 提升理解力，适合高阶学习者             | 给出含关键词的多个句子，按逻辑或语义正确性排序。适合高级学习者或挑战题型。                        |

## 说明

* **题目名称**：学生在 App 内看到的测验名，可以对应 UI 命名。
* **难度（1-5 星）**：综合考虑开发成本、用户理解、认知强度；
* **适合方式**：
  * `自动生成`：适合频繁复习、大量使用；
  * `LLM生成 + 人工审核`：适合质量优先、有挑战性的题；
  * `手动设计`：适合关键词、低频精练考察；
* **实现思路**：给出可落地的逻辑建议，能直接指导开发。

## 附加建议（为题型管理做准备）

1. **为每个单词记录题型分布与答题数据**（比如哪些题型容易错）；
2. **支持题型动态权重调整**（如掌握越差的题型权重越高）；
3. **每次测验混合题型展示**，避免认知疲劳；
4. **允许用户选择偏好题型**（如只测听力、只测理解）；
5. **题型配置结构建议**（如 JSON）：

```json
{
  "type": "listen_to_meaning",
  "difficulty": 2,
  "source": "auto",
  "generator": "listenAudio + getDefinition + randomDistractors",
  "title": "听音选义",
  "description": "播放音频，选择正确的中文释义"
}
```

## 实施补充建议

### 数据模型设计

1. **题型数据模型设计**：
```dart
class QuizItemModel {
  final int id;
  final String type; // 题型标识
  final int wordId; // 关联单词ID
  final int userWordBookId; // 关联用户单词本ID
  final Map<String, dynamic> content; // 题目内容（不同题型存储不同结构）
  final int difficulty;
  final String source; // 来源：auto/llm/manual
  
  // 统计数据
  int attemptCount;
  int correctCount;
  DateTime lastAttemptTime;
}
```

2. **测验会话模型**：
```dart
class QuizSessionModel {
  final int id;
  final int userWordBookId;
  final DateTime createTime;
  final List<int> quizItemIds; // 本次测验包含的题目
  final QuizMixStrategy mixStrategy; // 混合策略
  final int targetCount; // 目标题目数量
  
  // 会话状态
  int currentIndex;
  int correctCount;
  QuizSessionStatus status;
}
```

### 架构设计

1. **题型生成服务架构**：
   - 建立`QuizGeneratorService`接口
   - 为每种题型实现具体生成器
   - 集成LLM API（如OpenAI）处理高级题型

2. **测验进度与单词本关联**：
   - 在`UserWordBookModel`中添加测验相关字段
   - 为每个单词在特定单词本中记录各题型掌握度

3. **测验调度算法**：
   - 根据历史错误率调整题型出现频率
   - 实现间隔重复算法特别针对测验题
   - 加入优先测试新学/易错单词的机制

### 补充题型建议

1. **代码注释补全题**：展示代码片段，让用户补充正确的技术术语在注释中
2. **技术文档理解题**：给出API文档片段，测试对技术词汇在文档中的理解
3. **错误修正题**：展示含有词义错误使用的句子/代码，让用户指出并修正

### 实现分步计划

1. 先实现基础自动生成题型（1-6）
2. 建立测验框架和数据结构
3. 添加高级题型（7-11）和LLM集成
4. 优化算法和用户体验 