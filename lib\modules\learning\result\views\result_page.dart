import 'package:flutter/material.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/result/controllers/result_controller.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/widgets/learning_progress_bar.dart';
import 'package:get/get.dart';

class ResultPage extends GetView<ResultController> {
  const ResultPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('学习完成'),
        elevation: 0,
        // 禁用返回按钮，强制用户选择下一步操作
        automaticallyImplyLeading: false,
      ),
      body: Column(
        children: [
          // 顶部进度条 - 学习已完成，测验也已完成
          Obx(
            () => LearningProgressBar(
              learnedWords: controller.totalWords.value,
              totalWords: controller.totalWords.value,
              completedQuizzes: controller.totalQuizzes.value,
              totalQuizzes: controller.totalQuizzes.value,
              isInFinalQuiz: true,
            ),
          ),

          // 结果内容
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // 祝贺图标
                  const Icon(Icons.celebration, size: 80, color: Colors.amber),
                  const SizedBox(height: 24),

                  // 祝贺文本
                  const Text(
                    '恭喜完成学习！',
                    style: TextStyle(fontSize: 28, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 40),

                  // 学习统计卡片
                  _buildStatisticsCard(),
                  const SizedBox(height: 32),

                  // 表现评级
                  _buildPerformanceRating(),
                  const SizedBox(height: 48),

                  // 操作按钮
                  _buildActionButtons(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 学习统计卡片
  Widget _buildStatisticsCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          children: [
            const Text(
              '学习统计',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 24),

            // 统计数据
            Obx(
              () => Column(
                children: [
                  _buildStatItem('学习单词数', '${controller.totalWords.value}'),
                  const Divider(),
                  _buildStatItem('测验题数量', '${controller.totalQuizzes.value}'),
                  const Divider(),
                  _buildStatItem('正确答题数', '${controller.correctAnswers.value}'),
                  const Divider(),
                  _buildStatItem(
                    '正确率',
                    '${(controller.accuracy.value * 100).toInt()}%',
                    valueColor: Colors.green,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 单个统计项
  Widget _buildStatItem(String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontSize: 16)),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: valueColor,
            ),
          ),
        ],
      ),
    );
  }

  // 表现评级
  Widget _buildPerformanceRating() {
    return Obx(
      () => Column(
        children: [
          Text(
            controller.performanceRating,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            controller.ratingDescription,
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  // 操作按钮
  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: controller.startNewSession,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              backgroundColor: Colors.blue,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
            ),
            child: const Text('继续学习新单词', style: TextStyle(fontSize: 18)),
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton(
            onPressed: controller.moveToHome,
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
            ),
            child: const Text('返回首页', style: TextStyle(fontSize: 18)),
          ),
        ),
      ],
    );
  }
}
