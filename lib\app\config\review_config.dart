import 'package:get/get.dart';

/// 复习模块配置管理
///
/// 用于管理复习相关的配置，特别是测试模式的开关
class ReviewConfig extends GetxController {
  static ReviewConfig get instance {
    try {
      return Get.find<ReviewConfig>();
    } catch (e) {
      // 如果找不到实例，创建一个新的
      Get.put(ReviewConfig(), permanent: true);
      return Get.find<ReviewConfig>();
    }
  }

  // 测试模式开关
  final _isTestMode = false.obs;

  /// 是否为测试模式
  bool get isTestMode => _isTestMode.value;

  /// 时间缩放因子
  /// 测试模式：缩小1000倍（1天 ≈ 1.4分钟）
  /// 正常模式：1倍（正常时间）
  double get timeScaleFactor => isTestMode ? 0.001 : 1.0;

  /// 切换测试模式
  void toggleTestMode() {
    _isTestMode.value = !_isTestMode.value;
    Get.snackbar(
      '测试模式',
      isTestMode ? '已开启测试模式（时间缩小1000倍）' : '已关闭测试模式（恢复正常时间）',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  /// 设置测试模式
  void setTestMode(bool enabled) {
    _isTestMode.value = enabled;
  }

  /// 获取缩放后的时间间隔
  Duration getScaledDuration(Duration originalDuration) {
    final scaledMilliseconds =
        (originalDuration.inMilliseconds * timeScaleFactor).round();
    return Duration(milliseconds: scaledMilliseconds);
  }

  /// 获取缩放后的天数间隔
  int getScaledDays(int originalDays) {
    final scaledDays = (originalDays * timeScaleFactor).round();
    return scaledDays.clamp(1, originalDays); // 至少1天，最多原始天数
  }

  /// 计算缩放后的下次复习时间
  DateTime calculateNextReviewDate(int intervalDays) {
    if (isTestMode) {
      // 测试模式：将天数转换为分钟
      final intervalMinutes =
          (intervalDays * 24 * 60 * timeScaleFactor).round();
      return DateTime.now().add(Duration(minutes: intervalMinutes));
    } else {
      // 正常模式：使用天数
      return DateTime.now().add(Duration(days: intervalDays));
    }
  }
}
