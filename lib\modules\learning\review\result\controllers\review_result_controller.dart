﻿import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/services/review_state_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/services/review_navigation_service.dart';

/// 复习结果控制器
///
/// 负责管理复习结果页面的状态和交互逻辑，参考ResultController的设计
///
/// 核心职责：
/// - 展示复习统计结果（完成单词数、正确率、复习时长等）
/// - 处理用户的后续操作（返回首页）
/// - 管理结果页面的加载状态
class ReviewResultController extends GetxController {
  // ==================== 依赖注入 ====================
  final ReviewStateService _stateService;
  final ReviewNavigationService _navigationService;
  final LogService _log;

  ReviewResultController({
    required ReviewStateService stateService,
    required ReviewNavigationService navigationService,
    required LogService log,
  }) : _stateService = stateService,
       _navigationService = navigationService,
       _log = log;

  // ==================== 响应式状态 ====================

  /// 页面加载状态
  final isLoading = false.obs;

  /// 复习统计数据
  final reviewStats = RxMap<String, dynamic>({});

  // ==================== 生命周期管理 ====================

  @override
  void onInit() {
    super.onInit();
    _log.i('初始化复习结果控制器');
    _loadReviewStats();
  }

  // ==================== 数据加载管理 ====================

  /// 加载复习统计数据
  void _loadReviewStats() {
    try {
      isLoading.value = true;

      // 从状态服务获取统计数据
      final stats = _stateService.getReviewStats();
      reviewStats.value = stats;

      _log.i('复习统计数据加载完成: $stats');
    } catch (e) {
      _log.e('加载复习统计数据失败: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // ==================== 用户交互处理 ====================

  /// 返回首页
  void navigateToHome() {
    try {
      _log.i('用户选择返回首页');
      _navigationService.navigateToHome();
    } catch (e) {
      _log.e('返回首页失败: $e');
      // 备用方案：直接使用Get导航
      Get.offAllNamed('/main');
    }
  }

  // ==================== 计算属性 ====================

  /// 复习时长
  /// 从状态服务获取实际的复习时长
  Duration get reviewDuration {
    final durationSeconds = reviewStats['duration'] ?? 0;
    return Duration(seconds: durationSeconds);
  }

  /// 获取复习完成的单词数量
  int get completedWordsCount {
    return reviewStats['completedWords'] ?? 0;
  }

  /// 获取总单词数量
  int get totalWordsCount {
    return reviewStats['totalWords'] ?? 0;
  }

  /// 获取总答对次数
  int get totalCorrectCount {
    return reviewStats['correctAnswers'] ?? 0;
  }

  /// 获取总答错次数
  int get totalErrorCount {
    return reviewStats['incorrectAnswers'] ?? 0;
  }

  /// 获取正确率（0.0-1.0）
  double get accuracyRate {
    final total = totalCorrectCount + totalErrorCount;
    if (total == 0) return 0.0;
    return totalCorrectCount / total;
  }

  /// 获取正确率百分比文本
  /// 从统计数据中获取已计算好的正确率
  String get accuracyText {
    final accuracy = reviewStats['accuracy'] ?? 0.0;
    return '${accuracy.toStringAsFixed(1)}%';
  }

  // ==================== 生命周期管理 ====================

  @override
  void onClose() {
    _log.i('ReviewResultController销毁');
    super.onClose();
  }
}
