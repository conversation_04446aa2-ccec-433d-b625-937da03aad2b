import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/review_item_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/word_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/user_word_book_repository.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/services/review_state_service.dart';

/// 复习质量服务
///
/// 负责计算复习质量评分，专门用于复习流程的SM-2算法
///
/// 与LearningQualityService的区别：
/// - LearningQualityService：计算学习质量（0-100分），用于初次学习
/// - ReviewQualityService：计算复习质量（1-5分），用于SM-2间隔重复算法
///
/// 职责：
/// - 根据错误次数计算SM-2算法的质量评分（1-5分）
/// - 更新复习项的间隔时间和难度系数
class ReviewQualityService extends GetxService {
  // ==================== 依赖注入 ====================
  final ReviewStateService _stateService;
  final ReviewItemRepository _reviewItemRepository;
  final WordRepository _wordRepository;
  final UserWordBookRepository _userWordBookRepository;
  final LogService _log;

  ReviewQualityService({
    required ReviewStateService stateService,
    required ReviewItemRepository reviewItemRepository,
    required WordRepository wordRepository,
    required UserWordBookRepository userWordBookRepository,
    required LogService log,
  }) : _stateService = stateService,
       _reviewItemRepository = reviewItemRepository,
       _wordRepository = wordRepository,
       _userWordBookRepository = userWordBookRepository,
       _log = log;

  // ==================== 公开方法 ====================

  /// 计算单词的复习质量评分
  ///
  /// 根据错误次数计算质量评分，供ReviewController使用
  /// [wordId] 单词ID
  /// 返回1-5的质量评分
  double calculateWordQuality(int wordId) {
    try {
      final errorCount = _stateService.getWordErrorCount(wordId);
      final quality = _calculateQuality(errorCount);

      _log.i('单词$wordId质量评分: $quality (错误次数: $errorCount)');
      return quality;
    } catch (e) {
      _log.e('计算单词$wordId质量评分失败: $e');
      // 统一错误处理：显示错误提示并返回默认值
      Get.snackbar('错误', '计算质量评分失败，使用默认评分');
      return 2.0; // 默认返回2分（需要加强）
    }
  }

  /// 更新单个单词的复习项
  ///
  /// 参考LearningQualityService的updateWordReviewItem方法
  /// [wordId] 单词ID
  /// [userWordBookId] 用户单词本ID
  Future<void> updateWordReviewItem(int wordId, int userWordBookId) async {
    try {
      _log.i('更新单词复习项: $wordId');

      // 获取单词
      final word = await _wordRepository.getWordById(wordId);
      if (word == null) {
        _log.w('找不到单词: $wordId，跳过更新复习项');
        return;
      }

      // 获取用户单词本
      final userWordBook = await _userWordBookRepository.getUserWordBookById(
        userWordBookId,
      );
      if (userWordBook == null) {
        _log.w('找不到用户单词本: $userWordBookId，跳过更新复习项');
        return;
      }

      // 计算质量评分
      final quality = calculateWordQuality(wordId);

      // 调用Repository更新复习时间
      // 注意：SM-2算法要求整数评分(1-5)，将double转换为int是正确的
      // 例如：5.0 → 5, 4.0 → 4，不会丢失有效信息
      await _reviewItemRepository.processReviewSuccess(
        word,
        userWordBook,
        quality.round(), // 使用round()而不是toInt()，更安全
      );

      _log.i('已更新单词 $wordId 的复习计划，质量评分: $quality');
    } catch (e) {
      _log.e('更新单词复习项失败: $wordId, 错误: $e');
      Get.snackbar('错误', '更新复习进度失败，请重试');
    }
  }

  // ==================== 私有方法 ====================

  /// 根据错误次数计算质量评分
  ///
  /// 实现SM-2算法的质量评分逻辑：
  /// - 0次错误：5分（完美掌握）
  /// - 1次错误：4分（很好掌握）
  /// - 2次错误：3分（良好掌握）
  /// - 3次及以上错误：2分（需要加强）
  ///
  /// [errorCount] 该单词在本次复习中的错误次数
  /// 返回1.0-5.0的质量评分
  double _calculateQuality(int errorCount) {
    switch (errorCount) {
      case 0:
        return 5.0; // 完美掌握，间隔时间会显著增加
      case 1:
        return 4.0; // 很好掌握，间隔时间适度增加
      case 2:
        return 3.0; // 良好掌握，间隔时间略微增加
      default:
        return 2.0; // 需要加强，间隔时间保持较短
    }
  }
}
