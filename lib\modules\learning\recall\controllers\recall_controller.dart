import 'dart:async';
import 'package:flutter_study_flow_by_myself/app/services/audio_service.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_model.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/word_repository.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/controllers/learning_controller.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/services/learning_navigation_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/services/learning_state_service.dart';
import 'package:get/get.dart';

/// 单词回忆页面控制器
///
/// 职责：
/// - 管理单词回忆页面的数据加载和显示
/// - 控制单词释义的显示/隐藏状态
/// - 处理用户的认识/不认识操作
/// - 协调音频播放和页面导航
///
/// 架构说明：
/// 本控制器专注于回忆页面的业务逻辑，通过依赖注入获取所需服务，
/// 使用响应式监听替代定时器轮询，实现高效的状态管理。
class RecallController extends GetxController {
  // ==================== 依赖注入 ====================
  /// 学习主控制器 - 提供学习会话的上下文信息
  ///
  /// 调用位置：
  /// - 当前文件中未直接使用，但作为学习流程的上下文依赖保留
  ///
  /// 用途：提供学习会话状态和协调学习流程
  final LearningController learningController;

  /// 学习状态服务 - 管理学习过程中的状态数据
  ///
  /// 调用位置：
  /// - onInit() 中监听 wordIdsObs 和 currentWordIndexObs 变化
  /// - _updateCurrentWord() 中获取 getCurrentWordId() 和 currentWordIndex
  /// - _handleWordRecognition() 中调用 updateWordRecognition()
  /// - progressText 和 progressIndicatorText getter 中获取进度信息
  final LearningStateService _stateService;

  /// 学习导航服务 - 处理学习流程中的页面导航
  ///
  /// 调用位置：
  /// - _handleWordRecognition() 中调用 navigateToDetailById() 导航到详情页面
  final LearningNavigationService _navigationService;

  /// 音频服务 - 处理单词发音播放
  ///
  /// 调用位置：
  /// - playWordAudio() 中调用 playWordAudio() 播放单词音频
  /// - _updateCurrentWord() 中自动播放新单词音频
  final AudioService _audioService;

  /// 单词仓库 - 处理单词数据的获取
  ///
  /// 调用位置：
  /// - _updateCurrentWord() 中调用 getWordById() 获取单词详情
  final WordRepository _wordRepository;

  /// 日志服务 - 记录控制器运行日志
  ///
  /// 调用位置：
  /// - 所有方法中用于记录操作日志、错误信息和调试信息
  final LogService _log;

  // ==================== 响应式状态变量 ====================
  /// 控制单词释义的显示/隐藏状态
  ///
  /// 存储当前是否显示单词释义，默认为隐藏状态（只显示拼写）
  ///
  /// UI 使用位置：
  /// - recall_page.dart:96 - 通过 Obx 监听，控制释义的透明度动画
  /// - recall_page.dart:138 - 控制"查看释义"/"隐藏释义"按钮文本
  ///
  /// 内部使用：
  /// - _updateCurrentWord() 中每次加载新单词时重置为 false
  /// - toggleDefinition() 中切换显示状态
  ///
  /// 响应式监听：当状态变化时，自动更新UI显示和按钮文本
  final RxBool isDefinitionVisible = false.obs;

  /// 当前正在学习的单词数据
  ///
  /// 存储当前回忆页面展示的单词完整信息，包括拼写、释义、音标等
  ///
  /// UI 使用位置：
  /// - recall_page.dart:46 - 通过 Obx 监听，控制页面内容显示
  /// - recall_page.dart:63 - 显示单词拼写
  /// - recall_page.dart:107 - 显示单词释义和音标
  ///
  /// 内部使用：
  /// - _updateCurrentWord() 中更新单词数据
  /// - playWordAudio() 中获取单词信息播放音频
  /// - _handleWordRecognition() 中获取单词ID进行状态更新
  ///
  /// 响应式监听：当单词数据变化时，自动更新UI显示
  final Rx<WordModel?> currentWord = Rx<WordModel?>(null);

  // ==================== 构造函数 ====================
  /// 构造函数 - 依赖注入
  ///
  /// 通过构造函数注入所有必要的服务和控制器实例，确保依赖关系清晰
  /// 并提高代码的可测试性和可维护性
  ///
  /// 参数：
  /// - [learningController] 学习主控制器实例
  /// - [stateService] 学习状态服务实例
  /// - [navigationService] 学习导航服务实例
  /// - [audioService] 音频服务实例
  /// - [wordRepository] 单词仓库实例
  /// - [log] 日志服务实例
  ///
  /// 注册位置：lib/modules/learning/bindings/learning_binding.dart
  RecallController({
    required this.learningController,
    required LearningStateService stateService,
    required LearningNavigationService navigationService,
    required AudioService audioService,
    required WordRepository wordRepository,
    required LogService log,
  }) : _stateService = stateService,
       _navigationService = navigationService,
       _audioService = audioService,
       _wordRepository = wordRepository,
       _log = log;

  // ==================== 生命周期方法 ====================
  /// 控制器初始化方法
  ///
  /// 在控制器创建时自动调用，负责设置响应式监听和初始化数据加载
  ///
  /// 调用时机：
  /// - 当用户导航到单词回忆页面时，GetX 自动调用此方法
  /// - 通过学习流程中的导航服务触发
  ///
  /// 执行流程：
  /// 1. 调用父类的 onInit() 方法
  /// 2. 设置单词ID列表变化的监听
  /// 3. 设置单词索引变化的监听（替代定时器轮询）
  /// 4. 如果初始化时已有单词数据，立即加载当前单词
  ///
  /// 监听机制：
  /// - 监听单词ID列表变化：当学习数据加载完成时触发
  /// - 监听单词索引变化：当用户学习进度发生变化时触发
  @override
  void onInit() {
    super.onInit();
    _log.i('初始化回忆控制器');

    // 监听单词索引变化，当索引变化时自动更新当前单词
    // 注意：移除了对wordIdsObs的监听，因为该属性已被清理
    // 改为直接监听索引变化，这样更高效且能覆盖所有需要更新的场景

    // 🔑 关键改进：直接监听单词索引变化，替代定时器轮询
    // 这是性能优化的关键，避免了500ms的定时器轮询
    ever(_stateService.currentWordIndexObs, (int currentIndex) {
      _log.i('检测到单词索引变化: $currentIndex');
      _updateCurrentWord();
    });

    // 如果已经有数据，立即执行
    // 处理从其他页面导航过来时已经有学习数据的情况
    if (_stateService.wordIds.isNotEmpty) {
      _updateCurrentWord();
    }
  }

  /// 控制器销毁方法
  ///
  /// 在控制器销毁时自动调用，负责清理资源
  ///
  /// 调用时机：
  /// - 当用户离开回忆页面时，GetX 自动调用此方法
  ///
  /// 清理内容：
  /// - 移除了定时器相关代码，因为改用响应式监听
  /// - 响应式监听会自动清理，无需手动处理
  @override
  void onClose() {
    super.onClose();
  }

  /// 控制器就绪方法
  ///
  /// 在控制器初始化完成且页面渲染完成后调用
  ///
  /// 调用时机：
  /// - 页面完全加载并渲染完成后
  ///
  /// 执行逻辑：
  /// - 延迟300ms后自动播放当前单词音频
  /// - 确保页面动画完成后再播放音频，提升用户体验
  @override
  void onReady() {
    super.onReady();
    // 延迟播放确保页面渲染完成
    Future.delayed(const Duration(milliseconds: 300), () {
      playWordAudio();
    });
  }

  // ==================== 音频播放方法 ====================
  /// 播放单词音频（手动触发）
  ///
  /// 用户主动点击播放按钮时调用，直接播放当前单词的音频
  ///
  /// 调用位置：
  /// - recall_page.dart 中的音频播放按钮点击事件（如果有）
  /// - onReady() 中的自动播放
  ///
  /// 播放逻辑：
  /// 1. 检查当前是否有单词数据
  /// 2. 优先使用单词的自定义音频URL
  /// 3. 如果没有自定义URL，使用默认的TTS音频
  ///
  /// 音频来源优先级：
  /// - 单词自带的音频URL（如果存在且不为空）
  /// - 音频服务的默认TTS生成音频
  ///
  /// 返回值：无返回值（void）
  void playWordAudio() {
    if (currentWord.value != null) {
      final word = currentWord.value!;
      final String spelling = word.spelling;

      // 优先使用模型中的audioUrl，如果为空则使用默认TTS
      if (word.audioUrl != null && word.audioUrl!.isNotEmpty) {
        _audioService.playWordAudio(spelling, url: word.audioUrl);
      } else {
        _audioService.playWordAudio(spelling);
      }
    }
  }

  // ==================== 私有方法 ====================
  /// 更新当前单词数据
  ///
  /// 根据当前学习进度加载对应的单词数据并更新UI状态
  ///
  /// 调用位置：
  /// - onInit() 中在单词数据变化时调用
  /// - onInit() 中在单词索引变化时调用
  /// - onInit() 中在初始化时如果已有数据则调用
  ///
  /// 业务逻辑：
  /// 1. 从状态服务获取当前单词ID
  /// 2. 验证单词ID的有效性
  /// 3. 从仓库异步获取单词详细数据
  /// 4. 更新响应式变量触发UI更新
  /// 5. 重置释义显示状态
  /// 6. 自动播放单词音频
  ///
  /// 异常处理：
  /// - 捕获所有异常并记录错误日志
  /// - 显示用户友好的错误提示
  /// - 不重新抛出异常，确保应用稳定性
  ///
  /// 性能优化：
  /// - 使用 async/await 替代 .then() 链式调用
  /// - 提供详细的调试日志便于问题排查
  ///
  /// 返回值：无返回值（Future&lt;void&gt;）
  Future<void> _updateCurrentWord() async {
    try {
      final wordId = _stateService.getCurrentWordId();
      if (wordId == -1) {
        _log.d(
          '🔍 RecallController: _updateCurrentWord: ${currentWord.value?.spelling} -> null (index: ${_stateService.currentWordIndex})',
        );
        return;
      }

      _log.d('开始加载单词: ID=$wordId');

      // 🔑 关键改进：使用 async/await 替代 .then() 链式调用
      // 提高代码可读性和错误处理能力
      final word = await _wordRepository.getWordById(wordId);

      _log.d(
        '🔍 RecallController: _updateCurrentWord: ${currentWord.value?.spelling} -> ${word?.spelling} (index: ${_stateService.currentWordIndex})',
      );

      currentWord.value = word; // 更新响应式变量，触发UI更新

      // 每次更新单词时重置释义显示状态，确保新单词默认隐藏释义
      isDefinitionVisible.value = false;

      // 自动播放单词音频（每次更新单词都播放）
      if (word != null) {
        _audioService.playWordAudio(word.spelling);
        _log.d('自动播放单词音频: ${word.spelling}');
      }
    } catch (e) {
      _log.e('更新当前单词失败', error: e);
      // 显示用户友好的错误提示，不影响应用继续运行
      Get.snackbar('提示', '加载单词失败，请重试');
    }
  }

  // ==================== 用户交互方法 ====================
  /// 切换释义显示状态
  ///
  /// 用户点击"查看释义"/"隐藏释义"按钮时调用
  ///
  /// 调用位置：
  /// - recall_page.dart:136 - "查看释义"按钮的点击事件
  ///
  /// 业务逻辑：
  /// - 切换 isDefinitionVisible 的布尔值
  /// - 触发UI动画显示或隐藏释义内容
  ///
  /// 返回值：无返回值（void）
  void toggleDefinition() {
    isDefinitionVisible.value = !isDefinitionVisible.value;
  }

  /// 用户点击"认识"按钮
  ///
  /// 用户表示认识当前单词时调用
  ///
  /// 调用位置：
  /// - recall_page.dart:173 - "认识"按钮的点击事件
  ///
  /// 业务逻辑：
  /// - 调用通用的单词认知处理方法，传入 true 表示认识
  ///
  /// 返回值：无返回值（void）
  void onKnownPressed() {
    _handleWordRecognition(true);
  }

  /// 用户点击"不认识"按钮
  ///
  /// 用户表示不认识当前单词时调用
  ///
  /// 调用位置：
  /// - recall_page.dart:153 - "不认识"按钮的点击事件
  ///
  /// 业务逻辑：
  /// - 调用通用的单词认知处理方法，传入 false 表示不认识
  ///
  /// 返回值：无返回值（void）
  void onUnknownPressed() {
    _handleWordRecognition(false);
  }

  /// 处理单词认知状态并导航到详情页
  ///
  /// 统一处理用户的认识/不认识操作，避免代码重复
  ///
  /// 调用位置：
  /// - onKnownPressed() 中调用，传入 true
  /// - onUnknownPressed() 中调用，传入 false
  ///
  /// 业务逻辑：
  /// 1. 验证当前是否有单词数据
  /// 2. 更新单词的认知状态到状态服务
  /// 3. 通过导航服务导航到单词详情页面
  ///
  /// 参数：
  /// - [isKnown] 是否认识该单词，true表示认识，false表示不认识
  ///
  /// 架构优势：
  /// - 使用依赖注入的导航服务，符合SOLID原则
  /// - 统一的错误处理和状态管理
  /// - 消除了重复代码，提高可维护性
  ///
  /// 返回值：无返回值（void）
  void _handleWordRecognition(bool isKnown) {
    if (currentWord.value != null) {
      // 使用状态服务更新单词认知状态
      _stateService.updateWordRecognition(currentWord.value!.id, isKnown);

      // 🔑 关键改进：通过导航服务导航，而不是直接使用 Get.toNamed
      // 这样可以统一处理导航逻辑，便于测试和维护
      _navigationService.navigateToDetailById(currentWord.value!.id);
    }
  }

  // ==================== 数据访问接口 ====================
  /// 获取学习进度百分比的文本表示
  ///
  /// 返回当前学习进度的百分比字符串，用于显示学习进度
  ///
  /// UI 使用位置：
  /// - 可能在进度条或状态显示中使用（当前代码中未直接使用）
  ///
  /// 计算逻辑：
  /// - 当前单词索引 / 总单词数量 * 100
  /// - 结果取整并添加百分号
  ///
  /// 数据来源：
  /// - currentWordIndex: 当前单词在学习序列中的索引（从0开始）
  /// - wordIds.length: 总单词数量
  ///
  /// 返回值：String - 格式化的百分比文本，如 "30%"
  String get progressText {
    final progress =
        _stateService.currentWordIndex / _stateService.wordIds.length;
    return '${(progress * 100).toInt()}%';
  }

  /// 获取当前单词的学习进度文本
  ///
  /// 返回格式化的进度文本，显示当前单词在学习序列中的位置
  ///
  /// UI 使用位置：
  /// - recall_page.dart 中可能显示学习进度信息
  /// - 进度条或状态栏中使用
  ///
  /// 数据来源：
  /// - currentWordIndex: 当前单词在学习序列中的索引（从0开始）
  /// - wordIds.length: 总单词数量
  ///
  /// 格式说明：
  /// - 索引+1是因为用户界面通常从1开始计数
  /// - 返回格式："当前位置 / 总数"，如 "3 / 10"
  ///
  /// 返回值：String - 格式化的进度文本
  String get progressIndicatorText {
    final currentIndex = _stateService.currentWordIndex + 1;
    final totalWords = _stateService.wordIds.length;
    return '$currentIndex / $totalWords';
  }
}
