import '../models/word_model.dart';

/// 模拟的5个单词数据
final List<WordModel> mockWords = [
  WordModel(
    spelling: 'apple',
    phonetic: '/ˈæp.əl/',
    definition: 'A round fruit with red or green skin and a whitish interior.',
    examples: ['I eat an apple every day.', 'The apple fell from the tree.'],
    audioUrl: null,
    imageUrl: null,
    tags: ['fruit', 'food'],
  )..id = 1,
  WordModel(
    spelling: 'book',
    phonetic: '/bʊk/',
    definition:
        'A set of pages that have been fastened together inside a cover to be read or written in.',
    examples: ['She is reading a book.', 'I borrowed a book from the library.'],
    audioUrl: null,
    imageUrl: null,
    tags: ['object', 'education'],
  )..id = 2,
  WordModel(
    spelling: 'cat',
    phonetic: '/kæt/',
    definition: 'A small domesticated carnivorous mammal with soft fur.',
    examples: ['The cat is sleeping on the sofa.', 'I have a black cat.'],
    audioUrl: null,
    imageUrl: null,
    tags: ['animal', 'pet'],
  )..id = 3,
  WordModel(
    spelling: 'dog',
    phonetic: '/dɒɡ/',
    definition:
        'A domesticated carnivorous mammal that typically has a long snout and barks.',
    examples: ['The dog barked loudly.', 'Dogs are loyal animals.'],
    audioUrl: null,
    imageUrl: null,
    tags: ['animal', 'pet'],
  )..id = 4,
  WordModel(
    spelling: 'egg',
    phonetic: '/eɡ/',
    definition:
        'An oval or round object laid by a female bird, reptile, fish, or invertebrate, usually containing a developing embryo.',
    examples: ['I had an egg for breakfast.', 'The hen laid an egg.'],
    audioUrl: null,
    imageUrl: null,
    tags: ['food', 'animal'],
  )..id = 5,
];
