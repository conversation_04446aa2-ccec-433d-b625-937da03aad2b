# Detail Controllers 目录说明文档

## 目录职责

`detail/controllers` 目录负责实现学习模块中单词详情展示环节的业务逻辑控制，管理单词详细数据的获取、发音播放、收藏状态管理和相关词汇导航等功能。该目录是MVVM架构中的ViewModel层，连接单词数据模型和详情视图。

## 文件结构

```
detail/controllers/
└── detail_controller.dart      # 单词详情控制器，处理单词详情数据和用户交互
```

## 核心功能

- **单词详情数据管理**：获取和管理单词的详细信息
- **发音播放控制**：控制单词发音的播放和状态
- **收藏功能管理**：处理单词的收藏和取消收藏
- **界面状态管理**：管理详情页面各部分的展开/折叠状态
- **相关词汇导航**：处理相关词汇的查看和导航
- **返回学习流程**：协调返回到学习主流程

## 主要文件说明

### detail_controller.dart

- **职责**：单词详情页面的控制器
- **依赖关系**：
  - `LearningController`：获取当前学习会话状态
  - `WordRepository`：获取单词详细数据
  - `AudioService`：处理单词发音播放
  - `UserRepository`：管理单词收藏状态
- **主要方法和属性**：
  - **状态变量**：
    - `currentWord.obs`：当前展示的单词对象
    - `isPlaying.obs`：发音播放状态
    - `isFavorite.obs`：单词收藏状态
    - `isLoading.obs`：数据加载状态
    - `expandedSections`：各部分展开/折叠状态
  - **方法**：
    - `loadWordDetails()`：加载单词详细信息
    - `playPronunciation()`：播放单词发音
    - `toggleFavorite()`：切换单词收藏状态
    - `isExpanded()`：检查某部分是否展开
    - `setSectionExpanded()`：设置某部分的展开状态
    - `navigateToRelatedWord()`：导航到相关词汇
    - `returnToLearning()`：返回学习流程

## 运行流程

1. **初始化阶段**：
   - `DetailController` 在用户进入详情页面时初始化
   - 从 `LearningController` 获取当前单词基本信息
   - 异步加载单词的详细数据
   - 检查单词的收藏状态

2. **交互响应阶段**：
   - 响应用户的发音播放请求
   - 处理单词收藏/取消收藏操作
   - 管理页面各部分的展开/折叠状态
   - 处理相关词汇的导航请求

3. **返回流程**：
   - 用户完成查看后，协调返回到学习主流程
   - 保存用户在详情页的操作（如收藏状态）

## 状态管理

`DetailController` 使用GetX的响应式状态管理，主要包括：

- **响应式变量**：关键状态声明为.obs类型，用于自动触发UI更新
- **依赖注入**：通过Get.find()获取其他控制器和服务
- **异步处理**：使用Future和异步方法处理数据加载和音频播放
- **状态持久化**：确保用户操作（如收藏）被正确保存

## 与其他模块的关系

- **learning/controllers**：从`LearningController`获取当前学习状态
- **detail/views**：为详情视图提供数据和行为
- **data/repositories**：从`WordRepository`获取单词数据
- **app/services**：使用`AudioService`播放单词发音

## 常见混淆点

1. **详情控制器vs学习控制器**：
   - `DetailController` 专注于单个单词的详细信息展示
   - `LearningController` 管理整体学习流程和会话

2. **数据加载策略**：
   - 基本数据从学习控制器获取（避免重复请求）
   - 详细数据按需从数据仓库获取（如例句、相关词等）

3. **状态保存范围**：
   - 收藏等操作应保存到用户数据中
   - 展开/折叠等UI状态仅在当前会话中保存

## 最佳实践

- **数据缓存**：缓存已加载的单词详情，避免重复请求
- **错误处理**：妥善处理数据加载和音频播放失败情况
- **性能优化**：使用懒加载策略，仅加载必要的数据
- **用户体验**：提供清晰的加载状态和操作反馈
- **内存管理**：在控制器销毁时释放资源，如音频播放器

## 代码示例

```dart
class DetailController extends GetxController {
  // 依赖项
  final LearningController learningController = Get.find<LearningController>();
  final WordRepository wordRepository = Get.find<WordRepository>();
  final AudioService audioService = Get.find<AudioService>();
  final UserRepository userRepository = Get.find<UserRepository>();
  
  // 状态变量
  final Rx<Word> currentWord = Word.empty().obs;
  final RxBool isLoading = true.obs;
  final RxBool isPlaying = false.obs;
  final RxBool isFavorite = false.obs;
  final RxBool hasError = false.obs;
  final RxString errorMessage = ''.obs;
  
  // 界面状态 - 各部分的展开状态
  final Map<String, RxBool> expandedSections = {};
  
  @override
  void onInit() {
    super.onInit();
    // 获取传递的单词ID
    final wordId = Get.arguments['wordId'];
    if (wordId != null) {
      loadWordDetails(wordId);
    } else {
      hasError.value = true;
      errorMessage.value = '未找到单词ID';
    }
  }
  
  @override
  void onClose() {
    // 停止任何正在播放的音频
    if (isPlaying.value) {
      audioService.stop();
    }
    super.onClose();
  }
  
  // 加载单词详细信息
  Future<void> loadWordDetails(String wordId) async {
    isLoading.value = true;
    hasError.value = false;
    
    try {
      // 尝试从学习会话中获取基本单词信息
      Word? word = _findWordInSession(wordId);
      
      // 如果在会话中找不到，从仓库获取基本信息
      if (word == null) {
        word = await wordRepository.getWordById(wordId);
      }
      
      // 设置当前单词的基本信息
      currentWord.value = word;
      
      // 并行加载详细信息和收藏状态
      await Future.wait([
        _loadWordExtendedDetails(wordId),
        _checkFavoriteStatus(wordId),
      ]);
      
    } catch (e) {
      hasError.value = true;
      errorMessage.value = '加载单词详情失败: $e';
      Get.log('DetailController加载失败: $e');
    } finally {
      isLoading.value = false;
    }
  }
  
  // 从学习会话中查找单词
  Word? _findWordInSession(String wordId) {
    final session = learningController.session.value;
    
    // 在新词和复习词中查找
    for (final word in [...session.newWords, ...session.reviewWords]) {
      if (word.id == wordId) {
        return word;
      }
    }
    
    return null;
  }
  
  // 加载单词的扩展详情
  Future<void> _loadWordExtendedDetails(String wordId) async {
    try {
      // 获取详细信息
      final details = await wordRepository.getWordDetails(wordId);
      
      // 更新当前单词对象，保留原有属性
      final updatedWord = currentWord.value.copyWith(
        examples: details.examples,
        wordForms: details.wordForms,
        relatedWords: details.relatedWords,
        memoryTips: details.memoryTips,
      );
      
      currentWord.value = updatedWord;
    } catch (e) {
      Get.log('加载单词扩展详情失败: $e');
      // 错误不中断整体加载，使用默认空值
    }
  }
  
  // 检查单词收藏状态
  Future<void> _checkFavoriteStatus(String wordId) async {
    try {
      final isFav = await userRepository.isWordFavorite(wordId);
      isFavorite.value = isFav;
    } catch (e) {
      Get.log('检查收藏状态失败: $e');
      // 默认为未收藏
      isFavorite.value = false;
    }
  }
  
  // 播放单词发音
  Future<void> playPronunciation() async {
    if (isPlaying.value) {
      // 如果正在播放，停止播放
      audioService.stop();
      isPlaying.value = false;
      return;
    }
    
    try {
      isPlaying.value = true;
      
      // 获取发音URL
      final pronunciationUrl = currentWord.value.pronunciationUrl;
      
      if (pronunciationUrl != null && pronunciationUrl.isNotEmpty) {
        // 在线发音
        await audioService.playFromUrl(pronunciationUrl);
      } else {
        // 离线TTS发音
        await audioService.speakText(currentWord.value.word);
      }
      
      // 播放完成后更新状态
      isPlaying.value = false;
    } catch (e) {
      Get.log('播放发音失败: $e');
      Get.snackbar('播放失败', '无法播放单词发音');
      isPlaying.value = false;
    }
  }
  
  // 切换收藏状态
  Future<void> toggleFavorite() async {
    try {
      final wordId = currentWord.value.id;
      final newStatus = !isFavorite.value;
      
      // 更新UI状态（乐观更新）
      isFavorite.value = newStatus;
      
      // 保存到仓库
      if (newStatus) {
        await userRepository.addWordToFavorites(wordId);
      } else {
        await userRepository.removeWordFromFavorites(wordId);
      }
      
    } catch (e) {
      // 恢复原状态
      isFavorite.value = !isFavorite.value;
      Get.snackbar(
        '操作失败', 
        isFavorite.value ? '添加收藏失败' : '取消收藏失败',
      );
    }
  }
  
  // 检查某部分是否展开
  bool isExpanded(String sectionKey, bool defaultValue) {
    if (!expandedSections.containsKey(sectionKey)) {
      expandedSections[sectionKey] = RxBool(defaultValue);
    }
    return expandedSections[sectionKey]!.value;
  }
  
  // 设置某部分的展开状态
  void setSectionExpanded(String sectionKey, bool isExpanded) {
    if (!expandedSections.containsKey(sectionKey)) {
      expandedSections[sectionKey] = RxBool(isExpanded);
    } else {
      expandedSections[sectionKey]!.value = isExpanded;
    }
  }
  
  // 导航到相关词汇
  void navigateToRelatedWord(String word) {
    // 查找单词ID
    wordRepository.findWordIdByText(word).then((wordId) {
      if (wordId != null) {
        // 在新页面中打开相关词汇，保留当前页面
        Get.toNamed(
          '/learning/detail',
          arguments: {'wordId': wordId},
        );
      } else {
        Get.snackbar('未找到', '未找到相关单词信息');
      }
    }).catchError((e) {
      Get.snackbar('错误', '无法查看相关单词: $e');
    });
  }
  
  // 返回学习流程
  void returnToLearning() {
    Get.back();
  }
}
```

## 相关文件

- **lib/modules/learning/detail/views/detail_page.dart**: 单词详情页面UI实现
- **lib/modules/learning/controllers/learning_controller.dart**: 整体学习流程控制
- **lib/data/repositories/word_repository.dart**: 单词数据存储库
- **lib/data/repositories/user_repository.dart**: 用户数据存储库
- **lib/app/services/audio_service.dart**: 音频播放服务

## 常见问题与解决方案

1. **问题**：单词详情数据加载缓慢  
   **解决方案**：实现分阶段加载策略，先加载基本信息，再异步加载详细数据，显示加载进度

2. **问题**：音频播放失败或权限问题  
   **解决方案**：实现多种播放方式（在线URL、离线TTS），优雅处理权限拒绝情况

3. **问题**：相关词汇导航造成原学习流程丢失  
   **解决方案**：使用嵌套导航或临时页面展示相关词汇，确保能够返回原学习上下文

4. **问题**：收藏状态同步问题  
   **解决方案**：使用乐观更新UI并异步保存，实现失败时回滚状态，确保数据一致性 