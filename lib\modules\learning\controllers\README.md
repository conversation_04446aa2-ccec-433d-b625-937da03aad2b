# controllers 目录说明文档

## 目录职责

`controllers` 目录包含 `learning` 模块的主控制器，负责管理整个学习流程的状态和业务逻辑。这个控制器是学习模块的核心，协调各个子模块（如记忆、测验、结果等）之间的数据流和状态变化，并与数据层进行交互，为整个学习过程提供中央控制机制。

## 文件结构

```
controllers/
└── learning_controller.dart    # 学习模块的主控制器
```

## 核心文件说明

### `learning_controller.dart`

`learning_controller.dart` 是学习模块的主控制器，负责：

- **学习会话管理**：创建、更新和完成学习会话
- **学习流程控制**：管理从单词记忆到测验再到结果展示的完整流程
- **学习数据协调**：处理新词和复习词的选择与加载
- **进度跟踪**：记录学习进度和结果
- **状态管理**：维护整个学习过程的状态

## 实现详情

### 状态定义

```dart
class LearningController extends GetxController {
  // 学习会话
  final Rx<LearningSession> session = LearningSession().obs;
  
  // 加载状态
  final RxBool isLoading = false.obs;
  
  // 当前学习阶段
  final Rx<LearningStage> currentStage = LearningStage.NEW_WORDS.obs;
  
  // 新词列表
  final RxList<Word> newWords = <Word>[].obs;
  
  // 复习词列表
  final RxList<Word> reviewWords = <Word>[].obs;
  
  // 今日学习统计
  final Rx<LearningStats> todayStats = LearningStats().obs;
  
  // 依赖注入
  final WordRepository _wordRepository = Get.find<WordRepository>();
  final UserRepository _userRepository = Get.find<UserRepository>();
  final SettingsService _settingsService = Get.find<SettingsService>();
  
  // ...其他状态和依赖
}
```

### 主要方法

```dart
class LearningController extends GetxController {
  // 状态定义...
  
  // 初始化学习会话
  Future<void> initLearningSession() async {
    isLoading.value = true;
    try {
      // 1. 获取用户设置（每日新词数量、复习词数量等）
      final settings = await _settingsService.getLearningSettings();
      
      // 2. 获取今天需要学习的新词
      newWords.value = await _wordRepository.getTodayNewWords(
        limit: settings.dailyNewWords,
      );
      
      // 3. 获取今天需要复习的单词
      reviewWords.value = await _wordRepository.getTodayReviewWords(
        limit: settings.dailyReviewWords,
      );
      
      // 4. 创建新的学习会话
      session.value = LearningSession(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        startTime: DateTime.now(),
        newWords: newWords.map((w) => w.id).toList(),
        reviewWords: reviewWords.map((w) => w.id).toList(),
        stage: LearningStage.NEW_WORDS,
      );
      
      // 5. 设置初始阶段
      currentStage.value = LearningStage.NEW_WORDS;
      
      // 6. 导航到第一个阶段（单词记忆）
      navigateToCurrentStage();
    } catch (e) {
      // 错误处理
      Get.snackbar('初始化学习失败', e.toString());
    } finally {
      isLoading.value = false;
    }
  }
  
  // 记录新词学习结果
  void recordNewWordLearned(String wordId, int memoryLevel) {
    session.update((s) {
      s?.recordNewWordLearned(wordId, memoryLevel);
    });
  }
  
  // 记录复习词学习结果
  void recordReviewWordLearned(String wordId, int memoryLevel) {
    session.update((s) {
      s?.recordReviewWordLearned(wordId, memoryLevel);
    });
  }
  
  // 更新测验结果
  void updateQuizResults(List<QuizResult> results) {
    session.update((s) {
      s?.updateQuizResults(results);
    });
  }
  
  // 完成当前阶段并进入下一阶段
  void completeCurrentStage() {
    switch (currentStage.value) {
      case LearningStage.NEW_WORDS:
        if (reviewWords.isNotEmpty) {
          currentStage.value = LearningStage.REVIEW;
        } else {
          currentStage.value = LearningStage.QUIZ;
        }
        break;
      case LearningStage.REVIEW:
        currentStage.value = LearningStage.QUIZ;
        break;
      case LearningStage.QUIZ:
        currentStage.value = LearningStage.RESULT;
        break;
      case LearningStage.RESULT:
        currentStage.value = LearningStage.COMPLETE;
        completeSession();
        break;
      case LearningStage.COMPLETE:
        // 返回主页或重新开始
        Get.offAllNamed('/home');
        break;
    }
    
    // 导航到下一阶段
    navigateToCurrentStage();
  }
  
  // 导航到当前阶段对应的页面
  void navigateToCurrentStage() {
    switch (currentStage.value) {
      case LearningStage.NEW_WORDS:
      case LearningStage.REVIEW:
        Get.toNamed('/learning/recall');
        break;
      case LearningStage.QUIZ:
        Get.toNamed('/learning/quiz');
        break;
      case LearningStage.RESULT:
        Get.toNamed('/learning/result');
        break;
      case LearningStage.COMPLETE:
        Get.toNamed('/learning/complete');
        break;
    }
  }
  
  // 完成整个学习会话
  Future<void> completeSession() async {
    try {
      // 1. 更新会话结束时间
      session.update((s) {
        s?.endTime = DateTime.now();
        s?.isCompleted = true;
      });
      
      // 2. 计算学习统计数据
      final stats = session.value.calculateStats();
      todayStats.value = stats;
      
      // 3. 保存学习结果到用户记录
      await _userRepository.saveLearningRecord(session.value);
      
      // 4. 更新单词的记忆级别和下次复习时间
      await _wordRepository.updateWordsMemoryLevels(
        session.value.learningResults
      );
    } catch (e) {
      Get.snackbar('保存学习结果失败', e.toString());
    }
  }
  
  // 其他辅助方法...
}
```

## 完整控制器示例

```dart
import 'package:get/get.dart';
import '../models/learning_session.dart';
import '../../../data/repositories/word_repository.dart';
import '../../../data/repositories/user_repository.dart';
import '../../../app/services/settings_service.dart';
import '../../../data/models/word.dart';
import '../../../data/models/quiz_result.dart';

enum LearningStage { NEW_WORDS, REVIEW, QUIZ, RESULT, COMPLETE }

class LearningController extends GetxController {
  // =========== 状态定义 ===========
  // 学习会话
  final Rx<LearningSession> session = LearningSession().obs;
  
  // 加载状态
  final RxBool isLoading = false.obs;
  
  // 当前学习阶段
  final Rx<LearningStage> currentStage = LearningStage.NEW_WORDS.obs;
  
  // 新词列表
  final RxList<Word> newWords = <Word>[].obs;
  
  // 复习词列表
  final RxList<Word> reviewWords = <Word>[].obs;
  
  // 今日学习统计
  final Rx<LearningStats> todayStats = LearningStats().obs;
  
  // =========== 依赖注入 ===========
  final WordRepository _wordRepository = Get.find<WordRepository>();
  final UserRepository _userRepository = Get.find<UserRepository>();
  final SettingsService _settingsService = Get.find<SettingsService>();
  
  // =========== 生命周期方法 ===========
  @override
  void onInit() {
    super.onInit();
    // 初始化时不自动加载，等用户点击开始学习才初始化
  }
  
  @override
  void onClose() {
    // 如果学习未完成，保存中间状态
    if (session.value.id.isNotEmpty && !session.value.isCompleted) {
      _saveSessionState();
    }
    super.onClose();
  }
  
  // =========== 公共方法 ===========
  // 初始化学习会话
  Future<void> initLearningSession() async {
    isLoading.value = true;
    try {
      // 检查是否有未完成的会话
      final savedSession = await _loadSavedSession();
      if (savedSession != null) {
        // 恢复未完成的会话
        session.value = savedSession;
        _loadSessionWords();
        currentStage.value = savedSession.stage;
      } else {
        // 创建新的学习会话
        await _createNewSession();
      }
      
      // 导航到当前阶段
      navigateToCurrentStage();
    } catch (e) {
      Get.snackbar('初始化学习失败', e.toString());
    } finally {
      isLoading.value = false;
    }
  }
  
  // 记录新词学习结果
  void recordNewWordLearned(String wordId, int memoryLevel) {
    session.update((s) {
      s?.recordNewWordLearned(wordId, memoryLevel);
    });
  }
  
  // 记录复习词学习结果
  void recordReviewWordLearned(String wordId, int memoryLevel) {
    session.update((s) {
      s?.recordReviewWordLearned(wordId, memoryLevel);
    });
  }
  
  // 更新测验结果
  void updateQuizResults(List<QuizResult> results) {
    session.update((s) {
      s?.updateQuizResults(results);
    });
  }
  
  // 完成当前阶段并进入下一阶段
  void completeCurrentStage() {
    switch (currentStage.value) {
      case LearningStage.NEW_WORDS:
        if (reviewWords.isNotEmpty) {
          currentStage.value = LearningStage.REVIEW;
        } else {
          currentStage.value = LearningStage.QUIZ;
        }
        break;
      case LearningStage.REVIEW:
        currentStage.value = LearningStage.QUIZ;
        break;
      case LearningStage.QUIZ:
        currentStage.value = LearningStage.RESULT;
        break;
      case LearningStage.RESULT:
        currentStage.value = LearningStage.COMPLETE;
        completeSession();
        break;
      case LearningStage.COMPLETE:
        // 返回主页或重新开始
        Get.offAllNamed('/home');
        break;
    }
    
    // 更新会话状态
    session.update((s) {
      s?.stage = currentStage.value;
    });
    
    // 保存会话状态
    _saveSessionState();
    
    // 导航到下一阶段
    navigateToCurrentStage();
  }
  
  // 导航到当前阶段对应的页面
  void navigateToCurrentStage() {
    switch (currentStage.value) {
      case LearningStage.NEW_WORDS:
      case LearningStage.REVIEW:
        Get.toNamed('/learning/recall');
        break;
      case LearningStage.QUIZ:
        Get.toNamed('/learning/quiz');
        break;
      case LearningStage.RESULT:
        Get.toNamed('/learning/result');
        break;
      case LearningStage.COMPLETE:
        Get.toNamed('/learning/complete');
        break;
    }
  }
  
  // 完成整个学习会话
  Future<void> completeSession() async {
    try {
      // 1. 更新会话结束时间
      session.update((s) {
        s?.endTime = DateTime.now();
        s?.isCompleted = true;
      });
      
      // 2. 计算学习统计数据
      final stats = session.value.calculateStats();
      todayStats.value = stats;
      
      // 3. 保存学习结果到用户记录
      await _userRepository.saveLearningRecord(session.value);
      
      // 4. 更新单词的记忆级别和下次复习时间
      await _wordRepository.updateWordsMemoryLevels(
        session.value.learningResults
      );
      
      // 5. 清除保存的会话状态
      _clearSavedSession();
    } catch (e) {
      Get.snackbar('保存学习结果失败', e.toString());
    }
  }
  
  // 打开单词详情
  void openWordDetail(String wordId) {
    Get.toNamed('/learning/detail', parameters: {'wordId': wordId});
  }
  
  // =========== 私有辅助方法 ===========
  // 创建新的学习会话
  Future<void> _createNewSession() async {
    // 1. 获取用户设置
    final settings = await _settingsService.getLearningSettings();
    
    // 2. 获取今天需要学习的新词
    newWords.value = await _wordRepository.getTodayNewWords(
      limit: settings.dailyNewWords,
    );
    
    // 3. 获取今天需要复习的单词
    reviewWords.value = await _wordRepository.getTodayReviewWords(
      limit: settings.dailyReviewWords,
    );
    
    // 4. 创建新的学习会话
    session.value = LearningSession(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      startTime: DateTime.now(),
      newWords: newWords.map((w) => w.id).toList(),
      reviewWords: reviewWords.map((w) => w.id).toList(),
      stage: LearningStage.NEW_WORDS,
    );
    
    // 5. 设置初始阶段
    currentStage.value = LearningStage.NEW_WORDS;
  }
  
  // 加载会话中的单词
  Future<void> _loadSessionWords() async {
    // 加载新词
    if (session.value.newWords.isNotEmpty) {
      newWords.value = await _wordRepository.getWordsByIds(
        session.value.newWords
      );
    }
    
    // 加载复习词
    if (session.value.reviewWords.isNotEmpty) {
      reviewWords.value = await _wordRepository.getWordsByIds(
        session.value.reviewWords
      );
    }
  }
  
  // 保存会话状态
  Future<void> _saveSessionState() async {
    await _userRepository.saveSessionState(session.value);
  }
  
  // 加载保存的会话
  Future<LearningSession?> _loadSavedSession() async {
    return await _userRepository.getSavedSessionState();
  }
  
  // 清除保存的会话
  Future<void> _clearSavedSession() async {
    await _userRepository.clearSavedSessionState();
  }
}
```

## 学习流程示意图

```
                  初始化
                     ↓
             +----> 新词学习
             |        ↓
    （如有复习词）   复习词学习
             |        ↓
             +----> 测验阶段
                     ↓
                  结果展示
                     ↓
                 学习完成
```

## 与其他目录的关系

- **models目录**：控制器使用学习会话模型（`LearningSession`）
- **routes目录**：控制器使用路由进行导航
- **bindings目录**：负责注册控制器并解决依赖关系
- **子模块（recall/quiz/result等）**：控制器协调这些子模块的操作
- **全局服务和仓库**：控制器依赖于数据仓库和全局服务

## 设计考量

1. **分层责任**：
   - 主控制器（`LearningController`）：管理整体学习流程和状态
   - 子模块控制器（如`RecallController`）：管理具体页面的状态和交互

2. **状态管理方式**：
   - 使用GetX的响应式编程模式（Rx变量）
   - 将复杂状态封装在`LearningSession`模型中
   - 子控制器通过依赖注入访问主控制器

3. **错误处理**：
   - 使用try-catch处理异步操作可能出现的错误
   - 使用snackbar提供用户友好的错误提示

4. **持久化策略**：
   - 自动保存会话状态，支持中断后继续
   - 学习完成后将结果保存到用户记录

## 最佳实践

1. **依赖注入**：
   - 使用GetX的依赖注入系统解耦控制器和其依赖
   - 在binding中注册所有需要的依赖

2. **状态隔离**：
   - 主控制器只管理全局状态
   - 子模块控制器管理页面级别的状态

3. **错误处理**：
   - 所有异步操作都应有适当的错误处理
   - 为用户提供清晰的错误信息

4. **会话恢复**：
   - 自动保存会话状态以支持中断后继续
   - 在初始化时检查是否有未完成的会话

5. **代码组织**：
   - 按功能分组方法
   - 私有辅助方法与公共API分开
   - 使用注释分隔不同功能区域

## 常见问题与解决方案

1. **状态同步问题**：
   - 问题：子模块可能修改主控制器中的状态，导致状态不一致
   - 解决：所有状态修改都通过主控制器提供的方法进行，确保状态更新的一致性

2. **导航冲突**：
   - 问题：复杂导航逻辑可能导致路由堆栈混乱
   - 解决：使用清晰的导航策略，如`toNamed`、`offNamed`和`offAllNamed`的适当组合

3. **性能问题**：
   - 问题：频繁更新响应式变量可能导致性能问题
   - 解决：使用`.update()`方法批量更新状态，减少重绘次数

4. **会话恢复失败**：
   - 问题：会话状态恢复可能因数据不完整而失败
   - 解决：添加数据完整性验证，并在恢复失败时优雅降级到创建新会话
</rewritten_file> 