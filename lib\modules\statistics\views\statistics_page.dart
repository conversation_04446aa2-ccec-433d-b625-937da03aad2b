import 'package:flutter/material.dart';

import 'package:flutter_study_flow_by_myself/modules/statistics/controllers/statistics_controller.dart';
import 'package:get/get.dart';

class StatisticsPage extends GetView<StatisticsController> {
  const StatisticsPage({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('统计'), automaticallyImplyLeading: false),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Obx(
          () => Column(
            children: [
              _buildStatCard(
                '学习天数',
                '${controller.studyDays}天',
                Icons.calendar_today,
              ),
              const SizedBox(height: 16),
              _buildStatCard('学习单词', '${controller.totalWords}个', Icons.book),
              const SizedBox(height: 16),
              _buildStatCard(
                '学习时长',
                controller.totalStudyTime.value,
                Icons.access_time,
              ),
              const SizedBox(height: 32),
              const Text('图表功能开发中...', style: TextStyle(color: Colors.grey)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(icon, size: 32, color: Colors.blue),
            const SizedBox(width: 16),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(fontSize: 14, color: Colors.grey),
                ),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
