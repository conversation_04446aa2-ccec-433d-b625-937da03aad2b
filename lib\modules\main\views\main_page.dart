import 'package:flutter/material.dart';
import 'package:flutter_study_flow_by_myself/modules/home/<USER>/home_page.dart';
import 'package:flutter_study_flow_by_myself/modules/main/controllers/main_controller.dart';
import 'package:flutter_study_flow_by_myself/modules/profile/views/profile_page.dart';
import 'package:flutter_study_flow_by_myself/modules/statistics/views/statistics_page.dart';
import 'package:flutter_study_flow_by_myself/modules/vocabulary/views/vocabulary_page.dart';
import 'package:get/get.dart';

class MainPage extends GetView<MainController> {
  const MainPage({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(
        () => IndexedStack(
          index: controller.currentIndex.value,
          children: const [
            HomePage(),
            VocabularyPage(),
            StatisticsPage(),
            ProfilePage(),
          ],
        ),
      ),
      bottomNavigationBar: Obx(
        () => BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          currentIndex: controller.currentIndex.value,
          onTap: controller.changeTab,
          selectedItemColor: Theme.of(context).primaryColor,
          unselectedItemColor: Colors.grey,
          items: const [
            BottomNavigationBarItem(icon: Icon(Icons.home), label: '首页'),
            BottomNavigationBarItem(
              icon: Icon(Icons.library_books),
              label: '词汇库',
            ),
            BottomNavigationBarItem(icon: Icon(Icons.analytics), label: '统计'),
            BottomNavigationBarItem(icon: Icon(Icons.person), label: '我的'),
          ],
        ),
      ),
    );
  }
}
