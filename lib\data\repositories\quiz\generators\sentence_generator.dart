import 'dart:math';

import 'package:flutter_study_flow_by_myself/data/models/quiz/choice_quiz_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/quiz/quiz_type.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_model.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/quiz/generators/distractor_utils.dart';

/// 例句填空测验题生成器
class SentenceGenerator {
  // 私有构造函数，防止实例化
  SentenceGenerator._();

  /// 静态生成方法
  ///
  /// [word] 目标单词
  /// [allWords] 所有单词列表，用于生成干扰项
  ///
  /// 返回生成的例句填空测验题，如果没有例句则返回null
  static ChoiceQuizModel? generate(WordModel word, List<WordModel> allWords) {
    // 如果没有例句，则跳过生成此题型
    if (word.examples.isEmpty) {
      return null;
    }

    // 随机选择一个例句
    final example = word.examples[Random().nextInt(word.examples.length)];

    // 将例句中的目标单词替换为下划线，创建一个填空题
    final blankSentence = _createBlankSentence(example, word.spelling);

    // 如果无法创建填空题（例如例句中没有目标单词），则返回null
    if (blankSentence == example) {
      return null;
    }

    // 使用工具类生成干扰项
    final distractors = DistractorUtils.generateDistractors(
      word,
      allWords,
      3,
      DistractorType.englishWord,
    );

    // 构建选项，包含正确答案和干扰项
    final options = [word.spelling, ...distractors];
    options.shuffle(); // 随机排序

    // 创建测验模型
    return ChoiceQuizModel(
      wordId: word.id,
      question: blankSentence,
      options: options,
      correctOptionIndex: options.indexOf(word.spelling),
      type: QuizType.sentenceCompletion,
    );
  }

  /// 创建填空例句，将目标单词替换为下划线
  static String _createBlankSentence(String sentence, String targetWord) {
    // 不区分大小写的替换，保留原始格式
    final RegExp regExp = RegExp(targetWord, caseSensitive: false);

    // 如果没找到目标单词，直接返回原句
    if (!regExp.hasMatch(sentence)) {
      return sentence;
    }

    // 替换目标单词为下划线
    return sentence.replaceFirst(regExp, '________');
  }
}
