/// 应用程序配置类
/// 用于从 Dart Defines 或其他配置源读取和管理应用程序的环境变量和配置。
library;

import 'package:flutter_dotenv/flutter_dotenv.dart'; // 导入 flutter_dotenv
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:get/get.dart'; // 导入 LogService

class AppConfig {
  /// API 的基础 URL。
  /// 从 .env 文件中读取，例如:
  /// BASE_URL=https://dev.api.example.com/api/v1/
  /// 如果未定义，则提供一个默认值。
  static String get baseUrl =>
      dotenv.env['BASE_URL'] ??
      'https://dev.api.example.com/api/v1/'; // 使用 dotenv.env

  /// 当前应用运行的环境
  /// 从 .env 文件中读取，例如:
  /// APP_ENV=development
  /// 如果未定义，则默认为 'development'
  static String get appEnvironment =>
      dotenv.env['APP_ENV'] ?? 'development'; // 使用 dotenv.env

  // 可以在这里添加其他配置，例如：
  // static String? get apiKey => dotenv.env['API_KEY']; // 示例：可选的 API Key
  // static bool get enableAnalytics => dotenv.env['ENABLE_ANALYTICS'] == 'true'; // 示例：布尔值配置

  /// 判断当前是否为生产环境
  static bool get isProduction => appEnvironment == 'production';

  /// 判断当前是否为开发环境
  static bool get isDevelopment => appEnvironment == 'development';

  /// 判断当前是否为测试环境
  static bool get isStaging => appEnvironment == 'staging';

  /// 打印当前配置信息 (用于调试)
  static void printConfig() {
    final logService = Get.find<LogService>();
    logService.d('--- App Configuration ---');
    logService.d('Base URL: $baseUrl');
    logService.d('App Environment: $appEnvironment');
    logService.d('Is Production: $isProduction');
    logService.d('Is Development: $isDevelopment');
    logService.d('Is Staging: $isStaging');
    logService.d('-------------------------');
  }
}
