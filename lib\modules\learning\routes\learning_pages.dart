import 'package:flutter_study_flow_by_myself/modules/learning/recall/views/recall_page.dart';
import 'package:get/get.dart';
import '../bindings/learning_binding.dart';
import '../review/bindings/review_binding.dart';
// 页面文件尚未创建，先注释掉
// import '../recall/views/recall_page.dart';
import '../detail/views/detail_page.dart';
import '../quiz/views/quiz_page.dart';
import '../result/views/result_page.dart';
import '../completion/views/word_book_completion_page.dart';
// 复习模块页面
import '../review/quiz/views/review_quiz_page.dart';
import '../review/detail/views/review_detail_page.dart';
import '../review/result/views/review_result_page.dart';
import 'learning_routes.dart';

/// 学习模块路由配置
final List<GetPage> learningPages = [
  GetPage(
    name: LearningRoutes.RECALL,
    // 页面文件尚未创建，使用占位符
    page: () => const RecallPage(),
    binding: LearningBinding(),
    transition: Transition.rightToLeft,
  ),
  GetPage(
    name: LearningRoutes.DETAIL,
    page: () => const DetailPage(),
    binding: LearningBinding(),
    transition: Transition.rightToLeft,
  ),
  GetPage(
    name: LearningRoutes.QUIZ,
    page: () => const QuizPage(),
    binding: LearningBinding(),
    transition: Transition.rightToLeft,
  ),
  GetPage(
    name: LearningRoutes.RESULT,
    page: () => const ResultPage(),
    binding: LearningBinding(),
    transition: Transition.rightToLeft,
  ),
  GetPage(
    name: LearningRoutes.WORD_BOOK_COMPLETION,
    page: () => const WordBookCompletionPage(),
    binding: LearningBinding(),
    transition: Transition.fadeIn,
  ),

  // ==================== 复习模块路由 ====================
  // 只在测验页（入口页面）绑定依赖，其他页面复用已有依赖
  GetPage(
    name: LearningRoutes.REVIEW_QUIZ,
    page: () => const ReviewQuizPage(),
    binding: ReviewBinding(), // 只有这里绑定依赖
    transition: Transition.rightToLeft,
  ),
  GetPage(
    name: LearningRoutes.REVIEW_DETAIL,
    page: () => const ReviewDetailPage(),
    // 不绑定依赖，复用测验页的依赖
    transition: Transition.rightToLeft,
  ),
  GetPage(
    name: LearningRoutes.REVIEW_RESULT,
    page: () => const ReviewResultPage(),
    // 不绑定依赖，复用测验页的依赖
    transition: Transition.fadeIn,
  ),
];
