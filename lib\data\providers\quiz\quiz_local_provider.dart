///
/// 职责：与本地数据库（通过 IsarService）交互，处理与 ChoiceQuizModel 和 SpellingQuizModel 相关的本地数据操作。
/// 这里的操作是针对具体业务实体的，而不是 IsarService 中的通用泛型操作。
/// 它应该包含所有与测验题相关的本地数据业务逻辑，例如：
/// - 获取所有选择题/拼写题
/// - 根据ID获取测验题
/// - 保存/批量保存测验题
/// - 删除/批量删除测验题
/// - 清空所有测验题
/// - 查询测验题数量
library;

import 'package:flutter_study_flow_by_myself/app/services/isar_service.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/app/exceptions/app_exception.dart';
import 'package:flutter_study_flow_by_myself/data/models/quiz/choice_quiz_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/quiz/spelling_quiz_model.dart';

class QuizLocalProvider {
  final IsarService _isarService;
  final LogService _log;
  QuizLocalProvider({required IsarService isarService, required LogService log})
    : _isarService = isarService,
      _log = log;

  /// 获取所有选择题
  Future<List<ChoiceQuizModel>> getAllQuizzes() async {
    try {
      final quizzes = await _isarService.getAll<ChoiceQuizModel>();
      return quizzes;
    } catch (e) {
      _log.e('获取所有选择题失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：获取所有选择题失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 根据ID获取单个选择题
  Future<ChoiceQuizModel?> getQuizById(int quizId) async {
    try {
      return await _isarService.get<ChoiceQuizModel>(quizId);
    } catch (e) {
      _log.e('根据ID获取选择题失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：根据ID获取选择题失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 保存单个选择题
  Future<int> saveQuiz(ChoiceQuizModel quiz) async {
    try {
      return await _isarService.put<ChoiceQuizModel>(quiz);
    } catch (e) {
      _log.e('保存选择题失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：保存选择题失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 批量保存选择题
  Future<List<int>> saveAllQuizzes(List<ChoiceQuizModel> quizzes) async {
    try {
      return await _isarService.putAll<ChoiceQuizModel>(quizzes);
    } catch (e) {
      _log.e('批量保存选择题失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：批量保存选择题失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 根据ID删除单个选择题
  Future<bool> deleteQuizById(int quizId) async {
    try {
      return await _isarService.delete<ChoiceQuizModel>(quizId);
    } catch (e) {
      _log.e('删除选择题失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：删除选择题失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 批量删除选择题
  Future<int> deleteQuizzesByIds(List<int> quizIds) async {
    try {
      return await _isarService.deleteByIds<ChoiceQuizModel>(quizIds);
    } catch (e) {
      _log.e('批量删除选择题失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：批量删除选择题失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 清空所有选择题
  Future<void> clearAllQuizzes() async {
    try {
      await _isarService.clearCollection<ChoiceQuizModel>();
    } catch (e) {
      _log.e('清空所有选择题失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：清空所有选择题失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 查询选择题数量
  Future<int> countQuizzes() async {
    try {
      return await _isarService.count<ChoiceQuizModel>();
    } catch (e) {
      _log.e('查询选择题数量失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：查询选择题数量失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 根据单词ID列表批量获取关联的选择题
  /// [wordIds] - 单词ID列表
  /// 返回找到的选择题列表
  Future<List<ChoiceQuizModel>> getQuizzesByWordIds(List<int> wordIds) async {
    if (wordIds.isEmpty) {
      return [];
    }
    try {
      // 获取所有测验
      final allQuizzes = await _isarService.getAll<ChoiceQuizModel>();
      // 在 Dart 中进行过滤
      final filteredQuizzes =
          allQuizzes.where((quiz) => wordIds.contains(quiz.wordId)).toList();
      return filteredQuizzes;
    } catch (e) {
      _log.e('根据单词ID列表获取选择题失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：根据单词ID列表获取选择题失败',
        type: AppExceptionType.database,
      );
    }
  }

  // ----- 以下是拼写题相关的方法 ----- //

  /// 获取所有拼写题
  Future<List<SpellingQuizModel>> getAllSpellingQuizzes() async {
    try {
      final quizzes = await _isarService.getAll<SpellingQuizModel>();
      return quizzes;
    } catch (e) {
      _log.e('获取所有拼写题失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：获取所有拼写题失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 根据ID获取单个拼写题
  Future<SpellingQuizModel?> getSpellingQuizById(int quizId) async {
    try {
      return await _isarService.get<SpellingQuizModel>(quizId);
    } catch (e) {
      _log.e('根据ID获取拼写题失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：根据ID获取拼写题失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 保存单个拼写题
  Future<int> saveSpellingQuiz(SpellingQuizModel quiz) async {
    try {
      return await _isarService.put<SpellingQuizModel>(quiz);
    } catch (e) {
      _log.e('保存拼写题失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：保存拼写题失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 批量保存拼写题
  Future<List<int>> saveAllSpellingQuizzes(
    List<SpellingQuizModel> quizzes,
  ) async {
    try {
      return await _isarService.putAll<SpellingQuizModel>(quizzes);
    } catch (e) {
      _log.e('批量保存拼写题失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：批量保存拼写题失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 根据ID删除单个拼写题
  Future<bool> deleteSpellingQuizById(int quizId) async {
    try {
      return await _isarService.delete<SpellingQuizModel>(quizId);
    } catch (e) {
      _log.e('删除拼写题失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：删除拼写题失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 清空所有拼写题
  Future<void> clearAllSpellingQuizzes() async {
    try {
      await _isarService.clearCollection<SpellingQuizModel>();
    } catch (e) {
      _log.e('清空所有拼写题失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：清空所有拼写题失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 根据单词ID列表批量获取关联的拼写题
  /// [wordIds] - 单词ID列表
  /// 返回找到的拼写题列表
  Future<List<SpellingQuizModel>> getSpellingQuizzesByWordIds(
    List<int> wordIds,
  ) async {
    if (wordIds.isEmpty) {
      return [];
    }
    try {
      // 获取所有拼写题
      final allQuizzes = await _isarService.getAll<SpellingQuizModel>();
      // 在 Dart 中进行过滤
      final filteredQuizzes =
          allQuizzes.where((quiz) => wordIds.contains(quiz.wordId)).toList();
      return filteredQuizzes;
    } catch (e) {
      _log.e('根据单词ID列表获取拼写题失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '本地数据：根据单词ID列表获取拼写题失败',
        type: AppExceptionType.database,
      );
    }
  }
}
