// 复习测验答题状态模型
//
// 使用状态模式来管理复习测验的答题状态，确保状态的一致性和类型安全
// 复用新学流程的QuizAnswerState设计模式

/// 答题状态的基类
abstract class ReviewQuizAnswerState {
  const ReviewQuizAnswerState();

  /// 是否已答题
  bool get hasAnswered => this is ReviewQuizAnsweredState;

  /// 获取选择题的选项索引
  int? get selectedOptionIndex {
    final answeredState = this;
    if (answeredState is ReviewQuizAnsweredState) {
      return answeredState.selectedOptionIndex;
    }
    return null;
  }

  /// 获取拼写题的用户输入
  String? get userInput {
    final answeredState = this;
    if (answeredState is ReviewQuizAnsweredState) {
      return answeredState.userInput;
    }
    return null;
  }

  /// 获取答题是否正确
  bool? get isCorrect {
    final answeredState = this;
    if (answeredState is ReviewQuizAnsweredState) {
      return answeredState.isCorrect;
    }
    return null;
  }
}

/// 未答题状态
class ReviewQuizUnansweredState extends ReviewQuizAnswerState {
  const ReviewQuizUnansweredState();

  @override
  String toString() => 'ReviewQuizUnansweredState()';

  @override
  bool operator ==(Object other) {
    return other is ReviewQuizUnansweredState;
  }

  @override
  int get hashCode => runtimeType.hashCode;
}

/// 已答题状态
class ReviewQuizAnsweredState extends ReviewQuizAnswerState {
  /// 选择题的选项索引
  @override
  final int? selectedOptionIndex;

  /// 拼写题的用户输入
  @override
  final String? userInput;

  /// 是否正确
  @override
  final bool isCorrect;

  /// 答题时间
  final DateTime answeredAt;

  const ReviewQuizAnsweredState({
    this.selectedOptionIndex,
    this.userInput,
    required this.isCorrect,
    required this.answeredAt,
  });

  /// 工厂方法：创建选择题答题状态
  factory ReviewQuizAnsweredState.choice({
    required int selectedIndex,
    required bool isCorrect,
  }) {
    return ReviewQuizAnsweredState(
      selectedOptionIndex: selectedIndex,
      isCorrect: isCorrect,
      answeredAt: DateTime.now(),
    );
  }

  /// 工厂方法：创建拼写题答题状态
  factory ReviewQuizAnsweredState.spelling({
    required String userInput,
    required bool isCorrect,
  }) {
    return ReviewQuizAnsweredState(
      userInput: userInput,
      isCorrect: isCorrect,
      answeredAt: DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'ReviewQuizAnsweredState(selectedOptionIndex: $selectedOptionIndex, '
        'userInput: $userInput, isCorrect: $isCorrect, answeredAt: $answeredAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ReviewQuizAnsweredState &&
        other.selectedOptionIndex == selectedOptionIndex &&
        other.userInput == userInput &&
        other.isCorrect == isCorrect &&
        other.answeredAt == answeredAt;
  }

  @override
  int get hashCode {
    return Object.hash(selectedOptionIndex, userInput, isCorrect, answeredAt);
  }
}
