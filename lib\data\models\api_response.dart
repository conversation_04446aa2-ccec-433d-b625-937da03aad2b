/// 统一的API响应数据模型，用于封装后端返回的数据结构
/// 这样可以让业务层只关心 data 字段，简化数据处理和错误判断
class ApiResponse<T> {
  /// 状态码，通常 0 表示成功，其他为错误
  final int code;

  /// 提示信息，后端返回的 message 字段
  final String message;

  /// 业务数据，类型为泛型 T，适配不同接口的数据结构
  final T? data;

  ApiResponse({required this.code, required this.message, this.data});

  /// 工厂方法：从 JSON 解析为 ApiResponse 对象
  /// [fromJsonT] 是一个函数，用于将 data 字段解析为具体的业务对象
  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic) fromJsonT,
  ) {
    return ApiResponse(
      code: (json['code'] as int?) ?? -1,
      message: (json['message'] as String?) ?? '未知错误',
      data: json['data'] != null ? fromJsonT(json['data']) : null,
    );
  }
}
