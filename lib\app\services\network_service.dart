import 'package:dio/dio.dart';
import 'package:get/get.dart';

import 'package:flutter_study_flow_by_myself/app/exceptions/app_exception.dart'; // 引入自定义异常模型
// import 'dart:developer' as developer; // 移除 dart:developer
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/data/models/api_response.dart'; // 引入 LogService

/// 网络请求服务类
///
/// 继承GetxService的原因：
/// 1. 管理Dio实例的生命周期和资源
/// 2. 作为全局基础服务，在整个应用中持续存在
/// 3. 便于通过Get.find()在应用任何位置访问
/// 4. 可以利用GetxService的生命周期管理拦截器和请求配置
class NetworkService extends GetxService {
  // Dio 实例，用于发起网络请求
  late final Dio _dio;
  // LogService 实例
  final LogService _logService;

  /// 构造函数
  /// [baseUrl] API 的根路径
  NetworkService({required String baseUrl, required LogService logService})
    : _logService = logService {
    // Dio 基础配置
    BaseOptions options = BaseOptions(
      baseUrl: baseUrl, // 使用传入的 baseUrl
      connectTimeout: const Duration(seconds: 10), // 连接超时时间
      receiveTimeout: const Duration(seconds: 10), // 接收超时时间
      headers: {'Content-Type': 'application/json'}, // 默认请求头
    );
    _dio = Dio(options);
    // 添加拦截器
    _addInterceptors();
  }

  /// 添加 Dio 拦截器
  /// 拦截器可以在请求发送前、响应返回后、发生错误时进行统一处理
  void _addInterceptors() {
    _dio.interceptors.add(
      InterceptorsWrapper(
        /// 请求拦截器：在请求发送前调用
        /// 可以在这里统一添加鉴权 token、请求头、打印请求日志等
        onRequest: (options, handler) {
          _logService.d('请求URL: ${options.uri}');
          _logService.d('请求方式: ${options.method}');
          _logService.d('请求头: ${options.headers}');
          _logService.d('请求参数: ${options.data}');
          // 例如：统一添加 Authorization Header
          // options.headers['Authorization'] = 'Bearer your_token_here';
          return handler.next(options); // 继续请求
        },

        /// 响应拦截器：在响应返回后调用
        /// 可以在这里统一处理响应数据、打印响应日志、处理业务错误码等
        onResponse: (response, handler) {
          _logService.i('响应URL: ${response.requestOptions.uri}');
          _logService.i('响应状态码: ${response.statusCode}');
          _logService.i('响应数据: ${response.data}');
          // 例如：统一处理后端返回的 code 字段，进行通用错误判断
          // if (response.data['code'] != 0) {
          //   throw AppException(response.data['code'], response.data['message']);
          // }
          return handler.next(response); // 继续处理响应
        },

        /// 错误拦截器：在请求失败或发生 DioException 时调用
        /// 可以在这里统一处理网络异常、鉴权失败（如 token 过期自动跳转登录）、错误弹窗提示等
        onError: (DioException e, handler) {
          _logService.e('请求错误: ${e.requestOptions.uri}');
          _logService.e('错误类型: ${e.type}');
          _logService.e('错误信息: ${e.message}');
          // 这里可以统一处理错误，例如：
          // if (e.response?.statusCode == 401) {
          //   // token 过期，跳转到登录页
          // }
          return handler.next(e); // 继续传递错误
        },
      ),
    );
  }

  /// 发起 GET 请求
  /// [path] 请求路径
  /// [queryParameters] 查询参数
  /// [fromJsonT] 将响应数据中的 'data' 字段转换为指定泛型 T 的函数
  Future<ApiResponse<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    required T Function(dynamic) fromJsonT, // 必须传入解析函数
  }) async {
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      // 使用统一响应数据模型解析响应
      //       response（比如 Dio 的 Response 对象）包含了很多网络相关的元数据（如 headers、statusCode、request 信息等），而业务层只关心数据本身。
      // 只传 response.data，可以让数据解析逻辑更专注于业务数据，避免耦合网络细节。
      // 这也是很多主流网络库的最佳实践。
      return ApiResponse<T>.fromJson(response.data, fromJsonT);
    } on DioException catch (e) {
      // 捕获 DioException 并转换为自定义的 AppException 后向上抛出
      _logService.e('GET 请求失败', error: e);
      throw AppException.fromDioError(e);
    }
  }

  /// 发起 POST 请求
  /// [path] 请求路径
  /// [data] 请求体数据
  /// [fromJsonT] 将响应数据中的 'data' 字段转换为指定泛型 T 的函数
  Future<ApiResponse<T>> post<T>(
    String path, {
    dynamic data,
    required T Function(dynamic) fromJsonT, // 必须传入解析函数
  }) async {
    try {
      final response = await _dio.post(path, data: data);
      // 使用统一响应数据模型解析响应
      return ApiResponse<T>.fromJson(response.data, fromJsonT);
    } on DioException catch (e) {
      // 捕获 DioException 并转换为自定义的 AppException 后向上抛出
      _logService.e('POST 请求失败', error: e);
      throw AppException.fromDioError(e);
    }
  }

  /// 发起 PUT 请求
  /// [path] 请求路径
  /// [data] 请求体数据
  /// [fromJsonT] 将响应数据中的 'data' 字段转换为指定泛型 T 的函数
  Future<ApiResponse<T>> put<T>(
    String path, {
    dynamic data,
    required T Function(dynamic) fromJsonT,
  }) async {
    try {
      final response = await _dio.put(path, data: data);
      return ApiResponse<T>.fromJson(response.data, fromJsonT);
    } on DioException catch (e) {
      throw AppException.fromDioError(e);
    }
  }

  /// 发起 DELETE 请求
  /// [path] 请求路径
  /// [queryParameters] 查询参数
  /// [fromJsonT] 将响应数据中的 'data' 字段转换为指定泛型 T 的函数
  Future<ApiResponse<T>> delete<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    required T Function(dynamic) fromJsonT,
  }) async {
    try {
      final response = await _dio.delete(
        path,
        queryParameters: queryParameters,
      );
      return ApiResponse<T>.fromJson(response.data, fromJsonT);
    } on DioException catch (e) {
      throw AppException.fromDioError(e);
    }
  }

  /// 发起 PATCH 请求
  /// [path] 请求路径
  /// [data] 请求体数据
  /// [fromJsonT] 将响应数据中的 'data' 字段转换为指定泛型 T 的函数
  Future<ApiResponse<T>> patch<T>(
    String path, {
    dynamic data,
    required T Function(dynamic) fromJsonT,
  }) async {
    try {
      final response = await _dio.patch(path, data: data);
      return ApiResponse<T>.fromJson(response.data, fromJsonT);
    } on DioException catch (e) {
      throw AppException.fromDioError(e);
    }
  }

  // TODO: 如果有文件上传下载需求，可以在这里添加对应的方法
  // 例如：
  // Future<ApiResponse<T>> uploadFile<T>(
  //   String path, {
  //   required FormData formData,
  //   ProgressCallback? onSendProgress,
  //   required T Function(dynamic) fromJsonT,
  // }) async {
  //   try {
  //     final response = await _dio.post(
  //       path,
  //       data: formData,
  //       onSendProgress: onSendProgress,
  //     );
  //     return ApiResponse<T>.fromJson(response.data, fromJsonT);
  //   } on DioException catch (e) {
  //     throw AppException.fromDioError(e);
  //   }
  // }

  // Future<void> downloadFile(
  //   String url,
  //   String savePath, {
  //   ProgressCallback? onReceiveProgress,
  // }) async {
  //   try {
  //     await _dio.download(
  //       url,
  //       savePath,
  //       onReceiveProgress: onReceiveProgress,
  //     );
  //   } on DioException catch (e) {
  //     throw AppException.fromDioError(e);
  //   }
  // }
}
