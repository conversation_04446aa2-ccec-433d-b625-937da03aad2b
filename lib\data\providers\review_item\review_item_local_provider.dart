import 'package:isar/isar.dart';
import 'package:flutter_study_flow_by_myself/app/services/isar_service.dart';
import 'package:flutter_study_flow_by_myself/data/models/review_item_model.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/app/exceptions/app_exception.dart';

/// 复习项本地数据提供者
///
/// 负责ReviewItemModel的本地数据库操作，封装与Isar数据库的交互
/// 提供与单词本关联的复习项查询方法，支持基于单词本的学习和复习功能
class ReviewItemLocalProvider {
  // Isar数据库服务依赖
  final IsarService _isarService;
  // 日志服务依赖
  final LogService _logService;

  /// 构造函数，注入依赖
  ReviewItemLocalProvider({
    required IsarService isarService,
    required LogService logService,
  }) : _isarService = isarService,
       _logService = logService;

  /// 保存或更新复习项
  ///
  /// 如果[item]已存在（有ID）则更新，否则新增
  /// 返回保存后的ID
  Future<int> saveItem(ReviewItemModel item) async {
    try {
      return await _isarService.isar.writeTxn(() async {
        return await _isarService.isar.reviewItemModels.put(item);
      });
    } catch (e) {
      _logService.e('保存复习项失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '保存复习项失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 批量保存复习项
  ///
  /// [items] 要保存的复习项列表
  /// 返回保存后的ID列表
  Future<List<int>> saveItems(List<ReviewItemModel> items) async {
    try {
      return await _isarService.isar.writeTxn(() async {
        return await _isarService.isar.reviewItemModels.putAll(items);
      });
    } catch (e) {
      _logService.e('批量保存复习项失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '批量保存复习项失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 根据ID获取复习项
  ///
  /// [id] 复习项ID
  /// 返回对应的复习项，不存在则返回null
  Future<ReviewItemModel?> getItem(int id) async {
    try {
      return await _isarService.isar.reviewItemModels.get(id);
    } catch (e) {
      _logService.e('获取复习项失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '获取复习项失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 获取某个单词和用户单词本组合的复习项
  ///
  /// [wordId] 单词ID
  /// [userWordBookId] 用户单词本ID
  /// 返回对应的复习项，不存在则返回null
  Future<ReviewItemModel?> getReviewItemByWordIdAndUserWordBook(
    int wordId,
    int userWordBookId,
  ) async {
    try {
      return await _isarService.isar.reviewItemModels
          .filter()
          .wordIdEqualTo(wordId)
          .userWordBookIdEqualTo(userWordBookId)
          .findFirst();
    } catch (e) {
      _logService.e('获取单词在指定单词本中的复习项失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '获取单词在指定单词本中的复习项失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 获取特定用户单词本中到期需要复习的项目
  ///
  /// [userWordBookId] 用户单词本ID
  /// [now] 当前时间点，用于比较是否到期
  /// 返回该用户单词本中下次复习时间早于或等于当前时间的复习项
  Future<List<ReviewItemModel>> getDueReviewItemsForUserWordBook(
    int userWordBookId,
    DateTime now,
  ) async {
    try {
      return await _isarService.isar.reviewItemModels
          .filter()
          .userWordBookIdEqualTo(userWordBookId)
          .nextReviewDateLessThan(now)
          .isNewWordEqualTo(false) // 只获取已学习过的单词
          .findAll();
    } catch (e) {
      _logService.e('获取用户单词本到期复习项失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '获取用户单词本到期复习项失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 获取特定用户单词本中已学习的单词数量
  ///
  /// [userWordBookId] 用户单词本ID
  /// 返回该用户单词本中已学习（非新词）的单词数量
  Future<int> getLearnedWordsCountByUserWordBook(int userWordBookId) async {
    try {
      return await _isarService.isar.reviewItemModels
          .filter()
          .userWordBookIdEqualTo(userWordBookId)
          .isNewWordEqualTo(false)
          .count();
    } catch (e) {
      _logService.e('获取用户单词本已学习单词数量失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '获取用户单词本已学习单词数量失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 获取特定用户单词本中待学习的单词ID列表
  ///
  /// [userWordBookId] 用户单词本ID
  /// 返回该用户单词本中已创建复习项但尚未学习的单词ID列表
  Future<List<int>> getNewWordIdsByUserWordBook(int userWordBookId) async {
    try {
      final items =
          await _isarService.isar.reviewItemModels
              .filter()
              .userWordBookIdEqualTo(userWordBookId)
              .isNewWordEqualTo(true)
              .findAll();

      return items.map((item) => item.wordId).toList();
    } catch (e) {
      _logService.e('获取用户单词本待学习单词ID列表失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '获取用户单词本待学习单词ID列表失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 删除复习项
  ///
  /// [id] 要删除的复习项ID
  /// 返回是否删除成功
  Future<bool> deleteItem(int id) async {
    try {
      return await _isarService.isar.writeTxn(() async {
        return await _isarService.isar.reviewItemModels.delete(id);
      });
    } catch (e) {
      _logService.e('删除复习项失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '删除复习项失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 删除指定用户单词本中与单词关联的所有复习项
  ///
  /// [wordId] 单词ID
  /// [userWordBookId] 用户单词本ID
  /// 返回删除的数量
  Future<int> deleteItemsByWordIdAndUserWordBook(
    int wordId,
    int userWordBookId,
  ) async {
    try {
      return await _isarService.isar.writeTxn(() async {
        final itemsToDelete =
            await _isarService.isar.reviewItemModels
                .filter()
                .wordIdEqualTo(wordId)
                .userWordBookIdEqualTo(userWordBookId)
                .findAll();
        final ids = itemsToDelete.map((item) => item.id).toList();
        return await _isarService.isar.reviewItemModels.deleteAll(ids);
      });
    } catch (e) {
      _logService.e('删除单词本中特定单词的复习项失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '删除单词本中特定单词的复习项失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 获取指定用户单词本的所有复习项
  ///
  /// [userWordBookId] 用户单词本ID
  /// 返回该用户单词本的所有复习项
  Future<List<ReviewItemModel>> getAllItemsByUserWordBook(
    int userWordBookId,
  ) async {
    try {
      return await _isarService.isar.reviewItemModels
          .filter()
          .userWordBookIdEqualTo(userWordBookId)
          .findAll();
    } catch (e) {
      _logService.e('获取用户单词本所有复习项失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '获取用户单词本所有复习项失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 统计指定用户单词本的复习项总数
  ///
  /// [userWordBookId] 用户单词本ID
  /// 返回该用户单词本的复习项总数
  Future<int> countItemsByUserWordBook(int userWordBookId) async {
    try {
      return await _isarService.isar.reviewItemModels
          .filter()
          .userWordBookIdEqualTo(userWordBookId)
          .count();
    } catch (e) {
      _logService.e('统计用户单词本复习项总数失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '统计用户单词本复习项总数失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 获取某个时间段内指定用户单词本到期的复习项数量
  ///
  /// [userWordBookId] 用户单词本ID
  /// [startDate] 开始时间
  /// [endDate] 结束时间
  /// 返回该时间段内到期的复习项数量
  Future<int> countDueItemsInRangeByUserWordBook(
    int userWordBookId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      return await _isarService.isar.reviewItemModels
          .filter()
          .userWordBookIdEqualTo(userWordBookId)
          .nextReviewDateBetween(startDate, endDate)
          .isNewWordEqualTo(false)
          .count();
    } catch (e) {
      _logService.e('统计用户单词本时间段内复习项数量失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '统计用户单词本时间段内复习项数量失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 获取指定用户单词本中当前时间点之前到期的复习项数量
  ///
  /// [userWordBookId] 用户单词本ID
  /// [currentTime] 当前时间点
  /// 返回下次复习时间早于当前时间的复习项数量
  Future<int> countDueItemsByCurrentTimeByUserWordBook(
    int userWordBookId,
    DateTime currentTime,
  ) async {
    try {
      return await _isarService.isar.reviewItemModels
          .filter()
          .userWordBookIdEqualTo(userWordBookId)
          .nextReviewDateLessThan(currentTime)
          .isNewWordEqualTo(false)
          .count();
    } catch (e) {
      _logService.e('统计用户单词本当前时间点到期复习项数量失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '统计用户单词本当前时间点到期复习项数量失败',
        type: AppExceptionType.database,
      );
    }
  }

  /// 清空指定用户单词本的所有复习项
  ///
  /// [userWordBookId] 用户单词本ID
  /// 返回删除的数量
  Future<int> clearAllItemsByUserWordBook(int userWordBookId) async {
    try {
      return await _isarService.isar.writeTxn(() async {
        final itemsToDelete =
            await _isarService.isar.reviewItemModels
                .filter()
                .userWordBookIdEqualTo(userWordBookId)
                .findAll();
        final ids = itemsToDelete.map((item) => item.id).toList();
        return await _isarService.isar.reviewItemModels.deleteAll(ids);
      });
    } catch (e) {
      _logService.e('清空用户单词本复习项失败', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '清空用户单词本复习项失败',
        type: AppExceptionType.database,
      );
    }
  }
}
