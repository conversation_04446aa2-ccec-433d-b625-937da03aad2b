import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/app/services/review_plan_service.dart';

import 'package:flutter_study_flow_by_myself/modules/learning/routes/learning_routes.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/services/review_state_service.dart';

/// 复习导航服务
///
/// 负责处理复习流程中的页面导航逻辑，专门用于复习模块
///
/// 与LearningNavigationService的区别：
/// - LearningNavigationService：使用路由参数传递数据，用于学习流程
/// - ReviewNavigationService：使用状态服务传递数据，用于复习流程
///
/// 职责：
/// - 统一管理复习流程中的页面跳转
/// - 通过ReviewStateService传递数据，避免路由参数
/// - 管理复习完成后的导航和时间记录
class ReviewNavigationService extends GetxService {
  // ==================== 依赖注入 ====================
  final ReviewStateService _stateService;
  final LogService _log;

  ReviewNavigationService({
    required ReviewStateService stateService,
    required LogService log,
  }) : _stateService = stateService,
       _log = log;

  // ==================== 导航方法 ====================

  /// 导航到复习详情页面
  ///
  /// 当用户答错题目时调用，跳转到单词详情页面让用户学习
  /// [wordId] 需要查看详情的单词ID
  /// 通过状态服务传递数据，而不是路由参数
  Future<void> navigateToDetail(int wordId) async {
    try {
      _log.i('导航到复习详情页面，单词ID: $wordId');

      // 🔑 关键改变：通过状态管理传递数据，而不是路由参数
      _stateService.setCurrentDetailWordId(wordId);

      await Get.toNamed(LearningRoutes.REVIEW_DETAIL);

      _log.i('成功导航到复习详情页面');
    } catch (e) {
      _log.e('导航到复习详情页面失败: $e');
      // 如果导航失败，尝试返回首页
      navigateToHome();
    }
  }

  /// 导航到复习测验页面
  ///
  /// 从详情页到测验页面时调用，模仿新学流程使用toNamed创建新页面
  /// 这样可以保持测验页面在栈中，避免依赖被销毁
  Future<void> navigateToQuiz() async {
    try {
      _log.i('从详情页导航到复习测验页面');

      // 使用toNamed创建新的测验页面，保持页面栈结构
      await Get.toNamed(LearningRoutes.REVIEW_QUIZ);

      _log.i('成功导航到复习测验页面');
    } catch (e) {
      _log.e('导航到复习测验页面失败: $e');
      // 如果导航失败，尝试返回首页
      navigateToHome();
    }
  }

  /// 导航到复习结果页面
  ///
  /// 当所有复习任务完成时调用，展示复习统计结果
  /// 使用offAllNamed清理整个复习页面栈，避免用户返回
  ///
  /// 🔑 关键：这是复习真正完成的地方，在这里通知Service刷新数据
  Future<void> navigateToResult() async {
    try {
      _log.i('导航到复习结果页面');

      // 在导航到结果页之前，记录复习结束时间
      _stateService.recordReviewEndTime();

      // 🔑 复习完成，通知Service刷新数据
      await _notifyReviewCompleted();

      // 使用offAllNamed清理所有复习页面，防止用户返回
      await Get.offAllNamed(LearningRoutes.REVIEW_RESULT);

      _log.i('成功导航到复习结果页面');
    } catch (e) {
      _log.e('导航到复习结果页面失败: $e');
      // 如果导航失败，尝试返回首页
      navigateToHome();
    }
  }

  /// 通知复习完成，刷新相关数据
  ///
  /// 复习流程真正完成时调用，通知全局Service刷新数据
  /// 这样确保用户返回首页时看到最新的复习数据
  Future<void> _notifyReviewCompleted() async {
    try {
      _log.i('复习完成，开始通知相关Service刷新数据');

      // 🔑 优化：只刷新ReviewPlanService，避免重复刷新
      // HomeController.refreshData()会重复调用reviewPlanService.refreshReviewPlan()
      if (Get.isRegistered<ReviewPlanService>()) {
        final reviewPlanService = Get.find<ReviewPlanService>();
        await reviewPlanService.refreshReviewPlan();
        _log.i('复习完成，已通知ReviewPlanService刷新复习计划');
      } else {
        _log.w('ReviewPlanService未注册，跳过复习计划刷新');
      }

      // Service状态变化会自动通知HomeController更新UI
      // 不需要手动调用homeController.refreshData()，避免重复刷新
      _log.i('Service状态变化将自动通知HomeController更新UI');
    } catch (e) {
      _log.e('通知复习完成失败: $e');
      // 不抛出异常，避免影响导航流程
    }
  }

  /// 返回首页
  ///
  /// 复习流程结束后返回主页面
  void navigateToHome() {
    try {
      _log.i('返回首页');

      // 清除所有页面栈，返回到主页面
      Get.offAllNamed('/main');

      _log.i('成功返回首页');
    } catch (e) {
      _log.e('返回首页失败: $e');
    }
  }
}
