import 'package:flutter_study_flow_by_myself/app/services/audio_service.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/review_item_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/quiz/quiz_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/user_word_book_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/word_repository.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/controllers/review_controller.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/detail/controllers/review_detail_controller.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/quiz/controllers/review_quiz_controller.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/result/controllers/review_result_controller.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/services/review_state_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/services/review_navigation_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/services/review_quiz_generation_service.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/services/review_quality_service.dart';
import 'package:get/get.dart';

/// 复习功能的统一依赖注入绑定
///
/// 负责注册所有与复习功能相关的服务和控制器，参考LearningBinding的设计
/// 先注册服务层，再注册控制器层，确保依赖关系正确
class ReviewBinding extends Bindings {
  @override
  void dependencies() {
    // 获取全局服务
    final logService = Get.find<LogService>();

    // 获取仓库
    final wordRepository = Get.find<WordRepository>();
    final quizRepository = Get.find<QuizRepository>();
    final reviewItemRepository = Get.find<ReviewItemRepository>();
    final userWordBookRepository = Get.find<UserWordBookRepository>();

    // ==================== 注册服务层 ====================

    // 1. 状态服务
    Get.lazyPut<ReviewStateService>(() => ReviewStateService(log: logService));

    // 2. 导航服务
    Get.lazyPut<ReviewNavigationService>(
      () => ReviewNavigationService(
        stateService: Get.find<ReviewStateService>(),
        log: logService,
      ),
    );

    // 3. 测验生成服务
    Get.lazyPut<ReviewQuizGenerationService>(
      () => ReviewQuizGenerationService(
        quizRepository: quizRepository,
        wordRepository: wordRepository,
        stateService: Get.find<ReviewStateService>(),
        navigationService: Get.find<ReviewNavigationService>(),
        log: logService,
      ),
    );

    // 4. 质量服务
    Get.lazyPut<ReviewQualityService>(
      () => ReviewQualityService(
        stateService: Get.find<ReviewStateService>(),
        reviewItemRepository: reviewItemRepository,
        wordRepository: wordRepository,
        userWordBookRepository: userWordBookRepository,
        log: logService,
      ),
    );

    // ==================== 注册控制器层 ====================

    // 1. 主控制器 - 负责复习流程初始化和启动
    Get.lazyPut<ReviewController>(
      () => ReviewController(
        stateService: Get.find<ReviewStateService>(),
        reviewItemRepository: reviewItemRepository,
        quizGenerationService: Get.find<ReviewQuizGenerationService>(),
        log: logService,
      ),
    );

    // 2. 测验控制器 - 负责复习测验流程
    Get.lazyPut<ReviewQuizController>(
      () => ReviewQuizController(
        reviewController: Get.find<ReviewController>(),
        stateService: Get.find<ReviewStateService>(),
        qualityService: Get.find<ReviewQualityService>(),
        navigationService: Get.find<ReviewNavigationService>(),
        quizGenerationService: Get.find<ReviewQuizGenerationService>(),
        log: logService,
      ),
    );

    // 3. 详情页控制器 - 负责复习详情展示
    Get.lazyPut<ReviewDetailController>(
      () => ReviewDetailController(
        stateService: Get.find<ReviewStateService>(),
        navigationService: Get.find<ReviewNavigationService>(),
        wordRepository: wordRepository,
        audioService: Get.find<AudioService>(),
        quizGenerationService: Get.find<ReviewQuizGenerationService>(),
        log: logService,
      ),
    );

    // 4. 结果页控制器 - 负责结果统计和展示
    Get.lazyPut<ReviewResultController>(
      () => ReviewResultController(
        stateService: Get.find<ReviewStateService>(),
        navigationService: Get.find<ReviewNavigationService>(),
        log: logService,
      ),
    );
  }
}
