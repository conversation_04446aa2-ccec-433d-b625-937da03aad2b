import 'package:flutter_study_flow_by_myself/data/models/quiz/choice_quiz_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/quiz/spelling_quiz_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/quiz/quiz_type.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_model.dart';
import 'package:flutter_study_flow_by_myself/data/providers/quiz/quiz_local_provider.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/word_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/quiz/generators/english_chinese_generator.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/quiz/generators/chinese_english_generator.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/quiz/generators/spelling_generator.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/quiz/generators/audio_generator.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/quiz/generators/sentence_generator.dart';

import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/app/exceptions/app_exception.dart';

/// QuizRepository 负责协调测验题生成器和本地Provider，提供测验题相关的业务数据接口
/// 典型职责：
/// - 动态生成与单词相关的测验题
/// - 调用各种测验题生成器
/// - 存取本地预先生成的复杂测验题
/// - 统一异常和日志处理
class QuizRepository {
  final QuizLocalProvider _localProvider;
  final LogService _log;

  QuizRepository({
    required QuizLocalProvider localProvider,
    required WordRepository wordRepository,
    required LogService log,
  }) : _localProvider = localProvider,
       _log = log;

  /// 为单个单词随机生成一个测验题
  /// [word] 目标单词
  /// [allWords] 所有单词列表，用于生成干扰项
  /// 返回为单词生成的测验题，如果生成失败则返回null
  Future<dynamic> generateRandomQuizForWord(
    WordModel word,
    List<WordModel> allWords,
  ) async {
    try {
      _log.i('开始为单词 ${word.spelling} (ID: ${word.id}) 生成随机测验题');

      final availableTypes = QuizType.values.toList()..shuffle(); // 打乱题型顺序

      // 记录已尝试过的题型
      final Set<QuizType> attemptedTypes = {};

      // 尝试生成一个测验题，如果失败则尝试其他题型
      for (final quizType in availableTypes) {
        // 如果已经尝试过这个题型，则跳过
        if (attemptedTypes.contains(quizType)) {
          continue;
        }

        // 标记为已尝试
        attemptedTypes.add(quizType);

        dynamic quiz;

        // 关键修复：确保每个生成器都使用正确的单词ID
        switch (quizType) {
          case QuizType.englishToChinese:
            quiz = EnglishChineseGenerator.generate(word, allWords);
            // 强制确保wordId正确
            if (quiz != null && quiz is ChoiceQuizModel) {
              // 验证并修复单词ID
              if (quiz.wordId != word.id) {
                _log.w('修复英译中测验题的单词ID: ${quiz.wordId} -> ${word.id}');
                quiz = ChoiceQuizModel(
                  wordId: word.id, // 确保使用正确的单词ID
                  question: quiz.question,
                  options: quiz.options,
                  correctOptionIndex: quiz.correctOptionIndex,
                  type: quiz.type,
                  explanation: quiz.explanation,
                );
              }
            }
            break;

          case QuizType.chineseToEnglish:
            quiz = ChineseEnglishGenerator.generate(word, allWords);
            // 强制确保wordId正确
            if (quiz != null && quiz is ChoiceQuizModel) {
              // 验证并修复单词ID
              if (quiz.wordId != word.id) {
                _log.w('修复中译英测验题的单词ID: ${quiz.wordId} -> ${word.id}');
                quiz = ChoiceQuizModel(
                  wordId: word.id, // 确保使用正确的单词ID
                  question: quiz.question,
                  options: quiz.options,
                  correctOptionIndex: quiz.correctOptionIndex,
                  type: quiz.type,
                  explanation: quiz.explanation,
                );
              }
            }
            break;

          case QuizType.audioToMeaning:
            final audioQuiz = AudioGenerator.generate(word, allWords);
            // 强制确保wordId正确
            if (audioQuiz != null) {
              // 验证并修复单词ID
              if (audioQuiz.wordId != word.id) {
                _log.w('修复听音选义测验题的单词ID: ${audioQuiz.wordId} -> ${word.id}');
                quiz = ChoiceQuizModel(
                  wordId: word.id, // 确保使用正确的单词ID
                  question: audioQuiz.question,
                  options: audioQuiz.options,
                  correctOptionIndex: audioQuiz.correctOptionIndex,
                  type: audioQuiz.type,
                  explanation: audioQuiz.explanation,
                );
              } else {
                quiz = audioQuiz;
              }
            } else {
              quiz = audioQuiz; // 可能为null
            }
            break;

          case QuizType.sentenceCompletion:
            final sentenceQuiz = SentenceGenerator.generate(word, allWords);
            // 强制确保wordId正确
            if (sentenceQuiz != null) {
              // 验证并修复单词ID
              if (sentenceQuiz.wordId != word.id) {
                _log.w('修复例句填空测验题的单词ID: ${sentenceQuiz.wordId} -> ${word.id}');
                quiz = ChoiceQuizModel(
                  wordId: word.id, // 确保使用正确的单词ID
                  question: sentenceQuiz.question,
                  options: sentenceQuiz.options,
                  correctOptionIndex: sentenceQuiz.correctOptionIndex,
                  type: sentenceQuiz.type,
                  explanation: sentenceQuiz.explanation,
                );
              } else {
                quiz = sentenceQuiz;
              }
            } else {
              quiz = sentenceQuiz; // 可能为null
            }
            break;

          case QuizType.spelling:
            quiz = SpellingGenerator.generate(word);
            // 强制确保wordId正确
            if (quiz != null && quiz is SpellingQuizModel) {
              // 验证并修复单词ID
              if (quiz.wordId != word.id) {
                _log.w('修复拼写测验题的单词ID: ${quiz.wordId} -> ${word.id}');
                quiz = SpellingQuizModel(
                  wordId: word.id, // 确保使用正确的单词ID
                  correctAnswer: quiz.correctAnswer,
                  hint: quiz.hint,
                  type: quiz.type,
                  explanation: quiz.explanation,
                );
              }
            }
            break;
        }

        if (quiz != null) {
          // 最终验证测验题的单词ID是否正确
          bool isValid = false;
          if (quiz is ChoiceQuizModel) {
            isValid = quiz.wordId == word.id;
            if (!isValid) {
              _log.e('错误：生成的选择题单词ID(${quiz.wordId})与目标单词ID(${word.id})不匹配!');
            }
          } else if (quiz is SpellingQuizModel) {
            isValid = quiz.wordId == word.id;
            if (!isValid) {
              _log.e('错误：生成的拼写题单词ID(${quiz.wordId})与目标单词ID(${word.id})不匹配!');
            }
          }

          if (isValid) {
            _log.i('成功为单词 ${word.spelling} 生成了 ${quizType.name} 类型的测验题');
            return quiz; // 成功生成一个测验题，直接返回
          }
        }
      }

      // 如果所有题型都尝试过了，仍然没有成功生成测验题
      _log.w('警告：单词 ${word.spelling} 无法生成任何类型的测验题');
      return null;
    } catch (e) {
      _log.e('为单词生成测验题失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '为单词生成测验题失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 为单个单词生成多种测验题（向后兼容方法）
  /// [word] 目标单词
  /// [allWords] 所有单词列表，用于生成干扰项
  /// [count] 要生成的测验题数量，默认为2
  /// 返回为单词生成的各种测验题
  Future<List<dynamic>> generateQuizzesForWord(
    WordModel word,
    List<WordModel> allWords, {
    int count = 2,
  }) async {
    try {
      _log.i('开始为单词 ${word.spelling} (ID: ${word.id}) 生成 $count 个测验题');

      final quizzes = <dynamic>[];
      final availableTypes = QuizType.values.toList()..shuffle(); // 打乱题型顺序
      final Set<QuizType> usedTypes = {};

      // 尝试生成指定数量的不同类型测验题
      for (
        int i = 0;
        i < count && usedTypes.length < QuizType.values.length;
        i++
      ) {
        for (final quizType in availableTypes) {
          // 如果已经使用过这个题型，则跳过
          if (usedTypes.contains(quizType)) {
            continue;
          }

          // 尝试生成这种类型的测验题
          dynamic quiz;
          switch (quizType) {
            case QuizType.englishToChinese:
              quiz = EnglishChineseGenerator.generate(word, allWords);
              break;
            case QuizType.chineseToEnglish:
              quiz = ChineseEnglishGenerator.generate(word, allWords);
              break;
            case QuizType.audioToMeaning:
              quiz = AudioGenerator.generate(word, allWords);
              break;
            case QuizType.sentenceCompletion:
              quiz = SentenceGenerator.generate(word, allWords);
              break;
            case QuizType.spelling:
              quiz = SpellingGenerator.generate(word);
              break;
          }

          if (quiz != null) {
            // 验证单词ID
            bool isValid = false;
            if (quiz is ChoiceQuizModel) {
              isValid = quiz.wordId == word.id;
              if (!isValid) {
                // 修复单词ID
                quiz = ChoiceQuizModel(
                  wordId: word.id,
                  question: quiz.question,
                  options: quiz.options,
                  correctOptionIndex: quiz.correctOptionIndex,
                  type: quiz.type,
                  explanation: quiz.explanation,
                );
                isValid = true;
              }
            } else if (quiz is SpellingQuizModel) {
              isValid = quiz.wordId == word.id;
              if (!isValid) {
                // 修复单词ID
                quiz = SpellingQuizModel(
                  wordId: word.id,
                  correctAnswer: quiz.correctAnswer,
                  hint: quiz.hint,
                  type: quiz.type,
                  explanation: quiz.explanation,
                );
                isValid = true;
              }
            }

            if (isValid) {
              quizzes.add(quiz);
              usedTypes.add(quizType);
              break; // 成功生成一个测验题，跳出内层循环
            }
          }
        }
      }

      _log.i('为单词 ${word.spelling} 成功生成了 ${quizzes.length} 个测验题');
      return quizzes;
    } catch (e) {
      _log.e('为单词生成多个测验题失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '为单词生成多个测验题失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 获取所有选择题
  Future<List<ChoiceQuizModel>> getAllQuizzes() async {
    try {
      return await _localProvider.getAllQuizzes();
    } on AppException catch (e) {
      _log.e('获取所有选择题失败: ${e.message}', error: e);
      rethrow;
    } catch (e) {
      _log.e('获取所有选择题失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '获取所有选择题失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 根据ID获取单个选择题
  Future<ChoiceQuizModel?> getQuizById(int quizId) async {
    try {
      return await _localProvider.getQuizById(quizId);
    } on AppException catch (e) {
      _log.e('根据ID获取选择题失败: ${e.message}', error: e);
      rethrow;
    } catch (e) {
      _log.e('根据ID获取选择题失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '根据ID获取选择题失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 保存单个选择题到本地
  Future<int> saveQuiz(ChoiceQuizModel quiz) async {
    try {
      return await _localProvider.saveQuiz(quiz);
    } on AppException catch (e) {
      _log.e('保存选择题失败: ${e.message}', error: e);
      rethrow;
    } catch (e) {
      _log.e('保存选择题失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '保存选择题失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 批量保存选择题到本地
  Future<List<int>> saveAllQuizzes(List<ChoiceQuizModel> quizzes) async {
    try {
      return await _localProvider.saveAllQuizzes(quizzes);
    } on AppException catch (e) {
      _log.e('批量保存选择题失败: ${e.message}', error: e);
      rethrow;
    } catch (e) {
      _log.e('批量保存选择题失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '批量保存选择题失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 根据ID删除单个选择题
  Future<bool> deleteQuizById(int quizId) async {
    try {
      return await _localProvider.deleteQuizById(quizId);
    } on AppException catch (e) {
      _log.e('删除选择题失败: ${e.message}', error: e);
      rethrow;
    } catch (e) {
      _log.e('删除选择题失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '删除选择题失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 批量删除选择题
  Future<int> deleteQuizzesByIds(List<int> quizIds) async {
    try {
      return await _localProvider.deleteQuizzesByIds(quizIds);
    } on AppException catch (e) {
      _log.e('批量删除选择题失败: ${e.message}', error: e);
      rethrow;
    } catch (e) {
      _log.e('批量删除选择题失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '批量删除选择题失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 清空所有选择题
  Future<void> clearAllQuizzes() async {
    try {
      await _localProvider.clearAllQuizzes();
    } on AppException catch (e) {
      _log.e('清空所有选择题失败: ${e.message}', error: e);
      rethrow;
    } catch (e) {
      _log.e('清空所有选择题失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '清空所有选择题失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 查询本地选择题数量
  Future<int> countQuizzes() async {
    try {
      return await _localProvider.countQuizzes();
    } on AppException catch (e) {
      _log.e('查询选择题数量失败: ${e.message}', error: e);
      rethrow;
    } catch (e) {
      _log.e('查询选择题数量失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '查询选择题数量失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 保存拼写题到本地
  Future<int> saveSpellingQuiz(SpellingQuizModel quiz) async {
    try {
      return await _localProvider.saveSpellingQuiz(quiz);
    } on AppException catch (e) {
      _log.e('保存拼写题失败: ${e.message}', error: e);
      rethrow;
    } catch (e) {
      _log.e('保存拼写题失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '保存拼写题失败',
        type: AppExceptionType.unknown,
      );
    }
  }

  /// 批量保存拼写题到本地
  Future<List<int>> saveAllSpellingQuizzes(
    List<SpellingQuizModel> quizzes,
  ) async {
    try {
      return await _localProvider.saveAllSpellingQuizzes(quizzes);
    } on AppException catch (e) {
      _log.e('批量保存拼写题失败: ${e.message}', error: e);
      rethrow;
    } catch (e) {
      _log.e('批量保存拼写题失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '批量保存拼写题失败',
        type: AppExceptionType.unknown,
      );
    }
  }
}
