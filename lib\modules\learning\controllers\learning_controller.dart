import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/app/services/review_plan_service.dart';
import 'package:flutter_study_flow_by_myself/app/exceptions/app_exception.dart';
import 'package:flutter_study_flow_by_myself/data/models/learning_session.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/learning_session_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/user_word_book_repository.dart';

import '../services/learning_state_service.dart';
import '../services/learning_persistence_service.dart';
import '../services/learning_navigation_service.dart';
import '../services/learning_quality_service.dart';

/// 学习控制器 - 学习模块的核心协调器
///
/// 职责：
/// - 协调各个服务之间的交互，管理整体学习流程
/// - 处理学习会话的创建、恢复和完成
/// - 管理学习阶段的转换和状态同步
/// - 提供统一的学习会话访问接口
///
/// 架构说明：
/// 本控制器作为学习模块的中央协调器，不直接处理UI交互，
/// 而是通过子控制器（RecallController、QuizController等）间接影响UI。
/// 主要负责会话级别的状态管理和服务协调。
class LearningController extends GetxController {
  // ==================== 依赖注入 ====================
  /// 学习状态服务 - 管理学习过程中的状态数据
  ///
  /// 调用位置：
  /// - _initializeFromArguments() 中调用 initialize() 和 setInFinalQuiz()
  /// - _updateLearningProgress() 中访问 learnedWordCount
  final LearningStateService _stateService;

  /// 学习持久化服务 - 处理学习会话的保存和恢复
  ///
  /// 调用位置：
  /// - _initializeFromArguments() 中调用 createNewSession()
  /// - _restoreSessionFromDatabase() 中调用 restoreSession()
  /// - _performSmartCleanup() 中调用 performSmartCleanup()
  /// - _markSessionAsCompleted() 中调用 markSessionAsCompleted()
  final LearningPersistenceService _persistenceService;

  /// 学习导航服务 - 处理学习流程中的页面导航
  ///
  /// 调用位置：
  /// - _initializeFromArguments() 中调用 navigateToRecall()
  /// - _restoreSessionFromDatabase() 中调用 navigateBasedOnCurrentState()
  /// - completeSession() 中调用 navigateToWordBookCompletion() 和 navigateToResult()
  final LearningNavigationService _navigationService;

  /// 学习质量服务 - 处理学习质量评估和复习项更新
  ///
  /// 调用位置：
  /// - updateWordReviewItem() 中调用 updateWordReviewItem()
  final LearningQualityService _qualityService;

  /// 学习会话仓库 - 处理学习会话的数据库操作
  ///
  /// 调用位置：
  /// - _restoreSessionFromDatabase() 中调用 getLatestUnfinishedSession()
  final LearningSessionRepository _sessionRepository;

  /// 用户单词本仓库 - 处理用户单词本的数据操作
  ///
  /// 调用位置：
  /// - _updateLearningProgress() 中调用 getUserWordBookById() 和 updateLearningProgress()
  /// - _checkAndHandleWordBookCompletion() 中调用 getUserWordBookById()
  /// - _markWordBookAsCompleted() 中调用 markWordBookAsCompleted()
  final UserWordBookRepository _userWordBookRepository;

  /// 日志服务 - 记录控制器运行日志
  ///
  /// 调用位置：
  /// - 所有方法中用于记录操作日志、错误信息和调试信息
  final LogService _log;

  // ==================== 响应式状态变量 ====================
  /// 当前学习会话数据
  ///
  /// 存储当前正在进行的学习会话信息，包括会话ID、单词本ID、开始时间等
  ///
  /// 子控制器使用位置：
  /// - RecallController 中通过 learningController.session.value 访问会话数据
  /// - QuizController 中获取学习会话信息
  /// - ResultController 中获取会话统计数据
  /// - DetailController 中获取当前学习状态
  ///
  /// 响应式监听：子控制器通过响应式访问，当会话变化时自动更新相关状态
  final Rx<LearningSession?> session = Rx<LearningSession?>(null);

  /// 控制器加载状态标识
  ///
  /// 用于标识控制器是否正在执行异步操作，主要用于内部状态管理
  ///
  /// 使用位置：
  /// - _initializeFromArguments() 中控制初始化状态
  ///
  /// 注意：此变量主要用于内部逻辑控制，未发现直接的UI监听使用
  final RxBool isLoading = false.obs;

  /// 错误信息存储
  ///
  /// 存储控制器运行过程中产生的错误信息
  ///
  /// 使用位置：
  /// - _handleError() 方法中设置错误信息
  ///
  /// 注意：此变量主要用于错误状态记录，未发现直接的UI监听使用
  final RxString errorMessage = ''.obs;

  // ==================== 构造函数 ====================
  /// 构造函数 - 依赖注入
  ///
  /// 通过构造函数注入所有必要的服务和仓库实例，确保控制器的依赖关系清晰
  /// 并提高代码的可测试性和可维护性
  ///
  /// 参数：
  /// - [stateService] 学习状态服务实例
  /// - [persistenceService] 学习持久化服务实例
  /// - [navigationService] 学习导航服务实例
  /// - [qualityService] 学习质量服务实例
  /// - [sessionRepository] 学习会话仓库实例
  /// - [userWordBookRepository] 用户单词本仓库实例
  /// - [log] 日志服务实例
  ///
  /// 注册位置：lib/modules/learning/bindings/learning_binding.dart
  LearningController({
    required LearningStateService stateService,
    required LearningPersistenceService persistenceService,
    required LearningNavigationService navigationService,
    required LearningQualityService qualityService,
    required LearningSessionRepository sessionRepository,
    required UserWordBookRepository userWordBookRepository,
    required LogService log,
  }) : _stateService = stateService,
       _persistenceService = persistenceService,
       _navigationService = navigationService,
       _qualityService = qualityService,
       _sessionRepository = sessionRepository,
       _userWordBookRepository = userWordBookRepository,
       _log = log;

  // ==================== 生命周期方法 ====================
  /// 控制器初始化方法
  ///
  /// 在控制器创建时自动调用，负责初始化控制器状态和处理路由参数
  ///
  /// 调用时机：
  /// - 当用户导航到学习相关页面时，GetX 自动调用此方法
  /// - 通过路由导航（如 Get.toNamed(LearningRoutes.RECALL)）触发
  ///
  /// 执行流程：
  /// 1. 调用父类的 onInit() 方法
  /// 2. 记录初始化日志
  /// 3. 获取路由传递的参数
  /// 4. 如果有参数，使用微任务延迟初始化（避免在构建期间执行）
  ///
  /// 路由参数说明：
  /// - wordIds: 单词ID列表（新会话模式）
  /// - userWordBookId: 用户单词本ID
  /// - isNewSession: 是否为新会话
  /// - restoreSession: 是否恢复会话
  @override
  void onInit() {
    super.onInit();
    _log.i('学习控制器初始化');

    // 获取传入的参数
    final Map<String, dynamic>? args = Get.arguments;

    if (args != null) {
      // 使用微任务延迟初始化，避免在构建期间执行
      Future.microtask(() => _initializeFromArguments(args));
    }
  }

  // ==================== 初始化方法 ====================
  /// 从路由参数初始化学习会话
  ///
  /// 根据路由传递的参数决定是恢复现有会话还是创建新会话
  ///
  /// 调用位置：
  /// - onInit() 方法中通过微任务调用
  ///
  /// 参数：
  /// - [args] 路由传递的参数映射，包含会话初始化所需的信息
  ///
  /// 支持的参数：
  /// - restoreSession: bool - 是否恢复会话模式
  /// - wordIds: List&lt;int&gt; - 单词ID列表（新会话模式）
  /// - userWordBookId: int - 用户单词本ID
  /// - isNewSession: bool - 是否为新会话
  ///
  /// 异常处理：
  /// - 捕获所有异常并通过 _handleError 统一处理
  /// - 确保 isLoading 状态在 finally 块中被重置
  Future<void> _initializeFromArguments(Map<String, dynamic> args) async {
    try {
      isLoading.value = true;

      // 检查是否是恢复会话模式
      if (args.containsKey('restoreSession') &&
          args['restoreSession'] == true) {
        await _handleRestoreSession(args);
      }
      // 检查是否传递了单词ID列表（新会话模式）
      else if (args.containsKey('wordIds')) {
        await _handleNewSession(args);
      } else {
        throw AppException(AppExceptionType.unknown, '缺少必要的学习参数');
      }
    } catch (e) {
      _handleError('初始化学习会话失败', e);
    } finally {
      isLoading.value = false;
    }
  }

  /// 处理恢复会话模式的初始化
  ///
  /// 从数据库恢复之前未完成的学习会话
  ///
  /// 调用位置：
  /// - _initializeFromArguments() 方法中调用
  ///
  /// 参数：
  /// - [args] 包含 userWordBookId 的参数映射
  Future<void> _handleRestoreSession(Map<String, dynamic> args) async {
    _log.i('恢复学习会话模式');
    final userWordBookId = args['userWordBookId'] as int;
    await _restoreSessionFromDatabase(userWordBookId);
  }

  /// 处理新会话模式的初始化
  ///
  /// 根据传入的单词ID列表创建新的学习会话
  ///
  /// 调用位置：
  /// - _initializeFromArguments() 方法中调用
  ///
  /// 参数：
  /// - [args] 包含 wordIds、userWordBookId、isNewSession 的参数映射
  ///
  /// 业务逻辑：
  /// 1. 提取并验证参数
  /// 2. 初始化状态服务
  /// 3. 如果是新会话，创建会话并导航到回忆页面
  Future<void> _handleNewSession(Map<String, dynamic> args) async {
    _log.i('从参数初始化学习会话');

    final List<int> wordIds = List<int>.from(args['wordIds']);
    final int userWordBookId = args['userWordBookId'] as int;
    final bool isNewSession =
        args.containsKey('isNewSession') ? args['isNewSession'] as bool : false;

    if (wordIds.isEmpty) {
      _log.w('传入的单词ID列表为空');
      return;
    }

    _log.i(
      '初始化学习会话: 单词ID数量=${wordIds.length}, 单词本ID=$userWordBookId, 新会话=$isNewSession',
    );

    // 直接初始化状态服务，不使用微任务
    _stateService.initialize(wordIds);

    if (isNewSession) {
      // 创建新会话
      final newSession = await _persistenceService.createNewSession(
        userWordBookId,
        wordIds,
      );
      session.value = newSession;

      // 导航到第一个单词的回忆页面
      await _navigationService.navigateToRecall();
    }
  }

  // ==================== 会话恢复方法 ====================
  /// 从数据库恢复学习会话
  ///
  /// 恢复用户之前未完成的学习会话，包括会话状态、学习进度和导航状态
  ///
  /// 调用位置：
  /// - _handleRestoreSession() 方法中调用
  ///
  /// 参数：
  /// - [userWordBookId] 用户单词本ID，用于查找对应的未完成会话
  ///
  /// 恢复流程：
  /// 1. 从数据库获取最新的未完成会话
  /// 2. 恢复持久化服务的状态
  /// 3. 更新当前会话和学习状态
  /// 4. 根据会话状态导航到合适的页面
  ///
  /// 异常处理：
  /// - 如果找不到未完成会话，抛出 AppException
  /// - 其他异常会被重新抛出，由上层处理
  ///
  /// 返回值：无返回值（Future&lt;void&gt;）
  Future<void> _restoreSessionFromDatabase(int userWordBookId) async {
    try {
      _log.i('从数据库恢复学习会话: 单词本ID=$userWordBookId');

      // 1. 获取最新的未完成会话
      final existingSession = await _sessionRepository
          .getLatestUnfinishedSession(userWordBookId);
      if (existingSession == null) {
        throw AppException(AppExceptionType.database, '找不到未完成的学习会话');
      }

      // 2. 恢复持久化服务的状态
      await _persistenceService.restoreSession(existingSession);

      // 3. 更新当前会话和状态
      session.value = existingSession;
      _stateService.setInFinalQuiz(existingSession.isInFinalQuiz);

      // 4. 根据当前状态导航到合适的页面
      await _navigationService.navigateBasedOnCurrentState();

      _log.i('成功恢复学习会话: ${existingSession.sessionId}');
    } catch (e) {
      _log.e('从数据库恢复学习会话失败: $e');
      rethrow;
    }
  }

  // ==================== 公共方法 ====================

  /// 更新单个单词的复习项
  ///
  /// 更新指定单词的复习质量数据，用于调整后续的复习间隔和难度
  ///
  /// 调用位置：
  /// - QuizController 中当用户完成单词测验时调用
  /// - RecallController 中当用户完成单词回忆时调用
  ///
  /// 参数：
  /// - [wordId] 要更新的单词ID
  ///
  /// 业务逻辑：
  /// - 检查当前会话是否存在
  /// - 如果会话存在，调用质量服务更新复习项
  ///
  /// 返回值：无返回值（Future&lt;void&gt;）
  Future<void> updateWordReviewItem(int wordId) async {
    if (session.value == null) return;
    await _qualityService.updateWordReviewItem(
      wordId,
      session.value!.userWordBookId,
    );
  }

  /// 完成学习会话
  ///
  /// 当用户完成所有学习内容后，执行会话完成的完整流程
  ///
  /// 调用位置：
  /// - ResultController 中当用户确认完成学习时调用
  /// - QuizController 中在特定条件下可能调用
  ///
  /// 执行流程：
  /// 1. 更新用户的学习进度统计
  /// 2. 检查单词本是否完全完成（在进度更新后立即检查）
  /// 3. 执行智能清理（清理过期数据）
  /// 4. 标记当前会话为已完成状态
  /// 5. 通知HomeController刷新数据
  /// 6. 根据单词本完成状态导航到相应页面
  ///
  /// 导航逻辑：
  /// - 如果单词本完全完成：导航到单词本完成庆祝页面
  /// - 如果单词本未完成：导航到结果页面
  ///
  /// 异常处理：
  /// - 捕获所有异常并记录错误日志
  /// - 不重新抛出异常，确保流程能够继续
  ///
  /// 返回值：无返回值（Future&lt;void&gt;）
  Future<void> completeSession() async {
    try {
      _log.i('开始执行学习会话完成流程');

      // 1. 更新学习进度
      await _updateLearningProgress();

      // 2. 检查是否完成整个单词本（在进度更新后立即检查）
      final isWordBookCompleted = await _checkAndHandleWordBookCompletion();

      // 3. 执行智能清理
      await _performSmartCleanup();

      // 4. 标记会话为已完成
      await _markSessionAsCompleted();

      // 5. 通知HomeController刷新数据
      await _notifyHomeControllerRefresh();

      // 6. 根据单词本完成状态决定导航
      if (isWordBookCompleted) {
        // 单词本已完成，导航到完成庆祝页面
        _navigationService.navigateToWordBookCompletion();
      } else {
        // 正常导航到结果页面
        _navigationService.navigateToResult();
      }

      _log.i('学习会话完成流程执行成功');
    } catch (e) {
      _log.e('学习会话完成流程失败: $e');
    }
  }

  // ==================== 私有方法 ====================
  /// 更新学习进度
  ///
  /// 将当前会话的学习进度同步到用户单词本，累加已学习单词数量
  ///
  /// 调用位置：
  /// - completeSession() 方法中调用
  ///
  /// 业务逻辑：
  /// 1. 检查当前会话是否存在
  /// 2. 获取本次学习的单词数量
  /// 3. 如果有新学习的单词，获取当前单词本数据
  /// 4. 累加本次学习的单词数到总学习数
  /// 5. 更新数据库中的学习进度
  ///
  /// 异常处理：
  /// - 捕获所有异常并记录错误日志
  /// - 不重新抛出异常，避免影响主流程
  ///
  /// 返回值：无返回值（Future&lt;void&gt;）
  Future<void> _updateLearningProgress() async {
    try {
      final currentSession = session.value;

      if (currentSession == null) {
        _log.w('没有找到学习会话，跳过进度更新');
        return;
      }

      final userWordBookId = currentSession.userWordBookId;

      // 获取本次学习的单词数量
      final learnedWordsCount = _stateService.learnedWordCount;

      if (learnedWordsCount > 0) {
        // 获取当前单词本的已学单词数
        final currentWordBook = await _userWordBookRepository
            .getUserWordBookById(userWordBookId);

        if (currentWordBook != null) {
          // 累加本次学习的单词数
          final newTotalLearnedWords =
              currentWordBook.totalLearnedWords + learnedWordsCount;

          await _userWordBookRepository.updateLearningProgress(
            userWordBookId,
            newTotalLearnedWords.toInt(),
          );

          _log.i(
            '已更新学习进度: 单词本=$userWordBookId, 新增单词=$learnedWordsCount, 总计=$newTotalLearnedWords',
          );
        }
      }
    } catch (e) {
      _log.e('更新学习进度失败: $e');
      // 不抛出异常，避免影响主流程
    }
  }

  /// 执行智能清理
  ///
  /// 清理过期的学习会话数据，释放存储空间并优化性能
  ///
  /// 调用位置：
  /// - completeSession() 方法中调用
  ///
  /// 业务逻辑：
  /// - 调用持久化服务的智能清理方法
  /// - 清理过期或无用的会话数据
  ///
  /// 异常处理：
  /// - 捕获所有异常并记录错误日志
  /// - 不重新抛出异常，避免影响主流程
  ///
  /// 返回值：无返回值（Future&lt;void&gt;）
  Future<void> _performSmartCleanup() async {
    try {
      _log.i('开始执行智能会话清理');

      // 调用统一的智能清理方法
      await _persistenceService.performSmartCleanup();

      _log.i('智能会话清理完成');
    } catch (e) {
      _log.e('智能会话清理失败: $e');
      // 不抛出异常，避免影响主流程
    }
  }

  /// 标记会话为已完成
  ///
  /// 将当前学习会话标记为已完成状态，更新数据库记录
  ///
  /// 调用位置：
  /// - completeSession() 方法中调用
  ///
  /// 业务逻辑：
  /// - 调用持久化服务标记会话完成
  /// - 更新会话的完成时间和状态
  ///
  /// 异常处理：
  /// - 捕获所有异常并记录错误日志
  /// - 不重新抛出异常，避免影响主流程
  ///
  /// 返回值：无返回值（Future&lt;void&gt;）
  Future<void> _markSessionAsCompleted() async {
    try {
      await _persistenceService.markSessionAsCompleted();
      _log.i('会话已标记为完成');
    } catch (e) {
      _log.e('标记会话完成失败: $e');
      // 不抛出异常，避免影响主流程
    }
  }

  /// 检查并处理单词本完成状态
  ///
  /// 检查用户是否已完成整个单词本的学习，如果完成则标记单词本为已完成状态
  ///
  /// 调用位置：
  /// - completeSession() 方法中调用
  ///
  /// 业务逻辑：
  /// 1. 检查当前学习会话是否存在
  /// 2. 获取用户单词本的完整数据
  /// 3. 比较已学习单词数与总单词数
  /// 4. 如果完成，标记单词本为已完成状态
  ///
  /// 判断标准：
  /// - 已学习单词数 >= 总单词数
  /// - 总单词数 > 0（避免空单词本被误判为完成）
  ///
  /// 异常处理：
  /// - 捕获所有异常并记录错误日志
  /// - 异常情况下返回 false
  ///
  /// 返回值：bool - 是否完成了整个单词本
  Future<bool> _checkAndHandleWordBookCompletion() async {
    try {
      final currentSession = session.value;
      if (currentSession == null) {
        _log.w('检查单词本完成状态失败: 当前没有学习会话');
        return false;
      }

      // 获取用户单词本的实际学习进度
      final userWordBook = await _userWordBookRepository.getUserWordBookById(
        currentSession.userWordBookId,
      );

      if (userWordBook == null) {
        _log.w('检查单词本完成状态失败: 找不到用户单词本 ${currentSession.userWordBookId}');
        return false;
      }

      // 检查整个单词本的学习进度（而不是本次会话的进度）
      final totalWords = userWordBook.totalWords;
      final totalLearnedWords = userWordBook.totalLearnedWords;

      _log.i('单词本完成检查: 整个单词本已学习 $totalLearnedWords/$totalWords 个单词');

      if (totalLearnedWords >= totalWords && totalWords > 0) {
        // 标记单词本为已完成
        await _markWordBookAsCompleted(currentSession.userWordBookId);
        _log.i('单词本已完成！用户单词本ID: ${currentSession.userWordBookId}');
        return true;
      }

      return false;
    } catch (e) {
      _log.e('检查单词本完成状态失败: $e');
      return false;
    }
  }

  /// 标记单词本为已完成
  ///
  /// 将指定的用户单词本标记为已完成状态，更新完成时间
  ///
  /// 调用位置：
  /// - _checkAndHandleWordBookCompletion() 方法中调用
  ///
  /// 参数：
  /// - [userWordBookId] 要标记为完成的用户单词本ID
  ///
  /// 业务逻辑：
  /// - 调用仓库方法标记单词本完成
  /// - 设置完成时间为当前时间
  ///
  /// 异常处理：
  /// - 捕获所有异常并记录错误日志
  /// - 不重新抛出异常，避免影响主流程
  ///
  /// 返回值：无返回值（Future&lt;void&gt;）
  Future<void> _markWordBookAsCompleted(int userWordBookId) async {
    try {
      await _userWordBookRepository.markWordBookAsCompleted(userWordBookId);
      _log.i('已标记单词本为完成状态: $userWordBookId');
    } catch (e) {
      _log.e('标记单词本完成失败: $e');
      // 不抛出异常，避免影响主流程
    }
  }

  /// 通知HomeController刷新数据
  ///
  /// 通知首页控制器刷新数据，确保学习完成后首页显示最新状态
  ///
  /// 调用位置：
  /// - completeSession() 方法中调用
  ///
  /// 业务逻辑：
  /// 1. 检查HomeController是否已注册
  /// 2. 如果已注册，获取实例并调用刷新方法
  /// 3. 如果未注册，记录警告日志
  ///
  /// 设计说明：
  /// 使用 Get.isRegistered() 检查避免不必要的依赖，
  /// 因为HomeController可能在某些场景下未被注册
  ///
  /// 异常处理：
  /// - 捕获所有异常并记录错误日志
  /// - 不重新抛出异常，避免影响主流程
  ///
  /// 返回值：无返回值（Future&lt;void&gt;）
  Future<void> _notifyHomeControllerRefresh() async {
    try {
      // 🔑 优化：只刷新ReviewPlanService，避免重复刷新
      // HomeController.refreshData()会重复调用reviewPlanService.refreshReviewPlan()
      if (Get.isRegistered<ReviewPlanService>()) {
        final reviewPlanService = Get.find<ReviewPlanService>();
        await reviewPlanService.refreshReviewPlan();
        _log.i('学习完成，已通知ReviewPlanService刷新复习计划');
      } else {
        _log.w('ReviewPlanService未注册，跳过复习计划刷新');
      }

      // Service状态变化会自动通知HomeController更新UI
      // 不需要手动调用homeController.refreshData()，避免重复刷新
      _log.i('Service状态变化将自动通知HomeController更新UI');
    } catch (e) {
      _log.e('通知数据刷新失败: $e');
      // 不抛出异常，避免影响主流程
    }
  }

  // ==================== 错误处理方法 ====================
  /// 统一处理错误
  ///
  /// 统一处理控制器中发生的各种错误，提供一致的错误处理逻辑
  ///
  /// 调用位置：
  /// - _initializeFromArguments() 方法中调用
  /// - completeSession() 方法中调用
  ///
  /// 参数：
  /// - [message] 错误描述信息，用于日志记录
  /// - [error] 具体的错误对象，可能是 AppException 或其他异常
  ///
  /// 处理逻辑：
  /// - 如果是 AppException，使用其 message 属性
  /// - 如果是其他异常，使用通用错误信息
  /// - 记录详细的错误日志
  /// - 更新 errorMessage 响应式变量
  ///
  /// 返回值：无返回值（void）
  void _handleError(String message, dynamic error) {
    if (error is AppException) {
      _log.e('$message: ${error.message}', error: error);
      errorMessage.value = error.message;
    } else {
      _log.e('$message: $error', error: error);
      errorMessage.value = '操作失败，请重试';
    }
  }
}
