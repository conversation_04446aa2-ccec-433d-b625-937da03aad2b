# auth 模块说明文档

## 模块职责

`auth` 模块负责处理用户认证相关的所有功能，包括登录、注册、找回密码、身份验证等。这是应用程序的安全入口，管理用户的身份验证过程和认证状态。

## 目录结构

```
auth/
├── bindings/
│   └── auth_binding.dart       # 注册AuthController、LoginController等依赖
├── controllers/
│   ├── auth_controller.dart    # 管理全局认证状态
│   ├── login_controller.dart   # 处理登录逻辑
│   └── register_controller.dart # 处理注册逻辑
├── views/
│   ├── login_page.dart         # 登录页面UI
│   └── register_page.dart      # 注册页面UI
└── routes/
    └── auth_routes.dart        # 定义认证相关路由
```

## 核心功能

- **用户登录**：验证用户凭据并生成访问令牌
- **用户注册**：创建新用户账户
- **密码重置**：允许用户重置忘记的密码
- **身份验证**：验证用户的认证状态
- **令牌管理**：存储和刷新认证令牌

## 主要文件说明

### auth_binding.dart

- **职责**：注册认证模块所需的依赖项
- **依赖项**：AuthController、LoginController、RegisterController、UserRepository 等
- **使用方式**：在路由定义中通过 `binding: AuthBinding()` 使用

### auth_controller.dart

- **职责**：管理全局认证状态，提供登录、注销和检查认证状态的方法
- **依赖**：UserRepository、StorageService
- **主要功能**：
  - 存储当前登录用户信息
  - 提供 isLoggedIn 状态
  - 处理令牌刷新和过期
  - 管理自动登录逻辑

### login_controller.dart

- **职责**：处理登录页面的表单验证和提交逻辑
- **依赖**：AuthController、UserRepository
- **主要功能**：
  - 表单字段验证
  - 处理登录请求
  - 显示登录错误信息
  - 管理登录按钮状态（加载中等）

### login_page.dart

- **职责**：提供登录界面
- **依赖**：LoginController
- **主要组件**：
  - 用户名/邮箱输入框
  - 密码输入框
  - 登录按钮
  - 注册入口链接
  - 忘记密码链接

## 数据流

1. 用户在 `login_page.dart` 输入凭据并点击登录
2. `login_controller.dart` 验证输入并调用 UserRepository 的 login 方法
3. 登录成功后，`login_controller.dart` 通知 `auth_controller.dart` 更新认证状态
4. `auth_controller.dart` 存储用户信息和令牌，更新 isLoggedIn 状态
5. 应用导航到首页或之前尝试访问的受保护页面

## 与其他模块的关系

- **全局服务**：auth 模块通常依赖于 `AuthService`（在 `app/services/` 中）处理底层认证逻辑
- **数据层**：使用 `UserRepository` 和 `UserApiProvider` 进行API交互
- **其他模块**：其他模块可通过 `Get.find<AuthController>()` 获取认证状态

## 易混淆点

1. **AuthController vs AuthService**：
   - `AuthController`：模块内的控制器，管理UI状态和用户交互
   - `AuthService`：全局服务，处理底层认证逻辑，持久存在于整个应用生命周期

2. **登录状态管理**：
   - 登录状态应在 `AuthController` 中以响应式变量（`.obs`）管理
   - 其他模块应通过观察 `AuthController` 的状态变化来响应认证状态变更

## 最佳实践

- 使用 **表单验证** 在客户端进行基本的输入验证，提高用户体验
- 实现 **记住我** 功能，允许用户保持登录状态
- 使用 **令牌刷新** 机制自动更新过期的访问令牌
- 提供 **社交媒体登录** 选项作为替代登录方式
- 实现 **双因素认证** 增强安全性

## 代码示例

### 登录控制器实现

```dart
class LoginController extends GetxController {
  final AuthController _authController = Get.find<AuthController>();
  final UserRepository _userRepository = Get.find<UserRepository>();
  
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  
  final RxBool isLoading = false.obs;
  final RxBool rememberMe = false.obs;
  final RxString errorMessage = ''.obs;
  
  @override
  void onClose() {
    emailController.dispose();
    passwordController.dispose();
    super.onClose();
  }
  
  void toggleRememberMe() => rememberMe.value = !rememberMe.value;
  
  bool validateInputs() {
    if (emailController.text.isEmpty) {
      errorMessage.value = '请输入邮箱';
      return false;
    }
    if (passwordController.text.isEmpty) {
      errorMessage.value = '请输入密码';
      return false;
    }
    return true;
  }
  
  Future<void> login() async {
    if (!validateInputs()) return;
    
    isLoading.value = true;
    errorMessage.value = '';
    
    try {
      final result = await _userRepository.login(
        emailController.text,
        passwordController.text,
        rememberMe: rememberMe.value,
      );
      
      await _authController.setLoggedInUser(result.user, result.token);
      Get.offAllNamed(HomeRoutes.HOME);
    } catch (e) {
      errorMessage.value = e.toString();
    } finally {
      isLoading.value = false;
    }
  }
  
  void navigateToRegister() {
    Get.toNamed(AuthRoutes.REGISTER);
  }
  
  void navigateToForgotPassword() {
    Get.toNamed(AuthRoutes.FORGOT_PASSWORD);
  }
}
```

### 登录页面实现

```dart
class LoginPage extends GetView<LoginController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('登录')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            TextField(
              controller: controller.emailController,
              decoration: InputDecoration(
                labelText: '邮箱',
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
            SizedBox(height: 16),
            TextField(
              controller: controller.passwordController,
              decoration: InputDecoration(
                labelText: '密码',
                prefixIcon: Icon(Icons.lock),
              ),
              obscureText: true,
            ),
            Row(
              children: [
                Obx(() => Checkbox(
                  value: controller.rememberMe.value,
                  onChanged: (_) => controller.toggleRememberMe(),
                )),
                Text('记住我'),
                Spacer(),
                TextButton(
                  onPressed: controller.navigateToForgotPassword,
                  child: Text('忘记密码?'),
                ),
              ],
            ),
            SizedBox(height: 24),
            Obx(() => controller.errorMessage.value.isNotEmpty
                ? Text(
                    controller.errorMessage.value,
                    style: TextStyle(color: Colors.red),
                  )
                : SizedBox.shrink()),
            SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: Obx(() => ElevatedButton(
                onPressed: controller.isLoading.value
                    ? null
                    : controller.login,
                child: controller.isLoading.value
                    ? CircularProgressIndicator()
                    : Text('登录'),
              )),
            ),
            SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('还没有账号?'),
                TextButton(
                  onPressed: controller.navigateToRegister,
                  child: Text('注册'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
```

## 相关联文件

- `lib/app/services/auth_service.dart`：提供全局认证服务
- `lib/data/repositories/user_repository.dart`：处理用户数据访问
- `lib/data/providers/user_api_provider.dart`：与用户相关的API调用
- `lib/app/routes/app_routes.dart`：全局路由常量
- `lib/app/routes/app_pages.dart`：全局路由配置

## 常见问题与解决方案

- **令牌过期处理**：
  - 实现令牌刷新机制
  - 在API请求拦截器中检测401错误并自动刷新令牌
  
- **自动登录**：
  - 在应用启动时检查存储的令牌
  - 验证令牌有效性并自动恢复用户会话
  
- **安全存储**：
  - 使用安全存储机制（如Flutter Secure Storage）存储敏感信息
  - 避免在日志中打印敏感信息 