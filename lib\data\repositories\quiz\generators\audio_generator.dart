import 'package:flutter_study_flow_by_myself/data/models/quiz/choice_quiz_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/quiz/quiz_type.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_model.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/quiz/generators/distractor_utils.dart';

/// 听音选义测验题生成器
class AudioGenerator {
  // 私有构造函数，防止实例化
  AudioGenerator._();

  /// 静态生成方法
  ///
  /// [word] 目标单词
  /// [allWords] 所有单词列表，用于生成干扰项
  ///
  /// 返回生成的听音选义测验题，如果没有音频则返回null
  static ChoiceQuizModel? generate(WordModel word, List<WordModel> allWords) {
    // 如果没有音频URL，则跳过生成此题型
    if (word.audioUrl == null || word.audioUrl!.isEmpty) {
      return null;
    }

    // 使用工具类生成干扰项
    final distractors = DistractorUtils.generateDistractors(
      word,
      allWords,
      3,
      DistractorType.chineseTranslation,
    );

    // 构建选项，包含正确答案和干扰项
    final options = [word.definition, ...distractors];
    options.shuffle(); // 随机排序

    // 创建测验模型，这里的question存储音频URL
    return ChoiceQuizModel(
      wordId: word.id,
      question: word.audioUrl!,
      options: options,
      correctOptionIndex: options.indexOf(word.definition),
      type: QuizType.audioToMeaning,
    );
  }
}
