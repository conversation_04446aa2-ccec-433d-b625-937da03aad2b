# routes 目录说明文档

## 目录职责

`routes` 目录负责定义 `learning` 模块中所有页面的路由配置，包括路径常量和路由页面定义。它为整个学习模块提供统一的导航管理，确保模块内部的页面可以被正确访问，同时也允许其他模块导航到学习模块的相关页面。

## 文件结构

```
routes/
└── learning_routes.dart     # 学习模块的路由定义
```

## 核心文件说明

### `learning_routes.dart`

`learning_routes.dart` 是学习模块的路由管理文件，负责：

- **定义路由路径常量**：为每个页面定义唯一的路由路径字符串
- **创建 GetPage 对象**：将路由路径与实际页面组件及其依赖绑定关联
- **提供导出的路由列表**：供全局路由表（`app_pages.dart`）使用

## 实现详情

### 路由路径常量定义

```dart
abstract class LearningRoutes {
  // 主入口路由
  static const String main = '/learning';
  
  // 子页面路由
  static const String recall = '/learning/recall';
  static const String quiz = '/learning/quiz';
  static const String result = '/learning/result';
  static const String complete = '/learning/complete';
  static const String detail = '/learning/detail';
}
```

### GetPage 定义

```dart
class LearningPages {
  static final routes = [
    GetPage(
      name: LearningRoutes.main,
      page: () => LearningPage(),
      binding: LearningBinding(),
      children: [
        GetPage(
          name: '/recall',
          page: () => RecallPage(),
        ),
        GetPage(
          name: '/quiz',
          page: () => QuizPage(),
        ),
        GetPage(
          name: '/result',
          page: () => ResultPage(),
        ),
        GetPage(
          name: '/complete',
          page: () => CompletePage(),
        ),
        GetPage(
          name: '/detail',
          page: () => DetailPage(),
        ),
      ],
    ),
  ];
}
```

## 完整文件示例

```dart
import 'package:get/get.dart';
import '../bindings/learning_binding.dart';
import '../views/learning_page.dart';
import '../recall/views/recall_page.dart';
import '../quiz/views/quiz_page.dart';
import '../result/views/result_page.dart';
import '../complete/views/complete_page.dart';
import '../detail/views/detail_page.dart';

// 路由路径常量
abstract class LearningRoutes {
  // 主入口路由
  static const String main = '/learning';
  
  // 子页面路由（使用相对路径）
  static const String recall = '/learning/recall';
  static const String quiz = '/learning/quiz';
  static const String result = '/learning/result';
  static const String complete = '/learning/complete';
  static const String detail = '/learning/detail';
}

// 路由页面定义
class LearningPages {
  static final routes = [
    GetPage(
      name: LearningRoutes.main,
      page: () => LearningPage(),
      binding: LearningBinding(),
      children: [
        GetPage(
          name: '/recall',
          page: () => RecallPage(),
        ),
        GetPage(
          name: '/quiz',
          page: () => QuizPage(),
        ),
        GetPage(
          name: '/result',
          page: () => ResultPage(),
        ),
        GetPage(
          name: '/complete',
          page: () => CompletePage(),
        ),
        GetPage(
          name: '/detail',
          page: () => DetailPage(),
          transition: Transition.rightToLeft,
          transitionDuration: Duration(milliseconds: 250),
        ),
      ],
    ),
  ];
}
```

## 使用方式

### 在主路由表中使用

在全局路由表（`lib/app/routes/app_pages.dart`）中导入和使用学习模块的路由：

```dart
import '../../modules/learning/routes/learning_routes.dart';

class AppPages {
  static final routes = [
    ...HomePages.routes,
    ...AuthPages.routes,
    ...LearningPages.routes,  // 导入学习模块的路由
    // 其他模块路由...
  ];
}
```

### 在模块内部导航

在学习模块的控制器中使用定义的路由进行导航：

```dart
class LearningController extends GetxController {
  void navigateToCurrentStage() {
    switch (session.value.stage) {
      case LearningStage.NEW_WORDS:
      case LearningStage.REVIEW:
        Get.toNamed(LearningRoutes.recall);
        break;
      case LearningStage.QUIZ:
        Get.toNamed(LearningRoutes.quiz);
        break;
      case LearningStage.RESULT:
        Get.toNamed(LearningRoutes.result);
        break;
      case LearningStage.COMPLETE:
        Get.toNamed(LearningRoutes.complete);
        break;
    }
  }
  
  void openWordDetail(String wordId) {
    Get.toNamed(LearningRoutes.detail, parameters: {'wordId': wordId});
  }
}
```

### 从其他模块导航到学习模块

在其他模块中使用学习模块的路由：

```dart
class HomeController extends GetxController {
  void startLearning() {
    Get.toNamed(LearningRoutes.main);
  }
  
  void viewWordDetail(String wordId) {
    Get.toNamed(LearningRoutes.detail, parameters: {'wordId': wordId});
  }
}
```

## 路由设计考量

1. **嵌套路由结构**：
   - 主路由（`/learning`）作为模块入口
   - 子路由作为模块内部页面，定义为主路由的子项
   - 这种结构使得路由层次更清晰，也便于管理模块内部的导航

2. **路由参数传递**：
   - 使用 `parameters` 参数传递路由参数（如单词ID）
   - 在目标页面控制器中通过 `Get.parameters` 获取参数

3. **转场动画**：
   - 为不同类型的页面设置适合的转场动画
   - 例如，详情页使用右滑入场动画，表示进入下一级内容

4. **路由命名规范**：
   - 模块路径使用模块名（如 `/learning`）
   - 子页面路径附加在模块路径后（如 `/learning/recall`）
   - 保持一致的命名风格，便于理解和维护

## 与其他目录的关系

- **bindings目录**：路由定义中关联相应的绑定（如 `LearningBinding`）
- **views目录**：路由指向对应的页面组件
- **controllers目录**：控制器通过路由导航来切换页面
- **全局routes目录**：模块路由被导入到全局路由表中

## 最佳实践

1. **集中管理**：
   - 所有路由相关的定义都集中在 `learning_routes.dart` 中
   - 避免在多个文件中定义路由，以免造成混乱和冲突

2. **常量使用**：
   - 使用常量定义路由路径，而不是硬编码字符串
   - 这样可以避免拼写错误，也便于重构

3. **清晰文档**：
   - 为每个路由添加注释，说明其用途和可能的参数
   - 特别是对于需要参数的路由，应当明确说明参数的名称和格式

4. **路由中间件**：
   - 如有需要，可以添加路由中间件（如认证检查）
   - 例如，确保只有登录用户才能访问学习页面

## 常见问题

1. **命名冲突**：
   - 问题：不同模块可能定义相同的子路由名称
   - 解决：始终使用完整路径（如 `/learning/detail` 而非 `/detail`）

2. **参数传递**：
   - 问题：复杂对象无法直接通过URL参数传递
   - 解决：使用 `Get.arguments` 传递复杂对象，或存储在全局状态中

3. **返回导航**：
   - 问题：特定场景下需要返回到特定页面而非上一页
   - 解决：使用 `Get.offNamed()` 或 `Get.offAllNamed()` 配合参数 