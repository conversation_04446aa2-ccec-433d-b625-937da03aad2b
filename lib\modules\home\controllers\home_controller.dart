import 'package:flutter_study_flow_by_myself/app/routes/app_pages.dart';
import 'dart:async';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/app/services/user_word_book_service.dart';
import 'package:flutter_study_flow_by_myself/app/services/review_plan_service.dart';
import 'package:flutter_study_flow_by_myself/data/models/user_word_book_model.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/learning_session_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/word_repository.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/routes/learning_routes.dart';
import 'package:flutter_study_flow_by_myself/modules/home/<USER>/message_helper.dart';
import 'package:get/get.dart';

/// 首页按钮状态枚举
///
/// 定义首页主按钮的不同显示状态，根据用户的学习进度和单词本状态动态切换
enum HomeButtonState {
  noWordBook, // 没有激活单词本 - 显示"请选择单词本"
  startLearning, // 开始学习 - 显示"开始学习"按钮
  continueAndNew, // 继续学习 + 开始新学习 - 显示两个按钮
  completedAndActive, // 单词本已完成且激活（可复习）- 显示"复习"按钮
  completedInactive, // 单词本已完成但未激活 - 显示"选择新单词本"
}

/// 首页控制器
///
/// 重构后的职责：
/// - 管理首页特有的UI状态和用户交互
/// - 通过Service获取全局状态数据（单词本、复习计划等）
/// - 处理学习会话的启动和恢复
/// - 协调首页各组件的数据展示
///
/// 架构说明：
/// - 不再直接管理全局状态，改为通过Service获取
/// - Service状态变化会自动触发UI更新
/// - 保持首页特有的业务逻辑（如学习会话检查）
class HomeController extends GetxController with GetxServiceMixin {
  // ==================== 首页特有的UI状态 ====================
  /// 每日学习单词数，默认5，现在会从单词本设置读取
  final RxInt dailyWordCount = 5.obs;

  /// 防抖定时器，用于延迟更新每日学习数量
  Timer? _updateDailyCountTimer;

  // ==================== 学习会话相关状态 ====================
  /// 是否有未完成的学习会话
  final RxBool hasOngoingSession = false.obs;

  // ==================== 通过Service获取状态的Getter ====================
  /// 当前激活的单词本（通过Service获取）
  UserWordBookModel? get activeWordBook =>
      _userWordBookService.activeWordBook.value;

  /// 是否有激活的单词本（通过Service获取）
  bool get hasActiveWordBook =>
      _userWordBookService.activeWordBook.value != null;

  /// 待复习单词数量（通过Service获取）
  int get dueReviewCount => _reviewPlanService.dueReviewCount.value;

  /// 单词本学习进度（百分比）- 通过Service获取
  int get progressPercentage {
    // TODO: 后续可以通过UserWordBookService获取进度数据
    // 目前返回一个默认值，避免UI报错
    return 0;
  }

  // ==================== 统一加载状态管理 ====================
  /// 统一的加载状态
  final RxBool isLoading = false.obs;

  /// 当前加载的操作描述
  final RxString loadingMessage = ''.obs;

  /// 特定操作的加载状态（保留关键的独立状态）
  final RxBool isLoadingNewWords = false.obs;

  /// 兼容性：复习加载状态（UI需要）
  RxBool get isLoadingReview => isLoading;

  /// 统一的加载状态管理方法
  Future<T> _withLoading<T>(
    String message,
    Future<T> Function() operation,
  ) async {
    isLoading.value = true;
    loadingMessage.value = message;
    try {
      return await operation();
    } finally {
      isLoading.value = false;
      loadingMessage.value = '';
    }
  }

  /// 计算当前首页按钮状态
  HomeButtonState get currentButtonState {
    if (!hasActiveWordBook) {
      return HomeButtonState.noWordBook;
    }

    final wordBook = activeWordBook!;

    if (wordBook.isCompleted && wordBook.isActive) {
      return HomeButtonState.completedAndActive; // 已完成且激活，可复习
    } else if (wordBook.isCompleted) {
      return HomeButtonState.completedInactive; // 已完成但未激活
    } else if (hasOngoingSession.value) {
      return HomeButtonState.continueAndNew;
    } else {
      return HomeButtonState.startLearning;
    }
  }

  // ==================== 服务依赖注入 ====================
  final LearningSessionRepository _learningSessionRepository;
  final WordRepository _wordRepository;
  final UserWordBookService _userWordBookService;
  final ReviewPlanService _reviewPlanService;
  final LogService _log;

  HomeController({
    required LearningSessionRepository learningSessionRepository,
    required WordRepository wordRepository,
    required UserWordBookService userWordBookService,
    required ReviewPlanService reviewPlanService,
    required LogService log,
  }) : _learningSessionRepository = learningSessionRepository,
       _wordRepository = wordRepository,
       _userWordBookService = userWordBookService,
       _reviewPlanService = reviewPlanService,
       _log = log;

  // ==================== 控制器生命周期 ====================
  @override
  void onInit() {
    super.onInit();
    _log.i('HomeController 初始化开始 时间: ${DateTime.now()}');
    _initializeHomePageDataAsync();

    // 设置Service状态变化监听
    _setupServiceListeners();
  }

  @override
  void onClose() {
    // 清理防抖定时器
    _updateDailyCountTimer?.cancel();
    super.onClose();
  }

  /// 设置Service状态变化监听
  ///
  /// 核心响应式机制：
  /// - 监听UserWordBookService的activeWordBook变化
  /// - 监听ReviewPlanService的dueReviewCount变化
  /// - 当Service状态变化时，自动触发UI更新
  ///
  /// 这样实现了：
  /// 1. 用户激活单词本 → Service状态变化 → 首页自动更新
  /// 2. 学习/复习完成 → Service刷新数据 → 首页自动更新
  /// 3. 其他页面操作 → Service状态变化 → 首页自动同步
  void _setupServiceListeners() {
    // 监听激活单词本变化 - 当用户切换单词本时自动更新首页
    ever(_userWordBookService.activeWordBook, (_) {
      _log.i('检测到激活单词本变化，更新UI状态');
      update(); // 触发UI更新，刷新按钮状态和单词本信息
    });

    // 监听复习计划数据变化 - 当复习数据更新时自动更新首页
    ever(_reviewPlanService.dueReviewCount, (_) {
      _log.i('检测到复习数据变化，更新UI状态');
      update(); // 触发UI更新，刷新复习按钮和数量显示
    });
  }

  // ==================== 首页数据初始化 ====================
  /// 异步初始化首页数据，不阻塞UI
  void _initializeHomePageDataAsync() async {
    try {
      // 在微任务中执行，避免阻塞UI
      await Future.microtask(() async {
        // Service会在onInit中自动加载数据，这里只需要加载首页特有的数据
        await _loadLearningSessionData();
        _log.i('HomeController 数据初始化完成 时间: ${DateTime.now()}');
      });
    } catch (e) {
      _log.e('HomeController 初始化失败: $e');
      _showErrorMessage('首页数据加载失败');
    }
  }

  /// 加载首页特有的数据（学习会话相关）
  Future<void> _loadLearningSessionData() async {
    try {
      await _withLoading('检查学习会话', () async {
        if (!hasActiveWordBook) {
          hasOngoingSession.value = false;
          return;
        }

        // 检查是否有未完成的学习会话
        final session = await _learningSessionRepository
            .getLatestUnfinishedSession(activeWordBook!.id);
        hasOngoingSession.value = session != null;

        _log.i('学习会话状态: ${session != null ? "有未完成会话" : "无未完成会话"}');
      });
    } catch (e) {
      _log.e('检查学习会话状态失败: $e');
      hasOngoingSession.value = false;
    }
  }

  // ==================== 首页UI交互功能 ====================
  /// 选择器变动时调用
  ///
  /// 🔑 优化：使用防抖机制，避免拖动滑块时频繁触发数据库更新
  void onCountChanged(int value) {
    dailyWordCount.value = value;

    // 如果有激活的单词本，使用防抖机制延迟更新
    if (hasActiveWordBook) {
      // 取消之前的定时器
      _updateDailyCountTimer?.cancel();

      // 设置新的定时器，500ms后执行更新
      _updateDailyCountTimer = Timer(const Duration(milliseconds: 500), () {
        updateDailyNewWordsCount(value);
      });
    }
  }

  /// 更新单词本的每日学习单词数量
  Future<void> updateDailyNewWordsCount(int value) async {
    try {
      if (!hasActiveWordBook) return;

      // 通过Service更新单词本设置
      await _userWordBookService.updateDailyNewWordsCount(
        activeWordBook!.id,
        value,
      );

      _log.i('更新单词本每日学习数量成功: $value');
      _showInfoMessage('每日学习单词数量已更新为 $value 个');
    } catch (e) {
      _log.e('更新单词本每日学习数量失败: $e');
      _showErrorMessage('更新每日学习单词数量失败');
    }
  }

  // ==================== 用户操作响应 ====================
  /// 用户点击继续学习按钮
  void onUserClickContinueLearning() {
    if (!_validateActiveWordBook()) return;
    if (!hasOngoingSession.value) {
      _showInfoMessage('没有未完成的学习会话，请开始新的学习');
      return;
    }

    _log.i('继续学习: 单词本ID=${activeWordBook!.id}');

    // 导航到学习页面，传递会话恢复标识
    Get.toNamed(
      LearningRoutes.RECALL,
      arguments: {
        'userWordBookId': activeWordBook!.id,
        'restoreSession': true, // 标识这是恢复会话
      },
    );
  }

  /// 用户点击开始新学习按钮
  void onUserClickStartNewLearning() async {
    if (!_validateActiveWordBook()) return;

    try {
      isLoadingNewWords.value = true;

      // 使用注入的_wordRepository获取新单词
      final words = await _wordRepository.getNewWordsFromWordBook(
        activeWordBook!.id,
        dailyWordCount.value,
      );

      if (words.isEmpty) {
        _showInfoMessage('当前单词本没有可学习的单词');
        return;
      }

      // 提取单词ID列表
      final wordIds = words.map((word) => word.id).toList();

      _log.i('开始学习新单词，共${wordIds.length}个: $wordIds');

      // 通过路由参数指定这是一个新会话、单词列表和单词本ID
      Get.toNamed(
        LearningRoutes.RECALL,
        arguments: {
          'wordIds': wordIds,
          'isNewSession': true,
          'userWordBookId': activeWordBook!.id,
        },
      );
    } catch (e) {
      _log.e('获取学习单词失败: $e');
      _showErrorMessage('获取学习单词失败');
    } finally {
      isLoadingNewWords.value = false;
    }
  }

  /// 用户点击开始复习按钮
  void onUserClickStartReview() {
    if (!_validateActiveWordBook()) return;

    if (dueReviewCount > 0) {
      _log.i('开始复习，待复习单词数量: $dueReviewCount');
      Get.toNamed(
        LearningRoutes.REVIEW_QUIZ,
        arguments: {'userWordBookId': activeWordBook!.id},
      );
    } else {
      _showInfoMessage('当前没有需要复习的单词');
    }
  }

  // ==================== 页面导航功能 ====================
  /// 用户点击前往词汇库页面
  void onGoToVocabulary() {
    Get.toNamed(AppRoutes.VOCABULARY);
  }

  // ==================== 数据验证工具 ====================
  /// 验证是否有激活的单词本
  /// 返回true表示有激活的单词本，false表示没有
  bool _validateActiveWordBook() {
    if (!hasActiveWordBook) {
      _showInfoMessage('请先激活一个单词本');
      return false;
    }
    return true;
  }

  // ==================== 用户消息提示 ====================
  /// 显示错误消息给用户
  void _showErrorMessage(String message) {
    HomeMessageHelper.showErrorMessage(message);
  }

  /// 显示提示消息给用户
  void _showInfoMessage(String message) {
    HomeMessageHelper.showInfoMessage(message);
  }

  // ==================== 数据刷新功能 ====================
  /// 通用数据刷新方法
  ///
  /// 用于在页面切换、激活单词本变化等场景下刷新首页数据
  /// 调用位置：
  /// - MainController.changeTab() 中切换到首页时调用
  /// - Service状态变化时自动调用
  Future<void> refreshData() async {
    try {
      _log.i('刷新首页数据');
      // 刷新Service数据，Service状态变化会自动触发UI更新
      await _userWordBookService.refreshWordBookData();
      await _reviewPlanService.refreshReviewPlan();

      // 刷新首页特有的数据
      await _loadLearningSessionData();
    } catch (e) {
      _log.e('刷新首页数据失败', error: e);
    }
  }
}
