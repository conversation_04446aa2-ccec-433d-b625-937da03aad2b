import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/routes/learning_routes.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/app/exceptions/app_exception.dart';
import 'learning_state_service.dart';

/// 导航类型枚举
///
/// 定义不同的导航方式，用于统一的导航方法中
enum NavigationType {
  /// 使用 Get.toNamed() - 在栈顶添加新页面
  push,

  /// 使用 Get.offNamed() - 替换当前页面
  replace,

  /// 使用 Get.offAllNamed() - 清空栈并添加新页面
  replaceAll,
}

/// 学习导航服务
///
/// 职责：
/// - 管理学习流程中的页面导航逻辑
/// - 提供统一的导航接口和异常处理
/// - 通过状态管理传递数据，避免路由参数耦合
/// - 根据学习状态智能恢复导航位置
///
/// 架构说明：
/// 本服务专注于学习模块的导航管理，通过依赖注入获取状态服务，
/// 使用统一的异常处理模式，确保导航操作的可靠性和一致性。
/// 采用状态管理传递数据的设计，避免了路由参数的复杂性。
class LearningNavigationService extends GetxService {
  // ==================== 依赖注入 ====================
  /// 学习状态服务 - 管理学习过程中的状态数据
  ///
  /// 调用位置：
  /// - navigateToDetailById() 中调用 setCurrentDetailWordId() 设置详情页单词ID
  /// - navigateToDetail() 中获取 wordIds 进行索引验证
  /// - navigateBasedOnCurrentState() 中获取 isInFinalQuiz 判断当前阶段
  ///
  /// 用途：获取学习状态信息和设置导航相关的状态数据
  final LearningStateService _stateService;

  /// 日志服务 - 记录导航操作的日志信息
  ///
  /// 调用位置：
  /// - 所有导航方法中用于记录操作日志、错误信息和调试信息
  ///
  /// 用途：记录导航操作的成功/失败状态，便于问题排查和性能监控
  final LogService _log;

  // ==================== 构造函数 ====================
  /// 构造函数 - 依赖注入
  ///
  /// 通过构造函数注入所有必要的服务实例，确保依赖关系清晰
  /// 并提高代码的可测试性和可维护性
  ///
  /// 参数：
  /// - [stateService] 学习状态服务实例
  /// - [log] 日志服务实例
  ///
  /// 注册位置：lib/modules/learning/bindings/learning_binding.dart
  LearningNavigationService({
    required LearningStateService stateService,
    required LogService log,
  }) : _stateService = stateService,
       _log = log;

  // ==================== 私有通用方法 ====================
  /// 通用导航方法，统一处理异常和日志
  ///
  /// 提供统一的导航逻辑，减少重复代码，确保所有导航操作的一致性
  ///
  /// 参数：
  /// - [routeName] 目标路由名称
  /// - [actionDescription] 操作描述，用于日志记录
  /// - [navigationType] 导航类型：push(默认)、replace、replaceAll
  ///
  /// 导航类型说明：
  /// - push: 使用 Get.toNamed()，在栈顶添加新页面
  /// - replace: 使用 Get.offNamed()，替换当前页面
  /// - replaceAll: 使用 Get.offAllNamed()，清空栈并添加新页面
  ///
  /// 异常处理：
  /// - 统一的错误日志记录
  /// - 包装为 AppException 抛出
  /// - 保留原始异常信息便于调试
  ///
  /// 返回值：无返回值（void）
  Future<void> _navigateWithErrorHandling(
    String routeName,
    String actionDescription, {
    NavigationType navigationType = NavigationType.push,
  }) async {
    try {
      _log.i('导航到$actionDescription');

      switch (navigationType) {
        case NavigationType.push:
          await Get.toNamed(routeName);
          break;
        case NavigationType.replace:
          await Get.offNamed(routeName);
          break;
        case NavigationType.replaceAll:
          await Get.offAllNamed(routeName);
          break;
      }

      _log.i('成功导航到$actionDescription');
    } catch (e) {
      _log.e('导航到$actionDescription失败', error: e);
      throw AppException(
        AppExceptionType.unknown,
        '导航到$actionDescription失败',
        originalException: e,
      );
    }
  }

  // ==================== 学习流程导航方法 ====================
  /// 导航到回忆页面
  ///
  /// 用户开始学习或从其他页面返回回忆阶段时调用
  ///
  /// 调用位置：
  /// - learning_controller.dart:292 - 新会话创建后导航到第一个单词
  /// - quiz_controller.dart:320 - 学习阶段答对后移动到下一个单词
  /// - navigateBasedOnCurrentState() - 状态恢复时的智能导航
  ///
  /// 业务逻辑：
  /// - 使用 replace 导航，替换当前页面避免返回栈混乱
  /// - RecallController 通过状态服务获取当前单词数据
  /// - 不传递路由参数，保持导航逻辑简洁
  ///
  /// 导航类型：replace - 替换当前页面，保持导航栈清洁
  ///
  /// 返回值：无返回值（void）
  Future<void> navigateToRecall() async {
    await _navigateWithErrorHandling(
      LearningRoutes.RECALL,
      '回忆页面',
      navigationType: NavigationType.replace,
    );
  }

  /// 导航到详情页面（通过单词ID）
  ///
  /// 用户点击"认识"或"不认识"按钮后导航到单词详情页面
  ///
  /// 调用位置：
  /// - recall_controller.dart:380 - 处理单词认知状态后导航
  /// - quiz_controller.dart:212 - 最终测验阶段答错后导航
  /// - navigateToDetail() - 通过单词索引导航的内部调用
  ///
  /// 业务逻辑：
  /// 1. 验证单词ID的有效性（必须大于0）
  /// 2. 通过状态服务设置当前详情页要显示的单词ID
  /// 3. 导航到详情页面，DetailController会自动获取单词数据
  ///
  /// 设计优势：
  /// - 通过状态管理传递数据，避免路由参数的复杂性
  /// - 统一的数据传递方式，便于维护和测试
  /// - 详情页控制器可以响应式监听单词ID变化
  ///
  /// 参数：
  /// - [wordId] 单词ID，必须大于0
  ///
  /// 异常处理：
  /// - 验证单词ID有效性，无效时抛出AppException
  /// - 保留原始异常信息，便于调试
  ///
  /// 导航类型：push - 在当前页面上添加详情页
  ///
  /// 返回值：无返回值（void）
  Future<void> navigateToDetailById(int wordId) async {
    try {
      _log.i('准备导航到详情页面: 单词ID=$wordId');

      if (wordId <= 0) {
        _log.w('导航到详情页面失败: 单词ID无效 $wordId');
        throw AppException(AppExceptionType.unknown, '导航失败: 单词ID无效');
      }

      // 🔑 关键设计：通过状态管理传递数据，而不是路由参数
      // 这样可以避免路由参数的序列化/反序列化开销
      // 同时让DetailController可以响应式监听数据变化
      _stateService.setCurrentDetailWordId(wordId);

      // 使用通用导航方法，push类型在当前页面上添加详情页
      await _navigateWithErrorHandling(
        LearningRoutes.DETAIL,
        '详情页面(单词ID:$wordId)',
        navigationType: NavigationType.push,
      );
    } catch (e) {
      if (e is AppException) {
        _log.e('导航到详情页面失败: ${e.message}', error: e);
        rethrow;
      } else {
        _log.e('导航到详情页面失败', error: e);
        throw AppException(
          AppExceptionType.unknown,
          '导航到详情页面失败',
          originalException: e,
        );
      }
    }
  }

  /// 导航到详情页面（通过单词索引）
  ///
  /// 便利方法，通过单词在学习列表中的索引导航到详情页面
  ///
  /// 调用位置：
  /// - quiz_controller.dart:222 - 学习阶段或最终测验阶段的fallback导航
  ///
  /// 业务逻辑：
  /// 1. 验证单词索引的有效性（范围检查）
  /// 2. 通过索引从状态服务获取对应的单词ID
  /// 3. 委托给 navigateToDetailById() 方法执行实际导航
  ///
  /// 设计说明：
  /// - 这是一个便利方法，内部转换索引为ID后调用主要方法
  /// - 避免重复的导航逻辑，保持代码DRY原则
  /// - 提供索引和ID两种导航方式，满足不同场景需求
  ///
  /// 参数：
  /// - [wordIndex] 单词在学习列表中的索引，从0开始
  ///
  /// 异常处理：
  /// - 验证索引范围，超出范围时抛出AppException
  /// - 委托给主要方法处理，保持异常处理的一致性
  ///
  /// 返回值：无返回值（void）
  Future<void> navigateToDetail(int wordIndex) async {
    try {
      _log.i('准备导航到详情页面: 单词索引=$wordIndex');

      // 验证索引范围
      if (wordIndex < 0 || wordIndex >= _stateService.wordIds.length) {
        _log.w(
          '导航到详情页面失败: 单词索引无效 $wordIndex，有效范围: 0-${_stateService.wordIds.length - 1}',
        );
        throw AppException(AppExceptionType.unknown, '导航失败: 单词索引无效');
      }

      // 通过索引获取单词ID
      final wordId = _stateService.wordIds[wordIndex];
      _log.d('单词索引 $wordIndex 对应的单词ID: $wordId');

      // 委托给主要的导航方法
      await navigateToDetailById(wordId);
    } catch (e) {
      if (e is AppException) {
        rethrow;
      } else {
        _log.e('导航到详情页面失败', error: e);
        throw AppException(
          AppExceptionType.unknown,
          '导航到详情页面失败',
          originalException: e,
        );
      }
    }
  }

  // ==================== 测验阶段导航方法 ====================
  /// 导航到测验页面
  ///
  /// 从详情页面返回测验页面时调用，继续当前单词的测验
  ///
  /// 调用位置：
  /// - 目前代码中未发现直接调用，可能在详情页面的返回逻辑中使用
  ///
  /// 业务逻辑：
  /// - QuizController 通过状态服务获取当前测验数据
  /// - 不传递路由参数，保持导航逻辑简洁
  /// - 使用push导航，在详情页上添加测验页
  ///
  /// 导航类型：push - 在当前页面上添加测验页
  ///
  /// 返回值：无返回值（void）
  Future<void> navigateToQuiz() async {
    await _navigateWithErrorHandling(
      LearningRoutes.QUIZ,
      '测验页面',
      navigationType: NavigationType.push,
    );
  }

  /// 导航到最终测验页面
  ///
  /// 当所有单词学习完成后，进入随机测验阶段时调用
  ///
  /// 调用位置：
  /// - navigateBasedOnCurrentState() - 状态恢复时的智能导航
  ///
  /// 业务逻辑：
  /// - 进入最终的随机测验阶段，测试所有学过的单词
  /// - QuizController 通过状态服务获取随机测验数据
  /// - 使用replace导航而非replaceAll，避免清空首页
  ///
  /// 🔧 重要修复：
  /// - 原来使用Get.offAllNamed()会清空包括首页在内的所有页面
  /// - 现在使用replace导航，只替换当前学习流程页面
  /// - 保留首页控制器，避免不必要的资源释放
  ///
  /// 导航类型：replace - 替换当前页面，保留首页
  ///
  /// 返回值：无返回值（void）
  Future<void> navigateToFinalQuiz() async {
    await _navigateWithErrorHandling(
      LearningRoutes.QUIZ,
      '最终测验页面',
      navigationType: NavigationType.replace,
    );
  }

  /// 导航到结果页面
  ///
  /// 学习会话完成后导航到结果统计页面
  ///
  /// 调用位置：
  /// - learning_controller.dart:386,478 - 无题目时或正常完成时导航
  /// - quiz_controller.dart:277,300 - 随机测验完成后导航
  ///
  /// 业务逻辑：
  /// - 显示本次学习会话的统计结果
  /// - ResultController 通过状态服务获取统计数据
  /// - 用户可以查看学习成果和选择下一步操作
  ///
  /// 导航类型：push - 在当前页面上添加结果页
  ///
  /// 返回值：无返回值（void）
  Future<void> navigateToResult() async {
    await _navigateWithErrorHandling(
      LearningRoutes.RESULT,
      '结果页面',
      navigationType: NavigationType.push,
    );
  }

  // ==================== 智能导航方法 ====================
  /// 根据当前状态导航到正确的页面
  ///
  /// 用于恢复学习会话时智能确定应该导航到哪个页面
  ///
  /// 调用位置：
  /// - learning_controller.dart:337 - 从数据库恢复学习会话后调用
  ///
  /// 业务逻辑：
  /// 1. 检查当前是否处于最终测验阶段
  /// 2. 根据学习阶段导航到对应页面
  /// 3. 确保用户能够从中断的地方继续学习
  ///
  /// 导航逻辑：
  /// - 如果 isInFinalQuiz = true：导航到最终测验页面
  /// - 如果 isInFinalQuiz = false：导航到回忆页面
  ///
  /// 设计优势：
  /// - 智能恢复用户的学习进度
  /// - 避免用户重复已完成的学习内容
  /// - 提供无缝的学习体验
  ///
  /// 返回值：无返回值（void）
  Future<void> navigateBasedOnCurrentState() async {
    try {
      _log.i(
        '根据当前状态智能导航: 当前索引=${_stateService.currentWordIndex}, 最终测验=${_stateService.isInFinalQuiz}',
      );

      if (_stateService.isInFinalQuiz) {
        // 如果在最终测验阶段，导航到最终测验页面
        _log.i('检测到最终测验阶段，导航到最终测验页面');
        await navigateToFinalQuiz();
      } else {
        // 否则导航到回忆页面
        _log.i('检测到学习阶段，导航到回忆页面');
        await navigateToRecall();
      }
    } catch (e) {
      _log.e('根据当前状态导航失败', error: e);
      throw AppException(
        AppExceptionType.unknown,
        '根据当前状态导航失败',
        originalException: e,
      );
    }
  }

  // ==================== 退出和完成导航方法 ====================
  /// 导航到首页
  ///
  /// 用户完成学习或选择退出学习流程时调用
  ///
  /// 调用位置：
  /// - result_controller.dart:320,346 - 用户选择返回首页或开始新学习
  ///
  /// 业务逻辑：
  /// - 结束当前学习流程，返回应用主页面
  /// - 清空学习相关的页面栈，释放学习流程资源
  /// - 用户可以选择新的学习内容或进行其他操作
  ///
  /// 🔧 修复说明：
  /// - 改为异步方法，与其他导航方法保持一致
  /// - 使用统一的异常处理模式
  /// - 使用replaceAll导航，清空学习流程返回首页
  ///
  /// 导航类型：replaceAll - 清空所有页面并导航到首页
  ///
  /// 返回值：无返回值（void）
  Future<void> navigateToHome() async {
    await _navigateWithErrorHandling(
      '/main',
      '首页',
      navigationType: NavigationType.replaceAll,
    );
  }

  /// 导航到单词本完成庆祝页面
  ///
  /// 当用户完成整个单词本的学习时调用，显示庆祝页面
  ///
  /// 调用位置：
  /// - learning_controller.dart:475 - 单词本完成时导航
  ///
  /// 业务逻辑：
  /// - 显示单词本完成的庆祝界面
  /// - 提供成就感和学习激励
  /// - 用户可以选择学习新的单词本或查看学习统计
  ///
  /// 🔧 修复说明：
  /// - 改为异步方法，与其他导航方法保持一致
  /// - 使用统一的异常处理模式
  /// - 使用replaceAll导航，清空学习流程进入庆祝页面
  ///
  /// 导航类型：replaceAll - 清空所有页面并导航到庆祝页面
  ///
  /// 返回值：无返回值（void）
  Future<void> navigateToWordBookCompletion() async {
    await _navigateWithErrorHandling(
      LearningRoutes.WORD_BOOK_COMPLETION,
      '单词本完成庆祝页面',
      navigationType: NavigationType.replaceAll,
    );
  }
}
