# constants 目录说明文档

## 目录职责

`constants` 目录负责存储整个应用程序中使用的常量定义，包括配置参数、固定值、枚举类型等。将常量集中管理可以提高代码的可维护性，避免魔法字符串和魔法数字的出现，并使得这些值在整个应用中保持一致。

## 文件结构

```
constants/
├── api_constants.dart        # API相关常量，如基础URL、端点路径、超时设置等
├── app_constants.dart        # 应用级常量，如版本号、应用名称、缓存键等
├── ui_constants.dart         # UI相关常量，如间距、动画时长、最大宽度等
├── enum_constants.dart       # 应用中使用的枚举类型定义
└── error_constants.dart      # 错误码和错误消息定义
```

## 使用方式

常量定义采用大写下划线风格（SCREAMING_SNAKE_CASE），例如：

```dart
// api_constants.dart
const String API_BASE_URL = 'https://api.example.com/v1';
const int API_TIMEOUT_SECONDS = 30;

// app_constants.dart
const String APP_NAME = '单词学习';
const String USER_TOKEN_KEY = 'user_token';

// ui_constants.dart
const double DEFAULT_PADDING = 16.0;
const Duration ANIMATION_DURATION = Duration(milliseconds: 300);

// enum_constants.dart
enum LearningStage {
  NEW_WORDS,
  REVIEW,
  QUIZ,
  RESULT,
  COMPLETE
}

// error_constants.dart
const Map<int, String> ERROR_MESSAGES = {
  400: '请求参数错误',
  401: '未授权，请重新登录',
  500: '服务器错误，请稍后重试'
};
```

## 最佳实践

1. **命名规范**：
   - 使用大写下划线命名法（SCREAMING_SNAKE_CASE）
   - 名称应清晰表达常量的用途
   - 相关常量应使用相似的前缀便于识别

2. **组织结构**：
   - 按功能或模块将常量分组到不同文件
   - 相关常量放在一起
   - 为大型枚举或常量组添加适当的注释

3. **类型安全**：
   - 优先使用枚举（enum）而非字符串常量表示有限集合
   - 为常量提供明确的类型定义
   - 考虑使用类或对象存储相关常量组

4. **文档说明**：
   - 为非自解释的常量添加注释
   - 对敏感数值（如超时时间、最大尝试次数）提供选择该值的理由
   - 标记可能需要根据环境变化的配置常量

## 注意事项

- 避免在此目录存储敏感信息（如API密钥），应考虑使用环境变量或加密存储
- 避免定义过于具体或仅在单个模块使用的常量，这类常量应该在相应模块中定义
- 定期审查和清理未使用的常量
- 保持向后兼容性，修改常量值可能影响整个应用

## 与其他目录的关系

- **utils目录**：常量可能被工具函数使用
- **themes目录**：UI常量可能与主题设置相关
- **services目录**：服务可能使用API常量和配置常量
- **各业务模块**：使用全局常量确保一致性

## 扩展计划

- 为不同环境（开发、测试、生产）提供不同的配置常量
- 支持从服务器远程获取部分可变配置常量
- 实现多语言支持的字符串常量 