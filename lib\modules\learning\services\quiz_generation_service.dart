import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/quiz/quiz_repository.dart';
import 'package:flutter_study_flow_by_myself/data/repositories/word_repository.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/app/exceptions/app_exception.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_model.dart';
import '../services/learning_state_service.dart';

/// 测验题生成服务
///
/// 职责：
/// - 即时生成单个单词的测验题，避免批量生成的内存占用
/// - 管理当前测验题的响应式状态，支持UI实时更新
/// - 为随机测验智能选择需要练习的单词（完成次数<3）
/// - 提供高性能的单词缓存机制，减少重复数据库查询
///
/// 架构说明：
/// 本服务采用即时生成策略，避免了批量生成的复杂性和内存占用。
/// 使用响应式状态管理，确保UI能够实时响应题目变化。
/// 通过智能的单词选择算法，确保随机测验的有效性和针对性。
/// 集成缓存机制，优化性能并减少数据库访问频率。
class QuizGenerationService extends GetxService {
  // ==================== 依赖注入 ====================
  /// 测验题仓库 - 处理测验题的生成逻辑
  ///
  /// 调用位置：
  /// - generateQuizForWord() 中调用 generateRandomQuizForWord() 生成题目
  ///
  /// 用途：根据单词和干扰项生成不同类型的测验题
  final QuizRepository _quizRepository;

  /// 单词仓库 - 处理单词数据的获取
  ///
  /// 调用位置：
  /// - generateQuizForWord() 中调用 getWordById() 获取单词对象
  /// - _getAllWordsWithCache() 中调用 getAllWords() 获取所有单词
  ///
  /// 用途：获取单词详细信息和干扰项列表
  final WordRepository _wordRepository;

  /// 学习状态服务 - 管理学习状态和进度
  ///
  /// 调用位置：
  /// - _selectWordForRandomQuiz() 中调用 getAllWordStates() 获取学习状态
  ///
  /// 用途：根据学习状态选择需要练习的单词
  final LearningStateService _stateService;

  /// 日志服务 - 记录题目生成和选择的日志
  ///
  /// 调用位置：
  /// - 所有方法中用于记录操作日志、错误信息和调试信息
  ///
  /// 用途：记录题目生成过程和单词选择的成功/失败状态
  final LogService _log;

  // ==================== 私有状态变量 ====================
  /// 单词缓存 - 避免重复获取所有单词
  ///
  /// 用途：
  /// - 缓存所有单词列表，减少数据库查询频率
  /// - 在题目生成过程中提供干扰项
  /// - 提高性能，特别是在连续生成多道题目时
  ///
  /// 缓存策略：
  /// - 首次使用时从数据库加载
  /// - 后续使用直接从缓存获取
  /// - 服务重启时自动清空缓存
  List<WordModel>? _cachedAllWords;

  // ==================== 响应式状态变量 ====================
  /// 当前测验题（响应式）
  ///
  /// 存储当前生成的测验题，可以是 ChoiceQuizModel 或 SpellingQuizModel
  ///
  /// 调用位置：
  /// - quiz_controller.dart:101,114,243,274 - 监听和获取当前题目
  /// - detail_controller.dart:354,357 - 生成题目后的状态检查
  /// - learning_controller.dart:381 - 检查题目生成结果
  ///
  /// 响应式特性：
  /// - 支持UI监听题目变化，自动更新显示
  /// - 题目生成完成后自动通知相关控制器
  /// - 支持空值状态，表示没有可用题目
  ///
  /// 用途：UI组件获取当前题目内容，控制器监听题目变化
  final Rx<dynamic> currentQuiz = Rx<dynamic>(null);

  // ==================== 构造函数 ====================
  /// 构造函数 - 依赖注入
  ///
  /// 通过构造函数注入所有必要的仓库和服务实例，确保依赖关系清晰
  /// 并提高代码的可测试性和可维护性
  ///
  /// 参数：
  /// - [quizRepository] 测验题仓库实例
  /// - [wordRepository] 单词仓库实例
  /// - [stateService] 学习状态服务实例
  /// - [log] 日志服务实例
  ///
  /// 注册位置：lib/modules/learning/bindings/learning_binding.dart
  QuizGenerationService({
    required QuizRepository quizRepository,
    required WordRepository wordRepository,
    required LearningStateService stateService,
    required LogService log,
  }) : _quizRepository = quizRepository,
       _wordRepository = wordRepository,
       _stateService = stateService,
       _log = log;

  // ==================== 私有通用方法 ====================
  /// 通用异常处理方法
  ///
  /// 提供统一的异常处理逻辑，减少重复代码，确保所有操作的一致性
  ///
  /// 参数：
  /// - [operationName] 操作名称，用于日志记录和错误信息
  /// - [operation] 要执行的异步操作函数
  ///
  /// 异常处理逻辑：
  /// - 如果是AppException，记录详细错误信息并重新抛出
  /// - 如果是其他异常，记录错误信息并重置状态
  /// - 保留原始异常信息便于调试
  ///
  /// 返回值：无返回值（Future&lt;void&gt;）
  Future<void> _handleAsyncOperation(
    String operationName,
    Future<void> Function() operation,
  ) async {
    try {
      await operation();
    } catch (e) {
      if (e is AppException) {
        _log.e('$operationName失败: ${e.message}', error: e);
        rethrow;
      } else {
        _log.e('$operationName失败', error: e);
        _resetQuizState();
      }
    }
  }

  /// 重置测验题状态
  ///
  /// 统一的状态重置方法，避免重复代码
  ///
  /// 调用位置：
  /// - generateQuizForWord() 中出错时重置状态
  /// - generateRandomQuiz() 中出错时重置状态
  /// - _handleAsyncOperation() 中异常处理时重置状态
  ///
  /// 重置内容：
  /// - 将 currentQuiz 设置为 null，表示没有可用题目
  ///
  /// 用途：确保UI能够正确处理无题目状态
  void _resetQuizState() {
    currentQuiz.value = null;
  }

  /// 获取所有单词（带缓存）
  ///
  /// 提供高性能的单词获取机制，避免重复数据库查询
  ///
  /// 调用位置：
  /// - generateQuizForWord() 中获取干扰项列表
  ///
  /// 缓存策略：
  /// - 首次调用时从数据库加载并缓存
  /// - 后续调用直接返回缓存数据
  /// - 服务重启时自动清空缓存
  ///
  /// 性能优化：
  /// - 减少数据库访问频率
  /// - 提高连续题目生成的性能
  /// - 降低内存占用（相比批量生成题目）
  ///
  /// 异常处理：
  /// - 获取失败时返回空列表
  /// - 记录详细的错误信息
  ///
  /// 返回值：List&lt;WordModel&gt; - 所有单词列表，失败时返回空列表
  Future<List<WordModel>> _getAllWordsWithCache() async {
    if (_cachedAllWords != null) {
      return _cachedAllWords!;
    }

    try {
      _cachedAllWords = await _wordRepository.getAllWords();
      _log.d('缓存所有单词，数量: ${_cachedAllWords!.length}');
      return _cachedAllWords!;
    } catch (e) {
      _log.e('获取所有单词失败', error: e);
      return [];
    }
  }

  // ==================== 题目生成方法 ====================
  /// 为指定单词生成一道测验题并设置为当前题目
  ///
  /// 根据单词ID生成对应的测验题，支持选择题和拼写题等多种类型
  ///
  /// 调用位置：
  /// - detail_controller.dart:354,364 - 详情页生成题目
  /// - learning_controller.dart:381 - 学习控制器生成题目
  /// - generateRandomQuiz() 中为选中的单词生成题目
  ///
  /// 业务逻辑：
  /// 1. 根据单词ID获取单词详细信息
  /// 2. 获取所有单词作为干扰项（使用缓存优化性能）
  /// 3. 调用仓库层生成随机类型的测验题
  /// 4. 更新响应式状态，通知UI更新
  ///
  /// 参数：
  /// - [wordId] 单词ID，用于获取单词信息和生成题目
  ///
  /// 性能优化：
  /// - 使用缓存机制获取干扰项，避免重复数据库查询
  /// - 即时生成策略，避免内存占用
  ///
  /// 异常处理：
  /// - 使用统一异常处理方法
  /// - 出错时重置状态，确保UI正常显示
  ///
  /// 返回值：无返回值（Future&lt;void&gt;）
  Future<void> generateQuizForWord(int wordId) async {
    await _handleAsyncOperation('为单词生成测验题', () async {
      _log.i('为单词 $wordId 生成测验题');

      // 获取单词
      final word = await _wordRepository.getWordById(wordId);
      if (word == null) {
        _log.w('找不到单词: $wordId');
        _resetQuizState();
        return;
      }

      // 获取所有单词（用于生成干扰项）- 使用缓存优化性能
      final allWords = await _getAllWordsWithCache();
      if (allWords.isEmpty) {
        _log.w('获取所有单词失败，无法生成干扰项');
        _resetQuizState();
        return;
      }

      // 生成一道测验题
      final quiz = await _quizRepository.generateRandomQuizForWord(
        word,
        allWords,
      );

      if (quiz != null) {
        currentQuiz.value = quiz;
        _log.i('成功为单词 ${word.spelling} 生成测验题');
      } else {
        _log.w('未能为单词 $wordId 生成测验题');
        _resetQuizState();
      }
    });
  }

  /// 为随机测验生成一道题目
  ///
  /// 智能选择一个还需要练习的单词（完成次数<3）并生成测验题
  ///
  /// 调用位置：
  /// - quiz_controller.dart:271,294 - 启动随机测验和生成下一道随机题
  /// - learning_controller.dart:380 - 进入最终测验阶段时生成第一道题
  /// - detail_controller.dart:357 - 详情页无法获取单词ID时的备选方案
  ///
  /// 业务逻辑：
  /// 1. 调用单词选择算法，找到需要练习的单词
  /// 2. 如果找到合适的单词，为其生成测验题
  /// 3. 如果没有需要练习的单词，重置状态表示测验完成
  ///
  /// 智能选择策略：
  /// - 优先选择完成次数少于3次的单词
  /// - 随机化选择，避免固定顺序
  /// - 确保测验的有效性和针对性
  ///
  /// 异常处理：
  /// - 使用统一异常处理方法
  /// - 出错时重置状态，确保UI正常显示
  ///
  /// 返回值：无返回值（Future&lt;void&gt;）
  Future<void> generateRandomQuiz() async {
    await _handleAsyncOperation('生成随机测验题', () async {
      _log.i('生成随机测验题');

      // 选择需要练习的单词
      final wordId = await _selectWordForRandomQuiz();
      if (wordId > 0) {
        await generateQuizForWord(wordId);
      } else {
        // 所有单词都完成了
        _log.i('所有单词都已完成练习');
        _resetQuizState();
      }
    });
  }

  // ==================== 单词选择方法 ====================
  /// 选择一个需要练习的单词（完成次数<3）
  ///
  /// 智能选择算法，根据学习状态选择最需要练习的单词
  ///
  /// 调用位置：
  /// - generateRandomQuiz() 中选择随机测验的目标单词
  ///
  /// 选择策略：
  /// 1. 获取所有单词的学习状态
  /// 2. 筛选出完成次数少于3次的单词
  /// 3. 随机选择一个单词，确保测验的随机性
  /// 4. 如果没有需要练习的单词，返回-1表示测验完成
  ///
  /// 业务逻辑：
  /// - 完成次数<3的单词被认为需要继续练习
  /// - 随机化选择避免固定的练习顺序
  /// - 确保每个需要练习的单词都有被选中的机会
  ///
  /// 异常处理：
  /// - 获取状态失败时返回-1
  /// - 记录详细的错误信息便于调试
  ///
  /// 返回值：int - 选中的单词ID，没有需要练习的单词时返回-1
  Future<int> _selectWordForRandomQuiz() async {
    try {
      // 使用注入的状态服务
      final allWordStates = _stateService.getAllWordStates();

      // 找到还需要练习的单词（完成次数<3）
      final needPracticeWords =
          allWordStates.entries
              .where((entry) => entry.value.completedQuizzes < 3)
              .map((entry) => entry.key)
              .toList();

      if (needPracticeWords.isEmpty) {
        _log.i('没有需要练习的单词');
        return -1;
      }

      // 随机选择一个单词
      needPracticeWords.shuffle();
      final selectedWordId = needPracticeWords.first;

      _log.d('选择单词 $selectedWordId 进行随机测验');
      return selectedWordId;
    } catch (e) {
      _log.e('选择随机测验单词失败', error: e);
      return -1;
    }
  }
}
