import 'dart:async';

import 'package:flutter_study_flow_by_myself/app/exceptions/app_exception.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/app/services/network_service.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_book_model.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_model.dart';
import 'package:flutter_study_flow_by_myself/data/mock/mock_word_books.dart';

/// 单词本下载结果类
class WordBookDownloadResult {
  final WordBookModel wordBook;
  final List<WordModel> words;

  WordBookDownloadResult({required this.wordBook, required this.words});
}

/// WordBookRemoteProvider 负责与远程API交互，获取单词本相关数据
///
/// 主要功能：
/// - 获取可用单词本列表
/// - 下载特定单词本的详细内容（包含单词）
/// - 检查单词本更新
class WordBookRemoteProvider {
  // final NetworkService _networkService;
  final LogService _log;

  // 模拟网络延迟
  final Duration _simulatedDelay = const Duration(milliseconds: 800);

  WordBookRemoteProvider({
    required NetworkService networkService,
    required LogService log,
  }) : _log = log;

  /// 获取可用的单词本列表
  ///
  /// 返回远程服务器上可用的单词本基本信息列表
  Future<List<WordBookModel>> getAvailableWordBooks() async {
    try {
      _log.i('获取可用单词本列表');

      // 模拟网络请求延迟
      await Future.delayed(_simulatedDelay);

      // 在实际应用中，这里应该使用NetworkService发起真实的网络请求
      // 例如：
      // final response = await _networkService.get<List<WordBookModel>>(
      //   '/api/wordbooks',
      //   fromJsonT: (json) => WordBookModel.fromJsonList(json),
      // );
      // return response.data ?? [];

      // 现在使用模拟数据
      return mockWordBooks;
    } catch (e) {
      _log.e('获取可用单词本列表失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '远程数据：获取可用单词本列表失败',
        type: AppExceptionType.network,
      );
    }
  }

  /// 下载单词本详细内容
  ///
  /// [wordBookId] 要下载的单词本ID
  /// [onProgress] 可选的进度回调，用于更新下载进度
  /// 返回下载结果，包含单词本信息和单词列表
  Future<WordBookDownloadResult> downloadWordBook(
    int wordBookId, {
    Function(double progress)? onProgress,
  }) async {
    try {
      _log.i('下载单词本: $wordBookId');

      // 模拟下载进度
      if (onProgress != null) {
        // 模拟进度更新
        for (int i = 1; i <= 10; i++) {
          await Future.delayed(_simulatedDelay ~/ 10);
          onProgress(i / 10);
        }
      } else {
        // 无进度回调时使用普通延迟
        await Future.delayed(_simulatedDelay);
      }

      // 在实际应用中，这里应该使用NetworkService发起真实的网络请求
      // 例如：
      // final response = await _networkService.get<Map<String, dynamic>>(
      //   '/api/wordbooks/$wordBookId/download',
      //   fromJsonT: (json) => json as Map<String, dynamic>,
      //   onDownloadProgress: (received, total) {
      //     if (total != -1 && onProgress != null) {
      //       onProgress(received / total);
      //     }
      //   },
      // );
      //
      // final wordBook = WordBookModel.fromJson(response.data!['wordBook']);
      // final words = (response.data!['words'] as List)
      //     .map((w) => WordModel.fromJson(w))
      //     .toList();
      //
      // return WordBookDownloadResult(wordBook: wordBook, words: words);

      // 现在使用模拟数据
      final mockData = getMockWordBookDownload(wordBookId);
      return WordBookDownloadResult(
        wordBook: mockData.wordBook,
        words: mockData.words,
      );
    } catch (e) {
      _log.e('下载单词本失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '远程数据：下载单词本失败',
        type: AppExceptionType.network,
      );
    }
  }

  /// 检查单词本是否有更新
  ///
  /// [wordBookId] 要检查的单词本ID
  /// [version] 当前版本号
  /// 返回是否有更新可用
  Future<bool> checkWordBookUpdates(int wordBookId, String version) async {
    try {
      _log.i('检查单词本更新: $wordBookId, 当前版本: $version');

      // 模拟网络请求延迟
      await Future.delayed(_simulatedDelay);

      // 在实际应用中，这里应该使用NetworkService发起真实的网络请求
      // 例如：
      // final response = await _networkService.get<bool>(
      //   '/api/wordbooks/$wordBookId/check-update',
      //   queryParameters: {'version': version},
      //   fromJsonT: (json) => json as bool,
      // );
      // return response.data ?? false;

      // 现在随机返回是否有更新
      return DateTime.now().millisecondsSinceEpoch % 2 == 0;
    } catch (e) {
      _log.e('检查单词本更新失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '远程数据：检查单词本更新失败',
        type: AppExceptionType.network,
      );
    }
  }

  /// 获取单词本的详细信息（不包含单词）
  ///
  /// [wordBookId] 要获取的单词本ID
  /// 返回单词本的详细信息
  Future<WordBookModel> getWordBookDetails(int wordBookId) async {
    try {
      _log.i('获取单词本详细信息: $wordBookId');

      // 模拟网络请求延迟
      await Future.delayed(_simulatedDelay);

      // 在实际应用中，这里应该使用NetworkService发起真实的网络请求
      // 例如：
      // final response = await _networkService.get<WordBookModel>(
      //   '/api/wordbooks/$wordBookId',
      //   fromJsonT: (json) => WordBookModel.fromJson(json),
      // );
      // return response.data!;

      // 现在使用模拟数据
      return mockWordBooks.firstWhere(
        (book) => book.wordBookId == wordBookId,
        orElse: () => throw Exception('未找到ID为$wordBookId的单词本'),
      );
    } catch (e) {
      _log.e('获取单词本详细信息失败: $e', error: e);
      throw AppException.fromException(
        e,
        defaultMessage: '远程数据：获取单词本详细信息失败',
        type: AppExceptionType.network,
      );
    }
  }
}
