import 'package:flutter/material.dart';
import 'package:flutter_study_flow_by_myself/app/routes/app_pages.dart';
import 'package:flutter_study_flow_by_myself/modules/vocabulary/controllers/vocabulary_controller.dart';
import 'package:get/get.dart';

class VocabularyPage extends GetView<VocabularyController> {
  const VocabularyPage({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('词汇库'),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            onPressed: () => _showSearchBar(context),
            icon: const Icon(Icons.search),
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        return RefreshIndicator(
          onRefresh: controller.loadWordBooks,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSearchBar(),
                _buildFilterToggle(),

                // 本地词书区域
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    '我的词书',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                ),
                _buildLocalWordBooks(),

                // 远程词书区域（仅在不过滤时显示）
                if (!controller.isFilteringLocal.value) ...[
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      '可下载词书',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                  ),
                  _buildRemoteWordBooks(),
                ],

                // 下载进度指示器
                _buildDownloadProgress(),
              ],
            ),
          ),
        );
      }),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: TextField(
        decoration: const InputDecoration(
          hintText: '搜索词书...',
          prefixIcon: Icon(Icons.search),
          border: OutlineInputBorder(),
        ),
        onChanged: controller.setSearchQuery,
      ),
    );
  }

  Widget _buildFilterToggle() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        children: [
          Obx(
            () => Checkbox(
              value: controller.isFilteringLocal.value,
              onChanged: (_) => controller.toggleFilter(),
            ),
          ),
          const Text('只显示已下载的词书'),
        ],
      ),
    );
  }

  Widget _buildLocalWordBooks() {
    return Obx(() {
      final wordBooks = controller.filteredLocalWordBooks;

      if (wordBooks.isEmpty) {
        return const Center(
          child: Padding(
            padding: EdgeInsets.all(32.0),
            child: Text('没有已下载的词书'),
          ),
        );
      }

      return ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: wordBooks.length,
        itemBuilder: (context, index) {
          final book = wordBooks[index];
          final isActive = book.isActive;

          return Card(
            margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
            // 激活状态的词书使用不同的颜色
            color: isActive ? Colors.green.shade50 : null,
            child: ListTile(
              leading: Icon(
                Icons.library_books,
                color: isActive ? Colors.green : Colors.grey,
              ),
              title: Text(
                book.wordBook.value?.name ?? '未知词书',
                style: TextStyle(
                  fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                ),
              ),
              subtitle: Text('${book.wordBook.value?.wordCount ?? 0}个单词'),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 根据激活状态显示不同的按钮
                  if (isActive) ...[
                    // 已激活状态：显示停用按钮
                    ElevatedButton.icon(
                      icon: const Icon(Icons.cancel),
                      label: const Text('停用'),
                      onPressed: () => controller.deactivateWordBook(book.id),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ] else ...[
                    // 未激活状态：显示激活按钮
                    ElevatedButton.icon(
                      icon: const Icon(Icons.check_circle),
                      label: const Text('激活'),
                      onPressed: () => controller.activateWordBook(book.id),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                  const SizedBox(width: 8),
                  const Icon(Icons.arrow_forward_ios),
                ],
              ),
              onTap: () {
                // 导航到词书详情页
                Get.toNamed(
                  AppRoutes.WORD_BOOK_DETAIL,
                  arguments: {'userWordBookId': book.id},
                );
              },
            ),
          );
        },
      );
    });
  }

  Widget _buildRemoteWordBooks() {
    return Obx(() {
      final wordBooks = controller.filteredRemoteWordBooks;

      if (wordBooks.isEmpty) {
        return Center(
          child: Padding(
            padding: const EdgeInsets.all(32.0),
            child: Column(
              children: [
                const Icon(Icons.book, size: 48, color: Colors.grey),
                const SizedBox(height: 16),
                const Text('没有可下载的词书'),
                if (controller.remoteWordBooks.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      '所有词书已下载',
                      style: TextStyle(
                        color: Colors.green[800],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      }

      return ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: wordBooks.length,
        itemBuilder: (context, index) {
          final book = wordBooks[index];
          final bookId = book.wordBookId ?? 0;

          return Card(
            margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
            child: ListTile(
              leading: const Icon(Icons.cloud_download),
              title: Text(book.name),
              subtitle: Text('${book.wordCount}个单词'),
              trailing: ElevatedButton.icon(
                icon: const Icon(Icons.download),
                onPressed: () => controller.downloadWordBook(bookId),
                label: const Text('下载'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          );
        },
      );
    });
  }

  Widget _buildDownloadProgress() {
    return Obx(() {
      if (!controller.isDownloading.value) {
        return const SizedBox.shrink();
      }

      return Card(
        margin: const EdgeInsets.all(16.0),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('正在下载词书...', style: Get.textTheme.titleMedium),
              const SizedBox(height: 8),
              LinearProgressIndicator(value: controller.downloadProgress.value),
              const SizedBox(height: 16),
              if (controller.creatingReviewItemsMessage.isNotEmpty) ...[
                Text(controller.creatingReviewItemsMessage.value),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: controller.creatingReviewItemsProgress.value,
                ),
              ],
            ],
          ),
        ),
      );
    });
  }

  void _showSearchBar(BuildContext context) {
    showSearch(context: context, delegate: _WordBookSearchDelegate(controller));
  }
}

// 搜索代理类
class _WordBookSearchDelegate extends SearchDelegate<String> {
  final VocabularyController controller;

  _WordBookSearchDelegate(this.controller);

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
          controller.setSearchQuery('');
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () => close(context, ''),
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    controller.setSearchQuery(query);
    return _buildResultList();
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    controller.setSearchQuery(query);
    return _buildResultList();
  }

  Widget _buildResultList() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text('我的词书', style: Get.textTheme.titleLarge),
          ),
          Obx(() {
            final books = controller.filteredLocalWordBooks;
            return ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: books.length,
              itemBuilder: (context, index) {
                final book = books[index];
                final isActive = book.isActive;

                return Card(
                  margin: const EdgeInsets.symmetric(
                    horizontal: 16.0,
                    vertical: 4.0,
                  ),
                  color: isActive ? Colors.green.shade50 : null,
                  child: ListTile(
                    leading: Icon(
                      Icons.library_books,
                      color: isActive ? Colors.green : Colors.grey,
                    ),
                    title: Text(
                      book.wordBook.value?.name ?? '未知词书',
                      style: TextStyle(
                        fontWeight:
                            isActive ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                    subtitle: Text('${book.wordBook.value?.wordCount ?? 0}个单词'),
                    trailing:
                        isActive
                            ? const Chip(
                              label: Text('已激活'),
                              backgroundColor: Colors.green,
                              labelStyle: TextStyle(color: Colors.white),
                            )
                            : ElevatedButton.icon(
                              icon: const Icon(Icons.check_circle),
                              label: const Text('激活'),
                              onPressed: () {
                                controller.activateWordBook(book.id);
                                close(context, '');
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green,
                                foregroundColor: Colors.white,
                              ),
                            ),
                    onTap: () {
                      close(context, '');
                      Get.toNamed(
                        AppRoutes.WORD_BOOK_DETAIL,
                        arguments: {'userWordBookId': book.id},
                      );
                    },
                  ),
                );
              },
            );
          }),

          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text('可下载词书', style: Get.textTheme.titleLarge),
          ),
          Obx(() {
            final books = controller.filteredRemoteWordBooks;

            if (books.isEmpty) {
              return Center(
                child: Padding(
                  padding: const EdgeInsets.all(32.0),
                  child: Column(
                    children: [
                      const Icon(Icons.book, size: 48, color: Colors.grey),
                      const SizedBox(height: 16),
                      const Text('没有可下载的词书'),
                      if (controller.remoteWordBooks.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: Text(
                            '所有词书已下载',
                            style: TextStyle(
                              color: Colors.green[800],
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              );
            }

            return ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: books.length,
              itemBuilder: (context, index) {
                final book = books[index];
                final bookId = book.wordBookId ?? 0;

                return Card(
                  margin: const EdgeInsets.symmetric(
                    horizontal: 16.0,
                    vertical: 4.0,
                  ),
                  child: ListTile(
                    leading: const Icon(Icons.cloud_download),
                    title: Text(book.name),
                    subtitle: Text('${book.wordCount}个单词'),
                    trailing: ElevatedButton.icon(
                      icon: const Icon(Icons.download),
                      label: const Text('下载'),
                      onPressed: () {
                        close(context, '');
                        controller.downloadWordBook(bookId);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    onTap: () {
                      close(context, '');
                      controller.downloadWordBook(bookId);
                    },
                  ),
                );
              },
            );
          }),
        ],
      ),
    );
  }
}
