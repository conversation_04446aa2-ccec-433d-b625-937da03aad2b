import 'package:flutter/material.dart';

/// 学习进度条组件
///
/// 显示学习阶段和测验阶段的进度
/// - 学习阶段：显示已学习单词数/总单词数
/// - 测验阶段：显示已完成测验题数/总测验题数
class LearningProgressBar extends StatelessWidget {
  // 学习阶段进度
  final int learnedWords;
  final int totalWords;

  // 测验阶段进度
  final int completedQuizzes;
  final int totalQuizzes;

  // 是否处于最终测验阶段
  final bool isInFinalQuiz;

  const LearningProgressBar({
    super.key,
    required this.learnedWords,
    required this.totalWords,
    required this.completedQuizzes,
    required this.totalQuizzes,
    required this.isInFinalQuiz,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              // 学习阶段进度
              Expanded(
                child: _buildProgressSection(
                  context,
                  '学习阶段',
                  learnedWords,
                  totalWords,
                  isInFinalQuiz ? 1.0 : learnedWords / totalWords,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 16),
              // 测验阶段进度
              Expanded(
                child: _buildProgressSection(
                  context,
                  '测验阶段',
                  completedQuizzes,
                  totalQuizzes,
                  isInFinalQuiz ? completedQuizzes / totalQuizzes : 0.0,
                  Colors.orange,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 构建单个进度部分
  Widget _buildProgressSection(
    BuildContext context,
    String title,
    int current,
    int total,
    double progress,
    Color color,
  ) {
    // 确保进度值在0.0-1.0之间
    progress = progress.clamp(0.0, 1.0);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black54,
          ),
        ),
        const SizedBox(height: 4),

        // 进度指示
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // 进度文本
            Text(
              '$current/$total',
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
            ),
            // 百分比
            Text(
              '${(progress * 100).toInt()}%',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),

        // 进度条
        ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child: LinearProgressIndicator(
            value: progress,
            minHeight: 6,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ),
      ],
    );
  }
}
