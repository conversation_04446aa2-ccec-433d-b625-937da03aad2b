import 'package:flutter_study_flow_by_myself/data/models/word_model.dart';

/// 干扰项类型枚举，用于指定要获取的干扰项类型
enum DistractorType {
  /// 英文单词干扰项
  englishWord,

  /// 中文释义干扰项
  chineseTranslation,
}

/// 干扰项生成工具类
///
/// 用于生成各种测验题类型所需的干扰项
class DistractorUtils {
  // 私有构造函数，防止实例化
  DistractorUtils._();

  // 英文单词通用干扰项
  static final _genericEnglishDistractors = [
    'example',
    'sample',
    'test',
    'practice',
    'concept',
    'function',
    'method',
    'variable',
    'system',
    'program',
    'object',
    'class',
    'interface',
    'implement',
    'extend',
    'application',
  ];

  // 中文释义通用干扰项
  static final _genericChineseDistractors = [
    '测试',
    '样例',
    '练习',
    '示例',
    '概念',
    '功能',
    '方法',
    '变量',
    '系统',
    '程序',
    '对象',
    '类',
    '接口',
    '实现',
    '扩展',
    '应用程序',
  ];

  /// 为给定单词生成干扰项
  ///
  /// [targetWord] 目标单词，不应该包含在返回结果中
  /// [allWords] 所有可用的单词
  /// [count] 需要的干扰项数量
  /// [type] 干扰项类型
  static List<String> generateDistractors(
    WordModel targetWord,
    List<WordModel> allWords,
    int count,
    DistractorType type,
  ) {
    // 创建单词副本并移除目标单词
    final availableWords = List<WordModel>.from(allWords);
    availableWords.removeWhere((word) => word.id == targetWord.id);

    // 当可用单词不足时，添加通用干扰项
    if (availableWords.length < count) {
      final extractedValues = _extractValues(availableWords, type);
      final genericDistractors =
          type == DistractorType.englishWord
              ? _genericEnglishDistractors
              : _genericChineseDistractors;

      // 过滤掉与目标单词相同的通用干扰项
      final targetValue =
          type == DistractorType.englishWord
              ? targetWord.spelling
              : targetWord.definition;
      final filteredGenericDistractors =
          genericDistractors.where((item) => item != targetValue).toList();

      // 打乱通用干扰项并取需要的数量
      filteredGenericDistractors.shuffle();

      // 组合提取的值和通用干扰项，确保不重复且数量足够
      final result = <String>{...extractedValues};
      for (final item in filteredGenericDistractors) {
        if (result.length >= count) break;
        result.add(item);
      }

      return result.toList();
    }

    // 根据不同的干扰项类型，可能需要不同的筛选策略
    List<WordModel> candidateWords;

    switch (type) {
      case DistractorType.englishWord:
        // 尝试找到长度相似的单词作为干扰项
        candidateWords = _getSimilarLengthWords(
          targetWord,
          availableWords,
          count * 3,
        );
        break;

      case DistractorType.chineseTranslation:
        // 尝试找到释义长度相似的单词作为干扰项
        candidateWords = _getSimilarTranslationWords(
          targetWord,
          availableWords,
          count * 3,
        );
        break;
    }

    // 打乱顺序并取前count个
    candidateWords.shuffle();
    return _extractValues(candidateWords.take(count).toList(), type);
  }

  /// 根据指定类型提取单词列表中的相应值
  static List<String> _extractValues(
    List<WordModel> words,
    DistractorType type,
  ) {
    switch (type) {
      case DistractorType.englishWord:
        return words.map((w) => w.spelling).toList();
      case DistractorType.chineseTranslation:
        return words.map((w) => w.definition).toList();
    }
  }

  /// 获取与目标单词长度相似的单词
  static List<WordModel> _getSimilarLengthWords(
    WordModel targetWord,
    List<WordModel> allWords,
    int limit,
  ) {
    final targetLength = targetWord.spelling.length;

    // 根据单词长度排序
    allWords.sort((a, b) {
      final diffA = (a.spelling.length - targetLength).abs();
      final diffB = (b.spelling.length - targetLength).abs();
      return diffA.compareTo(diffB);
    });

    // 返回长度最相似的单词
    return allWords.take(limit).toList();
  }

  /// 获取与目标单词释义长度相似的单词
  static List<WordModel> _getSimilarTranslationWords(
    WordModel targetWord,
    List<WordModel> allWords,
    int limit,
  ) {
    final targetLength = targetWord.definition.length;

    // 根据释义长度排序
    allWords.sort((a, b) {
      final diffA = (a.definition.length - targetLength).abs();
      final diffB = (b.definition.length - targetLength).abs();
      return diffA.compareTo(diffB);
    });

    // 返回释义长度最相似的单词
    return allWords.take(limit).toList();
  }
}
