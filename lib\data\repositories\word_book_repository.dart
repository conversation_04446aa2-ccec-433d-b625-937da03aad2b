import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/app/exceptions/app_exception.dart';
import 'package:flutter_study_flow_by_myself/data/models/word_book_model.dart';
import 'package:flutter_study_flow_by_myself/data/providers/word_book/word_book_local_provider.dart';
import 'package:flutter_study_flow_by_myself/data/providers/word_book/word_book_remote_provider.dart';

/// WordBookRepository 负责协调本地和远程单词本数据源
///
/// 这个Repository专注于单词本本身的数据管理，不涉及用户关系。
/// 它提供单词本的基础CRUD操作，严格遵循分层架构，通过Provider层访问数据。
///
/// 主要功能：
/// - 获取本地已下载的单词本列表
/// - 获取远程可下载的单词本列表
/// - 提供单词本的基础查询功能（按ID、搜索等）
/// - 协调本地和远程数据源的操作
/// - 提供单词本的统计信息
///
/// 架构说明：
/// - 严格通过Provider层访问数据，遵循分层架构原则
/// - 专注于单词本数据本身，不处理用户关系逻辑
/// - 为其他Repository和Controller提供基础的数据服务
class WordBookRepository {
  final WordBookLocalProvider _localProvider;
  final WordBookRemoteProvider _remoteProvider;
  final LogService _log;

  WordBookRepository({
    required WordBookLocalProvider localProvider,
    required WordBookRemoteProvider remoteProvider,
    required LogService log,
  }) : _localProvider = localProvider,
       _remoteProvider = remoteProvider,
       _log = log;

  /// 获取本地所有已下载的单词本
  Future<List<WordBookModel>> getLocalWordBooks() async {
    try {
      return await _localProvider.getAllWordBooks();
    } catch (e) {
      _log.e('获取本地单词本列表失败: $e', error: e);
      rethrow;
    }
  }

  /// 根据ID获取本地单词本
  /// [id] 单词本的本地数据库ID
  Future<WordBookModel?> getLocalWordBookById(int id) async {
    try {
      return await _localProvider.getWordBookById(id);
    } catch (e) {
      _log.e('根据ID获取本地单词本失败: $e', error: e);
      rethrow;
    }
  }

  /// 根据后端ID获取本地单词本
  /// [wordBookId] 单词本的后端ID
  Future<WordBookModel?> getLocalWordBookByRemoteId(int wordBookId) async {
    try {
      return await _localProvider.getWordBookByBackendId(wordBookId);
    } catch (e) {
      _log.e('根据后端ID获取本地单词本失败: $e', error: e);
      rethrow;
    }
  }

  /// 获取远程可下载的单词本列表
  ///
  /// 从远程服务器获取所有可供下载的单词本信息。
  /// 这个方法通过RemoteProvider访问网络API，遵循分层架构。
  ///
  /// 返回：包含单词本基本信息的列表，用户可以选择下载
  /// 抛出异常：网络错误或服务器错误时抛出相应异常
  Future<List<WordBookModel>> getAvailableWordBooks() async {
    try {
      return await _remoteProvider.getAvailableWordBooks();
    } catch (e) {
      _log.e('获取远程单词本列表失败: $e', error: e);
      rethrow;
    }
  }

  /// 检查本地是否已存在指定的单词本
  ///
  /// 通过远程单词本ID检查本地是否已经下载了该单词本。
  /// 这个方法常用于避免重复下载，提高用户体验。
  ///
  /// [remoteWordBookId] 远程单词本ID
  /// 返回：true 表示已下载，false 表示未下载
  /// 抛出异常：数据库查询失败时抛出异常
  Future<bool> isWordBookDownloaded(int remoteWordBookId) async {
    try {
      final localWordBook = await getLocalWordBookByRemoteId(remoteWordBookId);
      return localWordBook != null;
    } catch (e) {
      _log.e('检查单词本是否已下载失败: $e', error: e);
      rethrow;
    }
  }

  /// 保存单词本到本地数据库
  /// [wordBook] 要保存的单词本
  /// 返回保存后的本地数据库ID
  Future<int> saveWordBook(WordBookModel wordBook) async {
    try {
      return await _localProvider.saveWordBook(wordBook);
    } catch (e) {
      _log.e('保存单词本失败: $e', error: e);
      rethrow;
    }
  }

  /// 批量保存单词本到本地数据库
  /// [wordBooks] 要保存的单词本列表
  /// 返回保存后的本地数据库ID列表
  Future<List<int>> saveWordBooks(List<WordBookModel> wordBooks) async {
    try {
      return await _localProvider.saveAllWordBooks(wordBooks);
    } catch (e) {
      _log.e('批量保存单词本失败: $e', error: e);
      rethrow;
    }
  }

  /// 删除本地单词本
  /// [id] 要删除的单词本ID
  /// 返回 true 表示删除成功，false 表示删除失败
  Future<bool> deleteWordBook(int id) async {
    try {
      return await _localProvider.deleteWordBookById(id);
    } catch (e) {
      _log.e('删除单词本失败: $e', error: e);
      rethrow;
    }
  }

  /// 获取本地单词本数量
  Future<int> getLocalWordBookCount() async {
    try {
      return await _localProvider.countWordBooks();
    } catch (e) {
      _log.e('获取本地单词本数量失败: $e', error: e);
      rethrow;
    }
  }

  /// 清空所有本地单词本数据
  /// 注意：这是危险操作，会删除所有本地单词本数据
  Future<void> clearAllLocalWordBooks() async {
    try {
      await _localProvider.clearAllWordBooks();
      _log.i('已清空所有本地单词本数据');
    } catch (e) {
      _log.e('清空本地单词本数据失败: $e', error: e);
      rethrow;
    }
  }

  /// 搜索本地单词本
  /// [keyword] 搜索关键词，会在名称和描述中搜索
  Future<List<WordBookModel>> searchLocalWordBooks(String keyword) async {
    try {
      if (keyword.trim().isEmpty) {
        return await getLocalWordBooks();
      }

      final allWordBooks = await getLocalWordBooks();
      return allWordBooks.where((wordBook) {
        final name = wordBook.name.toLowerCase();
        final description = wordBook.description.toLowerCase();
        final searchTerm = keyword.toLowerCase();

        return name.contains(searchTerm) || description.contains(searchTerm);
      }).toList();
    } catch (e) {
      _log.e('搜索本地单词本失败: $e', error: e);
      rethrow;
    }
  }

  /// 获取单词本统计信息
  /// [wordBookId] 单词本ID
  /// 返回包含统计信息的Map
  Future<Map<String, dynamic>> getWordBookStatistics(int wordBookId) async {
    try {
      final wordBook = await getLocalWordBookById(wordBookId);
      if (wordBook == null) {
        throw AppException(AppExceptionType.unknown, '单词本不存在');
      }

      // 加载关联的单词
      await wordBook.words.load();

      return {
        'id': wordBook.id,
        'name': wordBook.name,
        'description': wordBook.description,
        'totalWords': wordBook.wordCount,
        'actualWordsCount': wordBook.words.length,
        'isCustom': wordBook.isCustom,
        'remoteId': wordBook.wordBookId,
      };
    } catch (e) {
      _log.e('获取单词本统计信息失败: $e', error: e);
      rethrow;
    }
  }
}
