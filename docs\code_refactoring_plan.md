# 代码重构计划

## 🎯 重构目标

解决代码中的重复、混乱和冗余问题，提升代码质量和可维护性。

## 🔍 发现的问题

### 问题1：selectOption 方法重复
- **QuizController.selectOption(int index)** - 业务逻辑层
- **ChoiceQuizWidget._selectOption(int index)** - UI组件层
- **职责混乱**：两层都在处理选项选择逻辑

### 问题2：LearningController 代码问题
1. **索引管理混乱**：`currentWordIndex` 和 `currentQuizIndex` 职责重叠
2. **变量命名不清晰**：`quizzesByWord`、`currentPhaseQuizzes` 等
3. **状态管理复杂**：过多相互依赖的状态变量
4. **计算属性冗余**：简单的状态检查包装成 getter

## ✅ 解决方案

### 方案1：重构 selectOption 架构

#### 1.1 明确职责分离
```dart
// UI组件层：只处理UI状态
class ChoiceQuizWidget {
  void _selectOption(int index) {
    setState(() {
      selectedOptionIndex = index;
    });
    // 通过回调通知控制器
    widget.onOptionSelected?.call(index);
  }
}

// 业务逻辑层：处理答案验证和状态管理
class QuizController {
  void onOptionSelected(int index) async {
    if (hasAnswered.value) return;
    
    selectedOptionIndex.value = index;
    hasAnswered.value = true;
    
    await _learningController.onQuizAnswered(index);
  }
}
```

#### 1.2 统一回调接口
```dart
// 为所有测验组件定义统一的回调接口
abstract class QuizCallbacks {
  void onOptionSelected(int index);
  void onAnswerSubmitted(bool isCorrect);
  void onNextRequested();
}
```

### 方案2：重构 LearningController

#### 2.1 简化索引管理
```dart
class LearningController {
  // 移除 currentQuizIndex，统一使用 currentWordIndex
  final currentWordIndex = 0.obs;
  
  // 重命名变量，提高可读性
  final quizzesByWord = <List<dynamic>>[].obs;           // → wordQuizzes
  final currentPhaseQuizzes = <dynamic>[].obs;          // → activeQuizzes
  final wordCompletedQuizzes = <int, int>{}.obs;        // → wordQuizProgress
  
  // 简化 currentQuiz getter
  dynamic get currentQuiz {
    if (activeQuizzes.isEmpty || currentWordIndex.value >= activeQuizzes.length) {
      return null;
    }
    return activeQuizzes[currentWordIndex.value];
  }
}
```

#### 2.2 重构状态管理
```dart
// 将相关状态合并为状态对象
class WordLearningState {
  final bool isRecognized;
  final int errorCount;
  final int completedQuizzes;
  
  WordLearningState({
    this.isRecognized = false,
    this.errorCount = 0,
    this.completedQuizzes = 0,
  });
}

class LearningController {
  // 使用状态对象替代多个 Map
  final wordStates = <int, WordLearningState>{}.obs;
  
  // 简化状态访问
  WordLearningState getWordState(int wordId) {
    return wordStates[wordId] ?? WordLearningState();
  }
}
```

#### 2.3 移除冗余计算属性
```dart
class LearningController {
  // 移除这些简单的 getter，直接使用状态比较
  // bool get isLoading => state.value == LearningFlowState.loading;
  // bool get isLearning => state.value == LearningFlowState.learning;
  // bool get isQuizzing => state.value == LearningFlowState.quizzing;
  
  // 保留有实际计算逻辑的 getter
  double get totalProgress {
    if (totalQuizCount.value == 0) return 0.0;
    return (completedQuizCount.value / totalQuizCount.value).clamp(0.0, 1.0);
  }
}
```

## 📋 重构步骤

### 阶段1：修复 selectOption 重复（优先级：高）
1. 修改 `ChoiceQuizWidget`，移除业务逻辑
2. 添加 `onOptionSelected` 回调
3. 更新 `QuizController` 处理回调
4. 测试选择题功能

### 阶段2：重构 LearningController（优先级：中）
1. 重命名变量，提高可读性
2. 简化索引管理逻辑
3. 合并相关状态为状态对象
4. 移除冗余的计算属性
5. 全面测试学习流程

### 阶段3：代码清理（优先级：低）
1. 移除未使用的变量和方法
2. 统一代码风格和注释
3. 优化性能和内存使用

## 🎯 预期效果

### 代码质量提升
- ✅ **职责清晰**：UI层和业务层职责分离
- ✅ **命名清晰**：变量和方法名称更具描述性
- ✅ **结构简化**：减少冗余代码和复杂依赖
- ✅ **可维护性**：更容易理解和修改

### 功能稳定性
- ✅ **bug减少**：简化的逻辑减少出错可能
- ✅ **测试友好**：清晰的职责分离便于单元测试
- ✅ **扩展性**：统一的接口便于添加新功能

## 🚀 实施建议

### 立即执行（问题1）
selectOption 重复问题影响代码理解和维护，建议立即修复。

### 分阶段执行（问题2）
LearningController 重构涉及面较广，建议分阶段进行，每次重构后充分测试。

### 保持向后兼容
重构过程中保持API兼容性，避免破坏现有功能。
