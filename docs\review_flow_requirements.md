# 单词复习流程需求文档

## 1. 总体复习流程

单词复习应用的核心流程分为两个主要阶段：测验阶段和结果展示阶段。整个流程设计遵循科学的间隔重复记忆方法，确保用户能高效地复习和巩固已学单词。

## 2. 复习阶段

### 2.1 复习测验阶段
- 用户在首页选择"开始复习"按钮
- 系统筛选当前激活单词本中下次复习时间小于当前时间的所有单词
- 系统为每个单词随机生成3道测验题
- 系统按组（默认每组5个单词，约15道题）呈现单词
- 系统将每组内的测验题随机打乱顺序
- 用户进行测验：答对进入下一题，答错进入详情页面查看后再次作答同一题
- 当用户完成某单词的第3道题时，系统根据该单词的答题情况计算质量评分和下次复习时间
- 当前组所有测验题全部答对后，系统进入下一组
- 系统详细记录每个单词的答题情况和错误次数

### 2.2 结果展示阶段
- 当所有单词都通过复习测验后，系统进入结果展示阶段
- 系统跳转到结果页面
- 展示详细的复习统计数据
- 提供本次复习的完整总结

## 3. 复习测验流程

### 3.1 测验流程
- 系统显示当前组进度和总进度
- 自动播放单词发音（如适用）
- 用户回答当前题目
- 系统判断答案是否正确
- 答对：
  * 系统记录正确答题
  * 增加该单词的已完成测验数量
  * 如果已完成该单词的所有3道题，计算质量评分并设置下次复习时间
  * 进入下一题
- 答错：
  * 系统记录错误次数
  * 导航到该单词的详情页面
  * 用户查看完详情后返回，继续回答同一题
- 当前组所有题目都答对后，进入下一组
- 所有组的所有题目都答对后，进入结果页面

### 3.2 详情查看流程
- 系统展示单词的完整信息（拼写、音标、释义、例句等）
- 自动播放单词发音
- 用户学习完成后点击"返回测验"按钮
- 系统返回到之前的测验题，用户可以再次作答

## 4. 状态管理需求

### 4.1 状态类型
- 复习流程状态：空闲、加载中、复习中、已完成、错误

### 4.2 状态保存内容
- 当前单词组索引
- 当前测验题索引
- 单词错误次数记录(wordErrorCountMap)
- 单词测验完成情况(wordCompletedQuizzes)
- 最后复习时间(lastReviewTime)

## 5. 间隔重复复习算法

### 5.1 复习质量评分计算
- 系统在复习过程中，当单词完成所有3道测验题时，调用_recordReviewQuality方法计算复习质量
- 具体计算方法：
  * 全部答对（错误次数=0）：5分
  * 错了1次：4分
  * 错了2次：3分
  * 错了3-4次：2分
  * 错了5次或更多：1分

### 5.2 下次复习时间计算
- 当单词在复习过程中完成所有3道测验题时，系统立即调用processReviewSuccess方法处理复习结果
- 根据SM-2间隔重复算法计算下次复习间隔：
  * 评分低于3分：安排明天复习（1天）
  * 首次成功复习：1天后复习
  * 第二次成功复习：6天后复习
  * 其后每次间隔 = 上次间隔 * 难易度因子

## 6. UI交互需求

### 6.1 进度显示
- 顶部进度条显示整体复习进度（已完成题数/总题数）
- 显示当前组进度（例如："第2组/共8组"）
- 显示累计答对题数和总题数

### 6.2 测验页面
- 清晰显示当前题目内容
- 提供选项列表或输入框（根据题型）
- 答题后立即显示正误反馈
- 答错时显示"查看详情"按钮
- 答对时 底部出现下一题按钮供用户点击

### 6.3 详情页面
- 展示单词的完整信息
- 提供返回测验的按钮
- 提供播放发音的功能

### 6.4 结果页面
- 显示本次复习的总体统计
  * 复习单词总数
  * 总用时
  * 平均正确率
  * 下次需复习的单词数量及时间
- 提供返回首页的选项

## 7. 导航策略

### 7.1 页面切换逻辑
- 从测验页面答错后，导航到详情页面
- 从详情页面点击返回，导航回测验页面继续同一题
- 所有题目完成后，导航到结果页面
- 结果页面可导航回首页 