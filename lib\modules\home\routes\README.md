# routes 目录说明文档

## 目录职责

`routes` 目录负责定义 `home` 模块中所有页面的路由配置，包括路径常量和路由页面定义。它为整个首页模块提供统一的导航管理，确保模块内部的页面可以被正确访问，同时也允许其他模块导航到首页模块的相关页面。

## 文件结构

```
routes/
└── home_routes.dart     # 首页模块的路由定义
```

## 核心文件说明

### `home_routes.dart`

`home_routes.dart` 是首页模块的路由管理文件，负责：

- **定义路由路径常量**：为每个页面定义唯一的路由路径字符串
- **创建 GetPage 对象**：将路由路径与实际页面组件及其依赖绑定关联
- **提供导出的路由列表**：供全局路由表（`app_pages.dart`）使用

## 实现详情

### 路由路径常量定义

```dart
abstract class HomeRoutes {
  // 主入口路由
  static const String main = '/home';
  
  // 子页面路由
  static const String today = '/home/<USER>';
  static const String statistics = '/home/<USER>';
  static const String notifications = '/home/<USER>';
}
```

### GetPage 定义

```dart
class HomePages {
  static final routes = [
    GetPage(
      name: HomeRoutes.main,
      page: () => HomePage(),
      binding: HomeBinding(),
      children: [
        GetPage(
          name: '/today',
          page: () => TodayPage(),
        ),
        GetPage(
          name: '/statistics',
          page: () => StatisticsPage(),
        ),
        GetPage(
          name: '/notifications',
          page: () => NotificationsPage(),
        ),
      ],
    ),
  ];
}
```

## 完整文件示例

```dart
import 'package:get/get.dart';
import '../binding/home_binding.dart';
import '../views/home_page.dart';
import '../views/today_page.dart';
import '../views/statistics_page.dart';
import '../views/notifications_page.dart';

// 路由路径常量
abstract class HomeRoutes {
  // 主入口路由
  static const String main = '/home';
  
  // 子页面路由（使用相对路径）
  static const String today = '/home/<USER>';
  static const String statistics = '/home/<USER>';
  static const String notifications = '/home/<USER>';
}

// 路由页面定义
class HomePages {
  static final routes = [
    GetPage(
      name: HomeRoutes.main,
      page: () => HomePage(),
      binding: HomeBinding(),
      children: [
        GetPage(
          name: '/today',
          page: () => TodayPage(),
          transition: Transition.rightToLeft,
        ),
        GetPage(
          name: '/statistics',
          page: () => StatisticsPage(),
          transition: Transition.rightToLeft,
        ),
        GetPage(
          name: '/notifications',
          page: () => NotificationsPage(),
          transition: Transition.downToUp,
        ),
      ],
    ),
  ];
}
```

## 使用方式

### 在主路由表中使用

在全局路由表（`lib/app/routes/app_pages.dart`）中导入和使用首页模块的路由：

```dart
import '../../modules/home/<USER>/home_routes.dart';

class AppPages {
  static final routes = [
    ...HomePages.routes,
    ...AuthPages.routes,
    ...LearningPages.routes,
    // 其他模块路由...
  ];
}
```

### 在模块内部导航

在首页模块的控制器中使用定义的路由进行导航：

```dart
class HomeController extends GetxController {
  void navigateToStatistics() {
    Get.toNamed(HomeRoutes.statistics);
  }
  
  void navigateToTodayView() {
    Get.toNamed(HomeRoutes.today);
  }
  
  void viewNotifications() {
    Get.toNamed(HomeRoutes.notifications);
  }
}
```

### 从其他模块导航到首页模块

在其他模块中使用首页模块的路由：

```dart
class AuthController extends GetxController {
  void goToHome() {
    Get.offAllNamed(HomeRoutes.main);
  }
}
```

## 路由设计考量

1. **嵌套路由结构**：
   - 主路由（`/home`）作为模块入口
   - 子路由作为模块内部页面，定义为主路由的子项
   - 这种结构使得路由层次更清晰，也便于管理模块内部的导航

2. **转场动画**：
   - 为不同类型的页面设置适合的转场动画
   - 例如，子页面使用右滑入场动画，通知页面使用底部上滑动画

3. **路由命名规范**：
   - 模块路径使用模块名（如 `/home`）
   - 子页面路径附加在模块路径后（如 `/home/<USER>
   - 保持一致的命名风格，便于理解和维护

## 与其他目录的关系

- **binding目录**：路由定义中关联相应的绑定（如 `HomeBinding`）
- **views目录**：路由指向对应的页面组件
- **controllers目录**：控制器通过路由导航来切换页面
- **全局routes目录**：模块路由被导入到全局路由表中

## 最佳实践

1. **集中管理**：
   - 所有路由相关的定义都集中在 `home_routes.dart` 中
   - 避免在多个文件中定义路由，以免造成混乱和冲突

2. **常量使用**：
   - 使用常量定义路由路径，而不是硬编码字符串
   - 这样可以避免拼写错误，也便于重构

3. **清晰文档**：
   - 为每个路由添加注释，说明其用途和可能的参数
   - 特别是对于需要参数的路由，应当明确说明参数的名称和格式

4. **命名一致性**：
   - 保持路由命名的一致性，如使用小写和短横线
   - 按照功能或模块组织路由结构

## 常见问题与解决方案

1. **路由冲突**：
   - 问题：不同模块可能定义相似的路由名称
   - 解决：使用模块前缀确保路由名称唯一性，如`/home/<USER>/today`

2. **参数传递**：
   - 问题：需要在导航时传递参数
   - 解决：使用Get.toNamed()的parameters参数，或使用arguments传递复杂对象

3. **返回导航**：
   - 问题：需要从子页面返回到首页
   - 解决：使用Get.back()或Get.until()返回到特定页面

4. **深度链接**：
   - 问题：从外部链接或通知直接进入特定页面
   - 解决：在路由定义中处理深度链接逻辑，或使用中间件进行路由拦截 