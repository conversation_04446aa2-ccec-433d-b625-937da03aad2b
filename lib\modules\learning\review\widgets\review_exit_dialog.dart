import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_study_flow_by_myself/app/services/log_service.dart';
import 'package:flutter_study_flow_by_myself/modules/home/<USER>/home_controller.dart';
import 'package:flutter_study_flow_by_myself/modules/learning/review/services/review_state_service.dart';

/// 复习退出确认弹窗
///
/// 提供统一的复习流程退出确认界面，参考LearningExitDialog的设计
/// 复习流程无需保存状态，但需要刷新首页的复习数据
class ReviewExitDialog {
  static final LogService _log = Get.find<LogService>();

  /// 显示退出确认对话框
  ///
  /// [context] 上下文
  /// [customTitle] 自定义标题，默认为"退出复习"
  /// [customContent] 自定义内容，默认根据复习进度生成
  static void show({
    required BuildContext context,
    String? customTitle,
    String? customContent,
  }) {
    final stateService = Get.find<ReviewStateService>();

    // 根据复习进度生成不同的提示内容
    final content = customContent ?? _generateContentByProgress(stateService);

    Get.dialog(
      AlertDialog(
        title: Text(customTitle ?? '退出复习'),
        content: Text(content),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        actions: [
          // 继续复习按钮
          TextButton(
            onPressed: () => Get.back(),
            child: const Text(
              '继续复习',
              style: TextStyle(color: Colors.grey, fontSize: 16),
            ),
          ),

          // 退出复习按钮
          ElevatedButton(
            onPressed: () async {
              Get.back(); // 关闭对话框
              await _exitReviewSession();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('退出复习', style: TextStyle(fontSize: 16)),
          ),
        ],
      ),
      barrierDismissible: false, // 防止点击外部关闭
    );
  }

  /// 根据复习进度生成提示内容
  static String _generateContentByProgress(ReviewStateService stateService) {
    final completedCount = stateService.completedWords.length;
    final totalWords = stateService.allReviewWords.length;
    final currentGroup = stateService.currentGroupIndex + 1;
    final totalGroups = stateService.wordGroups.length;

    if (completedCount == 0) {
      return '确定要退出复习吗？\n复习进度不会保存，下次需要重新开始。';
    } else if (completedCount < 3) {
      return '您已经复习了 $completedCount 个单词。\n确定要退出吗？复习进度不会保存。';
    } else if (stateService.isAllCompleted) {
      return '恭喜！您已完成所有单词的复习！\n确定要退出吗？';
    } else {
      final progress = ((completedCount / totalWords) * 100).toInt();
      return '您已完成 $progress% 的复习进度（$completedCount/$totalWords）。\n'
          '当前第 $currentGroup 组，共 $totalGroups 组。\n'
          '确定要退出吗？复习进度不会保存。';
    }
  }

  /// 执行退出复习会话的逻辑
  static Future<void> _exitReviewSession() async {
    try {
      _log.i('用户确认退出复习，开始清理状态');

      // 显示加载提示
      Get.dialog(
        const AlertDialog(
          content: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在退出复习...'),
            ],
          ),
        ),
        barrierDismissible: false,
      );

      // 1. 等待一小段时间确保UI更新
      await Future.delayed(const Duration(milliseconds: 100));

      // 2. 刷新首页状态（更新复习数据）
      if (Get.isRegistered<HomeController>()) {
        final homeController = Get.find<HomeController>();
        await homeController.refreshData();
        _log.i('首页复习状态刷新成功');
      } else {
        _log.w('HomeController未注册，跳过状态刷新');
      }

      // 关闭加载对话框
      Get.back();

      // 3. 返回首页
      Get.offAllNamed('/main');

      // 显示退出提示
      Get.snackbar(
        '已退出复习',
        '复习进度未保存，下次需要重新开始',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.blue[700],
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      _log.e('退出复习时刷新状态失败: $e');

      // 关闭加载对话框
      if (Get.isDialogOpen == true) {
        Get.back();
      }

      // 显示错误提示但仍然退出
      Get.snackbar(
        '退出复习',
        '状态刷新失败，但已退出复习',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange[700],
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );

      // 仍然返回首页
      Get.offAllNamed('/main');
    }
  }

  /// 快速退出（无确认，用于特殊情况）
  ///
  /// [showLoading] 是否显示加载提示
  static Future<void> quickExit({bool showLoading = true}) async {
    if (showLoading) {
      Get.dialog(
        const AlertDialog(
          content: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在退出...'),
            ],
          ),
        ),
        barrierDismissible: false,
      );
    }

    await _exitReviewSession();
  }
}
