import 'package:flutter/material.dart';

/// 例句组件
///
/// 用于显示含有空白处的句子，支持高亮显示填空位置
class SentenceWidget extends StatelessWidget {
  /// 例句文本，使用 [blank] 标记空白处
  /// 例如："This is a [blank] sentence."
  final String sentence;

  /// 空白处的高亮颜色
  final Color blankColor;

  /// 构造函数
  const SentenceWidget({
    super.key,
    required this.sentence,
    this.blankColor = Colors.blue,
  });

  @override
  Widget build(BuildContext context) {
    // 解析句子，将带有 [blank] 的部分替换为空白区域
    final parts = _parseSentence();

    return RichText(
      text: TextSpan(
        style: const TextStyle(
          fontSize: 18,
          color: Colors.black87,
          height: 1.5,
        ),
        children: parts,
      ),
    );
  }

  /// 解析句子，将 [blank] 替换为下划线
  List<TextSpan> _parseSentence() {
    // 正则表达式匹配 [blank] 或其他带方括号的内容
    final pattern = RegExp(r'\[([^\]]+)\]');
    final parts = <TextSpan>[];

    // 当前处理的位置
    int currentPosition = 0;

    // 查找所有匹配项
    for (final match in pattern.allMatches(sentence)) {
      // 添加匹配项之前的文本
      if (match.start > currentPosition) {
        parts.add(
          TextSpan(text: sentence.substring(currentPosition, match.start)),
        );
      }

      // 添加下划线替代 [blank]
      final matchText = match.group(1);
      if (matchText == 'blank') {
        // 空白用下划线表示
        parts.add(
          TextSpan(
            text: '_____',
            style: TextStyle(fontWeight: FontWeight.bold, color: blankColor),
          ),
        );
      } else {
        // 如果不是 [blank]，而是其他方括号内容，保留显示内容但添加高亮
        parts.add(
          TextSpan(
            text: matchText,
            style: TextStyle(fontWeight: FontWeight.bold, color: blankColor),
          ),
        );
      }

      // 更新当前位置
      currentPosition = match.end;
    }

    // 添加剩余部分
    if (currentPosition < sentence.length) {
      parts.add(TextSpan(text: sentence.substring(currentPosition)));
    }

    return parts;
  }
}
