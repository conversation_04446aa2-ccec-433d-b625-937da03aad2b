# controllers 目录说明文档

## 目录职责

`controllers` 目录包含 `auth` 模块的所有控制器类，负责管理用户认证相关的业务逻辑和状态。这些控制器遵循MVVM架构模式，充当视图和数据模型之间的中介，处理用户交互、表单验证、API请求以及导航控制等逻辑。

## 文件结构

```
controllers/
├── auth_controller.dart       # 全局认证控制器
├── login_controller.dart      # 登录页面控制器
├── register_controller.dart   # 注册页面控制器
└── password_controller.dart   # 密码重置控制器
```

## 核心文件说明

### `auth_controller.dart`

`auth_controller.dart` 是认证模块的核心控制器，负责：

- **全局认证状态管理**：维护用户的登录状态
- **用户会话管理**：处理令牌存储和刷新逻辑
- **认证操作委托**：将具体的认证操作委托给专门的控制器
- **路由保护**：提供认证状态检查，用于路由守卫

主要属性和方法：

```dart
// 属性
final isLoggedIn = false.obs;     // 用户登录状态
final currentUser = Rxn<User>();  // 当前登录用户信息
final isLoading = false.obs;      // 加载状态

// 方法
Future<bool> checkAuth();            // 检查认证状态
Future<void> login(String email, String password); // 登录方法
Future<void> logout();               // 退出登录
Future<void> refreshToken();         // 刷新令牌
```

### `login_controller.dart`

`login_controller.dart` 负责处理登录页面的业务逻辑，包括：

- **表单管理**：管理和验证登录表单
- **登录操作**：处理登录请求并更新认证状态
- **社交媒体登录**：处理第三方登录逻辑
- **状态管理**：管理登录过程中的UI状态

主要属性和方法：

```dart
// 属性
final loginFormKey = GlobalKey<FormState>();
final emailController = TextEditingController();
final passwordController = TextEditingController();
final rememberMe = false.obs;
final isPasswordVisible = false.obs;
final isLoading = false.obs;

// 方法
String? validateEmail(String? value);     // 邮箱验证
String? validatePassword(String? value);  // 密码验证
Future<void> login();                     // 登录处理
void navigateToRegister();                // 导航到注册页
void navigateToForgotPassword();          // 导航到忘记密码页
void togglePasswordVisibility();          // 切换密码可见性
```

### `register_controller.dart`

`register_controller.dart` 负责处理用户注册相关的业务逻辑，包括：

- **注册表单管理**：管理和验证注册表单
- **用户注册**：处理注册请求并创建用户账号
- **协议同意**：管理用户协议和隐私政策的同意状态
- **验证码处理**：如果注册流程包含验证码，则处理相关逻辑

主要属性和方法：

```dart
// 属性
final registerFormKey = GlobalKey<FormState>();
final usernameController = TextEditingController();
final emailController = TextEditingController();
final passwordController = TextEditingController();
final confirmPasswordController = TextEditingController();
final agreeToTerms = false.obs;
final isPasswordVisible = false.obs;
final isLoading = false.obs;

// 方法
String? validateUsername(String? value);       // 用户名验证
String? validateEmail(String? value);          // 邮箱验证
String? validatePassword(String? value);       // 密码验证
String? validateConfirmPassword(String? value);// 确认密码验证
Future<void> register();                       // 注册处理
void navigateToLogin();                        // 导航到登录页
```

### `password_controller.dart`

`password_controller.dart` 负责处理密码找回和重置的业务逻辑，包括：

- **密码找回**：发送密码重置邮件或短信验证码
- **验证码验证**：验证用户提供的验证码
- **密码重置**：处理用户重置密码的请求
- **表单验证**：验证邮箱、验证码和新密码的有效性

主要属性和方法：

```dart
// 属性
final forgotPasswordFormKey = GlobalKey<FormState>();
final resetPasswordFormKey = GlobalKey<FormState>();
final emailController = TextEditingController();
final codeController = TextEditingController();
final newPasswordController = TextEditingController();
final confirmPasswordController = TextEditingController();
final isPasswordVisible = false.obs;
final isLoading = false.obs;
final countdown = 0.obs;  // 验证码倒计时

// 方法
String? validateEmail(String? value);           // 邮箱验证
String? validateCode(String? value);            // 验证码验证
String? validateNewPassword(String? value);     // 新密码验证
String? validateConfirmPassword(String? value); // 确认密码验证
Future<void> sendVerificationCode();            // 发送验证码
Future<void> verifyCode();                      // 验证验证码
Future<void> resetPassword();                   // 重置密码
void startCountdown();                          // 开始倒计时
void navigateToLogin();                         // 导航到登录页
```

## 实现示例

### `auth_controller.dart` 实现示例

```dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/models/user.dart';
import '../../../data/repositories/user_repository.dart';
import '../../../services/storage_service.dart';
import '../../../routes/app_routes.dart';

class AuthController extends GetxController {
  final UserRepository _userRepository;
  final StorageService _storageService;
  
  AuthController({
    required UserRepository userRepository,
    required StorageService storageService,
  }) : _userRepository = userRepository,
       _storageService = storageService;
  
  // 认证状态
  final isLoggedIn = false.obs;
  final currentUser = Rxn<User>();
  final isLoading = false.obs;
  
  // 令牌信息
  String? _accessToken;
  String? _refreshToken;
  
  @override
  void onInit() {
    super.onInit();
    ever(isLoggedIn, _handleAuthChanged);
    checkAuth();
  }
  
  // 处理认证状态变化
  void _handleAuthChanged(bool isAuthenticated) {
    if (isAuthenticated) {
      // 已登录，可以执行一些初始化操作
      Get.offAllNamed(AppRoutes.HOME);
    } else {
      // 未登录，跳转到登录页
      Get.offAllNamed(AppRoutes.AUTH);
    }
  }
  
  // 检查认证状态
  Future<bool> checkAuth() async {
    isLoading.value = true;
    try {
      // 从本地存储获取令牌
      _accessToken = await _storageService.read('access_token');
      _refreshToken = await _storageService.read('refresh_token');
      
      if (_accessToken != null) {
        // 验证令牌有效性
        final user = await _userRepository.getCurrentUser();
        if (user != null) {
          currentUser.value = user;
          isLoggedIn.value = true;
          return true;
        } else {
          // 令牌无效，尝试刷新
          return await refreshToken();
        }
      }
      return false;
    } catch (e) {
      return false;
    } finally {
      isLoading.value = false;
    }
  }
  
  // 登录
  Future<void> login(String email, String password, bool rememberMe) async {
    isLoading.value = true;
    try {
      final authResult = await _userRepository.login(email, password);
      
      // 保存令牌
      _accessToken = authResult.accessToken;
      _refreshToken = authResult.refreshToken;
      
      // 存储令牌
      await _storageService.write('access_token', _accessToken!);
      if (rememberMe) {
        await _storageService.write('refresh_token', _refreshToken!);
      }
      
      // 获取用户信息
      currentUser.value = await _userRepository.getCurrentUser();
      isLoggedIn.value = true;
    } catch (e) {
      Get.snackbar(
        '登录失败',
        e.toString(),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
  
  // 注册
  Future<void> register(User newUser, String password) async {
    isLoading.value = true;
    try {
      await _userRepository.register(newUser, password);
      Get.snackbar(
        '注册成功',
        '请登录您的账号',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      Get.toNamed(AppRoutes.LOGIN);
    } catch (e) {
      Get.snackbar(
        '注册失败',
        e.toString(),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
  
  // 退出登录
  Future<void> logout() async {
    isLoading.value = true;
    try {
      await _userRepository.logout();
      
      // 清除令牌和用户信息
      _accessToken = null;
      _refreshToken = null;
      await _storageService.remove('access_token');
      await _storageService.remove('refresh_token');
      
      currentUser.value = null;
      isLoggedIn.value = false;
    } catch (e) {
      print('Logout error: $e');
    } finally {
      isLoading.value = false;
    }
  }
  
  // 刷新令牌
  Future<bool> refreshToken() async {
    if (_refreshToken == null) return false;
    
    try {
      final authResult = await _userRepository.refreshToken(_refreshToken!);
      
      // 更新令牌
      _accessToken = authResult.accessToken;
      _refreshToken = authResult.refreshToken;
      
      // 存储新令牌
      await _storageService.write('access_token', _accessToken!);
      await _storageService.write('refresh_token', _refreshToken!);
      
      // 获取用户信息
      currentUser.value = await _userRepository.getCurrentUser();
      isLoggedIn.value = true;
      return true;
    } catch (e) {
      // 刷新失败，清除令牌
      _accessToken = null;
      _refreshToken = null;
      await _storageService.remove('access_token');
      await _storageService.remove('refresh_token');
      
      currentUser.value = null;
      isLoggedIn.value = false;
      return false;
    }
  }
  
  // 获取认证令牌
  String? getAccessToken() => _accessToken;
}
```

### `login_controller.dart` 实现示例

```dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../routes/app_routes.dart';
import '../controller/auth_controller.dart';

class LoginController extends GetxController {
  final AuthController _authController = Get.find<AuthController>();
  
  // 表单控制
  final loginFormKey = GlobalKey<FormState>();
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  
  // 状态管理
  final rememberMe = false.obs;
  final isPasswordVisible = false.obs;
  final isLoading = false.obs;
  
  @override
  void onClose() {
    emailController.dispose();
    passwordController.dispose();
    super.onClose();
  }
  
  // 切换密码可见性
  void togglePasswordVisibility() {
    isPasswordVisible.value = !isPasswordVisible.value;
  }
  
  // 邮箱验证
  String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入邮箱或用户名';
    }
    if (value.contains('@') && !GetUtils.isEmail(value)) {
      return '请输入有效的邮箱地址';
    }
    return null;
  }
  
  // 密码验证
  String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入密码';
    }
    if (value.length < 6) {
      return '密码至少需要6个字符';
    }
    return null;
  }
  
  // 登录处理
  Future<void> login() async {
    if (isLoading.value) return; // 防止重复点击
    
    // 表单验证
    if (loginFormKey.currentState?.validate() ?? false) {
      isLoading.value = true;
      try {
        await _authController.login(
          emailController.text.trim(),
          passwordController.text,
          rememberMe.value,
        );
      } finally {
        isLoading.value = false;
      }
    }
  }
  
  // 社交媒体登录
  Future<void> loginWithGoogle() async {
    if (isLoading.value) return;
    
    isLoading.value = true;
    try {
      // 实现谷歌登录逻辑
      // await _authController.loginWithGoogle();
    } catch (e) {
      Get.snackbar(
        '登录失败',
        '谷歌登录失败：${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }
  
  Future<void> loginWithApple() async {
    // 实现苹果登录逻辑
  }
  
  Future<void> loginWithWechat() async {
    // 实现微信登录逻辑
  }
  
  // 导航
  void navigateToRegister() {
    Get.toNamed(AppRoutes.REGISTER);
  }
  
  void navigateToForgotPassword() {
    Get.toNamed(AppRoutes.FORGOT_PASSWORD);
  }
}
```

## 设计考量

1. **状态管理**：
   - 使用GetX的响应式状态管理（Obx和.obs属性）
   - 集中管理UI状态和业务状态
   - 状态变化自动触发UI更新

2. **依赖注入**：
   - 通过GetX的依赖注入机制注入服务和仓库
   - 使用控制器之间的引用而非直接访问状态
   - 提高组件的可测试性和可重用性

3. **错误处理**：
   - 使用try-catch捕获并处理异常
   - 使用Snackbar等UI组件展示友好的错误信息
   - 日志记录错误，便于调试和分析

4. **安全性**：
   - 安全存储用户令牌
   - 使用刷新令牌机制增强安全性
   - 在适当的时候清除敏感信息

## 与其他目录的关系

- **views目录**：控制器提供UI所需的状态和行为
- **bindings目录**：注册控制器依赖
- **routes目录**：控制器处理页面导航
- **services/repositories目录**：控制器调用服务和仓库进行数据操作

## 最佳实践

1. **职责分离**：
   - 全局认证状态由AuthController集中管理
   - 特定页面的逻辑由专门的控制器处理
   - 避免控制器之间直接访问对方的内部状态

2. **响应式编程**：
   - 使用响应式变量管理状态
   - 使用ever、debounce等进行状态监听
   - 控制状态更新的粒度，避免不必要的重建

3. **资源管理**：
   - 在onClose中释放资源（如控制器）
   - 避免内存泄漏
   - 使用懒加载加载控制器

4. **表单处理**：
   - 集中管理表单验证逻辑
   - 提供明确的错误信息
   - 防止重复提交表单

## 常见问题与解决方案

1. **控制器生命周期**：
   - 问题：控制器创建和销毁的时机不明确
   - 解决：使用binding系统和GetX的生命周期方法管理控制器

2. **认证状态同步**：
   - 问题：多个控制器需要访问认证状态
   - 解决：通过Get.find()访问全局AuthController

3. **表单状态保持**：
   - 问题：页面切换后表单状态丢失
   - 解决：使用持久化存储或在全局控制器中保存临时状态

4. **控制器之间通信**：
   - 问题：不同控制器之间需要相互通信
   - 解决：使用依赖注入、事件总线或全局状态存储